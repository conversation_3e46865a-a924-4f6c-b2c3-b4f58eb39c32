(function(n,t,i,r){"use strict";function u(t,i){this._defaults=f;this.options=n.extend(!0,{},f,i);this.main=n(t);this.nav=this.main.children("ul");this.steps=n("li > a",this.nav);this.container=this.main.children("div");this.pages=this.container.children("div");this.current_index=null;this.is_animating=!1;this.init()}var f={selected:0,keyNavigation:!0,autoAdjustHeight:!0,cycleSteps:!1,backButtonSupport:!0,useURLhash:!0,lang:{next:"下一步",previous:"上一步"},toolbarSettings:{toolbarPosition:"bottom",toolbarButtonPosition:"right",showNextButton:!0,showPreviousButton:!0,toolbarExtraButtons:[]},anchorSettings:{anchorClickable:!0,enableAllAnchors:!1,markDoneStep:!0,enableAnchorOnDoneStep:!0},contentURL:null,disabledSteps:[],errorSteps:[],theme:"default",transitionEffect:"none",transitionSpeed:"400"};n.extend(u.prototype,{init:function(){var r=this.options.selected,i,u,f;this.options.useURLhash&&(i=t.location.hash,i&&i.length>0&&(u=n("a[href*="+i+"]"),u.length>0&&(f=this.steps.index(u),r=f>=0?f:r)));this._setElements();this._setToolbar();this._setEvents();this._showStep(r)},_setElements:function(){this.main.addClass("sw-main sw-theme-"+this.options.theme);this.nav.addClass("nav nav-tabs step-anchor");this.options.anchorSettings.enableAllAnchors!==!1&&this.options.anchorSettings.anchorClickable!==!1&&this.steps.parent("li").addClass("clickable");this.container.addClass("sw-container tab-content");this.pages.addClass("step-content");var t=this;return this.options.disabledSteps&&this.options.disabledSteps.length>0&&n.each(this.options.disabledSteps,function(n,i){t.steps.eq(i).parent("li").addClass("disabled")}),this.options.errorSteps&&this.options.errorSteps.length>0&&n.each(this.options.errorSteps,function(n,i){t.steps.eq(i).parent("li").addClass("danger")}),!0},_setToolbar:function(){var r,t;if(this.options.toolbarSettings.toolbarPosition==="none")return!0;var f=this.options.toolbarSettings.showNextButton!==!1?n("<button><\/button>").text(this.options.lang.next).addClass("btn btn-secondary sw-btn-next").attr("type","button"):null,e=this.options.toolbarSettings.showPreviousButton!==!1?n("<button><\/button>").text(this.options.lang.previous).addClass("btn btn-secondary sw-btn-prev").attr("type","button"):null,u=n("<div><\/div>").addClass("btn-group navbar-btn sw-btn-group pull-"+this.options.toolbarSettings.toolbarButtonPosition).attr("role","group").append(e,f),i=null;this.options.toolbarSettings.toolbarExtraButtons&&this.options.toolbarSettings.toolbarExtraButtons.length>0&&(i=n("<div><\/div>").addClass("btn-group navbar-btn sw-btn-group-extra pull-"+this.options.toolbarSettings.toolbarButtonPosition).attr("role","group"),n.each(this.options.toolbarSettings.toolbarExtraButtons,function(t,r){r.css=r.css&&r.css.length>0?r.css:"btn-secondary";i.append(n("<button><\/button>").text(r.label).addClass("btn "+r.css).attr("type","button").on("click",function(){r.onClick.call(this)}))}));switch(this.options.toolbarSettings.toolbarPosition){case"top":r=n("<nav><\/nav>").addClass("navbar btn-toolbar sw-toolbar sw-toolbar-top");r.append(u);this.options.toolbarSettings.toolbarButtonPosition==="left"?r.append(i):r.prepend(i);this.container.before(r);break;case"bottom":t=n("<nav><\/nav>").addClass("navbar btn-toolbar sw-toolbar sw-toolbar-bottom");t.append(u);this.options.toolbarSettings.toolbarButtonPosition==="left"?t.append(i):t.prepend(i);this.container.after(t);break;case"both":r=n("<nav><\/nav>").addClass("navbar btn-toolbar sw-toolbar sw-toolbar-top");r.append(u);this.options.toolbarSettings.toolbarButtonPosition==="left"?r.append(i):r.prepend(i);this.container.before(r);t=n("<nav><\/nav>").addClass("navbar btn-toolbar sw-toolbar sw-toolbar-bottom");t.append(u.clone(!0));this.options.toolbarSettings.toolbarButtonPosition==="left"?t.append(i.clone(!0)):t.prepend(i.clone(!0));this.container.after(t);break;default:t=n("<nav><\/nav>").addClass("navbar btn-toolbar sw-toolbar sw-toolbar-bottom");t.append(u);this.options.toolbarSettings.toolbarButtonPosition==="left"?t.append(i):t.prepend(i);this.container.after(t)}return!0},_setEvents:function(){var r=this;n(this.steps).on("click",function(n){if(n.preventDefault(),r.options.anchorSettings.anchorClickable===!1)return!0;var t=r.steps.index(this);if(r.options.anchorSettings.enableAnchorOnDoneStep===!1&&r.steps.eq(t).parent("li").hasClass("done"))return!0;t!==r.current_index&&(r.options.anchorSettings.enableAllAnchors!==!1&&r.options.anchorSettings.anchorClickable!==!1?r._showStep(t):r.steps.eq(t).parent("li").hasClass("done")&&r._showStep(t))});n(".sw-btn-next",this.main).on("click",function(n){n.preventDefault();r.steps.index(this)!==r.current_index&&r._showNext()});n(".sw-btn-prev",this.main).on("click",function(n){n.preventDefault();r.steps.index(this)!==r.current_index&&r._showPrevious()});if(this.options.keyNavigation&&n(i).keyup(function(n){r._keyNav(n)}),this.options.backButtonSupport)n(t).on("hashchange",function(){if(!r.options.useURLhash)return!0;if(t.location.hash){var i=n("a[href*="+t.location.hash+"]");i&&i.length>0&&r._showStep(r.steps.index(i))}});return!0},_showNext:function(){for(var n=this.current_index+1,t=n;t<this.steps.length;t++)if(!this.steps.eq(t).parent("li").hasClass("disabled")){n=t;break}if(this.steps.length<=n){if(!this.options.cycleSteps)return!1;n=0}return this._showStep(n),!0},_showPrevious:function(){for(var n=this.current_index-1,t=n;t>=0;t--)if(!this.steps.eq(t).parent("li").hasClass("disabled")){n=t;break}if(0>n){if(!this.options.cycleSteps)return!1;n=this.steps.length-1}return this._showStep(n),!0},_showStep:function(n){return this.steps.eq(n)?n==this.current_index?!1:this.steps.eq(n).parent("li").hasClass("disabled")?!1:(this._loadStepContent(n),!0):!1},_loadStepContent:function(t){var f=this,i=this.steps.eq(t),r=i.data("content-url")&&i.data("content-url").length>0?i.data("content-url"):this.options.contentURL,u;return r&&r.length>0&&!i.data("has-content")?(u=i.length>0?n(i.attr("href"),this.main):null,n.ajax({url:r,type:"POST",data:{step_number:t},dataType:"text",beforeSend:function(){i.parent("li").addClass("loading")},error:function(){i.parent("li").removeClass("loading")},success:function(n){n&&n.length>0&&(i.data("has-content",!0),u.html(n));i.parent("li").removeClass("loading");f._transitPage(t)}})):this._transitPage(t),!0},_transitPage:function(i){var e=this;if(this.is_animating)return!1;var o=this.steps.eq(this.current_index),r=o.length>0?n(o.attr("href"),this.main):null,f=this.steps.eq(i),u=f.length>0?n(f.attr("href"),this.main):null;return this.current_index!==null&&this._triggerEvent("leaveStep",[o,this.current_index])===!1?!1:(this.is_animating=!0,this.options.transitionEffect=this.options.transitionEffect.toLowerCase(),this.pages.finish(),this.options.transitionEffect==="slide"?r&&r.length>0?r.slideUp("fast",this.options.transitionEasing,function(){u.slideDown(e.options.transitionSpeed,e.options.transitionEasing)}):u.slideDown(this.options.transitionSpeed,this.options.transitionEasing):this.options.transitionEffect==="fade"?r&&r.length>0?r.fadeOut("fast",this.options.transitionEasing,function(){u.fadeIn("fast",e.options.transitionEasing,function(){n(this).show()})}):u.fadeIn(this.options.transitionSpeed,this.options.transitionEasing,function(){n(this).show()}):(r&&r.length>0&&r.hide(),u.show()),t.location.hash=f.attr("href"),this._setAnchor(i),this._setButtons(i),this._fixHeight(i),this.current_index=i,this.is_animating=!1,this._triggerEvent("showStep",[f,this.current_index]),!0)},_setAnchor:function(n){return this.steps.eq(this.current_index).parent("li").removeClass("active danger loading"),this.options.anchorSettings.markDoneStep!==!1&&this.current_index!==null&&this.steps.eq(this.current_index).parent("li").addClass("done"),this.steps.eq(n).parent("li").removeClass("done danger loading").addClass("active"),!0},_setButtons:function(t){return this.options.cycleSteps||(0>=t?n(".sw-btn-prev",this.main).addClass("disabled"):n(".sw-btn-prev",this.main).removeClass("disabled"),this.steps.length-1<=t?n(".sw-btn-next",this.main).addClass("disabled"):n(".sw-btn-next",this.main).removeClass("disabled")),!0},_keyNav:function(n){var t=this;switch(n.which){case 37:t._showPrevious();n.preventDefault();break;case 39:t._showNext();n.preventDefault();break;default:return}},_fixHeight:function(t){if(this.options.autoAdjustHeight){var i=this.steps.eq(t).length>0?n(this.steps.eq(t).attr("href"),this.main):null;this.container.finish().animate({height:i.outerHeight()},this.options.transitionSpeed,function(){})}return!0},_triggerEvent:function(t,i){var r=n.Event(t);return(this.main.trigger(r,i),r.isDefaultPrevented())?!1:r.result},theme:function(n){this.main.removeClass("sw-theme-"+this.options.theme);this.options.theme=n;this.main.addClass("sw-theme-"+this.options.theme)},next:function(){this._showNext()},prev:function(){this._showPrevious()},reset:function(){this.container.stop(!0);this.pages.stop(!0);this.pages.hide();this.current_index=null;t.location.hash=this.steps.eq(this.options.selected).attr("href");n(".sw-toolbar",this.main).remove();this.steps.removeClass();this.steps.parents("li").removeClass();this.steps.data("has-content",!1);this.init()}});n.fn.smartWizard=function(t){var f=arguments,i;return t===r||typeof t=="object"?this.each(function(){n.data(this,"smartWizard")||n.data(this,"smartWizard",new u(this,t))}):typeof t=="string"&&t[0]!=="_"&&t!=="init"?(i=n.data(this[0],"smartWizard"),t==="destroy"&&n.data(this,"smartWizard",null),i instanceof u&&typeof i[t]=="function"?i[t].apply(i,Array.prototype.slice.call(f,1)):this):void 0}})(jQuery,window,document);