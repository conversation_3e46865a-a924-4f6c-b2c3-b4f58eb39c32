/* iCheck plugin Square skin, red
----------------------------------- */
.icheckbox_square-red,
.iradio_square-red {
    display: inline-block;
    *display: inline;
    vertical-align: middle;
    margin: 0;
    padding: 0;
    width: 22px;
    height: 22px;
    background: url(red.png) no-repeat;
    border: none;
    cursor: pointer;
}

.icheckbox_square-red {
    background-position: 0 0;
}
    .icheckbox_square-red.hover {
        background-position: -24px 0;
    }
    .icheckbox_square-red.checked {
        background-position: -48px 0;
    }
    .icheckbox_square-red.disabled {
        background-position: -72px 0;
        cursor: default;
    }
    .icheckbox_square-red.checked.disabled {
        background-position: -96px 0;
    }

.iradio_square-red {
    background-position: -120px 0;
}
    .iradio_square-red.hover {
        background-position: -144px 0;
    }
    .iradio_square-red.checked {
        background-position: -168px 0;
    }
    .iradio_square-red.disabled {
        background-position: -192px 0;
        cursor: default;
    }
    .iradio_square-red.checked.disabled {
        background-position: -216px 0;
    }

/* HiDPI support */
@media (-o-min-device-pixel-ratio: 5/4), (-webkit-min-device-pixel-ratio: 1.25), (min-resolution: 120dpi) {
    .icheckbox_square-red,
    .iradio_square-red {
        background-image: url(<EMAIL>);
        -webkit-background-size: 240px 24px;
        background-size: 240px 24px;
    }
}