/*! laydate-v5.0.9 日期与时间组件 MIT License  http://www.layui.com/laydate/  By 贤心 */
!function(){"use strict";var c=window.layui&&layui.define,l={getPath:function(){var n=document.currentScript?document.currentScript.src:function(){for(var i,n=document.scripts,r=n.length-1,t=r;t>0;t--)if("interactive"===n[t].readyState){i=n[t].src;break}return i||n[r].src}();return n.substring(0,n.lastIndexOf("/")+1)}(),getStyle:function(n,t){var i=n.currentStyle?n.currentStyle:window.getComputedStyle(n,null);return i[i.getPropertyValue?"getPropertyValue":"getAttribute"](t)},link:function(n,t,i){var e,u;if(r.path){e=document.getElementsByTagName("head")[0];u=document.createElement("link");"string"==typeof t&&(i=t);var o=(i||n).replace(/\.|\//g,""),f="layuicss-"+o,s=0;u.rel="stylesheet";u.href=r.path+n;u.id=f;document.getElementById(f)||e.appendChild(u);"function"==typeof t&&!function h(){return++s>80?window.console&&console.error("laydate.css: Invalid"):void(1989===parseInt(l.getStyle(document.getElementById(f),"width"))?t():setTimeout(h,100))}()}}},r={v:"5.0.9",config:{},index:window.laydate&&window.laydate.v?1e5:0,path:l.getPath,set:function(t){var i=this;return i.config=n.extend({},i.config,t),i},ready:function(n){var t="laydate",i=(c?"modules/laydate/":"theme/")+"default/laydate.css?v="+r.v+"";return c?layui.addcss(i,n,t):l.link(i,n,t),this}},b=function(){var n=this;return{hint:function(t){n.hint.call(n,t)},config:n.config}},k="laydate",d=".layui-laydate",u="layui-this",f="laydate-disabled",s="开始日期超出了结束日期<br>建议重新选择",e=[100,2e5],p="layui-laydate-static",a="layui-laydate-list",h="laydate-selected",v="layui-laydate-hint",g="laydate-day-prev",nt="laydate-day-next",tt="layui-laydate-footer",o=".laydate-btns-confirm",y="laydate-time-text",w=".laydate-btns-time",t=function(t){var i=this;i.index=++r.index;i.config=n.extend({},i.config,r.config,t);r.ready(function(){i.init()})},n=function(n){return new i(n)},i=function(n){for(var t=0,i="object"==typeof n?[n]:(this.selector=n,document.querySelectorAll(n||null));t<i.length;t++)this.push(i[t])};i.prototype=[];i.prototype.constructor=i;n.extend=function(){var t=1,n=arguments,i=function(n,t){n=n||(t.constructor===Array?[]:{});for(var r in t)n[r]=t[r]&&t[r].constructor===Object?i(n[r],t[r]):t[r];return n};for(n[0]="object"==typeof n[0]?n[0]:{};t<n.length;t++)"object"==typeof n[t]&&i(n[0],n[t]);return n[0]};n.ie=function(){var n=navigator.userAgent.toLowerCase();return!!(window.ActiveXObject||"ActiveXObject"in window)&&((n.match(/msie\s(\d+)/)||[])[1]||"11")}();n.stope=function(n){n=n||window.event;n.stopPropagation?n.stopPropagation():n.cancelBubble=!0};n.each=function(n,t){var i,r=this;if("function"!=typeof t)return r;if(n=n||[],n.constructor===Object){for(i in n)if(t.call(n[i],i,n[i]))break}else for(i=0;i<n.length&&!t.call(n[i],i,n[i]);i++);return r};n.digit=function(n,t){var r="",i;for(n=String(n),t=t||2,i=n.length;i<t;i++)r+="0";return n<Math.pow(10,t)?r+(0|n):n};n.elem=function(t,i){var r=document.createElement(t);return n.each(i||{},function(n,t){r.setAttribute(n,t)}),r};i.addStr=function(t,i){return t=t.replace(/\s+/," "),i=i.replace(/\s+/," ").split(" "),n.each(i,function(n,i){new RegExp("\\b"+i+"\\b").test(t)||(t=t+" "+i)}),t.replace(/^\s|\s$/,"")};i.removeStr=function(t,i){return t=t.replace(/\s+/," "),i=i.replace(/\s+/," ").split(" "),n.each(i,function(n,i){var r=new RegExp("\\b"+i+"\\b");r.test(t)&&(t=t.replace(r,""))}),t.replace(/\s+/," ").replace(/^\s|\s$/,"")};i.prototype.find=function(t){var i=this,r=0,u=[],f="object"==typeof t;return this.each(function(n,e){for(var o=f?[t]:e.querySelectorAll(t||null);r<o.length;r++)u.push(o[r]);i.shift()}),f||(i.selector=(i.selector?i.selector+" ":"")+t),n.each(u,function(n,t){i.push(t)}),i};i.prototype.each=function(t){return n.each.call(this,this,t)};i.prototype.addClass=function(n,t){return this.each(function(r,u){u.className=i[t?"removeStr":"addStr"](u.className,n)})};i.prototype.removeClass=function(n){return this.addClass(n,!0)};i.prototype.hasClass=function(n){var t=!1;return this.each(function(i,r){new RegExp("\\b"+n+"\\b").test(r.className)&&(t=!0)}),t};i.prototype.attr=function(n,t){var i=this;return void 0===t?function(){if(i.length>0)return i[0].getAttribute(n)}():i.each(function(i,r){r.setAttribute(n,t)})};i.prototype.removeAttr=function(n){return this.each(function(t,i){i.removeAttribute(n)})};i.prototype.html=function(n){return this.each(function(t,i){i.innerHTML=n})};i.prototype.val=function(n){return this.each(function(t,i){i.value=n})};i.prototype.append=function(n){return this.each(function(t,i){"object"==typeof n?i.appendChild(n):i.innerHTML=i.innerHTML+n})};i.prototype.remove=function(n){return this.each(function(t,i){n?i.removeChild(n):i.parentNode.removeChild(i)})};i.prototype.on=function(n,t){return this.each(function(i,r){r.attachEvent?r.attachEvent("on"+n,function(n){n.target=n.srcElement;t.call(r,n)}):r.addEventListener(n,t,!1)})};i.prototype.off=function(n,t){return this.each(function(i,r){r.detachEvent?r.detachEvent("on"+n,t):r.removeEventListener(n,t,!1)})};t.isLeapYear=function(n){return n%4==0&&n%100!=0||n%400==0};t.prototype.config={type:"date",range:!1,format:"yyyy-MM-dd",value:null,min:"1900-1-1",max:"2099-12-31",trigger:"focus",show:!1,showBottom:!0,btns:["clear","now","confirm"],lang:"cn",theme:"default",position:null,calendar:!1,mark:{},zIndex:null,done:null,change:null};t.prototype.lang=function(){var t=this,i=t.config,n={cn:{weeks:["日","一","二","三","四","五","六"],time:["时","分","秒"],timeTips:"选择时间",startTime:"开始时间",endTime:"结束时间",dateTips:"返回日期",month:["一","二","三","四","五","六","七","八","九","十","十一","十二"],tools:{confirm:"确定",clear:"清空",now:"现在"}},en:{weeks:["Su","Mo","Tu","We","Th","Fr","Sa"],time:["Hours","Minutes","Seconds"],timeTips:"Select Time",startTime:"Start Time",endTime:"End Time",dateTips:"Select Date",month:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],tools:{confirm:"Confirm",clear:"Clear",now:"Now"}}};return n[i.lang]||n.cn};t.prototype.init=function(){var i=this,t=i.config,r="yyyy|y|MM|M|dd|d|HH|H|mm|m|ss|s",u="static"===t.position,f={year:"yyyy",month:"yyyy-MM",date:"yyyy-MM-dd",time:"HH:mm:ss",datetime:"yyyy-MM-dd HH:mm:ss"};t.elem=n(t.elem);t.eventElem=n(t.eventElem);t.elem[0]&&(t.range===!0&&(t.range="-"),t.format===f.date&&(t.format=f[t.type]),i.format=t.format.match(new RegExp(r+"|.","g"))||[],i.EXP_IF="",i.EXP_SPLIT="",n.each(i.format,function(n,t){var u=new RegExp(r).test(t)?"\\d{"+function(){return new RegExp(r).test(i.format[0===n?n+1:n-1]||"")?/^yyyy|y$/.test(t)?4:t.length:/^yyyy$/.test(t)?"1,4":/^y$/.test(t)?"1,308":"1,2"}()+"}":"\\"+t;i.EXP_IF=i.EXP_IF+u;i.EXP_SPLIT=i.EXP_SPLIT+"("+u+")"}),i.EXP_IF=new RegExp("^"+(t.range?i.EXP_IF+"\\s\\"+t.range+"\\s"+i.EXP_IF:i.EXP_IF)+"$"),i.EXP_SPLIT=new RegExp("^"+i.EXP_SPLIT+"$",""),i.isInput(t.elem[0])||"focus"===t.trigger&&(t.trigger="click"),t.elem.attr("lay-key")||(t.elem.attr("lay-key",i.index),t.eventElem.attr("lay-key",i.index)),t.mark=n.extend({},t.calendar&&"cn"===t.lang?{"0-1-1":"元旦","0-2-14":"情人","0-3-8":"妇女","0-3-12":"植树","0-4-1":"愚人","0-5-1":"劳动","0-5-4":"青年","0-6-1":"儿童","0-9-10":"教师","0-9-18":"国耻","0-10-1":"国庆","0-12-25":"圣诞"}:{},t.mark),n.each(["min","max"],function(n,i){var r=[],f=[];if("number"==typeof t[i]){var e=t[i],s=(new Date).getTime(),o=864e5,u=new Date(e?e<o?s+e*o:e:s);r=[u.getFullYear(),u.getMonth()+1,u.getDate()];e<o||(f=[u.getHours(),u.getMinutes(),u.getSeconds()])}else r=(t[i].match(/\d+-\d+-\d+/)||[""])[0].split("-"),f=(t[i].match(/\d+:\d+:\d+/)||[""])[0].split(":");t[i]={year:0|r[0]||(new Date).getFullYear(),month:r[1]?(0|r[1])-1:(new Date).getMonth(),date:0|r[2]||(new Date).getDate(),hours:0|f[0],minutes:0|f[1],seconds:0|f[2]}}),i.elemID="layui-laydate"+t.elem.attr("lay-key"),(t.show||u)&&i.render(),u||i.events(),t.value&&(t.value.constructor===Date?i.setValue(i.parse(0,i.systemDate(t.value))):i.setValue(t.value)))};t.prototype.render=function(){var r=this,i=r.config,o=r.lang(),s="static"===i.position,u=r.elem=n.elem("div",{id:r.elemID,"class":["layui-laydate",i.range?" layui-laydate-range":"",s?" "+p:"",i.theme&&"default"!==i.theme&&!/^#/.test(i.theme)?" laydate-theme-"+i.theme:""].join("")}),e=r.elemMain=[],l=r.elemHeader=[],a=r.elemCont=[],v=r.table=[],c=r.footer=n.elem("div",{"class":tt}),f,h;(i.zIndex&&(u.style.zIndex=i.zIndex),n.each(new Array(2),function(t){if(!i.range&&t>0)return!0;var f=n.elem("div",{"class":"layui-laydate-header"}),s=[function(){var t=n.elem("i",{"class":"layui-icon laydate-icon laydate-prev-y"});return t.innerHTML="&#xe65a;",t}(),function(){var t=n.elem("i",{"class":"layui-icon laydate-icon laydate-prev-m"});return t.innerHTML="&#xe603;",t}(),function(){var t=n.elem("div",{"class":"laydate-set-ym"}),i=n.elem("span"),r=n.elem("span");return t.appendChild(i),t.appendChild(r),t}(),function(){var t=n.elem("i",{"class":"layui-icon laydate-icon laydate-next-m"});return t.innerHTML="&#xe602;",t}(),function(){var t=n.elem("i",{"class":"layui-icon laydate-icon laydate-next-y"});return t.innerHTML="&#xe65b;",t}()],u=n.elem("div",{"class":"layui-laydate-content"}),r=n.elem("table"),h=n.elem("thead"),c=n.elem("tr");n.each(s,function(n,t){f.appendChild(t)});h.appendChild(c);n.each(new Array(6),function(t){var i=r.insertRow(0);n.each(new Array(7),function(r){if(0===t){var u=n.elem("th");u.innerHTML=o.weeks[r];c.appendChild(u)}i.insertCell(r)})});r.insertBefore(h,r.children[0]);u.appendChild(r);e[t]=n.elem("div",{"class":"layui-laydate-main laydate-main-list-"+t});e[t].appendChild(f);e[t].appendChild(u);l.push(s);a.push(u);v.push(r)}),n(c).html(function(){var t=[],r=[];return"datetime"===i.type&&t.push('<span lay-type="datetime" class="laydate-btns-time">'+o.timeTips+"<\/span>"),n.each(i.btns,function(n,t){var u=o.tools[t]||"btn";i.range&&"now"===t||(s&&"clear"===t&&(u="cn"===i.lang?"重置":"Reset"),r.push('<span lay-type="'+t+'" class="laydate-btns-'+t+'">'+u+"<\/span>"))}),t.push('<div class="laydate-footer-btns">'+r.join("")+"<\/div>"),t.join("")}()),n.each(e,function(n,t){u.appendChild(t)}),i.showBottom&&u.appendChild(c),/^#/.test(i.theme))&&(f=n.elem("style"),h="#{{id}} .layui-laydate-header{background-color:{{theme}};}#{{id}} .layui-this{background-color:{{theme}} !important;}".replace(/{{id}}/g,r.elemID).replace(/{{theme}}/g,i.theme),"styleSheet"in f?(f.setAttribute("type","text/css"),f.styleSheet.cssText=h):f.innerHTML=h,n(u).addClass("laydate-theme-molv"),u.appendChild(f));r.remove(t.thisElemDate);s?i.elem.append(u):(document.body.appendChild(u),r.position());r.checkDate().calendar();r.changeEvent();t.thisElemDate=r.elemID;"function"==typeof i.ready&&i.ready(n.extend({},i.dateTime,{month:i.dateTime.month+1}))};t.prototype.remove=function(t){var i=this,r=(i.config,n("#"+(t||i.elemID)));return r.hasClass(p)||i.checkDate(function(){r.remove()}),i};t.prototype.position=function(){var n=this,t=n.config,c=n.bindElem||t.elem[0],i=c.getBoundingClientRect(),s=n.elem.offsetWidth,r=n.elem.offsetHeight,h=function(n){return n=n?"scrollLeft":"scrollTop",document.body[n]|document.documentElement[n]},u=function(n){return document.documentElement[n?"clientWidth":"clientHeight"]},f=5,o=i.left,e=i.bottom;o+s+f>u("width")&&(o=u("width")-s-f);e+r+f>u()&&(e=i.top>r?i.top-r:u()-r,e-=2*f);t.position&&(n.elem.style.position=t.position);n.elem.style.left=o+("fixed"===t.position?0:h(1))+"px";n.elem.style.top=e+("fixed"===t.position?0:h())+"px"};t.prototype.hint=function(t){var i=this,r=(i.config,n.elem("div",{"class":v}));r.innerHTML=t||"";n(i.elem).find("."+v).remove();i.elem.appendChild(r);clearTimeout(i.hinTimer);i.hinTimer=setTimeout(function(){n(i.elem).find("."+v).remove()},3e3)};t.prototype.getAsYM=function(n,t,i){return i?t--:t++,t<0&&(t=11,n--),t>11&&(t=0,n++),[n,t]};t.prototype.systemDate=function(n){var t=n||new Date;return{year:t.getFullYear(),month:t.getMonth(),date:t.getDate(),hours:n?n.getHours():0,minutes:n?n.getMinutes():0,seconds:n?n.getSeconds():0}};t.prototype.checkDate=function(t){var h,o,i=this,u=(new Date,i.config),c=u.dateTime=u.dateTime||i.systemDate(),s=i.bindElem||u.elem[0],f=(i.isInput(s)?"val":"html",i.isInput(s)?s.value:"static"===u.position?"":s.innerHTML),l=function(n){n.year>e[1]&&(n.year=e[1],o=!0);n.month>11&&(n.month=11,o=!0);n.hours>23&&(n.hours=0,o=!0);n.minutes>59&&(n.minutes=0,n.hours++,o=!0);n.seconds>59&&(n.seconds=0,n.minutes++,o=!0);h=r.getEndDate(n.month+1,n.year);n.date>h&&(n.date=h,o=!0)},a=function(t,r,f){var s=["startTime","endTime"];r=(r.match(i.EXP_SPLIT)||[]).slice(1);f=f||0;u.range&&(i[s[f]]=i[s[f]]||{});n.each(i.format,function(n,h){var c=parseFloat(r[n]);r[n].length<h.length&&(o=!0);/yyyy|y/.test(h)?(c<e[0]&&(c=e[0],o=!0),t.year=c):/MM|M/.test(h)?(c<1&&(c=1,o=!0),t.month=c-1):/dd|d/.test(h)?(c<1&&(c=1,o=!0),t.date=c):/HH|H/.test(h)?(c<1&&(c=0,o=!0),t.hours=c,u.range&&(i[s[f]].hours=c)):/mm|m/.test(h)?(c<1&&(c=0,o=!0),t.minutes=c,u.range&&(i[s[f]].minutes=c)):/ss|s/.test(h)&&(c<1&&(c=0,o=!0),t.seconds=c,u.range&&(i[s[f]].seconds=c))});l(t)};return"limit"===t?(l(c),i):(f=f||u.value,"string"==typeof f&&(f=f.replace(/\s+/g," ").replace(/^\s|\s$/g,"")),i.startState&&!i.endState&&(delete i.startState,i.endState=!0),"string"==typeof f&&f?i.EXP_IF.test(f)?u.range?(f=f.split(" "+u.range+" "),i.startDate=i.startDate||i.systemDate(),i.endDate=i.endDate||i.systemDate(),u.dateTime=n.extend({},i.startDate),n.each([i.startDate,i.endDate],function(n,t){a(t,f[n],n)})):a(c,f):(i.hint("日期格式不合法<br>必须遵循下述格式：<br>"+(u.range?u.format+" "+u.range+" "+u.format:u.format)+"<br>已为你重置"),o=!0):f&&f.constructor===Date?u.dateTime=i.systemDate(f):(u.dateTime=i.systemDate(),delete i.startState,delete i.endState,delete i.startDate,delete i.endDate,delete i.startTime,delete i.endTime),l(c),o&&f&&i.setValue(u.range?i.endDate?i.parse():"":i.parse()),t&&t(),i)};t.prototype.mark=function(t,i){var r,u=this,f=u.config;return n.each(f.mark,function(n,t){var u=n.split("-");u[0]!=i[0]&&0!=u[0]||u[1]!=i[1]&&0!=u[1]||u[2]!=i[2]||(r=t||i[2])}),r&&t.html('<span class="laydate-day-mark">'+r+"<\/span>"),u};t.prototype.limit=function(t,i,r,u){var o,h=this,s=h.config,e={},c=s[r>41?"endDate":"dateTime"],l=n.extend({},c,i||{});return n.each({now:l,min:s.min,max:s.max},function(t,i){e[t]=h.newDate(n.extend({year:i.year,month:i.month,date:i.date},function(){var t={};return n.each(u,function(n,r){t[r]=i[r]}),t}())).getTime()}),o=e.now<e.min||e.now>e.max,t&&t[o?"addClass":"removeClass"](f),o};t.prototype.calendar=function(t){var h,v,l,i=this,s=i.config,f=t||s.dateTime,y=new Date,b=i.lang(),p="date"!==s.type&&"datetime"!==s.type,w=t?1:0,k=n(i.table[w]).find("td"),c=n(i.elemHeader[w][2]).find("span"),a;return(f.year<e[0]&&(f.year=e[0],i.hint("最低只能支持到公元"+e[0]+"年")),f.year>e[1]&&(f.year=e[1],i.hint("最高只能支持到公元"+e[1]+"年")),i.firstDate||(i.firstDate=n.extend({},f)),y.setFullYear(f.year,f.month,1),h=y.getDay(),v=r.getEndDate(f.month||12,f.year),l=r.getEndDate(f.month+1,f.year),n.each(k,function(t,r){var e=[f.year,f.month],o=0;r=n(r);r.removeAttr("class");t<h?(o=v-h+t,r.addClass("laydate-day-prev"),e=i.getAsYM(f.year,f.month,"sub")):t>=h&&t<l+h?(o=t-h,s.range||o+1===f.date&&r.addClass(u)):(o=t-l-h,r.addClass("laydate-day-next"),e=i.getAsYM(f.year,f.month));e[1]++;e[2]=o+1;r.attr("lay-ymd",e.join("-")).html(e[2]);i.mark(r,e).limit(r,{year:e[0],month:e[1]-1,date:e[2]},t)}),n(c[0]).attr("lay-ym",f.year+"-"+(f.month+1)),n(c[1]).attr("lay-ym",f.year+"-"+(f.month+1)),"cn"===s.lang?(n(c[0]).attr("lay-type","year").html(f.year+"年"),n(c[1]).attr("lay-type","month").html(f.month+1+"月")):(n(c[0]).attr("lay-type","month").html(b.month[f.month]),n(c[1]).attr("lay-type","year").html(f.year)),p&&(s.range&&(t?i.endDate=i.endDate||{year:f.year+("year"===s.type?1:0),month:f.month+("month"===s.type?0:-1)}:i.startDate=i.startDate||{year:f.year,month:f.month},t&&(i.listYM=[[i.startDate.year,i.startDate.month+1],[i.endDate.year,i.endDate.month+1]],i.list(s.type,0).list(s.type,1),"time"===s.type?i.setBtnStatus("时间",n.extend({},i.systemDate(),i.startTime),n.extend({},i.systemDate(),i.endTime)):i.setBtnStatus(!0))),s.range||(i.listYM=[[f.year,f.month+1]],i.list(s.type,0))),s.range&&!t)&&(a=i.getAsYM(f.year,f.month),i.calendar(n.extend({},f,{year:a[0],month:a[1]}))),s.range||i.limit(n(i.footer).find(o),null,0,["hours","minutes","seconds"]),s.range&&t&&!p&&i.stampRange(),i};t.prototype.list=function(t,i){var r=this,e=r.config,p=e.dateTime,v=r.lang(),nt=e.range&&"date"!==e.type&&"datetime"!==e.type,l=n.elem("ul",{"class":a+" "+{year:"laydate-year-list",month:"laydate-month-list",time:"laydate-time-list"}[t]}),tt=r.elemHeader[i],ut=n(tt[2]).find("span"),it=r.elemCont[i||0],ft=n(it).find("."+a)[0],b="cn"===e.lang,k=b?"年":"",s=r.listYM[i]||{},d=["hours","minutes","seconds"],c=["startTime","endTime"][i],h,g,rt;if((s[0]<1&&(s[0]=1),"year"===t)?(g=h=s[0]-7,g<1&&(g=h=1),n.each(new Array(15),function(){var f=n.elem("li",{"lay-ym":h}),t={year:h};h==s[0]&&n(f).addClass(u);f.innerHTML=h+k;l.appendChild(f);h<r.firstDate.year?(t.month=e.min.month,t.date=e.min.date):h>=r.firstDate.year&&(t.month=e.max.month,t.date=e.max.date);r.limit(n(f),t,i);h++}),n(ut[b?0:1]).attr("lay-ym",h-8+"-"+s[1]).html(g+k+" - "+(h-1+k))):"month"===t?(n.each(new Array(12),function(t){var f=n.elem("li",{"lay-ym":t}),o={year:s[0],month:t};t+1==s[1]&&n(f).addClass(u);f.innerHTML=v.month[t]+(b?"月":"");l.appendChild(f);s[0]<r.firstDate.year?o.date=e.min.date:s[0]>=r.firstDate.year&&(o.date=e.max.date);r.limit(n(f),o,i)}),n(ut[b?0:1]).attr("lay-ym",s[0]+"-"+s[1]).html(s[0]+k)):"time"===t&&(rt=function(){n(l).find("ol").each(function(t,u){n(u).find("li").each(function(u,f){r.limit(n(f),[{hours:u},{hours:r[c].hours,minutes:u},{hours:r[c].hours,minutes:r[c].minutes,seconds:u}][t],i,[["hours"],["hours","minutes"],["hours","minutes","seconds"]][t])})});e.range||r.limit(n(r.footer).find(o),r[c],0,["hours","minutes","seconds"])},e.range?r[c]||(r[c]={hours:0,minutes:0,seconds:0}):r[c]=p,n.each([24,60,60],function(t,i){var f=n.elem("li"),e=["<p>"+v.time[t]+"<\/p><ol>"];n.each(new Array(i),function(i){e.push("<li"+(r[c][d[t]]===i?' class="'+u+'"':"")+">"+n.digit(i,2)+"<\/li>")});f.innerHTML=e.join("")+"<\/ol>";l.appendChild(f)}),rt()),ft&&it.removeChild(ft),it.appendChild(l),"year"===t||"month"===t)n(r.elemMain[i]).addClass("laydate-ym-show"),n(l).find("li").on("click",function(){var h=0|n(this).attr("lay-ym"),c;n(this).hasClass(f)||(0===i?(p[t]=h,nt&&(r.startDate[t]=h),r.limit(n(r.footer).find(o),null,0)):nt?r.endDate[t]=h:(c="year"===t?r.getAsYM(h,s[1]-1,"sub"):r.getAsYM(s[0],h,"sub"),n.extend(p,{year:c[0],month:c[1]})),"year"===e.type||"month"===e.type?(n(l).find("."+u).removeClass(u),n(this).addClass(u),"month"===e.type&&"year"===t&&(r.listYM[i][0]=h,nt&&(r[["startDate","endDate"][i]].year=h),r.list("month",i))):(r.checkDate("limit").calendar(),r.closeList()),r.setBtnStatus(),e.range||r.done(null,"change"),n(r.footer).find(w).removeClass(f))});else{var et=n.elem("span",{"class":y}),ot=function(){n(l).find("ol").each(function(t){var i=this,u=n(i).find("li");i.scrollTop=30*(r[c][d[t]]-2);i.scrollTop<=0&&u.each(function(t){if(!n(this).hasClass(f))return i.scrollTop=30*(t-2),!0})})},st=n(tt[2]).find("."+y);ot();et.innerHTML=e.range?[v.startTime,v.endTime][i]:v.timeTips;n(r.elemMain[i]).addClass("laydate-time-show");st[0]&&st.remove();tt[2].appendChild(et);n(l).find("ol").each(function(t){var i=this;n(i).find("li").on("click",function(){var o=0|this.innerHTML;n(this).hasClass(f)||(e.range?r[c][d[t]]=o:p[d[t]]=o,n(i).find("."+u).removeClass(u),n(this).addClass(u),rt(),ot(),(r.endDate||"time"===e.type)&&r.done(null,"change"),r.setBtnStatus())})})}return r};t.prototype.listYM=[];t.prototype.closeList=function(){var t=this;t.config;n.each(t.elemCont,function(i){n(this).find("."+a).remove();n(t.elemMain[i]).removeClass("laydate-ym-show laydate-time-show")});n(t.elem).find("."+y).remove()};t.prototype.setBtnStatus=function(t,i,r){var e,u=this,h=u.config,c=n(u.footer).find(o),l=h.range&&"date"!==h.type&&"time"!==h.type;l&&(i=i||u.startDate,r=r||u.endDate,e=u.newDate(i).getTime()>u.newDate(r).getTime(),u.limit(null,i)||u.limit(null,r)?c.addClass(f):c[e?"addClass":"removeClass"](f),t&&e&&u.hint("string"==typeof t?s.replace(/日期/g,t):s))};t.prototype.parse=function(t,i){var u=this,e=u.config,f=i||(t?n.extend({},u.endDate,u.endTime):e.range?n.extend({},u.startDate,u.startTime):e.dateTime),r=u.format.concat();return n.each(r,function(t,i){/yyyy|y/.test(i)?r[t]=n.digit(f.year,i.length):/MM|M/.test(i)?r[t]=n.digit(f.month+1,i.length):/dd|d/.test(i)?r[t]=n.digit(f.date,i.length):/HH|H/.test(i)?r[t]=n.digit(f.hours,i.length):/mm|m/.test(i)?r[t]=n.digit(f.minutes,i.length):/ss|s/.test(i)&&(r[t]=n.digit(f.seconds,i.length))}),e.range&&!t?r.join("")+" "+e.range+" "+u.parse(1):r.join("")};t.prototype.newDate=function(n){return n=n||{},new Date(n.year||1,n.month||0,n.date||1,n.hours||0,n.minutes||0,n.seconds||0)};t.prototype.setValue=function(t){var i=this,r=i.config,u=i.bindElem||r.elem[0],f=i.isInput(u)?"val":"html";return"static"===r.position||n(u)[f](t||""),this};t.prototype.stampRange=function(){var i,r,t=this,e=t.config,c=n(t.elem).find("td");if(e.range&&!t.endDate&&n(t.footer).find(o).addClass(f),t.endDate)return i=t.newDate({year:t.startDate.year,month:t.startDate.month,date:t.startDate.date}).getTime(),r=t.newDate({year:t.endDate.year,month:t.endDate.month,date:t.endDate.date}).getTime(),i>r?t.hint(s):void n.each(c,function(f,e){var s=n(e).attr("lay-ymd").split("-"),o=t.newDate({year:s[0],month:s[1]-1,date:s[2]}).getTime();n(e).removeClass(h+" "+u);o!==i&&o!==r||n(e).addClass(n(e).hasClass(g)||n(e).hasClass(nt)?h:u);o>i&&o<r&&n(e).addClass(h)})};t.prototype.done=function(t,i){var r=this,u=r.config,f=n.extend({},r.startDate?n.extend(r.startDate,r.startTime):u.dateTime),e=n.extend({},n.extend(r.endDate,r.endTime));return n.each([f,e],function(t,i){"month"in i&&n.extend(i,{month:i.month+1})}),t=t||[r.parse(),f,e],"function"==typeof u[i||"done"]&&u[i||"done"].apply(u,t),r};t.prototype.choose=function(t){var i=this,e=i.config,l=e.dateTime,a=n(i.elem).find("td"),r=t.attr("lay-ymd").split("-"),s=function(t){new Date;t&&n.extend(l,r);e.range&&(i.startDate?n.extend(i.startDate,r):i.startDate=n.extend({},r,i.startTime),i.startYMD=r)},c;(r={year:0|r[0],month:(0|r[1])-1,date:0|r[2]},t.hasClass(f))||(e.range?((n.each(["startTime","endTime"],function(n,t){i[t]=i[t]||{hours:0,minutes:0,seconds:0}}),i.endState)?(s(),delete i.endState,delete i.endDate,i.startState=!0,a.removeClass(u+" "+h),t.addClass(u)):i.startState?((t.addClass(u),i.endDate?n.extend(i.endDate,r):i.endDate=n.extend({},r,i.endTime),i.newDate(r).getTime()<i.newDate(i.startYMD).getTime())&&(c=n.extend({},i.endDate,{hours:i.startDate.hours,minutes:i.startDate.minutes,seconds:i.startDate.seconds}),n.extend(i.endDate,i.startDate,{hours:i.endDate.hours,minutes:i.endDate.minutes,seconds:i.endDate.seconds}),i.startDate=c),e.showBottom||i.done(),i.stampRange(),i.endState=!0,i.done(null,"change")):(t.addClass(u),s(),i.startState=!0),n(i.footer).find(o)[i.endDate?"removeClass":"addClass"](f)):"static"===e.position?(s(!0),i.calendar().done().done(null,"change")):"date"===e.type?(s(!0),i.setValue(i.parse()).remove().done()):"datetime"===e.type&&(s(!0),i.calendar().done(null,"change")))};t.prototype.tool=function(t,i){var r=this,u=r.config,e=u.dateTime,o="static"===u.position,h={datetime:function(){n(t).hasClass(f)||(r.list("time",0),u.range&&r.list("time",1),n(t).attr("lay-type","date").html(r.lang().dateTips))},date:function(){r.closeList();n(t).attr("lay-type","datetime").html(r.lang().timeTips)},clear:function(){r.setValue("").remove();o&&(n.extend(e,r.firstDate),r.calendar());u.range&&(delete r.startState,delete r.endState,delete r.endDate,delete r.startTime,delete r.endTime);r.done(["",{},{}])},now:function(){var t=new Date;n.extend(e,r.systemDate(),{hours:t.getHours(),minutes:t.getMinutes(),seconds:t.getSeconds()});r.setValue(r.parse()).remove();o&&r.calendar();r.done()},confirm:function(){if(u.range){if(!r.endDate)return r.hint("请先选择日期范围");if(n(t).hasClass(f))return r.hint("time"===u.type?s.replace(/日期/g,"时间"):s)}else if(n(t).hasClass(f))return r.hint("不在有效日期或时间范围内");r.done();r.setValue(r.parse()).remove()}};h[i]&&h[i]()};t.prototype.change=function(t){var i=this,r=i.config,u=r.dateTime,h=r.range&&("year"===r.type||"month"===r.type),e=i.elemCont[t||0],f=i.listYM[t],s=function(s){var a=["startDate","endDate"][t],c=n(e).find(".laydate-year-list")[0],l=n(e).find(".laydate-month-list")[0];return c&&(f[0]=s?f[0]-15:f[0]+15,i.list("year",t)),l&&(s?f[0]--:f[0]++,i.list("month",t)),(c||l)&&(n.extend(u,{year:f[0]}),h&&(i[a].year=f[0]),r.range||i.done(null,"change"),i.setBtnStatus(),r.range||i.limit(n(i.footer).find(o),{year:f[0]})),c||l};return{prevYear:function(){s("sub")||(u.year--,i.checkDate("limit").calendar(),r.range||i.done(null,"change"))},prevMonth:function(){var t=i.getAsYM(u.year,u.month,"sub");n.extend(u,{year:t[0],month:t[1]});i.checkDate("limit").calendar();r.range||i.done(null,"change")},nextMonth:function(){var t=i.getAsYM(u.year,u.month);n.extend(u,{year:t[0],month:t[1]});i.checkDate("limit").calendar();r.range||i.done(null,"change")},nextYear:function(){s()||(u.year++,i.checkDate("limit").calendar(),r.range||i.done(null,"change"))}}};t.prototype.changeEvent=function(){var t=this;t.config;n(t.elem).on("click",function(t){n.stope(t)});n.each(t.elemHeader,function(i,r){n(r[0]).on("click",function(){t.change(i).prevYear()});n(r[1]).on("click",function(){t.change(i).prevMonth()});n(r[2]).find("span").on("click",function(){var u=n(this),r=u.attr("lay-ym"),e=u.attr("lay-type");r&&(r=r.split("-"),t.listYM[i]=[0|r[0],0|r[1]],t.list(e,i),n(t.footer).find(w).addClass(f))});n(r[3]).on("click",function(){t.change(i).nextMonth()});n(r[4]).on("click",function(){t.change(i).nextYear()})});n.each(t.table,function(i,r){var u=n(r).find("td");u.on("click",function(){t.choose(n(this))})});n(t.footer).find("span").on("click",function(){var i=n(this).attr("lay-type");t.tool(this,i)})};t.prototype.isInput=function(n){return/input|textarea/.test(n.tagName.toLocaleLowerCase())};t.prototype.events=function(){var i=this,r=i.config,u=function(n,t){n.on(r.trigger,function(){t&&(i.bindElem=this);i.render()})};r.elem[0]&&!r.elem[0].eventHandler&&(u(r.elem,"bind"),u(r.eventElem),n(document).on("click",function(t){t.target!==r.elem[0]&&t.target!==r.eventElem[0]&&t.target!==n(r.closeStop)[0]&&i.remove()}).on("keydown",function(r){13===r.keyCode&&n("#"+i.elemID)[0]&&i.elemID===t.thisElem&&(r.preventDefault(),n(i.footer).find(o)[0].click())}),n(window).on("resize",function(){return!(!i.elem||!n(d)[0])&&void i.position()}),r.elem[0].eventHandler=!0)};r.render=function(n){var i=new t(n);return b.call(i)};r.getEndDate=function(n,t){var i=new Date;return i.setFullYear(t||i.getFullYear(),n||i.getMonth()+1,1),new Date(i.getTime()-864e5).getDate()};window.lay=window.lay||n;c?(r.ready(),layui.define(function(n){r.path=layui.cache.dir;n(k,r)})):"function"==typeof define&&define.amd?define(function(){return r}):function(){r.ready();window.laydate=r}()}();