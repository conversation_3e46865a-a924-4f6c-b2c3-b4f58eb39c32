<div class="apiDetail">
<div>
	<h2><span>Boolean</span><span class="path">treeNode.</span>isParent</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.core</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Judge whether the node is the parent node.</p>
			<p class="highlight_red">1. When zTree initialize the node data, the node which has children is set to true, otherwise false.</p>
			<p class="highlight_red">2. When zTree initialize the node data, if set treeNode.isParent to true, the node will be set to be parent node.</p>
			<p class="highlight_red">3. In order to solve the problem of someone make json data, supporting "false", "true" format of the data string.</p>
		</div>
	</div>
	<h3>Boolean Format</h3>
	<div class="desc">
	<p> true means: the node is parent node.</p>
	<p> false means: the node is not parent node.</p>
	</div>
	<h3>Examples of treeNode</h3>
	<h4>1. Judge whether the first selected node is the parent node.</h4>
	<pre xmlns=""><code>var treeObj = $.fn.zTree.getZTreeObj("tree");
var sNodes = treeObj.getSelectedNodes();
if (sNodes.length > 0) {
	var isParent = sNodes[0].isParent;
}
</code></pre>
</div>
</div>