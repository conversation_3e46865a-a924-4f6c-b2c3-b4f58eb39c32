using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Quartz;
using Quartz.Impl.Triggers;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Service.SystemManage;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Business.AutoJob.Job;

namespace Dqy.Syjx.Business.AutoJob
{
    public class JobExecute : IJob
    {
        private AutoJobService autoJobService = new AutoJobService();
        private AutoJobLogService autoJobLogService = new AutoJobLogService();

        public Task Execute(IJobExecutionContext context)
        {
            return Task.Run(async () =>
            {
                TData obj = new TData();
                long jobId = 0;
                JobDataMap jobData = null;
                AutoJobEntity dbJobEntity = null;
                try
                {
                    jobData = context.JobDetail.JobDataMap;
                    jobId = jobData["Id"].ParseToLong();
                    // 获取数据库中的任务
                    dbJobEntity = await autoJobService.GetEntity(jobId);
                    if (dbJobEntity != null)
                    {
                        if (dbJobEntity.JobStatus == StatusEnum.Yes.ParseToInt())
                        {
                            CronTriggerImpl trigger = context.Trigger as CronTriggerImpl;
                            if (trigger != null)
                            {
                                if (trigger.CronExpressionString != dbJobEntity.CronExpression)
                                {
                                    // 更新任务周期
                                    trigger.CronExpressionString = dbJobEntity.CronExpression;
                                    await JobScheduler.GetScheduler().RescheduleJob(trigger.Key, trigger);
                                }

                                string taskname = context.JobDetail.Key.Name;

                                #region 执行任务
                                switch (context.JobDetail.Key.Name)
                                {
                                    case "数据库备份":
                                        obj = await new DatabasesBackupJob().Start();
                                        break;

                                    case "在线巡课":
                                        obj = await new PatrolClassJob().Start();
                                        break;
                                    case "装备统计备份":
                                        obj = await new EquipmentReportBackupJob().Start();
                                        break;
                                    case "实验教学版本库备份":
                                        obj = await new ExperimentTeachBackupJob().Start();
                                        break;
                                    case "统计本周未登记的实验情况":
                                        obj = await new ExperimentRecordInfoJob().Start();
                                        break;
                                }
                                #endregion
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    obj.Message = ex.GetOriginalException().Message;
                    LogHelper.Error(ex);
                }

                try
                {
                    if (dbJobEntity != null)
                    {
                        if (dbJobEntity.JobStatus == StatusEnum.Yes.ParseToInt())
                        {
                            #region 更新下次运行时间
                            await autoJobService.SaveForm(new AutoJobEntity
                            {
                                Id = dbJobEntity.Id,
                                NextStartTime = context.NextFireTimeUtc.HasValue ?
                                    TimeZoneInfo.ConvertTimeFromUtc(context.NextFireTimeUtc.Value.DateTime, TimeZoneInfo.Local) :
                                    null
                            });
                            #endregion

                            #region 记录执行状态
                            await autoJobLogService.SaveForm(new AutoJobLogEntity
                            {
                                JobGroupName = context.JobDetail.Key.Group,
                                JobName = context.JobDetail.Key.Name,
                                LogStatus = obj.Tag,
                                Remark = obj.Message
                            });
                            #endregion
                        }
                    }
                }
                catch (Exception ex)
                {
                    obj.Message = ex.GetOriginalException().Message;
                    LogHelper.Error(ex);
                }
            });
        }
    }
}
