{
  "Logging": {
    "LogLevel": {
      "Default": "Debug", // Trace,Debug,Information,Warning,Error,Critical
      "System": "Information",
      "Microsoft": "Information"
    }
  },
  "AllowedHosts": "*",
  "SystemConfig": {
    "LoginProvider": "WebApi", // 登录信息保存方式 Cookie Session WebApi
    "SnowFlakeWorkerId": 2, // SnowFlake 节点序号
    "AllowCorsSite": "http://localhost:5100", // 允许的其他站点访问Api

    "DBProvider": "SqlServer",
    "DBConnectionString": "Data Source=***********;Initial Catalog=syjx;Persist Security Info=True;User ID=****;Password=****",
    //"DBProvider": "Dm",
    //"DBConnectionString": "SERVER=***********;PORT=5236;USER=****;PASSWORD=****;DATABASE=****", //达梦

    "DBCommandTimeout": 180, // 数据库超时时间，单位秒
    "DBBackup": "", // 数据库备份路径

    "CacheProvider": "Memory", // 缓存使用方式 Memory Redis
    "RedisConnectionString": "127.0.0.1:6379",

    "FileSavePath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Web\\Dqy.Syjx.Web", //附件上传地址 这个路径应该是Web发布的地址，如Web发布：\wwwroot\syjx 这里就要配置这个路径 
    "LoginMultiplePhone": true, //是否允许一个账户在多台移动设备登录
    "IsOpenScanCodeLogin": 0, //是否启用扫码登录（0: 不启用；1: 开启）
    "MobileClientType": 0, //移动客户端类型 0: H5；1：微信小程序；2：钉钉小程序；3：企业微信
    "QyWeixinUrl": "http://oajjg.hzjyj.cn:23889/api/", //企业微信接口地址,
    "ValidateCodePeriod": "验证码：{0}。有效期{1}分钟。" //验证码提示语
  },
  //CO2NET 设置
  "SenparcSetting": {
    //以下为 CO2NET 的 SenparcSetting 全局配置，请勿修改 key，勿删除任何项

    "IsDebug": true,
    "DefaultCacheNamespace": "DefaultCache",

    //分布式缓存
    "Cache_Redis_Configuration": "#{Cache_Redis_Configuration}#", //Redis配置
    "Cache_Memcached_Configuration": "#{Cache_Memcached_Configuration}#", //Memcached配置
    "SenparcUnionAgentKey": "#{SenparcUnionAgentKey}#" //SenparcUnionAgentKey
  },
  //Senparc.Weixin SDK 设置
  "SenparcWeixinSetting": {
    //以下为 Senparc.Weixin 的 SenparcWeixinSetting 微信配置
    //注意：所有的字符串值都可能被用于字典索引，因此请勿留空字符串（但可以根据需要，删除对应的整条设置）！

    //微信全局
    "IsDebug": true,

    //以下不使用的参数可以删除，key 修改后将会失效

    //公众号
    "Token": "#{Token}#", //说明：字符串内两侧#和{}符号为 Azure DevOps 默认的占位符格式，如果您有明文信息，请删除同占位符，修改整体字符串，不保留#和{}，如：{"Token": "MyFullToken"}
    "EncodingAESKey": "#{EncodingAESKey}#",
    "WeixinAppId": "#{WeixinAppId}#",
    "WeixinAppSecret": "#{WeixinAppSecret}#",

    //小程序
    "WxOpenAppId": "wx8b5b7ff0c0cbe8bb",
    "WxOpenAppSecret": "cbcc08f10f5a658050bb054ca024bb01",
    "WxOpenToken": "#{WxOpenToken}#",
    "WxOpenEncodingAESKey": "#{WxOpenEncodingAESKey}#"
  },
  "sso": {
    "remark": "钉钉免登",
    "authHost": "https://api.dingtalk.com",
    "dataHost": "https://oapi.dingtalk.com",
    "dataHost2": "",
    "clientID": "ding0bv9qbncij6roir5",
    "clientSecret": "9TyoQ02lMJtc0M9br-lKBMmIO95i_ShTMMKv78PlYfrNpJCTMLrsNGZTB9IudKEd",
    "callBackUrl": "",
    "logout": "",
    "root": "",
    "key": "",
    "userScope": "userScope",
    "dataScope": ""
  },

  //"sso": {
  //  "remark": "吴中区",
  //  "authHost": "http://www.szwzedu.cn", //教育云地址
  //  "dataHost": "http://zzxx.szwzedu.cn:10080", //教育云接口
  //  "dataHost2": "",
  //  "clientID": "20232310081052577709125",
  //  "clientSecret": "c2VjcmV0MjAyMzEwMDgxMDUyNTc4Nzc4NTUz",
  //  "callBackUrl": "",
  //  "logout": "",
  //  "root": "443",
  //  "key": "",
  //  "userScope": "userScope",
  //  "dataScope": ""
  //},
  //"sso": {
  //  "remark": "常熟市",
  //  "authHost": "http://csjy.cssxsjy.cn/api",
  //  "dataHost": "https://csjy.cssxsjy.cn",
  //  "dataHost2": "",
  //  "clientID": "D3CF6EC459C848DDEC6C35DB00C3243A",
  //  "clientSecret": "e9495f6cbbb64a3c99892c4bd68b9234",
  //  "callBackUrl": "",
  //  "root": "",
  //  "key": "",
  //  "userScope": "userScope",
  //  "dataScope": ""
  //},
  //"sso": {
  //  "remark": "南通市通州区",
  //  "authHost": "https://yun.jstzjy.net",
  //  "dataHost": "http://open.yun.jstzjy.net",
  //  "dataHost2": "http://yun.jstzjy.net",
  //  "clientID": "093e6d86f4bb41c09c8b6109d703d42e",
  //  "clientSecret": "11a550f6df4232d18eb9a7d2993fd700",
  //  "callBackUrl": "https://sygl.jstzjy.net/h5/#/pages/login_third/tzqh5?ifuseriflyssost",
  //  "root": "443",
  //  "key": "305C300D06092A864886F70D0101010500034B003048024100A0877198F70BB1D8FA5E4FE8E479611C2F9D250A7B84B2CB4B42444A429D5C26142B022FF4985EBE12C89CABC1EB8A0724617F952C9BDF9729C81A67C557E0C10203010001",
  //  "userScope": "userScope",
  //  "dataScope": ""
  //},
  //"sso": {
  //  "remark": "海州区",
  //  "authHost": "http://unitplat.hzjyj.cn:8888/",
  //  "dataHost": "http://unitplat.hzjyj.cn:8888/api/webservice/webservice.php",
  //  "dataHost2": "http://unitplat.hzjyj.cn:8888/api/http/login.php",
  //  "clientID": "586A6560",
  //  "clientSecret": "",
  //  "callBackUrl": "",
  //  "logout": "http://oajjg.hzjyj.cn:23889/h5/",
  //  "root": "http://oajjg.hzjyj.cn:23889",
  //  "key": "",
  //  "userScope": "userScope",
  //  "dataScope": ""
  //},
  //"sso": {
  //  "remark": "成华区",
  //  "authHost": "https://api.cdchjyy.com",
  //  "dataHost": "https://cdchjyy.com",
  //  "dataHost2": "",
  //  "clientID": "********************************",
  //  "clientSecret": "f8a948eb6c7f47619441d88bde6c260c",
  //  "callBackUrl": "https://syjx.cdchjyy.com/accountthird/auth_chq",
  //  "root": "",
  //  "key": "",
  //  "userScope": "userScope",
  //  "dataScope": ""
  //},
  "Audience": {
    "Secret": "sdfsdfsrty45634kkhllghtdgdfss345t678fs",
    "Issuer": "Dqy.Syjx",
    "Audience": "Syjx"
  },
  "Startup": {
    "Domain": "http://localhost:5100",
    "Cors": {
      "PolicyName": "CorsIpAccess", //策略名称
      "EnableAllIPs": false, //当为true时，开放所有IP均可访问。
      // 支持多个域名端口，注意端口号后不要带/斜杆：比如localhost:8000/，是错的
      // 注意，http://127.0.0.1:1818 和 http://localhost:1818 是不一样的
      "IPs": "http://127.0.0.1:2364,http://localhost:2364,http://127.0.0.1:6688,http://localhost:6688"
    },
    "AppConfigAlert": {
      "Enabled": true
    },
    "ApiName": "Dqy.Syjx",
    "IdentityServer4": {
      "Enabled": false, // 这里默认是false，表示使用jwt，如果设置为true，则表示系统使用Ids4模式
      "AuthorizationUrl": "http://localhost:5100", // 认证中心域名
      "ApiName": "dqy.syjx.api" // 资源服务器
    },
    "Authing": {
      "Enabled": false,
      "Issuer": "Dqy.Syjx",
      "Audience": "63d51c4205c2849803be5178",
      "JwksUri": "https://uldr24esx31h-demo.authing.cn/oidc/.well-known/jwks.json"
    },
    "MiniProfiler": {
      "Enabled": false //性能分析开启
    },
    "Nacos": {
      "Enabled": false //Nacos注册中心
    }
  }
}
