/*上传图片插件的样式*/
.img-box {
}

    .img-box .up-p {
        margin-bottom: 20px;
        font-size: 16px;
        color: #555;
    }

.fl {
    float: left;
}

.clear {
    clear: both;
}

    .clear:after {
        content: '';
        display: block;
        clear: both;
    }


.z_photo {
    padding-left: 18px;
    border: 2px dashed #E7E6E6;
    /*padding: 18px;*/
}

    .z_photo .z_file {
        position: relative;
        margin-top: 10px;
        margin-bottom: 10px;
    }

.z_file .file-image {
    width: 100%;
    height: 100%;
    opacity: 0;
    position: absolute;
    top: 0px;
    left: 0px;
    z-index: 100;
}

.z_photo .up-section {
    position: relative;
    margin-right: 20px;
    margin-top: 10px;
    margin-bottom: 10px;
}

.up-section .close-upimg {
    position: absolute;
    top: 3px;
    right: 5px;
    width: 20px;
    display: none;
    z-index: 10;
}

.up-section .up-span {
    display: block;
    width: 100%;
    height: 100%;
    visibility: hidden;
    position: absolute;
    top: 0px;
    left: 0px;
    z-index: 9;
    background: rgba(0,0,0,.5);
}

.up-section:hover {
    border: 2px solid #f15134;
}

    .up-section:hover .close-upimg {
        display: block;
    }

    .up-section:hover .up-span {
        visibility: visible;
    }

.z_photo .up-img {
    display: block;
    width: 100%;
    height: 100%;
}

.loading {
    border: 1px solid #D1D1D1;
    background: url(../img/loading.gif) no-repeat center;
}

.up-opcity {
    opacity: 0;
}

.img-name-p {
    display: none;
}

.upimg-div .up-section {
    width: 70px;
    height: 70px;
}

.img-box .upimg-div .z_file {
    width: 70px;
    height: 70px;
}

.z_file .add-img {
    display: block;
    width: 70px;
    height: 70px;
}
/*遮罩层样式*/
.mask {
    z-index: 1000;
    display: none;
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,.4);
}

    .mask .mask-content {
        width: 500px;
        position: absolute;
        top: 50%;
        left: 50%;
        margin-left: -250px;
        margin-top: -80px;
        background: white;
        height: 160px;
        text-align: center;
    }

        .mask .mask-content .del-p {
            color: #555;
            height: 94px;
            line-height: 94px;
            font-size: 18px;
            border-bottom: 1px solid #D1D1D1;
        }

.mask-content .check-p {
    height: 66px;
    line-height: 66px;
    width: 100%;
}

    .mask-content .check-p span {
        width: 49%;
        display: inline-block;
        text-align: center;
        color: #d4361d;
        font-size: 18px;
    }

.check-p .del-com {
    border-right: 1px solid #D1D1D1;
}
