/**
 * @preserve
 * jquery.layout 1.4.4
 * $Date: 2014-11-29 08:00:00 (Sat, 29 November 2014) $
 * $Rev: 1.0404 $
 *
 * Copyright (c) 2014 <PERSON> (http://jquery-dev.com)
 * Based on work by <PERSON><PERSON><PERSON><PERSON> (http://www.fabrizioballiano.net)
 *
 * Dual licensed under the GPL (http://www.gnu.org/licenses/gpl.html)
 * and MIT (http://www.opensource.org/licenses/mit-license.php) licenses.
 *
 * SEE: http://layout.jquery-dev.com/LICENSE.txt
 *
 * Changelog: http://layout.jquery-dev.com/changelog.cfm
 *
 * Docs: http://layout.jquery-dev.com/documentation.html
 * Tips: http://layout.jquery-dev.com/tips.html
 * Help: http://groups.google.com/group/jquery-ui-layout
 */
(function(n){var f=Math.min,t=Math.max,u=Math.floor,i=function(t){return n.type(t)==="string"},r=function(t,r){function o(n){return n}var f,e,u;if(n.isArray(r))for(f=0,e=r.length;f<e;f++){u=r[f];try{i(u)&&(u=eval(u));n.isFunction(u)&&o(u)(t)}catch(s){}}};n.layout={version:"1.4.4",revision:1.0404,browser:{},effects:{slide:{all:{duration:"fast"},north:{direction:"up"},south:{direction:"down"},east:{direction:"right"},west:{direction:"left"}},drop:{all:{duration:"slow"},north:{direction:"up"},south:{direction:"down"},east:{direction:"right"},west:{direction:"left"}},scale:{all:{duration:"fast"}},blind:{},clip:{},explode:{},fade:{},fold:{},puff:{},size:{all:{easing:"swing"}}},config:{optionRootKeys:"effects,panes,north,south,west,east,center".split(","),allPanes:"north,south,west,east,center".split(","),borderPanes:"north,south,west,east".split(","),oppositeEdge:{north:"south",south:"north",east:"west",west:"east"},offscreenCSS:{left:"-99999px",right:"auto"},offscreenReset:"offscreenReset",hidden:{visibility:"hidden"},visible:{visibility:"visible"},resizers:{cssReq:{position:"absolute",padding:0,margin:0,fontSize:"1px",textAlign:"left",overflow:"hidden"},cssDemo:{background:"#DDD",border:"none"}},togglers:{cssReq:{position:"absolute",display:"block",padding:0,margin:0,overflow:"hidden",textAlign:"center",fontSize:"1px",cursor:"pointer",zIndex:1},cssDemo:{background:"#AAA"}},content:{cssReq:{position:"relative"},cssDemo:{overflow:"auto",padding:"10px"},cssDemoPane:{overflow:"hidden",padding:0}},panes:{cssReq:{position:"absolute",margin:0},cssDemo:{padding:"10px",background:"#FFF",border:"1px solid #BBB",overflow:"auto"}},north:{side:"top",sizeType:"Height",dir:"horz",cssReq:{top:0,bottom:"auto",left:0,right:0,width:"auto"}},south:{side:"bottom",sizeType:"Height",dir:"horz",cssReq:{top:"auto",bottom:0,left:0,right:0,width:"auto"}},east:{side:"right",sizeType:"Width",dir:"vert",cssReq:{left:"auto",right:0,top:"auto",bottom:"auto",height:"auto"}},west:{side:"left",sizeType:"Width",dir:"vert",cssReq:{left:0,right:"auto",top:"auto",bottom:"auto",height:"auto"}},center:{dir:"center",cssReq:{left:"auto",right:"auto",top:"auto",bottom:"auto",height:"auto",width:"auto"}}},callbacks:{},getParentPaneElem:function(t){var u=n(t),f=u.data("layout")||u.data("parentLayout"),i,r;if(f){if(i=f.container,i.data("layoutPane"))return i;if(r=i.closest("."+n.layout.defaults.panes.paneClass),r.data("layoutPane"))return r}return null},getParentPaneInstance:function(t){var i=n.layout.getParentPaneElem(t);return i?i.data("layoutPane"):null},getParentLayoutInstance:function(t){var i=n.layout.getParentPaneElem(t);return i?i.data("parentLayout"):null},getEventObject:function(n){return typeof n=="object"&&n.stopPropagation?n:null},parsePaneName:function(t){var r=n.layout.getEventObject(t),i=t;return r&&(r.stopPropagation(),i=n(this).data("layoutEdge")),i&&!/^(west|east|north|south|center)$/.test(i)&&(n.layout.msg('LAYOUT ERROR - Invalid pane-name: "'+i+'"'),i="error"),i},plugins:{draggable:!!n.fn.draggable,effects:{core:!!n.effects,slide:n.effects&&(n.effects.slide||n.effects.effect&&n.effects.effect.slide)}},onCreate:[],onLoad:[],onReady:[],onDestroy:[],onUnload:[],afterOpen:[],afterClose:[],scrollbarWidth:function(){return window.scrollbarWidth||n.layout.getScrollbarSize("width")},scrollbarHeight:function(){return window.scrollbarHeight||n.layout.getScrollbarSize("height")},getScrollbarSize:function(t){var i=n('<div style="position: absolute; top: -10000px; left: -10000px; width: 100px; height: 100px; border: 0; overflow: scroll;"><\/div>').appendTo("body"),r={width:i.outerWidth-i[0].clientWidth,height:100-i[0].clientHeight};return i.remove(),window.scrollbarWidth=r.width,window.scrollbarHeight=r.height,t.match(/^(width|height)$/)?r[t]:r},disableTextSelection:function(){var t=n(document),i="textSelectionDisabled",r="textSelectionInitialized";n.fn.disableSelection&&(t.data(r)||t.on("mouseup",n.layout.enableTextSelection).data(r,!0),t.data(i)||t.disableSelection().data(i,!0))},enableTextSelection:function(){var t=n(document),i="textSelectionDisabled";n.fn.enableSelection&&t.data(i)&&t.enableSelection().data(i,!1)},showInvisibly:function(n,t){if(n&&n.length&&(t||n.css("display")==="none")){var i=n[0].style,r={display:i.display||"",visibility:i.visibility||""};return n.css({display:"block",visibility:"hidden"}),r}return{}},getElementDimensions:function(i,r){var u={css:{},inset:{}},f=u.css,s={bottom:0},h=n.layout.cssNum,e=Math.round,c=i.offset(),l,a,o;return u.offsetLeft=c.left,u.offsetTop=c.top,r||(r={}),n.each("Left,Right,Top,Bottom".split(","),function(t,e){l=f["border"+e]=n.layout.borderWidth(i,e);a=f["padding"+e]=n.layout.cssNum(i,"padding"+e);o=e.toLowerCase();u.inset[o]=r[o]>=0?r[o]:a;s[o]=u.inset[o]+l}),f.width=e(i.width()),f.height=e(i.height()),f.top=h(i,"top",!0),f.bottom=h(i,"bottom",!0),f.left=h(i,"left",!0),f.right=h(i,"right",!0),u.outerWidth=e(i.outerWidth()),u.outerHeight=e(i.outerHeight()),u.innerWidth=t(0,u.outerWidth-s.left-s.right),u.innerHeight=t(0,u.outerHeight-s.top-s.bottom),u.layoutWidth=e(i.innerWidth()),u.layoutHeight=e(i.innerHeight()),u},getElementStyles:function(n,t){for(var u={},s=n[0].style,c=t.split(","),l="Top,Bottom,Left,Right".split(","),a="Color,Style,Width".split(","),i,r,h,e,o,f=0;f<c.length;f++)if(i=c[f],i.match(/(border|padding|margin)$/))for(e=0;e<4;e++)if(r=l[e],i==="border")for(o=0;o<3;o++)h=a[o],u[i+r+h]=s[i+r+h];else u[i+r]=s[i+r];else u[i]=s[i];return u},cssWidth:function(i,r){if(r<=0)return 0;var f=n.layout.browser,e=f.boxModel?f.boxSizing?i.css("boxSizing"):"content-box":"border-box",o=n.layout.borderWidth,s=n.layout.cssNum,u=r;return e!=="border-box"&&(u-=o(i,"Left")+o(i,"Right")),e==="content-box"&&(u-=s(i,"paddingLeft")+s(i,"paddingRight")),t(0,u)},cssHeight:function(i,r){if(r<=0)return 0;var f=n.layout.browser,e=f.boxModel?f.boxSizing?i.css("boxSizing"):"content-box":"border-box",o=n.layout.borderWidth,s=n.layout.cssNum,u=r;return e!=="border-box"&&(u-=o(i,"Top")+o(i,"Bottom")),e==="content-box"&&(u-=s(i,"paddingTop")+s(i,"paddingBottom")),t(0,u)},cssNum:function(t,i,r){t.jquery||(t=n(t));var f=n.layout.showInvisibly(t),u=n.css(t[0],i,!0),e=r&&u=="auto"?u:Math.round(parseFloat(u)||0);return t.css(f),e},borderWidth:function(t,i){t.jquery&&(t=t[0]);var r="border"+i.substr(0,1).toUpperCase()+i.substr(1);return n.css(t,r+"Style",!0)==="none"?0:Math.round(parseFloat(n.css(t,r+"Width",!0))||0)},isMouseOverElem:function(t,i){var r=n(i||this),e=r.offset(),o=e.top,s=e.left,h=s+r.outerWidth(),c=o+r.outerHeight(),u=t.pageX,f=t.pageY;return n.layout.browser.msie&&u<0&&f<0||u>=s&&u<=h&&f>=o&&f<=c},msg:function(t,i,r,u){function h(){var i=n.support.fixedPosition?"fixed":"absolute",t=n('<div id="layoutLogger" style="position: '+i+'; top: 5px; z-index: 999999; max-width: 25%; overflow: hidden; border: 1px solid #000; border-radius: 5px; background: #FBFBFB; box-shadow: 0 2px 10px rgba(0,0,0,0.3);"><div style="font-size: 13px; font-weight: bold; padding: 5px 10px; background: #F6F6F6; border-radius: 5px 5px 0 0; cursor: move;"><span style="float: right; padding-left: 7px; cursor: pointer;" title="Remove Console" onclick="$(this).closest(\'#layoutLogger\').remove()">X<\/span>Layout console.log<\/div><ul style="font-size: 13px; font-weight: none; list-style: none; margin: 0; padding: 0 0 2px;"><\/ul><\/div>').appendTo("body");return t.css("left",n(window).width()-t.outerWidth()-5),n.ui.draggable&&t.draggable({handle:":first-child"}),t}var o,f,s,e;n.isPlainObject(t)&&window.debugData?(typeof i=="string"?(u=r,r=i):typeof r=="object"&&(u=r,r=null),o=r||"log( <object> )",f=n.extend({sort:!1,returnHTML:!1,display:!1},u),i===!0||f.display?debugData(t,o,f):window.console&&console.log(debugData(t,o,f))):i?alert(t):window.console?console.log(t):(s="#layoutLogger",e=n(s),e.length||(e=h()),e.children("ul").append('<li style="padding: 4px 10px; margin: 0; border-top: 1px solid #CCC;">'+t.replace(/\</g,"&lt;").replace(/\>/g,"&gt;")+"<\/li>"))}},function(){var t=navigator.userAgent.toLowerCase(),o=/(chrome)[ \/]([\w.]+)/.exec(t)||/(webkit)[ \/]([\w.]+)/.exec(t)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(t)||/(msie) ([\w.]+)/.exec(t)||t.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(t)||[],i=o[1]||"",s=o[2]||0,u=i==="msie",f=document.compatMode,r=n.support,e=r.boxSizing!==undefined?r.boxSizing:r.boxSizingReliable,h=!u||!f||f==="CSS1Compat"||r.boxModel||!1,c=n.layout.browser={version:s,safari:i==="webkit",webkit:i==="chrome",msie:u,isIE6:u&&s==6,boxModel:h,boxSizing:!!(typeof e=="function"?e():e)};i&&(c[i]=!0);h||f||n(function(){c.boxModel=r.boxModel})}();n.layout.defaults={name:"",containerClass:"ui-layout-container",inset:null,scrollToBookmarkOnLoad:!0,resizeWithWindow:!0,resizeWithWindowDelay:200,resizeWithWindowMaxDelay:0,maskPanesEarly:!1,onresizeall_start:null,onresizeall_end:null,onload_start:null,onload_end:null,onunload_start:null,onunload_end:null,initPanes:!0,showErrorMessages:!0,showDebugMessages:!1,zIndex:null,zIndexes:{pane_normal:0,content_mask:1,resizer_normal:2,pane_sliding:100,pane_animate:1e3,resizer_drag:1e4},errors:{pane:"pane",selector:"selector",addButtonError:"Error Adding Button\nInvalid ",containerMissing:"UI Layout Initialization Error\nThe specified layout-container does not exist.",centerPaneMissing:"UI Layout Initialization Error\nThe center-pane element does not exist.\nThe center-pane is a required element.",noContainerHeight:"UI Layout Initialization Warning\nThe layout-container \"CONTAINER\" has no height.\nTherefore the layout is 0-height and hence 'invisible'!",callbackError:"UI Layout Callback Error\nThe EVENT callback is not a valid function."},panes:{applyDemoStyles:!1,closable:!0,resizable:!0,slidable:!0,initClosed:!1,initHidden:!1,contentSelector:".ui-layout-content",contentIgnoreSelector:".ui-layout-ignore",findNestedContent:!1,paneClass:"ui-layout-pane",resizerClass:"ui-layout-resizer",togglerClass:"ui-layout-toggler",buttonClass:"ui-layout-button",minSize:0,maxSize:0,spacing_open:6,spacing_closed:6,togglerLength_open:50,togglerLength_closed:50,togglerAlign_open:"center",togglerAlign_closed:"center",togglerContent_open:"<i class='fa'><\/i>",togglerContent_closed:"<i class='fa'><\/i>",resizerDblClickToggle:!0,autoResize:!0,autoReopen:!0,resizerDragOpacity:1,maskContents:!1,maskObjects:!1,maskZindex:null,resizingGrid:!1,livePaneResizing:!1,liveContentResizing:!1,liveResizingTolerance:1,sliderCursor:"pointer",slideTrigger_open:"click",slideTrigger_close:"mouseleave",slideDelay_open:300,slideDelay_close:300,hideTogglerOnSlide:!1,preventQuickSlideClose:n.layout.browser.webkit,preventPrematureSlideClose:!1,tips:{Open:"չ��",Close:"�۵�",Resize:"������С/˫���۵�",Slide:"���ͣ���Զ�չ��",Pin:"Pin",Unpin:"Un-Pin",noRoomToOpen:"Not enough room to show this panel.",minSizeWarning:"Panel has reached its minimum size",maxSizeWarning:"Panel has reached its maximum size"},showOverflowOnHover:!1,enableCursorHotkey:!0,customHotkeyModifier:"SHIFT",fxName:"slide",fxSpeed:null,fxSettings:{},fxOpacityFix:!0,animatePaneSizing:!1,children:null,containerSelector:"",initChildren:!0,destroyChildren:!0,resizeChildren:!0,triggerEventsOnLoad:!1,triggerEventsDuringLiveResize:!0,onshow_start:null,onshow_end:null,onhide_start:null,onhide_end:null,onopen_start:null,onopen_end:null,onclose_start:null,onclose_end:null,onresize_start:null,onresize_end:null,onsizecontent_start:null,onsizecontent_end:null,onswap_start:null,onswap_end:null,ondrag_start:null,ondrag_end:null},north:{paneSelector:".ui-layout-north",size:"auto",resizerCursor:"n-resize",customHotkey:""},south:{paneSelector:".ui-layout-south",size:"auto",resizerCursor:"s-resize",customHotkey:""},east:{paneSelector:".ui-layout-east",size:200,resizerCursor:"e-resize",customHotkey:""},west:{paneSelector:".ui-layout-west",size:200,resizerCursor:"w-resize",customHotkey:""},center:{paneSelector:".ui-layout-center",minWidth:0,minHeight:0}};n.layout.optionsMap={layout:"name,instanceKey,stateManagement,effects,inset,zIndexes,errors,zIndex,scrollToBookmarkOnLoad,showErrorMessages,maskPanesEarly,outset,resizeWithWindow,resizeWithWindowDelay,resizeWithWindowMaxDelay,onresizeall,onresizeall_start,onresizeall_end,onload,onload_start,onload_end,onunload,onunload_start,onunload_end".split(","),center:"paneClass,contentSelector,contentIgnoreSelector,findNestedContent,applyDemoStyles,triggerEventsOnLoad,showOverflowOnHover,maskContents,maskObjects,liveContentResizing,containerSelector,children,initChildren,resizeChildren,destroyChildren,onresize,onresize_start,onresize_end,onsizecontent,onsizecontent_start,onsizecontent_end".split(","),noDefault:"paneSelector,resizerCursor,customHotkey".split(",")};n.layout.transformData=function(t,i){var o=i?{panes:{},center:{}}:{},r,s,h,u,e,f,c;if(typeof t!="object")return o;for(s in t)for(r=o,e=t[s],h=s.split("__"),c=h.length-1,f=0;f<=c;f++)u=h[f],f===c?r[u]=n.isPlainObject(e)?n.layout.transformData(e):e:(r[u]||(r[u]={}),r=r[u]);return o};n.layout.backwardCompatibility={map:{applyDefaultStyles:"applyDemoStyles",childOptions:"children",initChildLayout:"initChildren",destroyChildLayout:"destroyChildren",resizeChildLayout:"resizeChildren",resizeNestedLayout:"resizeChildren",resizeWhileDragging:"livePaneResizing",resizeContentWhileDragging:"liveContentResizing",triggerEventsWhileDragging:"triggerEventsDuringLiveResize",maskIframesOnResize:"maskContents",useStateCookie:"stateManagement.enabled","cookie.autoLoad":"stateManagement.autoLoad","cookie.autoSave":"stateManagement.autoSave","cookie.keys":"stateManagement.stateKeys","cookie.name":"stateManagement.cookie.name","cookie.domain":"stateManagement.cookie.domain","cookie.path":"stateManagement.cookie.path","cookie.expires":"stateManagement.cookie.expires","cookie.secure":"stateManagement.cookie.secure",noRoomToOpenTip:"tips.noRoomToOpen",togglerTip_open:"tips.Close",togglerTip_closed:"tips.Open",resizerTip:"tips.Resize",sliderTip:"tips.Slide"},renameOptions:function(t){function o(n,i){for(var f=n.split("."),o=f.length-1,r={branch:t,key:f[o]},e=0,u;e<o;e++)u=f[e],r.branch=r.branch[u]==undefined?i?r.branch[u]={}:{}:r.branch[u];return r}var f=n.layout.backwardCompatibility.map,i,r,u;for(var e in f)i=o(e),u=i.branch[i.key],u!==undefined&&(r=o(f[e],!0),r.branch[r.key]=u,delete i.branch[i.key])},renameAllOptions:function(t){var i=n.layout.backwardCompatibility.renameOptions;return i(t),t.defaults&&(typeof t.panes!="object"&&(t.panes={}),n.extend(!0,t.panes,t.defaults),delete t.defaults),t.panes&&i(t.panes),n.each(n.layout.config.allPanes,function(n,r){t[r]&&i(t[r])}),t}};n.fn.layout=function(e){function nf(t){var i;if(!t||(i=t.keyCode,i<33))return!0;var v={38:"north",40:"south",37:"west",39:"east"},p=t.altKey,e=t.shiftKey,f=t.ctrlKey,y=f&&i>=37&&i<=40,l,u,a,r;return(y&&s[v[i]].enableCursorHotkey?r=v[i]:(f||e)&&n.each(h.borderPanes,function(n,t){return l=s[t],u=l.customHotkey,a=l.customHotkeyModifier,(e&&a=="SHIFT"||f&&a=="CTRL"||f&&e)&&u&&i===(isNaN(u)||u<=9?u.toUpperCase().charCodeAt(0):u)?(r=t,!1):void 0}),!r||!c[r]||!s[r].closable||o[r].isHidden)?!0:(bt(r),t.stopPropagation(),t.returnValue=!1,!1)}function nu(t){var r,f,u;if(b()&&(this&&this.tagName&&(t=this),i(t)?r=c[t]:n(t).data("layoutRole")?r=n(t):n(t).parents().each(function(){if(n(this).data("layoutRole"))return r=n(this),!1}),r&&r.length)){if(f=r.data("layoutEdge"),u=o[f],u.cssSaved&&vi(f),u.isSliding||u.isResizing||u.isClosed){u.cssSaved=!1;return}var e={zIndex:s.zIndexes.resizer_normal+1},l={},v=r.css("overflow"),a=r.css("overflowX"),y=r.css("overflowY");v!="visible"&&(l.overflow=v,e.overflow="visible");a&&!a.match(/(visible|auto)/)&&(l.overflowX=a,e.overflowX="visible");y&&!y.match(/(visible|auto)/)&&(l.overflowY=a,e.overflowY="visible");u.cssSaved=l;r.css(e);n.each(h.allPanes,function(n,t){t!=f&&vi(t)})}}function vi(t){var r;if(b()&&(this&&this.tagName&&(t=this),i(t)?r=c[t]:n(t).data("layoutRole")?r=n(t):n(t).parents().each(function(){if(n(this).data("layoutRole"))return r=n(this),!1}),r&&r.length)){var f=r.data("layoutEdge"),u=o[f],e=u.cssSaved||{};u.isSliding||u.isResizing||r.css("zIndex",s.zIndexes.pane_normal);r.css(e);u.cssSaved=!1}}var ht=n.layout.browser,h=n.layout.config,tt=n.layout.cssWidth,rt=n.layout.cssHeight,it=n.layout.getElementDimensions,ti=n.layout.getElementStyles,yi=n.layout.getEventObject,w=n.layout.parsePaneName,s=n.extend(!0,{},n.layout.defaults),tf=s.effects=n.extend(!0,{},n.layout.effects),o={id:"layout"+n.now(),initialized:!1,paneResizing:!1,panesSliding:{},container:{innerWidth:0,innerHeight:0,outerWidth:0,outerHeight:0,layoutWidth:0,layoutHeight:0},north:{childIdx:0},south:{childIdx:0},east:{childIdx:0},west:{childIdx:0},center:{childIdx:0}},et={north:null,south:null,east:null,west:null,center:null},d={data:{},set:function(n,t,i){d.clear(n);d.data[n]=setTimeout(t,i)},clear:function(n){var t=d.data;t[n]&&(clearTimeout(t[n]),delete t[n])}},ot=function(t,i,r){var u=s;return(u.showErrorMessages&&!r||r&&u.showDebugMessages)&&n.layout.msg(u.name+" / "+t,i!==!1),!1},v=function(t,r,u){function g(n){return n}var h=r&&i(r),l=h?o[r]:o,e=h?s[r]:s,p=s.name,v=t+(t.match(/_/)?"":"_end"),w=v.match(/_end$/)?v.substr(0,v.length-4):"",f=e[v]||e[w],k="NC",d=[],b=h?c[r]:0;if(h&&!b)return k;if(h||n.type(r)!=="boolean"||(u=r,r=""),f)try{i(f)&&(f.match(/,/)?(d=f.split(","),f=eval(d[0])):f=eval(f));n.isFunction(f)&&(k=d.length?g(f)(d[1]):h?g(f)(r,c[r],l,e,p):g(f)(y,l,e,p))}catch(nt){ot(s.errors.callbackError.replace(/EVENT/,n.trim((r||"")+" "+v)),!1);n.type(nt)==="string"&&string.length&&ot("Exception:  "+nt,!1)}return u||k===!1||(h?(e=s[r],l=o[r],b.triggerHandler("layoutpane"+v,[r,b,l,e,p]),w&&b.triggerHandler("layoutpane"+w,[r,b,l,e,p])):(a.triggerHandler("layout"+v,[y,l,e,p]),w&&a.triggerHandler("layout"+w,[y,l,e,p]))),h&&t==="onresize_end"&&dr(r+"",!0),k},ur=function(n){if(!ht.mozilla){var t=c[n];o[n].tagName==="IFRAME"?t.css(h.hidden).css(h.visible):t.find("IFRAME").css(h.hidden).css(h.visible)}},fr=function(n,t){var i=h[n].dir=="horz"?rt:tt;return i(c[n],t)},ui=function(n){var i=c[n],r=h[n].dir,t={minWidth:1001-tt(i,1e3),minHeight:1001-rt(i,1e3)};return r==="horz"&&(t.minSize=t.minHeight),r==="vert"&&(t.minSize=t.minWidth),t},rf=function(t,r,u){var f=t,e;i(t)?f=c[t]:t.jquery||(f=n(t));e=tt(f,r);f.css({width:e});e>0?u&&f.data("autoHidden")&&f.innerHeight()>0&&(f.show().data("autoHidden",!1),ht.mozilla||f.css(h.hidden).css(h.visible)):u&&!f.data("autoHidden")&&f.hide().data("autoHidden",!0)},tu=function(t,r,u){var f=t,e;i(t)?f=c[t]:t.jquery||(f=n(t));e=rt(f,r);f.css({height:e,visibility:"visible"});e>0&&f.innerWidth()>0?u&&f.data("autoHidden")&&(f.show().data("autoHidden",!1),ht.mozilla||f.css(h.hidden).css(h.visible)):u&&!f.data("autoHidden")&&f.hide().data("autoHidden",!0)},ct=function(t,r,f){var v,y;if(f||(f=h[t].dir),i(r)&&r.match(/%/)&&(r=r==="100%"?-1:parseInt(r,10)/100),r===0)return 0;if(r>=1)return parseInt(r,10);if(v=s,y=0,f=="horz"?y=l.innerHeight-(c.north?v.north.spacing_open:0)-(c.south?v.south.spacing_open:0):f=="vert"&&(y=l.innerWidth-(c.west?v.west.spacing_open:0)-(c.east?v.east.spacing_open:0)),r===-1)return y;if(r>0)return u(y*r);if(t=="center")return 0;var e=f==="horz"?"height":"width",o=c[t],a=e==="height"?nt[t]:!1,p=n.layout.showInvisibly(o),w=o.css(e),b=a?a.css(e):0;return o.css(e,"auto"),a&&a.css(e,"auto"),r=e==="height"?o.outerHeight():o.outerWidth(),o.css(e,w).css(p),a&&a.css(e,b),r},lt=function(n,t){var i=c[n],u=s[n],r=o[n],f=t?u.spacing_open:0,e=t?u.spacing_closed:0;return!i||r.isHidden?0:r.isClosed||r.isSliding&&t?e:h[n].dir==="horz"?i.outerHeight()+f:i.outerWidth()+f},ut=function(n,i){if(b()){var u=s[n],e=o[n],tt=h[n],a=tt.dir,vt=tt.sizeType.toLowerCase(),ft=i!=undefined?i:e.isSliding,yt=c[n],et=u.spacing_open,nt=h.oppositeEdge[n],v=o[nt],y=c[nt],ot=!y||v.isVisible===!1||v.isSliding?0:a=="horz"?y.outerHeight():y.outerWidth(),st=(!y||v.isHidden?0:s[nt][v.isClosed!==!1?"spacing_closed":"spacing_open"])||0,ht=a=="horz"?l.innerHeight:l.innerWidth,it=ui("center"),lt=a=="horz"?t(s.center.minHeight,it.minHeight):t(s.center.minWidth,it.minWidth),at=ht-et-(ft?0:ct("center",lt,a)+ot+st),p=e.minSize=t(ct(n,u.minSize),ui(n).minSize),w=e.maxSize=f(u.maxSize?ct(n,u.maxSize):1e5,at),r=e.resizerPosition={},k=l.inset.top,d=l.inset.left,rt=l.innerWidth,ut=l.innerHeight,g=u.spacing_open;switch(n){case"north":r.min=k+p;r.max=k+w;break;case"west":r.min=d+p;r.max=d+w;break;case"south":r.min=k+ut-w-g;r.max=k+ut-p-g;break;case"east":r.min=d+rt-w-g;r.max=d+rt-p-g}}},iu=function(){var n={top:lt("north",!0),bottom:lt("south",!0),left:lt("west",!0),right:lt("east",!0),width:0,height:0};return n.width=l.innerWidth-n.left-n.right,n.height=l.innerHeight-n.bottom-n.top,n.top+=l.inset.top,n.bottom+=l.inset.bottom,n.left+=l.inset.left,n.right+=l.inset.right,n},pi=function(t,i){var f=n(t),a=f.data("layoutRole"),v=f.data("layoutEdge"),w=s[v],r=w[a+"Class"],e="-"+v,y="-open",o="-closed",h="-sliding",u="-hover ",c=f.hasClass(r+o)?o:y,p=c===o?y:o,l=r+u+(r+e+u)+(r+c+u)+(r+e+c+u);return i&&(l+=r+p+u+(r+e+p+u)),a=="resizer"&&f.hasClass(r+h)&&(l+=r+h+u+(r+e+h+u)),n.trim(l)},wi=function(t,i){var r=n(i||this);t&&r.data("layoutRole")==="toggler"&&t.stopPropagation();r.addClass(pi(r))},st=function(t,i){var r=n(i||this);r.removeClass(pi(r,!0))},er=function(){var t=n(this).data("layoutEdge"),i=o[t],r=n(document);i.isResizing||o.paneResizing||s.maskPanesEarly&&ii(t,{resizing:!0})},or=function(t,i){var r=i||this,u=n(r).data("layoutEdge"),f=u+"ResizerLeave",e=n(document);d.clear(u+"_openSlider");d.clear(f);i?s.maskPanesEarly&&!o.paneResizing&&oi():d.set(f,function(){or(t,r)},200)},ru=function(){eu();var i=s,t=o;return(t.creatingLayout=!0,r(y,n.layout.onCreate),!1===v("onload_start"))?"cancel":(fu(),lr(),n(window).bind("unload."+k,cr),r(y,n.layout.onLoad),i.initPanes&&fi(),delete t.creatingLayout,o.initialized)},b=function(){return o.initialized||o.creatingLayout?!0:fi()},fi=function(t){var i=s,u;return a.is(":visible")?ar("center").length?(o.creatingLayout=!0,n.extend(l,it(a,i.inset)),ou(),i.scrollToBookmarkOnLoad&&(u=self.location,u.hash&&u.replace(u.hash)),y.hasParentLayout?i.resizeWithWindow=!1:i.resizeWithWindow&&n(window).bind("resize."+k,uu),delete o.creatingLayout,o.initialized=!0,r(y,n.layout.onReady),v("onload_end"),!0):ot(i.errors.centerPaneMissing):(!t&&ht.webkit&&a[0].tagName==="BODY"&&setTimeout(function(){fi(!0)},50),!1)},bi=function(t,i){var r=w.call(this,t),f=c[r];if(f){var h=nt[r],l=o[r],e=s[r],a=s.stateManagement||{},u=i?e.children=i:e.children;if(n.isPlainObject(u))u=[u];else if(!u||!n.isArray(u))return;n.each(u,function(t,i){if(n.isPlainObject(i)){var u=i.containerSelector?f.find(i.containerSelector):h||f;u.each(function(){var f=n(this),t=f.data("layout");if(!t){if(sr({container:f,options:i},l),a.includeChildren&&o.stateData[r]){var s=o.stateData[r].children||{},e=s[i.instanceKey],u=i.stateManagement||(i.stateManagement={autoLoad:!0});u.autoLoad===!0&&e&&(u.autoSave=!1,u.includeChildren=!0,u.autoLoad=n.extend(!0,{},e))}t=f.layout(i);t&&ei(r,t)}})}})}},sr=function(n,t){var f=n.container,r=n.options,u=r.stateManagement,i=r.instanceKey||f.data("layoutInstanceKey");return i||(i=(u&&u.cookie?u.cookie.name:"")||r.name),i=i?i.replace(/[^\w-]/gi,"_").replace(/_{2,}/g,"_"):"layout"+ ++t.childIdx,r.instanceKey=i,f.data("layoutInstanceKey",i),i},ei=function(t,i){var f=c[t],r=et[t],e=o[t],u;n.isPlainObject(r)&&(n.each(r,function(n,t){t.destroyed&&delete r[n]}),n.isEmptyObject(r)&&(r=et[t]=null));i||r||(i=f.data("layout"));i&&(i.hasParentLayout=!0,u=i.options,sr(i,e),r||(r=et[t]={}),r[u.instanceKey]=i.container.data("layout"));y[t].children=et[t];i||bi(t)},uu=function(){var t=s,n=Number(t.resizeWithWindowDelay);n<10&&(n=100);d.clear("winResize");d.set("winResize",function(){d.clear("winResize");d.clear("winResizeRepeater");var n=it(a,t.inset);(n.innerWidth!==l.innerWidth||n.innerHeight!==l.innerHeight)&&dt()},n);d.data.winResizeRepeater||hr()},hr=function(){var n=Number(s.resizeWithWindowMaxDelay);n>0&&d.set("winResizeRepeater",function(){hr();dt()},n)},cr=function(){var t=s;v("onunload_start");r(y,n.layout.onUnload);v("onunload_end")},fu=function(){var c=a[0],i=n("html"),nt=l.tagName=c.tagName,tt=l.id=c.id,rt=l.className=c.className,t=s,f=t.name,ut="position,margin,padding,border",e="layoutCSS",v={},p="hidden",o=a.data("parentLayout"),h=a.data("layoutEdge"),w=o&&h,u=n.layout.cssNum,b,r,d,g;l.ref=(t.name?t.name+" layout / ":"")+nt+(tt?"#"+tt:rt?".["+rt+"]":"");l.isBody=nt==="BODY";w||l.isBody||(b=a.closest("."+n.layout.defaults.panes.paneClass),o=b.data("parentLayout"),h=b.data("layoutEdge"),w=o&&h);a.data({layout:y,layoutContainer:k}).addClass(t.containerClass);d={destroy:"",initPanes:"",resizeAll:"resizeAll",resize:"resizeAll"};for(f in d)a.bind("layout"+f.toLowerCase()+"."+k,y[d[f]||f]);w&&(y.hasParentLayout=!0,o.refreshChildren(h,y));a.data(e)||(l.isBody?(a.data(e,n.extend(ti(a,ut),{height:a.css("height"),overflow:a.css("overflow"),overflowX:a.css("overflowX"),overflowY:a.css("overflowY")})),i.data(e,n.extend(ti(i,"padding"),{height:"auto",overflow:i.css("overflow"),overflowX:i.css("overflowX"),overflowY:i.css("overflowY")}))):a.data(e,ti(a,ut+",top,bottom,left,right,width,height,overflow,overflowX,overflowY")));try{v={overflow:p,overflowX:p,overflowY:p};a.css(v);t.inset&&!n.isPlainObject(t.inset)&&(r=parseInt(t.inset,10)||0,t.inset={top:r,bottom:r,left:r,right:r});l.isBody?(t.outset?n.isPlainObject(t.outset)||(r=parseInt(t.outset,10)||0,t.outset={top:r,bottom:r,left:r,right:r}):t.outset={top:u(i,"paddingTop"),bottom:u(i,"paddingBottom"),left:u(i,"paddingLeft"),right:u(i,"paddingRight")},i.css(v).css({height:"100%",border:"none",padding:0,margin:0}),ht.isIE6?(a.css({width:"100%",height:"100%",border:"none",padding:0,margin:0,position:"relative"}),t.inset||(t.inset=it(a).inset)):(a.css({width:"auto",height:"auto",margin:0,position:"absolute"}),a.css(t.outset)),n.extend(l,it(a,t.inset))):(g=a.css("position"),g&&g.match(/(fixed|absolute|relative)/)||a.css("position","relative"),a.is(":visible")&&(n.extend(l,it(a,t.inset)),l.innerHeight<1&&ot(t.errors.noContainerHeight.replace(/CONTAINER/,l.ref))));u(a,"minWidth")&&a.parent().css("overflowX","auto");u(a,"minHeight")&&a.parent().css("overflowY","auto")}catch(ft){}},lr=function(t){t=t?t.split(","):h.borderPanes;n.each(t,function(t,i){var r=s[i];if(r.enableCursorHotkey||r.customHotkey)return n(document).bind("keydown."+k,nf),!1})},eu=function(){function p(t){var i=s[t],r=s.panes;i.fxSettings||(i.fxSettings={});r.fxSettings||(r.fxSettings={});n.each(["_open","_close","_size"],function(u,f){var o="fxName"+f,h="fxSpeed"+f,c="fxSettings"+f,e=i[o]=i[o]||r[o]||i.fxName||r.fxName||"none",a=n.effects&&(n.effects[e]||n.effects.effect&&n.effects.effect[e]);e!=="none"&&s.effects[e]&&a||(e=i[o]="none");var l=s.effects[e]||{},v=l.all||null,y=l[t]||null;i[h]=i[h]||r[h]||i.fxSpeed||r.fxSpeed||null;i[c]=n.extend(!0,{},v,y,r.fxSettings,i.fxSettings,r[c],i[c])});delete i.fxName;delete i.fxSpeed;delete i.fxSettings}var r,v,i,a,f,o,u,y,l,c;if(e=n.layout.transformData(e,!0),e=n.layout.backwardCompatibility.renameAllOptions(e),!n.isEmptyObject(e.panes)){for(r=n.layout.optionsMap.noDefault,f=0,o=r.length;f<o;f++)i=r[f],delete e.panes[i];for(r=n.layout.optionsMap.layout,f=0,o=r.length;f<o;f++)i=r[f],delete e.panes[i]}r=n.layout.optionsMap.layout;y=n.layout.config.optionRootKeys;for(i in e)a=e[i],n.inArray(i,y)<0&&n.inArray(i,r)<0&&(e.panes[i]||(e.panes[i]=n.isPlainObject(a)?n.extend(!0,{},a):a),delete e[i]);n.extend(!0,s,e);n.each(h.allPanes,function(t,f){if(h[f]=n.extend(!0,{},h.panes,h[f]),v=s.panes,u=s[f],f==="center")for(r=n.layout.optionsMap.center,t=0,o=r.length;t<o;t++)i=r[t],e.center[i]||!e.panes[i]&&u[i]||(u[i]=v[i]);else u=s[f]=n.extend(!0,{},v,u),p(f),u.resizerClass||(u.resizerClass="ui-layout-resizer"),u.togglerClass||(u.togglerClass="ui-layout-toggler");u.paneClass||(u.paneClass="ui-layout-pane")});l=e.zIndex;c=s.zIndexes;l>0&&(c.pane_normal=l,c.content_mask=t(l+1,c.content_mask),c.resizer_normal=t(l+2,c.resizer_normal));delete s.panes},ar=function(n){var t=s[n].paneSelector,i;return t.substr(0,1)==="#"?a.find(t).eq(0):(i=a.children(t).eq(0),i.length?i:a.children("form:first").children(t).eq(0))},ou=function(t){w(t);n.each(h.allPanes,function(n,t){vr(t,!0)});ki();n.each(h.borderPanes,function(n,t){c[t]&&o[t].isVisible&&(ut(t),vt(t))});pt("center");n.each(h.allPanes,function(n,t){yr(t)})},vr=function(n,i){var ut,d,a;if(i||b()){var u=s[n],e=o[n],v=h[n],p=v.dir,ot=e.fx,ht=u.spacing_open||0,ft=n==="center",w={},r=c[n],l,g,it;if(r?gi(n,!1,!0,!1):nt[n]=!1,r=c[n]=ar(n),!r.length){c[n]=!1;return}r.data("layoutCSS")||(ut="position,top,left,bottom,right,width,height,overflow,zIndex,display,backgroundColor,padding,margin,border",r.data("layoutCSS",ti(r,ut)));y[n]={name:n,pane:c[n],content:nt[n],options:s[n],state:o[n],children:et[n]};r.data({parentLayout:y,layoutPane:y[n],layoutEdge:n,layoutRole:"pane"}).css(v.cssReq).css("zIndex",s.zIndexes.pane_normal).css(u.applyDemoStyles?v.cssDemo:{}).addClass(u.paneClass+" "+u.paneClass+"-"+n).bind("mouseenter."+k,wi).bind("mouseleave."+k,st);d={hide:"",show:"",toggle:"",close:"",open:"",slideOpen:"",slideClose:"",slideToggle:"",size:"sizePane",sizePane:"sizePane",sizeContent:"",sizeHandles:"",enableClosable:"",disableClosable:"",enableSlideable:"",disableSlideable:"",enableResizable:"",disableResizable:"",swapPanes:"swapPanes",swap:"swapPanes",move:"swapPanes",removePane:"removePane",remove:"removePane",createChildren:"",resizeChildren:"",resizeAll:"resizeAll",resizeLayout:"resizeAll"};for(a in d)r.bind("layoutpane"+a.toLowerCase()+"."+k,y[d[a]||a]);di(n,!1);ft||(l=e.size=ct(n,u.size),g=ct(n,u.minSize)||1,it=ct(n,u.maxSize)||1e5,l>0&&(l=t(f(l,it),g)),e.autoResize=u.autoResize,e.isClosed=!1,e.isSliding=!1,e.isResizing=!1,e.isHidden=!1,e.pins||(e.pins=[]));e.tagName=r[0].tagName;e.edge=n;e.noRoom=!1;e.isVisible=!0;pr(n);p==="horz"?w.height=rt(r,l):p==="vert"&&(w.width=tt(r,l));r.css(w);p!="horz"&&pt(n,!0);o.initialized&&(ki(n),lr(n));u.initClosed&&u.closable&&!u.initHidden?at(n,!0,!0):u.initHidden||u.initClosed?nr(n):e.noRoom||r.css("display","block");r.css("visibility","visible");u.showOverflowOnHover&&r.hover(nu,vi);o.initialized&&yr(n)}},yr=function(n){var t=c[n],r=o[n],i=s[n];t&&(t.data("layout")&&ei(n,t.data("layout")),r.isVisible&&(o.initialized?dt():gt(n),i.triggerEventsOnLoad?v("onresize_end",n):dr(n,!0)),i.initChildren&&i.children&&bi(n))},pr=function(t){t=t?t.split(","):h.borderPanes;n.each(t,function(n,t){var f=c[t],r=p[t],a=s[t],e=o[t],u=h[t].side,i={};if(f){switch(t){case"north":i.top=l.inset.top;i.left=l.inset.left;i.right=l.inset.right;break;case"south":i.bottom=l.inset.bottom;i.left=l.inset.left;i.right=l.inset.right;break;case"west":i.left=l.inset.left;break;case"east":i.right=l.inset.right}f.css(i);r&&e.isClosed?r.css(u,l.inset[u]):r&&!e.isHidden&&r.css(u,l.inset[u]+lt(t))}})},ki=function(t){t=t?t.split(","):h.borderPanes;n.each(t,function(t,i){var nt=c[i];if(p[i]=!1,g[i]=!1,nt){var r=s[i],f=o[i],tt=h[i],e=r.paneSelector.substr(0,1)==="#"?r.paneSelector.substr(1):"",v=r.resizerClass,w=r.togglerClass,it=f.isVisible?r.spacing_open:r.spacing_closed,b="-"+i,rt=f.isVisible?"-open":"-closed",d=y[i],u=d.resizer=p[i]=n("<div><\/div>"),l=d.toggler=r.closable?g[i]=n("<div><\/div>"):!1;!f.isVisible&&r.slidable&&u.attr("title",r.tips.Slide).css("cursor",r.sliderCursor);u.attr("id",e?e+"-resizer":"").data({parentLayout:y,layoutPane:y[i],layoutEdge:i,layoutRole:"resizer"}).css(h.resizers.cssReq).css("zIndex",s.zIndexes.resizer_normal).css(r.applyDemoStyles?h.resizers.cssDemo:{}).addClass(v+" "+v+b).hover(wi,st).hover(er,or).mousedown(n.layout.disableTextSelection).mouseup(n.layout.enableTextSelection).appendTo(a);n.fn.disableSelection&&u.disableSelection();r.resizerDblClickToggle&&u.bind("dblclick."+k,bt);l&&(l.attr("id",e?e+"-toggler":"").data({parentLayout:y,layoutPane:y[i],layoutEdge:i,layoutRole:"toggler"}).css(h.togglers.cssReq).css(r.applyDemoStyles?h.togglers.cssDemo:{}).addClass(w+" "+w+b).hover(wi,st).bind("mouseenter",er).appendTo(u),r.togglerContent_open&&n("<span>"+r.togglerContent_open+"<\/span>").data({layoutEdge:i,layoutRole:"togglerContent"}).data("layoutRole","togglerContent").data("layoutEdge",i).addClass("ui-content content-open").css("display","none").appendTo(l),r.togglerContent_closed&&n("<span>"+r.togglerContent_closed+"<\/span>").data({layoutEdge:i,layoutRole:"togglerContent"}).addClass("ui-content content-closed").css("display","none").appendTo(l),gr(i));su(i);f.isVisible?tr(i):(ci(i),wt(i,!0))}});ni()},di=function(n,t){if(b()){var u=s[n],f=u.contentSelector,e=y[n],r=c[n],i;f&&(i=e.content=nt[n]=u.findNestedContent?r.find(f).eq(0):r.children(f).eq(0));i&&i.length?(i.data("layoutRole","content"),i.data("layoutCSS")||i.data("layoutCSS",ti(i,"height")),i.css(h.content.cssReq),u.applyDemoStyles&&(i.css(h.content.cssDemo),r.css(h.content.cssDemoPane)),r.css("overflowX").match(/(scroll|auto)/)&&r.css("overflow","hidden"),o[n].content={},t!==!1&&gt(n)):e.content=nt[n]=!1}},su=function(t){var r=n.layout.plugins.draggable,i;t=t?t.split(","):h.borderPanes;n.each(t,function(t,u){var f=s[u];if(!r||!c[u]||!f.resizable)return f.resizable=!1,!0;var l=o[u],nt=s.zIndexes,tt=h[u],e=tt.dir=="horz"?"top":"left",ct=c[u],b=p[u],y=f.resizerClass,k=0,w,it,rt=y+"-drag",ft=y+"-"+u+"-drag",st=y+"-dragging",ht=y+"-"+u+"-dragging",et=y+"-dragging-limit",ot=y+"-"+u+"-dragging-limit",g=!1;l.isClosed||b.attr("title",f.tips.Resize).css("cursor",f.resizerCursor);b.draggable({containment:a[0],axis:tt.dir=="horz"?"y":"x",delay:0,distance:1,grid:f.resizingGrid,helper:"clone",opacity:f.resizerDragOpacity,addClasses:!1,zIndex:nt.resizer_drag,start:function(n,t){if(f=s[u],l=o[u],it=f.livePaneResizing,!1===v("ondrag_start",u))return!1;l.isResizing=!0;o.paneResizing=u;d.clear(u+"_closeSlider");ut(u);w=l.resizerPosition;k=t.position[e];b.addClass(rt+" "+ft);g=!1;ii(u,{resizing:!0})},drag:function(n,t){g||(t.helper.addClass(st+" "+ht).css({right:"auto",bottom:"auto"}).children().css("visibility","hidden"),g=!0,l.isSliding&&c[u].css("zIndex",nt.pane_sliding));var r=0;t.position[e]<w.min?(t.position[e]=w.min,r=-1):t.position[e]>w.max&&(t.position[e]=w.max,r=1);r?(t.helper.addClass(et+" "+ot),window.defaultStatus=r>0&&u.match(/(north|west)/)||r<0&&u.match(/(south|east)/)?f.tips.maxSizeWarning:f.tips.minSizeWarning):(t.helper.removeClass(et+" "+ot),window.defaultStatus="");it&&Math.abs(t.position[e]-k)>=f.liveResizingTolerance&&(k=t.position[e],i(n,t,u))},stop:function(t,r){n("body").enableSelection();window.defaultStatus="";b.removeClass(rt+" "+ft);l.isResizing=!1;o.paneResizing=!1;i(t,r,u,!0)}})});i=function(n,t,i,r){var f=t.position,y=h[i],c=s[i],a=o[i],u,e;switch(i){case"north":u=f.top;break;case"west":u=f.left;break;case"south":u=l.layoutHeight-f.top-c.spacing_open;break;case"east":u=l.layoutWidth-f.left-c.spacing_open}if(e=u-l.inset[y.side],r)!1!==v("ondrag_end",i)&&ai(i,e,!1,!0),oi(!0),a.isSliding&&ii(i,{resizing:!0});else{if(Math.abs(e-a.size)<c.liveResizingTolerance)return;ai(i,e,!1,!0);hu()}}},wr=function(){var i=n(this),r=i.data("layoutMask"),t=o[r];t.tagName=="IFRAME"&&t.isVisible&&i.css({top:t.offsetTop,left:t.offsetLeft,width:t.outerWidth,height:t.outerHeight})},hu=function(){ft.each(wr)},ii=function(t,i){var l=h[t],r=["center"],c=s.zIndexes,u=n.extend({objectsOnly:!1,animation:!1,resizing:!0,sliding:o[t].isSliding},i),f,e;u.resizing&&r.push(t);u.sliding&&r.push(h.oppositeEdge[t]);l.dir==="horz"&&(r.push("west"),r.push("east"));n.each(r,function(n,t){e=o[t];f=s[t];e.isVisible&&(f.maskObjects||!u.objectsOnly&&f.maskContents)&&cu(t).each(function(){wr.call(this);this.style.zIndex=e.isSliding?c.pane_sliding+1:c.pane_normal+1;this.style.display="block"})})},oi=function(t){if(t||!o.paneResizing)ft.hide();else if(!t&&!n.isEmptyObject(o.panesSliding))for(var i=ft.length-1,u,r;i>=0;i--)r=ft.eq(i),u=r.data("layoutMask"),s[u].maskObjects||r.hide()},cu=function(t){for(var i=n([]),r,u=0,f=ft.length;u<f;u++)r=ft.eq(u),r.data("layoutMask")===t&&(i=i.add(r));return i.length?i:lu(t)},lu=function(t){var v=c[t],y=o[t],u=s[t],h=s.zIndexes,e,r,l,i,f;if(!u.maskContents&&!u.maskObjects)return n([]);for(f=0;f<(u.maskObjects?2:1);f++)e=u.maskObjects&&f==0,r=document.createElement(e?"iframe":"div"),l=n(r).data("layoutMask",t),r.className="ui-layout-mask ui-layout-mask-"+t,i=r.style,i.background="#FFF",i.position="absolute",i.display="block",e?(r.src="about:blank",r.frameborder=0,i.border=0,i.opacity=0,i.filter="Alpha(Opacity='0')"):(i.opacity=.001,i.filter="Alpha(Opacity='1')"),y.tagName=="IFRAME"?(i.zIndex=h.pane_normal+1,a.append(r)):(l.addClass("ui-layout-mask-inside-pane"),i.zIndex=u.maskZindex||h.content_mask,i.top=0,i.left=0,i.width="100%",i.height="100%",v.append(r)),ft=ft.add(r);return ft},au=function(t,i){var u,f;n(window).unbind("."+k);n(document).unbind("."+k);typeof t=="object"?w(t):i=t;a.clearQueue().removeData("layout").removeData("layoutContainer").removeClass(s.containerClass).unbind("."+k);ft.remove();n.each(h.allPanes,function(n,t){gi(t,!1,!0,i)});u="layoutCSS";a.data(u)&&!a.data("layoutRole")&&a.css(a.data(u)).removeData(u);l.tagName==="BODY"&&(a=n("html")).data(u)&&a.css(a.data(u)).removeData(u);r(y,n.layout.onDestroy);cr();for(f in y)f.match(/^(container|options)$/)||delete y[f];return y.destroyed=!0,y},gi=function(t,i,r,u){if(b()){var f=w.call(this,t),e=c[f],h=nt[f],a=p[f],v=g[f];e&&n.isEmptyObject(e.data())&&(e=!1);h&&n.isEmptyObject(h.data())&&(h=!1);a&&n.isEmptyObject(a.data())&&(a=!1);v&&n.isEmptyObject(v.data())&&(v=!1);e&&e.stop(!0,!0);var ut=s[f],ct=o[f],tt="layoutCSS",l=et[f],it=n.isPlainObject(l)&&!n.isEmptyObject(l),lt=u!==undefined?u:ut.destroyChildren;if(it&&lt&&(n.each(l,function(n,t){t.destroyed||t.destroy(!0);t.destroyed&&delete l[n]}),n.isEmptyObject(l)&&(l=et[f]=null,it=!1)),e&&i&&!it)e.remove();else if(e&&e[0]){var d=ut.paneClass,rt=d+"-"+f,ft="-open",ot="-sliding",st="-closed",ht=[d,d+ft,d+st,d+ot,rt,rt+ft,rt+st,rt+ot];n.merge(ht,pi(e,!0));e.removeClass(ht.join(" ")).removeData("parentLayout").removeData("layoutPane").removeData("layoutRole").removeData("layoutEdge").removeData("autoHidden").unbind("."+k);it&&h?(h.width(h.width()),n.each(l,function(n,t){t.resizeAll()})):h&&h.css(h.data(tt)).removeData(tt).removeData("layoutRole");e.data("layout")||e.css(e.data(tt)).removeData(tt)}v&&v.remove();a&&a.remove();y[f]=c[f]=nt[f]=p[f]=g[f]=!1;ct={removed:!0};r||dt()}},si=function(n){var t=c[n],r=s[n],i=t[0].style;r.useOffscreenClose?(t.data(h.offscreenReset)||t.data(h.offscreenReset,{left:i.left,right:i.right}),t.css(h.offscreenCSS)):t.hide().removeData(h.offscreenReset)},br=function(n){var i=c[n],f=s[n],u=h.offscreenCSS,r=i.data(h.offscreenReset),t=i[0].style;i.show().removeData(h.offscreenReset);f.useOffscreenClose&&r&&(t.left==u.left&&(t.left=r.left),t.right==u.right&&(t.right=r.right))},nr=function(n,t){if(b()){var i=w.call(this,n),f=s[i],r=o[i],e=c[i],u=p[i];i!=="center"&&e&&!r.isHidden&&(o.initialized&&!1===v("onhide_start",i)||(r.isSliding=!1,delete o.panesSliding[i],u&&u.hide(),!o.initialized||r.isClosed?(r.isClosed=!0,r.isHidden=!0,r.isVisible=!1,o.initialized||si(i),pt(h[i].dir==="horz"?"":"center"),(o.initialized||f.triggerEventsOnLoad)&&v("onhide_end",i)):(r.isHiding=!0,at(i,!1,t))))}},hi=function(n,t,i,r){if(b()){var u=w.call(this,n),h=s[u],f=o[u],e=c[u],l=p[u];u!=="center"&&e&&f.isHidden&&!1!==v("onshow_start",u)&&(f.isShowing=!0,f.isSliding=!1,delete o.panesSliding[u],t===!1?at(u,!0):kt(u,!1,i,r))}},bt=function(n,t){if(b()){var r=yi(n),i=w.call(this,n),u=o[i];r&&r.stopImmediatePropagation();u.isHidden?hi(i):u.isClosed?kt(i,!!t):at(i)}},vu=function(n,t){var r=c[n],i=o[n];si(n);i.isClosed=!0;i.isVisible=!1;t&&ci(n)},at=function(n,t,i,r){function nt(){f.isMoving=!1;wt(u,!0);var n=h.oppositeEdge[u];o[n].noRoom&&(ut(n),vt(n));!r&&(o.initialized||e.triggerEventsOnLoad)&&(l||v("onclose_end",u),l&&v("onshow_end",u),y&&v("onhide_end",u))}var u=w.call(this,n);if(u!=="center"){if(!o.initialized&&c[u]){vu(u,!0);return}if(b()){var k=c[u],it=p[u],rt=g[u],e=s[u],f=o[u],ft=h[u],d,l,y,tt;a.queue(function(n){if(!k||!e.closable&&!f.isShowing&&!f.isHiding||!t&&f.isClosed&&!f.isShowing)return n();var r=!f.isShowing&&!1===v("onclose_start",u);if(l=f.isShowing,y=f.isHiding,tt=f.isSliding,delete f.isShowing,delete f.isHiding,r)return n();d=!i&&!f.isClosed&&e.fxName_close!="none";f.isMoving=!0;f.isClosed=!0;f.isVisible=!1;y?f.isHidden=!0:l&&(f.isHidden=!1);f.isSliding?ri(u,!1):pt(h[u].dir==="horz"?"":"center",!1);ci(u);d?(li(u,!0),k.hide(e.fxName_close,e.fxSettings_close,e.fxSpeed_close,function(){li(u,!1);f.isClosed&&nt();n()})):(si(u),nt(),n())})}}},ci=function(t){if(p[t]){var d=c[t],y=p[t],u=g[t],f=s[t],k=o[t],w=h[t].side,i=f.resizerClass,e=f.togglerClass,r="-"+t,a="-open",b="-sliding",v="-closed";y.css(w,l.inset[w]).removeClass(i+a+" "+i+r+a).removeClass(i+b+" "+i+r+b).addClass(i+v+" "+i+r+v);k.isHidden&&y.hide();f.resizable&&n.layout.plugins.draggable&&y.draggable("disable").removeClass("ui-state-disabled").css("cursor","default").attr("title","");u&&(u.removeClass(e+a+" "+e+r+a).addClass(e+v+" "+e+r+v).attr("title",f.tips.Open),u.children(".content-open").hide(),u.children(".content-closed").css("display","block"));rr(t,!1);o.initialized&&ni()}},kt=function(n,t,i,r){function d(){f.isMoving=!1;ur(u);f.isSliding||pt(h[u].dir=="vert"?"center":"",!1);tr(u)}if(b()){var u=w.call(this,n),l=c[u],nt=p[u],tt=g[u],e=s[u],f=o[u],it=h[u],y,k;u!=="center"&&a.queue(function(n){if(!l||!e.resizable&&!e.closable&&!f.isShowing||f.isVisible&&!f.isSliding)return n();if(f.isHidden&&!f.isShowing){n();hi(u,!0);return}f.autoResize&&f.size!=e.size?yt(u,e.size,!0,!0,!0):ut(u,t);var o=v("onopen_start",u);if(o==="abort")return n();if(o!=="NC"&&ut(u,t),f.minSize>f.maxSize)return rr(u,!1),!r&&e.tips.noRoomToOpen&&alert(e.tips.noRoomToOpen),n();t?ri(u,!0):f.isSliding?ri(u,!1):e.slidable&&wt(u,!1);f.noRoom=!1;vt(u);k=f.isShowing;delete f.isShowing;y=!i&&f.isClosed&&e.fxName_open!="none";f.isMoving=!0;f.isVisible=!0;f.isClosed=!1;k&&(f.isHidden=!1);y?(li(u,!0),l.show(e.fxName_open,e.fxSettings_open,e.fxSpeed_open,function(){li(u,!1);f.isVisible&&d();n()})):(br(u),d(),n())})}},tr=function(t,i){var nt=c[t],f=p[t],a=g[t],u=s[t],y=o[t],tt=h[t].side,r=u.resizerClass,w=u.togglerClass,e="-"+t,b="-open",k="-closed",d="-sliding";f.css(tt,l.inset[tt]+lt(t)).removeClass(r+k+" "+r+e+k).addClass(r+b+" "+r+e+b);y.isSliding?f.addClass(r+d+" "+r+e+d):f.removeClass(r+d+" "+r+e+d);st(0,f);u.resizable&&n.layout.plugins.draggable?f.draggable("enable").css("cursor",u.resizerCursor).attr("title",u.tips.Resize):y.isSliding||f.css("cursor","default");a&&(a.removeClass(w+k+" "+w+e+k).addClass(w+b+" "+w+e+b).attr("title",u.tips.Close),st(0,a),a.children(".content-closed").hide(),a.children(".content-open").css("display","block"));rr(t,!y.isSliding);n.extend(y,it(nt));o.initialized&&(ni(),gt(t,!0));!i&&(o.initialized||u.triggerEventsOnLoad)&&nt.is(":visible")&&(v("onopen_end",t),y.isShowing&&v("onshow_end",t),o.initialized&&v("onresize_end",t))},kr=function(n){function f(){r.isClosed?r.isMoving||kt(t,!0):ri(t,!0)}if(b()){var i=yi(n),t=w.call(this,n),r=o[t],u=s[t].slideDelay_open;t!=="center"&&(i&&i.stopImmediatePropagation(),r.isClosed&&i&&i.type==="mouseenter"&&u>0?d.set(t+"_openSlider",f,u):f())}},ir=function(i){function h(){u.isClosed?ri(r,!1):u.isMoving||at(r)}if(b()){var e=yi(i),r=w.call(this,i),f=s[r],u=o[r],l=u.isMoving?1e3:300;if(r!=="center"&&!u.isClosed&&!u.isResizing)if(f.slideTrigger_close==="click")h();else{if(f.preventQuickSlideClose&&u.isMoving)return;if(f.preventPrematureSlideClose&&e&&n.layout.isMouseOverElem(e,c[r]))return;e?d.set(r+"_closeSlider",h,t(f.slideDelay_close,l)):h()}}},yu=function(n){var t=w.call(this,n);bt(t,!0)},li=function(n,t){var i=c[n],f=o[n],u=s[n],r=s.zIndexes;t?(ii(n,{animation:!0,objectsOnly:!0}),i.css({zIndex:r.pane_animate}),n=="south"?i.css({top:l.inset.top+l.innerHeight-i.outerHeight()}):n=="east"&&i.css({left:l.inset.left+l.innerWidth-i.outerWidth()})):(oi(),i.css({zIndex:f.isSliding?r.pane_sliding:r.pane_normal}),n=="south"?i.css({top:"auto"}):n!="east"||i.css("left").match(/\-99999/)||i.css({left:"auto"}),ht.msie&&u.fxOpacityFix&&u.fxName_open!="slide"&&i.css("filter")&&i.css("opacity")==1&&i[0].style.removeAttribute("filter"))},wt=function(n,t){var i=s[n],f=c[n],u=p[n],r=i.slideTrigger_open.toLowerCase();u&&(!t||i.slidable)&&(r.match(/mouseover/)?r=i.slideTrigger_open="mouseenter":r.match(/(click|dblclick|mouseenter)/)||(r=i.slideTrigger_open="click"),i.resizerDblClickToggle&&r.match(/click/)&&u[t?"unbind":"bind"]("dblclick."+k,bt),u[t?"bind":"unbind"](r+"."+k,kr).css("cursor",t?i.sliderCursor:"default").attr("title",t?i.tips.Slide:""))},ri=function(n,t){function v(t){d.clear(n+"_closeSlider");t.stopPropagation()}var i=s[n],a=o[n],y=h[n],f=s.zIndexes,r=i.slideTrigger_close.toLowerCase(),e=t?"bind":"unbind",l=c[n],u=p[n];d.clear(n+"_closeSlider");t?(a.isSliding=!0,o.panesSliding[n]=!0,wt(n,!1)):(a.isSliding=!1,delete o.panesSliding[n]);l.css("zIndex",t?f.pane_sliding:f.pane_normal);u.css("zIndex",t?f.pane_sliding+2:f.resizer_normal);r.match(/(click|mouseleave)/)||(r=i.slideTrigger_close="mouseleave");u[e](r,ir);r==="mouseleave"&&(l[e]("mouseleave."+k,ir),u[e]("mouseenter."+k,v),l[e]("mouseenter."+k,v));t?r!=="click"||i.resizable||(u.css("cursor",t?i.sliderCursor:"default"),u.attr("title",t?i.tips.Close:"")):d.clear(n+"_closeSlider")},vt=function(t,i,r,u){var y=s[t],f=o[t],a=h[t],k=c[t],e=p[t],b=a.dir==="vert",v=!1,w;(t==="center"||b&&f.noVerticalRoom)&&(v=f.maxHeight>=0,v&&f.noRoom?(br(t),e&&e.show(),f.isVisible=!0,f.noRoom=!1,b&&(f.noVerticalRoom=!1),ur(t)):v||f.noRoom||(si(t),e&&e.hide(),f.isVisible=!1,f.noRoom=!0));t==="center"||(f.minSize<=f.maxSize?(v=!0,f.size>f.maxSize?yt(t,f.maxSize,r,!0,u):f.size<f.minSize?yt(t,f.minSize,r,!0,u):e&&f.isVisible&&k.is(":visible")&&(w=f.size+l.inset[a.side],n.layout.cssNum(e,a.side)!=w&&e.css(a.side,w)),f.noRoom&&(f.wasOpen&&y.closable?y.autoReopen?kt(t,!1,!0,!0):f.noRoom=!1:hi(t,f.wasOpen,!0,!0))):f.noRoom||(f.noRoom=!0,f.wasOpen=!f.isClosed&&!f.isSliding,f.isClosed||(y.closable?at(t,!0,!0):nr(t,!0))))},ai=function(n,t,i,r,u){if(b()){var f=w.call(this,n),h=s[f],e=o[f],c=u||h.livePaneResizing&&!e.isResizing;f!=="center"&&(e.autoResize=!1,yt(f,t,i,r,c))}},yt=function(i,r,u,e,y){function lt(){for(var c=tt==="width"?g.outerWidth():g.outerHeight(),e=[{pane:k,count:1,target:r,actual:c,correct:r===c,attempt:r,cssSize:rt}],f=e[0],i={},a="Inaccurate size after resizing the "+k+"-pane.",s;!f.correct;){if(i={pane:k,count:f.count+1,target:r},i.attempt=f.actual>r?t(0,f.attempt-(f.actual-r)):t(0,f.attempt+(r-f.actual)),i.cssSize=fr(k,i.attempt),g.css(tt,i.cssSize),i.actual=tt=="width"?g.outerWidth():g.outerHeight(),i.correct=r===i.actual,e.length===1&&(ot(a,!1,!0),ot(f,!1,!0)),ot(i,!1,!0),e.length>3)break;e.push(i);f=e[e.length-1]}d.size=r;n.extend(d,it(g));d.isVisible&&g.is(":visible")&&(et&&et.css(st,r+l.inset[st]),gt(k));!u&&!ht&&o.initialized&&d.isVisible&&v("onresize_end",k);u||(d.isSliding||pt(h[k].dir=="horz"?"":"center",ht,y),ni());s=h.oppositeEdge[k];r<ft&&o[s].noRoom&&(ut(s),vt(s,!1,u));e.length>1&&ot(a+"\nSee the Error Console for details.",!0,!0)}if(b()){var k=w.call(this,i),nt=s[k],d=o[k],g=c[k],et=p[k],st=h[k].side,tt=h[k].sizeType.toLowerCase(),ht=d.isResizing&&!nt.triggerEventsDuringLiveResize,at=e!==!0&&nt.animatePaneSizing,ft,rt;k!=="center"&&a.queue(function(i){if(ut(k),ft=d.size,r=ct(k,r),r=t(r,ct(k,nt.minSize)),r=f(r,d.maxSize),r<d.minSize){i();vt(k,!1,u);return}if(!y&&r===ft)return i();if(d.newSize=r,!u&&o.initialized&&d.isVisible&&v("onresize_start",k),rt=fr(k,r),at&&g.is(":visible")){var c=n.layout.effects.size[k]||n.layout.effects.size.all,l=nt.fxSettings_size.easing||c.easing,e=s.zIndexes,h={};h[tt]=rt+"px";d.isMoving=!0;g.css({zIndex:e.pane_animate}).show().animate(h,nt.fxSpeed_size,l,function(){g.css({zIndex:d.isSliding?e.pane_sliding:e.pane_normal});d.isMoving=!1;delete d.newSize;lt();i()})}else g.css(tt,rt),delete d.newSize,g.is(":visible")?lt():d.size=r,i()})}},pt=function(i,r,u){i=(i?i:"east,west,center").split(",");n.each(i,function(i,f){var ot;if(c[f]){var st=s[f],e=o[f],a=c[f],ct=p[f],lt=f=="center",d=!0,h={},g=n.layout.showInvisibly(a),y=iu();if(n.extend(e,it(a)),f==="center"){if(!u&&e.isVisible&&y.width===e.outerWidth&&y.height===e.outerHeight)return a.css(g),!0;if(n.extend(e,ui(f),{maxWidth:y.width,maxHeight:y.height}),h=y,e.newWidth=h.width,e.newHeight=h.height,h.width=tt(a,h.width),h.height=rt(a,h.height),d=h.width>=0&&h.height>=0,!o.initialized&&st.minWidth>y.width){var w=st.minWidth-e.outerWidth,nt=s.east.minSize||0,ut=s.west.minSize||0,b=o.east.size,k=o.west.size,ft=b,et=k;if(w>0&&o.east.isVisible&&b>nt&&(ft=t(b-nt,b-w),w-=b-ft),w>0&&o.west.isVisible&&k>ut&&(et=t(k-ut,k-w),w-=k-et),w===0){b&&b!=nt&&yt("east",ft,!0,!0,u);k&&k!=ut&&yt("west",et,!0,!0,u);pt("center",r,u);a.css(g);return}}}else{if(e.isVisible&&!e.noVerticalRoom&&n.extend(e,it(a),ui(f)),!u&&!e.noVerticalRoom&&y.height===e.outerHeight)return a.css(g),!0;h.top=y.top;h.bottom=y.bottom;e.newSize=y.height;h.height=rt(a,y.height);e.maxHeight=h.height;d=e.maxHeight>=0;d||(e.noVerticalRoom=!0)}if(d?(!r&&o.initialized&&v("onresize_start",f),a.css(h),f!=="center"&&ni(f),!e.noRoom||e.isClosed||e.isHidden||vt(f),e.isVisible&&(n.extend(e,it(a)),o.initialized&&gt(f))):!e.noRoom&&e.isVisible&&vt(f),a.css(g),delete e.newSize,delete e.newWidth,delete e.newHeight,!e.isVisible)return!0;f==="center"&&(ot=ht.isIE6||!ht.boxModel,c.north&&(ot||o.north.tagName=="IFRAME")&&c.north.css("width",tt(c.north,l.innerWidth)),c.south&&(ot||o.south.tagName=="IFRAME")&&c.south.css("width",tt(c.south,l.innerWidth)));!r&&o.initialized&&v("onresize_end",f)}})},dt=function(t){var f=l.innerWidth,e=l.innerHeight,y,p,u,i,r;if(w(t),a.is(":visible")){if(!o.initialized){fi();return}if(t===!0&&n.isPlainObject(s.outset)&&a.css(s.outset),n.extend(l,it(a,s.inset)),l.outerHeight){if(t===!0&&pr(),!1===v("onresizeall_start"))return!1;y=l.innerHeight<e;p=l.innerWidth<f;n.each(["south","north","east","west"],function(n,t){c[t]&&(i=s[t],r=o[t],r.autoResize&&r.size!=i.size?yt(t,i.size,!0,!0,!0):(ut(t),vt(t,!1,!0,!0)))});pt("",!0,!0);ni();n.each(h.allPanes,function(n,t){(u=c[t],u)&&o[t].isVisible&&v("onresize_end",t)});v("onresizeall_end")}}},dr=function(t,i){var r=w.call(this,t),u;s[r].resizeChildren&&(i||ei(r),u=et[r],n.isPlainObject(u)&&n.each(u,function(n,t){t.destroyed||t.resizeAll()}))},gt=function(i,r){if(b()){var u=w.call(this,i);u=u?u.split(","):h.allPanes;n.each(u,function(n,i){function a(n){return t(e.css.paddingBottom,parseInt(n.css("marginBottom"),10)||0)}function y(){var e=s[i].contentIgnoreSelector,t=f.nextAll().not(".ui-layout-mask").not(e||":lt(0)"),r=t.filter(":visible"),n=r.filter(":last");u={top:f[0].offsetTop,height:f.outerHeight(),numFooters:t.length,hiddenFooters:t.length-r.length,spaceBelow:0};u.spaceAbove=u.top;u.bottom=u.top+u.height;u.spaceBelow=n.length?n[0].offsetTop+n.outerHeight()-u.bottom+a(n):a(f)}var h=c[i],f=nt[i],p=s[i],e=o[i],u=e.content,l;if(!h||!f||!h.is(":visible"))return!0;(f.length||(di(i,!1),f))&&!1!==v("onsizecontent_start",i)&&((!e.isMoving&&!e.isResizing||p.liveContentResizing||r||u.top==undefined)&&(y(),u.hiddenFooters>0&&h.css("overflow")==="hidden"&&(h.css("overflow","visible"),y(),h.css("overflow","hidden"))),l=e.innerHeight-(u.spaceAbove-e.css.paddingTop)-(u.spaceBelow-e.css.paddingBottom),f.is(":visible")&&u.height==l||(tu(f,l,!0),u.height=l),o.initialized&&v("onsizecontent_end",i))})}},ni=function(t){var r=w.call(this,t);r=r?r.split(","):h.borderPanes;n.each(r,function(t,r){var b=s[r],y=o[r],it=c[r],e=p[r],f=g[r],k,et,ot,ht;if(it&&e){var ct=h[r].dir,ut=y.isClosed?"_closed":"_open",d=b["spacing"+ut],nt=b["togglerAlign"+ut],v=b["togglerLength"+ut],a,ft,w;if(d===0){e.hide();return}if(y.noRoom||y.isHidden||e.show(),ct==="horz"?(a=l.innerWidth,y.resizerLength=a,ft=n.layout.cssNum(it,"left"),e.css({width:tt(e,a),height:rt(e,d),left:ft>-9999?ft:l.inset.left})):(a=it.outerHeight(),y.resizerLength=a,e.css({height:rt(e,a),width:tt(e,d),top:l.inset.top+lt("north",!0)})),st(b,e),f){if(v===0||y.isSliding&&b.hideTogglerOnSlide){f.hide();return}if(f.show(),!(v>0)||v==="100%"||v>a)v=a,w=0;else if(i(nt))switch(nt){case"top":case"left":w=0;break;case"bottom":case"right":w=a-v;break;case"middle":case"center":default:w=u((a-v)/2)}else et=parseInt(nt,10),w=nt>=0?et:a-v+et;ct==="horz"?(ot=tt(f,v),f.css({width:ot,height:rt(f,d),left:w,top:0}),f.children(".ui-content").each(function(){k=n(this);k.css("marginLeft",u((ot-k.outerWidth())/2))})):(ht=rt(f,v),f.css({height:ht,width:tt(f,d),top:w,left:0}),f.children(".ui-content").each(function(){k=n(this);k.css("marginTop",u((ht-k.outerHeight())/2))}));st(0,f)}!o.initialized&&(b.initHidden||y.isHidden)&&(e.hide(),f&&f.hide())}})},gr=function(n){if(b()){var t=w.call(this,n),r=g[t],i=s[t];r&&(i.closable=!0,r.bind("click."+k,function(n){n.stopPropagation();bt(t)}).css("visibility","visible").css("cursor","pointer").attr("title",o[t].isClosed?i.tips.Open:i.tips.Close).show())}},pu=function(n,t){if(b()){var i=w.call(this,n),r=g[i];r&&(s[i].closable=!1,o[i].isClosed&&kt(i,!1,!0),r.unbind("."+k).css("visibility",t?"hidden":"visible").css("cursor","default").attr("title",""))}},wu=function(n){if(b()){var t=w.call(this,n),i=p[t];i&&i.data("draggable")&&(s[t].slidable=!0,o[t].isClosed&&wt(t,!0))}},bu=function(n){if(b()){var t=w.call(this,n),i=p[t];i&&(s[t].slidable=!1,o[t].isSliding?at(t,!1,!0):(wt(t,!1),i.css("cursor","default").attr("title",""),st(null,i[0])))}},ku=function(n){if(b()){var i=w.call(this,n),t=p[i],r=s[i];t&&t.data("draggable")&&(r.resizable=!0,t.draggable("enable"),o[i].isClosed||t.css("cursor",r.resizerCursor).attr("title",r.tips.Resize))}},du=function(n){if(b()){var i=w.call(this,n),t=p[i];t&&t.data("draggable")&&(s[i].resizable=!1,t.draggable("disable").css("cursor","default").attr("title",""),st(null,t[0]))}},gu=function(i,r){function k(t){var i=c[t],r=nt[t];return i?{pane:t,P:i?i[0]:!1,C:r?r[0]:!1,state:n.extend(!0,{},o[t]),options:n.extend(!0,{},s[t])}:!1}function d(i,r){if(i){var w=i.P,b=i.C,k=i.pane,f=h[r],g=n.extend(!0,{},o[r]),u=s[r],e={resizerCursor:u.resizerCursor},d,v;n.each("fxName,fxSpeed,fxSettings".split(","),function(n,t){e[t+"_open"]=u[t+"_open"];e[t+"_close"]=u[t+"_close"];e[t+"_size"]=u[t+"_size"]});c[r]=n(w).data({layoutPane:y[r],layoutEdge:r}).css(h.hidden).css(f.cssReq);nt[r]=b?n(b):!1;s[r]=n.extend(!0,{},i.options,e);o[r]=n.extend(!0,{},i.state);d=new RegExp(u.paneClass+"-"+k,"g");w.className=w.className.replace(d,u.paneClass+"-"+r);ki(r);f.dir!=h[k].dir?(v=a[r]||0,ut(r),v=t(v,o[r].minSize),ai(r,v,!0,!0)):p[r].css(f.side,l.inset[f.side]+(o[r].isVisible?lt(r):0));i.state.isVisible&&!g.isVisible?tr(r,!0):(ci(r),wt(r,!0));i=null}}var u;if(b()){if(u=w.call(this,i),o[u].edge=r,o[r].edge=u,!1===v("onswap_start",u)||!1===v("onswap_start",r)){o[u].edge=u;o[r].edge=r;return}var f=k(u),e=k(r),a={};a[u]=f?f.state.size:0;a[r]=e?e.state.size:0;c[u]=!1;c[r]=!1;o[u]={};o[r]={};g[u]&&g[u].remove();g[r]&&g[r].remove();p[u]&&p[u].remove();p[r]&&p[r].remove();p[u]=p[r]=g[u]=g[r]=!1;d(f,r);d(e,u);f=e=a=null;c[u]&&c[u].css(h.visible);c[r]&&c[r].css(h.visible);dt();v("onswap_end",u);v("onswap_end",r);return}},rr=function(t,i){n.layout.plugins.buttons&&n.each(o[t].pins,function(r,u){n.layout.buttons.setPinState(y,n(u),t,i)})},a=n(this).eq(0);if(!a.length)return ot(s.errors.containerMissing);if(a.data("layoutContainer")&&a.data("layout"))return a.data("layout");var c={},nt={},p={},g={},ft=n([]),l=o.container,k=o.id,y={options:s,state:o,container:a,panes:c,contents:nt,resizers:p,togglers:g,hide:nr,show:hi,toggle:bt,open:kt,close:at,slideOpen:kr,slideClose:ir,slideToggle:yu,setSizeLimits:ut,_sizePane:yt,sizePane:ai,sizeContent:gt,swapPanes:gu,showMasks:ii,hideMasks:oi,initContent:di,addPane:vr,removePane:gi,createChildren:bi,refreshChildren:ei,enableClosable:gr,disableClosable:pu,enableSlidable:wu,disableSlidable:bu,enableResizable:ku,disableResizable:du,allowOverflow:nu,resetOverflow:vi,destroy:au,initPanes:b,resizeAll:dt,runCallbacks:v,hasParentLayout:!1,children:et,north:!1,south:!1,west:!1,east:!1,center:!1};return ru()==="cancel"?null:y}})(jQuery),function(n){n.layout&&(n.ui||(n.ui={}),n.ui.cookie={acceptsCookies:!!navigator.cookieEnabled,read:function(t){for(var u=document.cookie,e=u?u.split(";"):[],f,i,r=0;f=e[r];r++)if(i=n.trim(f).split("="),i[0]==t)return decodeURIComponent(i[1]);return null},write:function(t,i,r){var o="",u="",h=!1,e=r||{},f=e.expires||null,s=n.type(f);s==="date"?u=f:s==="string"&&f>0&&(f=parseInt(f,10),s="number");s==="number"&&(u=new Date,f>0?u.setDate(u.getDate()+f):(u.setFullYear(1970),h=!0));u&&(o+=";expires="+u.toUTCString());e.path&&(o+=";path="+e.path);e.domain&&(o+=";domain="+e.domain);e.secure&&(o+=";secure");document.cookie=t+"="+(h?"":encodeURIComponent(i))+o},clear:function(t){n.ui.cookie.write(t,"",{expires:-1})}},n.cookie||(n.cookie=function(t,i,r){var u=n.ui.cookie;if(i===null)u.clear(t);else{if(i===undefined)return u.read(t);u.write(t,i,r)}}),n.layout.plugins.stateManagement=!0,n.layout.defaults.stateManagement={enabled:!1,autoSave:!0,autoLoad:!0,animateLoad:!0,includeChildren:!0,stateKeys:"north.size,south.size,east.size,west.size,north.isClosed,south.isClosed,east.isClosed,west.isClosed,north.isHidden,south.isHidden,east.isHidden,west.isHidden",cookie:{name:"",domain:"",path:"",expires:"",secure:!1}},n.layout.optionsMap.layout.push("stateManagement"),n.layout.config.optionRootKeys.push("stateManagement"),n.layout.state={saveCookie:function(t,i,r){var u=t.options,f=u.stateManagement,e=n.extend(!0,{},f.cookie,r||null),o=t.state.stateData=t.readState(i||f.stateKeys);return n.ui.cookie.write(e.name||u.name||"Layout",n.layout.state.encodeJSON(o),e),n.extend(!0,{},o)},deleteCookie:function(t){var i=t.options;n.ui.cookie.clear(i.stateManagement.cookie.name||i.name||"Layout")},readCookie:function(t){var i=t.options,r=n.ui.cookie.read(i.stateManagement.cookie.name||i.name||"Layout");return r?n.layout.state.decodeJSON(r):{}},loadCookie:function(t){var i=n.layout.state.readCookie(t);return i&&!n.isEmptyObject(i)&&(t.state.stateData=n.extend(!0,{},i),t.loadState(i)),i},loadState:function(t,i,r){var v,f,u,e,o,h,c,l,a;n.isPlainObject(i)&&!n.isEmptyObject(i)&&(i=t.state.stateData=n.layout.transformData(i),v=t.options.stateManagement,r=n.extend({animateLoad:!1,includeChildren:v.includeChildren},r),t.state.initialized?(f=!r.animateLoad,n.each(n.layout.config.borderPanes,function(r,l){(u=i[l],n.isPlainObject(u))&&(s=u.size,e=u.initClosed,o=u.initHidden,ar=u.autoResize,h=t.state[l],c=h.isVisible,ar&&(h.autoResize=ar),c||t._sizePane(l,s,!1,!1,!1),o===!0?t.hide(l,f):e===!0?t.close(l,!1,f):e===!1?t.open(l,!1,f):o===!1&&t.show(l,!1,f),c&&t._sizePane(l,s,!1,!1,f))}),r.includeChildren&&n.each(t.children,function(t,r){l=i[t]?i[t].children:0;l&&r&&n.each(r,function(n,t){a=l[n];t&&a&&t.loadState(a)})})):(u=n.extend(!0,{},i),n.each(n.layout.config.allPanes,function(n,t){u[t]&&delete u[t].children}),n.extend(!0,t.options,u)))},readState:function(t,i){var l,k;n.type(i)==="string"&&(i={keys:i});i||(i={});var y=t.options.stateManagement,p=i.includeChildren,d=p!==undefined?p:y.includeChildren,r=i.stateKeys||y.stateKeys,w={isClosed:"initClosed",isHidden:"initHidden"},a=t.state,b=n.layout.config.allPanes,e={},v,u,f,h,o,c,s;for(n.isArray(r)&&(r=r.join(",")),r=r.replace(/__/g,".").split(","),l=0,k=r.length;l<k;l++)(v=r[l].split("."),u=v[0],f=v[1],n.inArray(u,b)<0)||(h=a[u][f],h!=undefined)&&(f=="isClosed"&&a[u].isSliding&&(h=!0),(e[u]||(e[u]={}))[w[f]?w[f]:f]=h);return d&&n.each(b,function(i,r){c=t.children[r];o=a.stateData[r];n.isPlainObject(c)&&!n.isEmptyObject(c)&&(s=e[r]||(e[r]={}),s.children||(s.children={}),n.each(c,function(t,i){i.state.initialized?s.children[t]=n.layout.state.readState(i):o&&o.children&&o.children[t]&&(s.children[t]=n.extend(!0,{},o.children[t]))}))}),e},encodeJSON:function(t){function r(t){var e=[],o=0,r,i,u,f=n.isArray(t);for(r in t)i=t[r],u=typeof i,u=="string"?i='"'+i+'"':u=="object"&&(i=parse(i)),e[o++]=(f?"":'"'+r+'":')+i;return(f?"[":"{")+e.join(",")+(f?"]":"}")}var i=window.JSON||{};return(i.stringify||r)(t)},decodeJSON:function(t){try{return n.parseJSON?n.parseJSON(t):window.eval("("+t+")")||{}}catch(i){return{}}},_create:function(t){var i=n.layout.state,f=t.options,r=f.stateManagement,u;if(n.extend(t,{readCookie:function(){return i.readCookie(t)},deleteCookie:function(){i.deleteCookie(t)},saveCookie:function(n,r){return i.saveCookie(t,n,r)},loadCookie:function(){return i.loadCookie(t)},loadState:function(n,r){i.loadState(t,n,r)},readState:function(n){return i.readState(t,n)},encodeJSON:i.encodeJSON,decodeJSON:i.decodeJSON}),t.state.stateData={},r.autoLoad)if(n.isPlainObject(r.autoLoad))n.isEmptyObject(r.autoLoad)||t.loadState(r.autoLoad);else if(r.enabled)if(n.isFunction(r.autoLoad)){u={};try{u=r.autoLoad(t,t.state,t.options,t.options.name||"")}catch(e){}u&&n.isPlainObject(u)&&!n.isEmptyObject(u)&&t.loadState(u)}else t.loadCookie()},_unload:function(t){var i=t.options.stateManagement;if(i.enabled&&i.autoSave)if(n.isFunction(i.autoSave))try{i.autoSave(t,t.state,t.options,t.options.name||"")}catch(r){}else t.saveCookie()}},n.layout.onCreate.push(n.layout.state._create),n.layout.onUnload.push(n.layout.state._unload))}(jQuery);
/**
 * @preserve jquery.layout.buttons 1.0
 * $Date: 2011-07-16 08:00:00 (Sat, 16 July 2011) $
 *
 * Copyright (c) 2011 
 *   Kevin Dalman (http://allpro.net)
 *
 * Dual licensed under the GPL (http://www.gnu.org/licenses/gpl.html)
 * and MIT (http://www.opensource.org/licenses/mit-license.php) licenses.
 *
 * @dependancies: UI Layout 1.3.0.rc30.1 or higher
 *
 * @support: http://groups.google.com/group/jquery-ui-layout
 *
 * Docs: [ to come ]
 * Tips: [ to come ]
 */
(function(n){n.layout&&(n.layout.plugins.buttons=!0,n.layout.defaults.autoBindCustomButtons=!1,n.layout.optionsMap.layout.push("autoBindCustomButtons"),n.layout.buttons={config:{borderPanes:"north,south,west,east"},init:function(t){var r="ui-layout-button-",u=t.options.name||"",i;n.each("toggle,open,close,pin,toggle-slide,open-slide".split(","),function(f,e){n.each(n.layout.buttons.config.borderPanes.split(","),function(f,o){n("."+r+e+"-"+o).each(function(){i=n(this).data("layoutName")||n(this).attr("layoutName");(i==undefined||i===u)&&t.bindButton(this,e,o)})})})},get:function(t,i,r,u){var f=n(i),o=t.options,e;return f.length&&n.layout.buttons.config.borderPanes.indexOf(r)>=0&&(e=o[r].buttonClass+"-"+u,f.addClass(e+" "+e+"-"+r).data("layoutName",o.name)),f},bind:function(t,i,r,u){var f=n.layout.buttons;switch(r.toLowerCase()){case"toggle":f.addToggle(t,i,u);break;case"open":f.addOpen(t,i,u);break;case"close":f.addClose(t,i,u);break;case"pin":f.addPin(t,i,u);break;case"toggle-slide":f.addToggle(t,i,u,!0);break;case"open-slide":f.addOpen(t,i,u,!0)}return t},addToggle:function(t,i,r,u){return n.layout.buttons.get(t,i,r,"toggle").click(function(n){t.toggle(r,!!u);n.stopPropagation()}),t},addOpen:function(t,i,r,u){return n.layout.buttons.get(t,i,r,"open").attr("title",t.options[r].tips.Open).click(function(n){t.open(r,!!u);n.stopPropagation()}),t},addClose:function(t,i,r){return n.layout.buttons.get(t,i,r,"close").attr("title",t.options[r].tips.Close).click(function(n){t.close(r);n.stopPropagation()}),t},addPin:function(t,i,r){var f=n.layout.buttons.get(t,i,r,"pin"),u;return f.length&&(u=t.state[r],f.click(function(i){n.layout.buttons.setPinState(t,n(this),r,u.isSliding||u.isClosed);u.isSliding||u.isClosed?t.open(r):t.close(r);i.stopPropagation()}),n.layout.buttons.setPinState(t,f,r,!u.isClosed&&!u.isSliding),u.pins.push(i)),t},setPinState:function(n,t,i,r){var f=t.attr("pin");if(!f||r!==(f=="down")){var e=n.options[i],o=e.tips,u=e.buttonClass+"-pin",s=u+"-"+i,h=u+"-up "+s+"-up",c=u+"-down "+s+"-down";t.attr("pin",r?"down":"up").attr("title",r?o.Unpin:o.Pin).removeClass(r?h:c).addClass(r?c:h)}},syncPinBtns:function(t,i,r){n.each(state[i].pins,function(u,f){n.layout.buttons.setPinState(t,n(f),i,r)})},_load:function(t){var i,r;for(n.extend(t,{bindButton:function(i,r,u){return n.layout.buttons.bind(t,i,r,u)},addToggleBtn:function(i,r,u){return n.layout.buttons.addToggle(t,i,r,u)},addOpenBtn:function(i,r,u){return n.layout.buttons.addOpen(t,i,r,u)},addCloseBtn:function(i,r){return n.layout.buttons.addClose(t,i,r)},addPinBtn:function(i,r){return n.layout.buttons.addPin(t,i,r)}}),i=0;i<4;i++)r=n.layout.buttons.config.borderPanes[i],t.state[r].pins=[];t.options.autoBindCustomButtons&&n.layout.buttons.init(t)},_unload:function(){}},n.layout.onLoad.push(n.layout.buttons._load))})(jQuery),function(n){n.layout.plugins.browserZoom=!0;n.layout.defaults.browserZoomCheckInterval=1e3;n.layout.optionsMap.layout.push("browserZoomCheckInterval");n.layout.browserZoom={_init:function(t){n.layout.browserZoom.ratio()!==!1&&n.layout.browserZoom._setTimer(t)},_setTimer:function(t){if(!t.destroyed){var i=t.options,r=t.state,u=t.hasParentLayout?5e3:Math.max(i.browserZoomCheckInterval,100);setTimeout(function(){if(!t.destroyed&&i.resizeWithWindow){var u=n.layout.browserZoom.ratio();u!==r.browserZoom&&(r.browserZoom=u,t.resizeAll());n.layout.browserZoom._setTimer(t)}},u)}},ratio:function(){function f(n,t){return(parseInt(n,10)/parseInt(t,10)*100).toFixed()}var o=window,t=screen,i=document,h=i.documentElement||i.body,r=n.layout.browser,c=r.version,e,u,s;return!r.msie||c>8?!1:t.deviceXDPI&&t.systemXDPI?f(t.deviceXDPI,t.systemXDPI):r.webkit&&(e=i.body.getBoundingClientRect)?f(e.left-e.right,i.body.offsetWidth):r.webkit&&(u=o.outerWidth)?f(u,o.innerWidth):(u=t.width)&&(s=h.clientWidth)?f(u,s):!1}};n.layout.onReady.push(n.layout.browserZoom._init)}(jQuery);
/**
 *	UI Layout Plugin: Slide-Offscreen Animation
 *
 *	Prevent panes from being 'hidden' so that an iframes/objects 
 *	does not reload/refresh when pane 'opens' again.
 *	This plug-in adds a new animation called "slideOffscreen".
 *	It is identical to the normal "slide" effect, but avoids hiding the element
 *
 *	Requires Layout 1.3.0.RC30.1 or later for Close offscreen
 *	Requires Layout 1.3.0.RC30.5 or later for Hide, initClosed & initHidden offscreen
 *
 *	Version:	1.1 - 2012-11-18
 *	Author:		Kevin Dalman (<EMAIL>)
 *	@preserve	jquery.layout.slideOffscreen-1.1.js
 */
(function(n){n.effects&&(n.layout.defaults.panes.useOffscreenClose=!1,n.layout.plugins&&(n.layout.plugins.effects.slideOffscreen=!0),n.layout.effects.slideOffscreen=n.extend(!0,{},n.layout.effects.slide),n.effects.slideOffscreen=function(t){return this.queue(function(){var y=n.effects,s=t.options,i=n(this),p=i.data("layoutEdge"),h=i.data("parentLayout").state,e=h[p].size,r=this.style,w=y.setMode(i,s.mode||"show"),c=w=="show",u=s.direction||"left",l=u=="up"||u=="down"?"top":"left",a=u=="up"||u=="left",b=n.layout.config.offscreenCSS||{},o=n.layout.config.offscreenReset,f="offscreenResetTop",v={};v[l]=(c?a?"+=":"-=":a?"-=":"+=")+e;c?(i.data(f,{top:r.top,bottom:r.bottom}),a?i.css(l,isNaN(e)?"-"+e:-e):u==="right"?i.css({left:h.container.layoutWidth,right:"auto"}):i.css({top:h.container.layoutHeight,bottom:"auto"}),l==="top"&&i.css(i.data(o)||{})):(i.data(f,{top:r.top,bottom:r.bottom}),i.data(o,{left:r.left,right:r.right}));i.show().animate(v,{queue:!1,duration:t.duration,easing:s.easing,complete:function(){i.data(f)&&i.css(i.data(f)).removeData(f);c?i.css(i.data(o)||{}).removeData(o):i.css(b);t.callback&&t.callback.apply(this,arguments);i.dequeue()}})})})})(jQuery);