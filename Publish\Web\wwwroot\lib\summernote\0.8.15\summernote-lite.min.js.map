{"version": 3, "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///webpack/bootstrap", "webpack:///external {\"root\":\"jQ<PERSON>y\",\"commonjs2\":\"jquery\",\"commonjs\":\"jquery\",\"amd\":\"jquery\"}", "webpack:///./src/js/base/renderer.js", "webpack:///(webpack)/buildin/amd-options.js", "webpack:///./src/js/base/summernote-en-US.js", "webpack:///./src/js/base/core/env.js", "webpack:///./src/js/base/core/func.js", "webpack:///./src/js/base/core/lists.js", "webpack:///./src/js/base/core/dom.js", "webpack:///./src/js/base/Context.js", "webpack:///./src/js/base/core/range.js", "webpack:///./src/js/summernote.js", "webpack:///./src/js/base/core/key.js", "webpack:///./src/js/base/editing/History.js", "webpack:///./src/js/base/editing/Style.js", "webpack:///./src/js/base/editing/Bullet.js", "webpack:///./src/js/base/editing/Typing.js", "webpack:///./src/js/base/editing/Table.js", "webpack:///./src/js/base/module/Codeview.js", "webpack:///./src/js/base/module/AutoLink.js", "webpack:///./src/js/base/settings.js", "webpack:///./src/js/base/module/Editor.js", "webpack:///./src/js/base/core/async.js", "webpack:///./src/js/base/module/Clipboard.js", "webpack:///./src/js/base/module/Dropzone.js", "webpack:///./src/js/base/module/Statusbar.js", "webpack:///./src/js/base/module/Fullscreen.js", "webpack:///./src/js/base/module/Handle.js", "webpack:///./src/js/base/module/HintPopover.js", "webpack:///./src/js/base/module/AutoSync.js", "webpack:///./src/js/base/module/AutoReplace.js", "webpack:///./src/js/base/module/Placeholder.js", "webpack:///./src/js/base/module/Buttons.js", "webpack:///./src/js/base/module/Toolbar.js", "webpack:///./src/js/base/module/LinkDialog.js", "webpack:///./src/js/base/module/LinkPopover.js", "webpack:///./src/js/base/module/ImageDialog.js", "webpack:///./src/js/base/module/ImagePopover.js", "webpack:///./src/js/base/module/TablePopover.js", "webpack:///./src/js/base/module/VideoDialog.js", "webpack:///./src/js/base/module/HelpDialog.js", "webpack:///./src/js/base/module/AirPopover.js", "webpack:///./src/js/lite/ui/TooltipUI.js", "webpack:///./src/js/lite/ui/DropdownUI.js", "webpack:///./src/js/lite/ui/ModalUI.js", "webpack:///./src/js/lite/ui.js", "webpack:///./src/js/lite/settings.js"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "a", "i", "window", "__WEBPACK_EXTERNAL_MODULE__0__", "installedModules", "__webpack_require__", "moduleId", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "<PERSON><PERSON><PERSON>", "constructor", "markup", "children", "options", "callback", "this", "render", "$parent", "$node", "$", "contents", "html", "className", "addClass", "data", "each", "k", "v", "attr", "click", "on", "$container", "find", "for<PERSON>ach", "child", "length", "append", "arguments", "Array", "isArray", "__webpack_amd_options__", "summernote", "lang", "extend", "font", "bold", "italic", "underline", "clear", "height", "strikethrough", "subscript", "superscript", "size", "sizeunit", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "resizeNone", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "useProtocol", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "cpSelect", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "output", "noSelection", "isSupportAmd", "genericFontFamilies", "validFontName", "fontName", "inArray", "toLowerCase", "userAgent", "navigator", "isMSIE", "test", "browserVersion", "matches", "exec", "parseFloat", "isEdge", "hasCodeMirror", "CodeMirror", "isSupportTouch", "MaxTouchPoints", "msMaxTouchPoints", "inputEventName", "isMac", "appVersion", "indexOf", "isFF", "is<PERSON><PERSON><PERSON>", "isWebkit", "isChrome", "<PERSON><PERSON><PERSON><PERSON>", "jqueryVersion", "fn", "j<PERSON>y", "isFontInstalled", "testFontName", "context", "document", "createElement", "getContext", "testSize", "originalWidth", "measureText", "width", "isW3CRangeSupport", "createRange", "idCounter", "eq", "itemA", "itemB", "eq2", "peq2", "propName", "ok", "fail", "self", "not", "f", "apply", "and", "fA", "fB", "item", "invoke", "obj", "method", "resetUniqueId", "uniqueId", "prefix", "id", "rect2bnd", "rect", "$document", "top", "scrollTop", "scrollLeft", "bottom", "invertObject", "inverted", "namespaceToCamel", "namespace", "split", "map", "substring", "toUpperCase", "join", "debounce", "func", "wait", "immediate", "timeout", "args", "later", "callNow", "clearTimeout", "setTimeout", "isValidUrl", "head", "array", "last", "tail", "slice", "contains", "initial", "prev", "idx", "next", "pred", "len", "all", "sum", "reduce", "memo", "from", "collection", "result", "isEmpty", "clusterBy", "aLast", "compact", "aResult", "push", "unique", "results", "NBSP_CHAR", "String", "fromCharCode", "isEditable", "node", "hasClass", "makePredByNodeName", "nodeName", "isText", "nodeType", "isVoid", "isPara", "isPre", "isLi", "isTable", "isData", "isInline", "isBodyContainer", "isList", "isHr", "isBlockquote", "isCell", "isAnchor", "isBody", "blankHTML", "env", "node<PERSON><PERSON><PERSON>", "nodeValue", "childNodes", "innerHTML", "paddingBlankHTML", "ancestor", "parentNode", "listAncestor", "ancestors", "el", "listNext", "nodes", "nextS<PERSON>ling", "insertAfter", "preceding", "parent", "insertBefore", "append<PERSON><PERSON><PERSON>", "appendChildNodes", "<PERSON><PERSON><PERSON><PERSON>", "isLeftEdgePoint", "point", "offset", "isRightEdgePoint", "isEdgePoint", "isLeftEdgeOf", "position", "isRightEdgeOf", "previousSibling", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prevPoint", "isSkipInnerOffset", "nextPoint", "isSamePoint", "pointA", "pointB", "splitNode", "isSkipPaddingBlankHTML", "isNotSplitEdgePoint", "isDiscardEmptySplits", "splitText", "childNode", "clone", "cloneNode", "splitTree", "isRemoveChild", "removeNode", "<PERSON><PERSON><PERSON><PERSON>", "isTextarea", "stripLinebreaks", "val", "replace", "ZERO_WIDTH_NBSP_CHAR", "blank", "emptyPara", "isControlSizing", "isElement", "isPurePara", "isHeading", "isBlock", "isBodyInline", "isParaInline", "isDiv", "isBR", "isSpan", "isB", "isU", "isS", "isI", "isImg", "deepestChildIsEmpty", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "isEmptyAnchor", "isClosestSibling", "nodeA", "nodeB", "withClosest<PERSON><PERSON><PERSON>", "siblings", "isLeftEdgePointOf", "isRightEdgePointOf", "isVisiblePoint", "leftNode", "rightNode", "prevPointUntil", "nextPointUntil", "isCharPoint", "ch", "char<PERSON>t", "isSpacePoint", "walkPoint", "startPoint", "endPoint", "handler", "singleChildAncestor", "last<PERSON><PERSON><PERSON>", "filter", "listPrev", "listDescendant", "descendants", "fnWalk", "current", "commonAncestor", "wrap", "wrapperName", "wrapper", "makeOffsetPath", "reverse", "fromOffsetPath", "offsets", "splitPoint", "topAncestor", "splitRoot", "container", "pivot", "createText", "text", "createTextNode", "<PERSON><PERSON><PERSON><PERSON>", "newNode", "cssText", "isNewlineOnBlock", "regexTag", "match", "endSlash", "isEndOfInlineContainer", "isBlockNode", "trim", "posFromPlaceholder", "placeholder", "$placeholder", "pos", "outerHeight", "attachEvents", "events", "keys", "detachEvents", "off", "isCustomStyleTag", "classList", "Context", "$note", "memos", "layoutInfo", "ui", "ui_template", "initialize", "createLayout", "_initialize", "hide", "destroy", "_destroy", "removeData", "removeLayout", "disabled", "isDisabled", "code", "dom", "disable", "now", "editor", "buttons", "plugins", "initializeModule", "removeModule", "removeMemo", "triggerEvent", "isActivated", "undefined", "codable", "editable", "enable", "editing", "callbacks", "trigger", "shouldInitialize", "ModuleClass", "withoutIntialize", "createInvokeHandlerAndUpdateState", "event", "createInvokeHandler", "preventDefault", "$target", "target", "closest", "splits", "hasSeparator", "moduleName", "methodName", "textRangeToPoint", "textRange", "isStart", "parentElement", "tester", "body", "createTextRange", "prevContainer", "moveToElementText", "compareEndPoints", "textRangeStart", "curTextNode", "collapse", "<PERSON><PERSON><PERSON><PERSON>", "pointTester", "duplicate", "setEndPoint", "textCount", "cont", "pointToTextRange", "textRangeInfo", "isCollapseToStart", "prevTextNodes", "collapseToStart", "info", "moveStart", "type", "isExternalAPICalled", "hasInitOptions", "langInfo", "icons", "tooltip", "note", "first", "focus", "WrappedRange", "sc", "so", "ec", "eo", "isOnEditable", "makeIsOn", "isOnList", "isOnAnchor", "isOnCell", "isOnData", "nativeRange", "w3cRange", "setStart", "setEnd", "Math", "min", "getPoints", "getStartPoint", "getEndPoint", "nativeRng", "selection", "getSelection", "rangeCount", "removeAllRanges", "addRange", "scrollIntoView", "offsetTop", "abs", "normalize", "getVisiblePoint", "isLeftToRight", "block", "hasRightNode", "hasLeftNode", "isCollapsed", "includeAncestor", "fullyContains", "leftEdgeNodes", "expand", "startAncestor", "endAncestor", "boundaryPoints", "isSameContainer", "deleteContents", "rng", "emptyParents", "wrapBodyInlineWithPara", "inlineSiblings", "concat", "para", "insertNode", "pasteHTML", "contentsContainer", "toString", "getWordRange", "findAfter", "getWordsRange", "isNotTextPoint", "getWordsMatchRange", "regex", "index", "bookmark", "path", "e", "paraBookmark", "paras", "getClientRects", "<PERSON><PERSON><PERSON><PERSON>", "createFromSelection", "bodyElement", "<PERSON><PERSON><PERSON><PERSON>", "createFromBodyElement", "createFromNode", "anchorNode", "getRangeAt", "startContainer", "startOffset", "endContainer", "endOffset", "textRangeEnd", "isTextNode", "createFromNodeBefore", "createFromNodeAfter", "createFromBookmark", "createFromParaBookmark", "KEY_MAP", "isEdit", "keyCode", "BACKSPACE", "TAB", "ENTER", "SPACE", "DELETE", "isMove", "LEFT", "UP", "RIGHT", "DOWN", "isNavigation", "HOME", "END", "PAGEUP", "PAGEDOWN", "nameFromCode", "History", "$editable", "stack", "stackOffset", "makeSnapshot", "range", "applySnapshot", "snapshot", "rewind", "recordUndo", "commit", "Style", "jQueryCSS", "$obj", "propertyNames", "propertyName", "css", "fromNode", "styleInfo", "fontSize", "parseInt", "stylePara", "styleNodes", "expandClosestSibling", "onlyPartialContains", "nodesInRange", "tails", "elem", "$cont", "queryCommandState", "queryCommandValue", "isUnordered", "lineHeight", "toFixed", "anchor", "Bullet", "insertOrderedList", "toggleList", "insertUnorderedList", "clustereds", "previousList", "findList", "wrapList", "appendToPrevious", "releaseList", "listName", "<PERSON><PERSON><PERSON><PERSON>", "diffLists", "listNode", "prevList", "nextList", "isEscapseToBody", "<PERSON><PERSON><PERSON><PERSON>", "headList", "parentItem", "newList", "findNextSiblings", "lastList", "middleList", "rootLists", "rootList", "listNodes", "Typing", "bullet", "insertTab", "tabsize", "tab", "insertParagraph", "nextPara", "blockquoteBreakingLevel", "emptyAnchors", "TableResultAction", "where", "domTable", "_startPoint", "_virtualTable", "_actionCellList", "setVirtualTablePosition", "rowIndex", "cellIndex", "baseRow", "baseCell", "isRowSpan", "isColSpan", "isVirtualCell", "objPosition", "getActionCell", "virtualTableCellObj", "resultAction", "virtualRowPosition", "virtualColPosition", "recoverCellIndex", "newCellIndex", "addCellInfoToVirtual", "row", "cell", "cellHasColspan", "colSpan", "cellHasRowspan", "rowSpan", "isThisSelectedCell", "rowPos", "colPos", "rowspanNumber", "attributes", "rp", "rowspanIndex", "adjustStartPoint", "colspanNumber", "cp", "cellspanIndex", "isSelectedCell", "getDeleteResultActionToCell", "Column", "SubtractSpanCount", "Row", "isVirtual", "AddCell", "RemoveCell", "getAddResultActionToCell", "SumSpanCount", "Ignore", "getActionList", "fixedRow", "fixedCol", "actualPosition", "canContinue", "rowPosition", "colPosition", "requestAction", "Add", "Delete", "tagName", "console", "error", "rows", "cells", "createVirtualTable", "Table", "isShift", "nextCell", "addRow", "currentTr", "trAttributes", "recoverAttributes", "actions", "idCell", "currentCell", "tdAttributes", "newTd", "removeAttr", "setAttribute", "before", "lastTrIndex", "after", "addCol", "actionIndex", "resultStr", "attrList", "specified", "deleteRow", "cellPos", "virtualPosition", "virtualTable", "hasRowspan", "nextRow", "cloneRow", "removeAttribute", "deleteCol", "createTable", "col<PERSON>ount", "rowCount", "tds", "tdHTML", "idxCol", "trs", "trHTML", "idxRow", "$table", "tableClassName", "deleteTable", "linkPattern", "version", "$editor", "<PERSON><PERSON><PERSON><PERSON>", "typing", "untab", "formatPara", "insertHorizontalRule", "commands", "sCmd", "beforeCommand", "execCommand", "afterCommand", "wrapCommand", "fontStyling", "unit", "currentStyle", "fontSizeUnit", "formatBlock", "isLimited", "getLastRange", "setLastRange", "insertText", "textNode", "onApplyCustomStyle", "onFormatBlock", "hrNode", "createLink", "linkInfo", "linkUrl", "linkText", "isNewWindow", "checkProtocol", "additionalTextLength", "isTextChanged", "onCreateLink", "defaultProtocol", "anchors", "colorInfo", "foreColor", "backColor", "insertTable", "dim", "dimension", "removeMedia", "restore<PERSON>arget", "detach", "floatMe", "toggleClass", "resize", "isDefaultPrevented", "handleKeyMap", "preventDefaultEditableShortCuts", "spell<PERSON>heck", "disable<PERSON><PERSON><PERSON>", "airMode", "outerWidth", "maxHeight", "minHeight", "keyMap", "metaKey", "ctrl<PERSON>ey", "altKey", "shift<PERSON>ey", "keyName", "eventName", "tabDisable", "pad", "maxText<PERSON>ength", "saveRange", "thenCollapse", "restoreRange", "save<PERSON>arget", "<PERSON><PERSON><PERSON><PERSON>", "styleFromNode", "isPreventTrigger", "normalizeContent", "tabSize", "insertImage", "src", "param", "Deferred", "deferred", "$img", "one", "resolve", "reject", "display", "appendTo", "promise", "then", "$image", "show", "insertImagesAsDataURL", "files", "file", "filename", "maximumImageFileSize", "FileReader", "onload", "dataURL", "onerror", "err", "readAsDataURL", "readFileAsDataURL", "insertImages<PERSON><PERSON><PERSON><PERSON><PERSON>", "onImageUpload", "getSelectedText", "currentRange", "spans", "firstSpan", "noteStatusOutput", "getLinkInfo", "$anchor", "resizeTo", "bKeepRatio", "imageSize", "newRatio", "y", "x", "ratio", "hasFocus", "is", "empty", "pasteByEvent", "clipboardData", "originalEvent", "items", "kind", "getAsFile", "getData", "$eventListener", "documentEventHandlers", "$dropzone", "prependTo", "disableDragAndDrop", "onDrop", "attachDragAndDropEvent", "$dropzoneMessage", "onDragenter", "isCodeview", "hasEditorSize", "add", "onDragleave", "removeClass", "dataTransfer", "types", "content", "substr", "$codable", "sync", "save", "toggle", "deactivate", "activate", "purify", "codeviewFilter", "codeviewFilterRegex", "codeviewIframeFilter", "whitelist", "codeviewIframeWhitelistSrc", "codeviewIframeWhitelistSrcBase", "tag", "RegExp", "prettifyHtml", "cmEditor", "fromTextArea", "codemirror", "tern", "server", "TernServer", "ternServer", "cm", "updateArgHints", "getValue", "setSize", "toTextArea", "isChange", "$statusbar", "statusbar", "disableResizeEditor", "stopPropagation", "editableTop", "onMouseMove", "clientY", "minheight", "max", "$toolbar", "toolbar", "$window", "$scrollbar", "onResize", "h", "setsize", "isFullscreen", "$editingArea", "editingArea", "we", "update", "$handle", "disableResizeImage", "posStart", "clientX", "isImage", "$selection", "w", "origImageObj", "Image", "sizingText", "hint", "direction", "hintDirection", "hints", "handleKeyup", "handleKeydown", "lastWordRange", "matching<PERSON><PERSON>", "$popover", "popover", "hideArrow", "$content", "currentTarget", "selectItem", "$item", "innerHeight", "moveDown", "$current", "$next", "$nextGroup", "moveUp", "$prev", "$prevGroup", "nodeFromItem", "rangeCompute", "hintSelect", "createItemTemplates", "hintIdx", "template", "searchKeyword", "keyword", "search", "createGroup", "$group", "wordRange", "hintMode", "bnd", "containerOffset", "urlText", "linkTargetBlank", "PERIOD", "COMMA", "SEMICOLON", "SLASH", "previousKeydownCode", "lastWord", "j<PERSON><PERSON><PERSON>", "Node", "inheritPlaceholder", "isShow", "invertedKeyMap", "representShortcut", "editor<PERSON><PERSON><PERSON>", "button", "addToolbarButtons", "addImagePopoverButtons", "addLinkPopoverButtons", "addTablePopoverButtons", "fontInstalledMap", "fontNamesIgnoreCheck", "isFontDeservedToAdd", "colorPalette", "buttonGroup", "icon", "$button", "$recentColor", "colorButton", "dropdownButtonContents", "dropdown", "$dropdown", "$holder", "palette", "colors", "colorsName", "customColors", "change", "$chip", "$picker", "$palette", "prepend", "$color", "$currentButton", "magic", "styleTags", "title", "styleIdx", "styleLen", "eraser", "addDefaultFonts", "fontname", "fontNames", "dropdownCheck", "checkClassName", "menuCheck", "fontSizes", "fontSizeUnits", "unorderedlist", "orderedlist", "justifyLeft", "alignLeft", "justifyCenter", "alignCenter", "justifyRight", "alignRight", "justifyFull", "alignJustify", "textHeight", "lineHeights", "insertTableMaxSize", "col", "mousedown", "tableMoveHandler", "picture", "minus", "arrowsAlt", "question", "rollback", "trash", "rowAbove", "rowBelow", "colBefore", "colAfter", "rowRemove", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "build", "groups", "groupIdx", "groupLen", "group", "groupName", "btn", "updateCurrentStyle", "updateBtnStates", "isChecked", "infos", "selector", "toggleBtnActive", "$dimensionDisplay", "$catcher", "$highlighted", "$unhighlighted", "posOffset", "offsetX", "pos<PERSON><PERSON><PERSON>", "pageX", "pageY", "offsetY", "ceil", "isFollowing", "followScroll", "toolbarContainer", "changeContainer", "followingToolbar", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "toolbarHeight", "statusbarHeight", "otherBarHeight", "otherStaticBar", "currentOffset", "editorOffsetTop", "activateOffset", "deactivateOffsetBottom", "zIndex", "marginTop", "updateFullscreen", "updateCodeview", "isIncludeCodeview", "$btn", "toggleBtn", "$body", "dialogsInBody", "disableLinkTarget", "checkbox", "checked", "footer", "$dialog", "dialog", "fade", "dialogsFade", "hideDialog", "bindEnter<PERSON>ey", "$input", "toggleLinkBtn", "$linkBtn", "$linkText", "$linkUrl", "showLinkDialog", "$openInNewWindow", "$useProtocol", "onDialogShown", "isNewWindowChecked", "prop", "useProtocolChecked", "onDialogHidden", "state", "showDialog", "href", "imageLimitation", "floor", "log", "readableSize", "pow", "showImageDialog", "onImageLinkInsert", "$imageInput", "$imageUrl", "$imageBtn", "replaceWith", "popatmouse", "createVideoNode", "ytRegExpForStart", "ytMatch", "igMatch", "vMatch", "vimMatch", "dmMatch", "youkuMatch", "qqMatch", "qqMatch2", "mp4Match", "oggMatch", "webmMatch", "fbMatch", "$video", "youtubeId", "start", "ytMatchForStart", "vid", "encodeURIComponent", "showVideoDialog", "$videoUrl", "$videoBtn", "createShortcutList", "command", "$row", "showHelpDialog", "hidable", "air", "toolbarPosition", "tabDisabled", "styleWithSpan", "textareaAutoSync", "onBeforeCommand", "onBlur", "onBlurCodeview", "onChange", "onChangeCodeview", "onEnter", "onFocus", "onImageUploadError", "onInit", "onKeydown", "onKeyup", "onMousedown", "onMouseup", "onPaste", "onScroll", "htmlMode", "lineNumbers", "pc", "mac", "TooltipUI", "placement", "$tooltip", "showCallback", "hide<PERSON>allback", "toggleCallback", "targetOffset", "nodeWidth", "nodeHeight", "tooltipWidth", "tooltipHeight", "DropdownUI", "setEvent", "stopImmediatePropagation", "windowWidth", "targetMarginRight", "isOpened", "ModalUI", "$modal", "$backdrop", "which", "renderer", "airEditor", "airEditable", "$temp", "$a", "itemClick", "caret", "dropdownButton", "opt", "dropdownCheckButton", "paragraphDropdownButton", "tableDropdownButton", "mousemove", "rowSize", "colSize", "colorName", "colorDropdownButton", "currentClick", "foreinput", "getElementById", "backinput", "videoDialog", "imageDialog", "linkDialog", "iconClassName", "editorOptions", "isEnable", "isActive", "check", "$dom", "get<PERSON>opoverContent", "getDialogBody", "interface"], "mappings": ";CAAA,SAA2CA,EAAMC,GAChD,GAAsB,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,EAAQG,QAAQ,gBAC7B,GAAqB,mBAAXC,QAAyBA,OAAOC,IAC9CD,OAAO,CAAC,UAAWJ,OACf,CACJ,IAAIM,EAAuB,iBAAZL,QAAuBD,EAAQG,QAAQ,WAAaH,EAAQD,EAAa,QACxF,IAAI,IAAIQ,KAAKD,GAAuB,iBAAZL,QAAuBA,QAAUF,GAAMQ,GAAKD,EAAEC,IAPxE,CASGC,QAAQ,SAASC,GACpB,O,YCTE,IAAIC,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUX,QAGnC,IAAIC,EAASQ,EAAiBE,GAAY,CACzCL,EACAM,GAAG,EACHZ,QAAS,IAUV,OANAa,EAAQF,GAAUG,KAAKb,EAAOD,QAASC,EAAQA,EAAOD,QAASU,GAG/DT,EAAOW,GAAI,EAGJX,EAAOD,QA0Df,OArDAU,EAAoBK,EAAIF,EAGxBH,EAAoBM,EAAIP,EAGxBC,EAAoBO,EAAI,SAASjB,EAASkB,EAAMC,GAC3CT,EAAoBU,EAAEpB,EAASkB,IAClCG,OAAOC,eAAetB,EAASkB,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhET,EAAoBe,EAAI,SAASzB,GACX,oBAAX0B,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAetB,EAAS0B,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAetB,EAAS,aAAc,CAAE4B,OAAO,KAQvDlB,EAAoBmB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQlB,EAAoBkB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFAvB,EAAoBe,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOlB,EAAoBO,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRtB,EAAoB0B,EAAI,SAASnC,GAChC,IAAIkB,EAASlB,GAAUA,EAAO8B,WAC7B,WAAwB,OAAO9B,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAS,EAAoBO,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRT,EAAoBU,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG5B,EAAoB+B,EAAI,GAIjB/B,EAAoBA,EAAoBgC,EAAI,I,kBClFrDzC,EAAOD,QAAUQ,G,+BCAjB,oBAEA,MAAMmC,EACJC,YAAYC,EAAQC,EAAUC,EAASC,GACrCC,KAAKJ,OAASA,EACdI,KAAKH,SAAWA,EAChBG,KAAKF,QAAUA,EACfE,KAAKD,SAAWA,EAGlBE,OAAOC,GACL,MAAMC,EAAQC,IAAEJ,KAAKJ,QAoBrB,GAlBII,KAAKF,SAAWE,KAAKF,QAAQO,UAC/BF,EAAMG,KAAKN,KAAKF,QAAQO,UAGtBL,KAAKF,SAAWE,KAAKF,QAAQS,WAC/BJ,EAAMK,SAASR,KAAKF,QAAQS,WAG1BP,KAAKF,SAAWE,KAAKF,QAAQW,MAC/BL,IAAEM,KAAKV,KAAKF,QAAQW,KAAM,CAACE,EAAGC,KAC5BT,EAAMU,KAAK,QAAUF,EAAGC,KAIxBZ,KAAKF,SAAWE,KAAKF,QAAQgB,OAC/BX,EAAMY,GAAG,QAASf,KAAKF,QAAQgB,OAG7Bd,KAAKH,SAAU,CACjB,MAAMmB,EAAab,EAAMc,KAAK,4BAC9BjB,KAAKH,SAASqB,QAASC,IACrBA,EAAMlB,OAAOe,EAAWI,OAASJ,EAAab,KAgBlD,OAZIH,KAAKD,UACPC,KAAKD,SAASI,EAAOH,KAAKF,SAGxBE,KAAKF,SAAWE,KAAKF,QAAQC,UAC/BC,KAAKF,QAAQC,SAASI,GAGpBD,GACFA,EAAQmB,OAAOlB,GAGVA,GAII,KACbnB,OAAQ,CAACY,EAAQG,IACR,WACL,MAAMD,EAAkC,iBAAjBwB,UAAU,GAAkBA,UAAU,GAAKA,UAAU,GAC5E,IAAIzB,EAAW0B,MAAMC,QAAQF,UAAU,IAAMA,UAAU,GAAK,GAI5D,OAHIxB,GAAWA,EAAQD,WACrBA,EAAWC,EAAQD,UAEd,IAAIH,EAASE,EAAQC,EAAUC,EAASC,M,iBC9DrD,YACA/C,EAAOD,QAAU0E,I,kECCjBrB,IAAEsB,WAAatB,IAAEsB,YAAc,CAC7BC,KAAM,IAGRvB,IAAEwB,OAAOxB,IAAEsB,WAAWC,KAAM,CAC1B,QAAS,CACPE,KAAM,CACJC,KAAM,OACNC,OAAQ,SACRC,UAAW,YACXC,MAAO,oBACPC,OAAQ,cACRjE,KAAM,cACNkE,cAAe,gBACfC,UAAW,YACXC,YAAa,cACbC,KAAM,YACNC,SAAU,kBAEZC,MAAO,CACLA,MAAO,UACPC,OAAQ,eACRC,WAAY,cACZC,WAAY,cACZC,cAAe,iBACfC,WAAY,gBACZC,UAAW,aACXC,WAAY,cACZC,UAAW,eACXC,aAAc,iBACdC,YAAa,gBACbC,eAAgB,mBAChBC,UAAW,cACXC,cAAe,0BACfC,UAAW,qBACXC,gBAAiB,oBACjBC,gBAAiB,oBACjBC,qBAAsB,8BACtBC,IAAK,YACLC,OAAQ,eACRC,SAAU,YAEZC,MAAO,CACLA,MAAO,QACPC,UAAW,aACXrB,OAAQ,eACRiB,IAAK,YACLK,UAAW,2DAEbC,KAAM,CACJA,KAAM,OACNvB,OAAQ,cACRwB,OAAQ,SACRC,KAAM,OACNC,cAAe,kBACfT,IAAK,mCACLU,gBAAiB,qBACjBC,YAAa,wBAEfC,MAAO,CACLA,MAAO,QACPC,YAAa,gBACbC,YAAa,gBACbC,WAAY,kBACZC,YAAa,mBACbC,OAAQ,aACRC,OAAQ,gBACRC,SAAU,gBAEZC,GAAI,CACFrC,OAAQ,0BAEVsC,MAAO,CACLA,MAAO,QACPvF,EAAG,SACHwF,WAAY,QACZC,IAAK,OACLC,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,YAENC,MAAO,CACLC,UAAW,iBACXC,QAAS,gBAEX5F,QAAS,CACP6F,KAAM,OACNC,WAAY,cACZC,SAAU,aAEZC,UAAW,CACTA,UAAW,YACXC,QAAS,UACTC,OAAQ,SACRC,KAAM,aACNC,OAAQ,eACRC,MAAO,cACPC,QAAS,gBAEXC,MAAO,CACLC,OAAQ,eACRC,KAAM,aACNC,WAAY,mBACZC,WAAY,aACZC,YAAa,cACbC,eAAgB,kBAChBC,MAAO,QACPC,eAAgB,mBAChBC,SAAU,UAEZC,SAAU,CACRC,UAAW,qBACXC,MAAO,QACPC,eAAgB,kBAChBC,OAAQ,SACRC,oBAAqB,uBACrBC,cAAe,iBACfC,UAAW,cAEb3B,KAAM,CACJ,gBAAmB,mBACnB,KAAQ,0BACR,KAAQ,0BACR,IAAO,MACP,MAAS,QACT,KAAQ,mBACR,OAAU,qBACV,UAAa,wBACb,cAAiB,4BACjB,aAAgB,gBAChB,YAAe,iBACf,cAAiB,mBACjB,aAAgB,kBAChB,YAAe,iBACf,oBAAuB,wBACvB,kBAAqB,sBACrB,QAAW,+BACX,OAAU,8BACV,WAAc,sDACd,SAAY,sCACZ,SAAY,sCACZ,SAAY,sCACZ,SAAY,sCACZ,SAAY,sCACZ,SAAY,sCACZ,qBAAwB,yBACxB,kBAAmB,oBAErB4B,QAAS,CACPC,KAAM,OACNC,KAAM,QAERC,YAAa,CACXA,YAAa,qBACbC,OAAQ,6BAEVC,OAAQ,CACNC,YAAa,yBCjKnB,MAAMC,EAAiC,mBAAX5K,QAAyBA,KAQ/C6K,EAAsB,CAAC,aAAc,QAAS,YAAa,UAAW,WAE5E,SAASC,EAAcC,GACrB,OAAoE,IAA5D7H,IAAE8H,QAAQD,EAASE,cAAeJ,GAAgC,IAAGE,KAAcA,EAoB7F,MAAMG,EAAYC,UAAUD,UACtBE,EAAS,gBAAgBC,KAAKH,GACpC,IAAII,EACJ,GAAIF,EAAQ,CACV,IAAIG,EAAU,mBAAmBC,KAAKN,GAClCK,IACFD,EAAiBG,WAAWF,EAAQ,KAEtCA,EAAU,sCAAsCC,KAAKN,GACjDK,IACFD,EAAiBG,WAAWF,EAAQ,KAIxC,MAAMG,EAAS,YAAYL,KAAKH,GAEhC,IAAIS,IAAkBvL,OAAOwL,WAE7B,MAAMC,EACF,iBAAkBzL,QAClB+K,UAAUW,eAAiB,GAC3BX,UAAUY,iBAAmB,EAI3BC,EAAkBZ,EAAU,8DAAgE,QAUnF,OACba,MAAOd,UAAUe,WAAWC,QAAQ,QAAU,EAC9Cf,SACAM,SACAU,MAAOV,GAAU,WAAWL,KAAKH,GACjCmB,UAAW,aAAahB,KAAKH,GAC7BoB,UAAWZ,GAAU,UAAUL,KAAKH,GACpCqB,UAAWb,GAAU,UAAUL,KAAKH,GACpCsB,UAAWd,GAAU,UAAUL,KAAKH,KAAgB,UAAUG,KAAKH,GACnEI,iBACAmB,cAAehB,WAAWvI,IAAEwJ,GAAGC,QAC/B/B,eACAiB,iBACAF,gBACAiB,gBAlEF,SAAyB7B,GACvB,MAAM8B,EAA4B,kBAAb9B,EAA+B,cAAgB,gBAIpE,IACI+B,EADSC,SAASC,cAAc,UACfC,WAAW,MAEhCH,EAAQnI,KAAOuI,UAAkBL,EAAe,IAChD,MAAMM,EAAgBL,EAAQM,YAPb,mBAOmCC,MAKpD,OAHAP,EAAQnI,KAAOuI,SAAiBpC,EAAcC,GAAY,MAAQ8B,EAAe,IAG1EM,IAFOL,EAAQM,YAVL,mBAU2BC,OAuD5CC,oBAAqBP,SAASQ,YAC9BvB,iBACAnB,sBACAC,iBC7BF,IAAI0C,EAAY,EA8GD,OACbC,GA7JF,SAAYC,GACV,OAAO,SAASC,GACd,OAAOD,IAAUC,IA4JnBC,IAxJF,SAAaF,EAAOC,GAClB,OAAOD,IAAUC,GAwJjBE,KArJF,SAAcC,GACZ,OAAO,SAASJ,EAAOC,GACrB,OAAOD,EAAMI,KAAcH,EAAMG,KAoJnCC,GAhJF,WACE,OAAO,GAgJPC,KA7IF,WACE,OAAO,GA6IPC,KA9HF,SAAc/N,GACZ,OAAOA,GA8HPgO,IA3IF,SAAaC,GACX,OAAO,WACL,OAAQA,EAAEC,MAAMD,EAAG/J,aA0IrBiK,IAtIF,SAAaC,EAAIC,GACf,OAAO,SAASC,GACd,OAAOF,EAAGE,IAASD,EAAGC,KAqIxBC,OA7HF,SAAgBC,EAAKC,GACnB,OAAO,WACL,OAAOD,EAAIC,GAAQP,MAAMM,EAAKtK,aA4HhCwK,cAlHF,WACEpB,EAAY,GAkHZqB,SA1GF,SAAkBC,GAChB,MAAMC,IAAOvB,EAAY,GACzB,OAAOsB,EAASA,EAASC,EAAKA,GAyG9BC,SAzFF,SAAkBC,GAChB,MAAMC,EAAYhM,IAAE6J,UACpB,MAAO,CACLoC,IAAKF,EAAKE,IAAMD,EAAUE,YAC1BrG,KAAMkG,EAAKlG,KAAOmG,EAAUG,aAC5BhC,MAAO4B,EAAKhG,MAAQgG,EAAKlG,KACzB/D,OAAQiK,EAAKK,OAASL,EAAKE,MAoF7BI,aA3EF,SAAsBb,GACpB,MAAMc,EAAW,GACjB,IAAK,MAAMzN,KAAO2M,EACZA,EAAIrM,eAAeN,KACrByN,EAASd,EAAI3M,IAAQA,GAGzB,OAAOyN,GAqEPC,iBA7DF,SAA0BC,EAAWZ,GAEnC,OADAA,EAASA,GAAU,IACHY,EAAUC,MAAM,KAAKC,KAAI,SAAS7O,GAChD,OAAOA,EAAK8O,UAAU,EAAG,GAAGC,cAAgB/O,EAAK8O,UAAU,MAC1DE,KAAK,KA0DRC,SA7CF,SAAkBC,EAAMC,EAAMC,GAC5B,IAAIC,EACJ,OAAO,WACL,MAAMtD,EAAUhK,KACVuN,EAAOjM,UACPkM,EAAQ,KACZF,EAAU,KACLD,GACHF,EAAK7B,MAAMtB,EAASuD,IAGlBE,EAAUJ,IAAcC,EAC9BI,aAAaJ,GACbA,EAAUK,WAAWH,EAAOJ,GACxBK,GACFN,EAAK7B,MAAMtB,EAASuD,KA+BxBK,WArBF,SAAoBlK,GAElB,MADmB,6EACD6E,KAAK7E,KC5JzB,SAASmK,EAAKC,GACZ,OAAOA,EAAM,GAQf,SAASC,EAAKD,GACZ,OAAOA,EAAMA,EAAM1M,OAAS,GAiB9B,SAAS4M,EAAKF,GACZ,OAAOA,EAAMG,MAAM,GA8BrB,SAASC,EAASJ,EAAOpC,GACvB,GAAIoC,GAASA,EAAM1M,QAAUsK,EAAM,CACjC,GAAIoC,EAAMzE,QACR,OAAgC,IAAzByE,EAAMzE,QAAQqC,GAChB,GAAIoC,EAAMI,SAEf,OAAOJ,EAAMI,SAASxC,GAG1B,OAAO,EAyHM,OACbmC,KADa,EAEbE,KAFa,EAGbI,QA7KF,SAAiBL,GACf,OAAOA,EAAMG,MAAM,EAAGH,EAAM1M,OAAS,IA6KrC4M,OACAI,KArBF,SAAcN,EAAOpC,GACnB,GAAIoC,GAASA,EAAM1M,QAAUsK,EAAM,CACjC,MAAM2C,EAAMP,EAAMzE,QAAQqC,GAC1B,OAAgB,IAAT2C,EAAa,KAAOP,EAAMO,EAAM,GAEzC,OAAO,MAiBPC,KAlCF,SAAcR,EAAOpC,GACnB,GAAIoC,GAASA,EAAM1M,QAAUsK,EAAM,CACjC,MAAM2C,EAAMP,EAAMzE,QAAQqC,GAC1B,OAAgB,IAAT2C,EAAa,KAAOP,EAAMO,EAAM,GAEzC,OAAO,MA8BPpN,KAjKF,SAAc6M,EAAOS,GACnB,IAAK,IAAIF,EAAM,EAAGG,EAAMV,EAAM1M,OAAQiN,EAAMG,EAAKH,IAAO,CACtD,MAAM3C,EAAOoC,EAAMO,GACnB,GAAIE,EAAK7C,GACP,OAAOA,IA8JXwC,WACAO,IAvJF,SAAaX,EAAOS,GAClB,IAAK,IAAIF,EAAM,EAAGG,EAAMV,EAAM1M,OAAQiN,EAAMG,EAAKH,IAC/C,IAAKE,EAAKT,EAAMO,IACd,OAAO,EAGX,OAAO,GAkJPK,IA1HF,SAAaZ,EAAOlE,GAElB,OADAA,EAAKA,GAAMuD,EAAKhC,KACT2C,EAAMa,QAAO,SAASC,EAAMhO,GACjC,OAAOgO,EAAOhF,EAAGhJ,KAChB,IAuHHiO,KAhHF,SAAcC,GACZ,MAAMC,EAAS,GACT3N,EAAS0N,EAAW1N,OAC1B,IAAIiN,GAAO,EACX,OAASA,EAAMjN,GACb2N,EAAOV,GAAOS,EAAWT,GAE3B,OAAOU,GA0GPC,QApGF,SAAiBlB,GACf,OAAQA,IAAUA,EAAM1M,QAoGxB6N,UA1FF,SAAmBnB,EAAOlE,GACxB,OAAKkE,EAAM1M,OACG4M,EAAKF,GACNa,QAAO,SAASC,EAAMhO,GACjC,MAAMsO,EAAQnB,EAAKa,GAMnB,OALIhF,EAAGmE,EAAKmB,GAAQtO,GAClBsO,EAAMA,EAAM9N,QAAUR,EAEtBgO,EAAKA,EAAKxN,QAAU,CAACR,GAEhBgO,IACN,CAAC,CAACf,EAAKC,MAVkB,IA0F5BqB,QAvEF,SAAiBrB,GACf,MAAMsB,EAAU,GAChB,IAAK,IAAIf,EAAM,EAAGG,EAAMV,EAAM1M,OAAQiN,EAAMG,EAAKH,IAC3CP,EAAMO,IAAQe,EAAQC,KAAKvB,EAAMO,IAEvC,OAAOe,GAmEPE,OA3DF,SAAgBxB,GACd,MAAMyB,EAAU,GAEhB,IAAK,IAAIlB,EAAM,EAAGG,EAAMV,EAAM1M,OAAQiN,EAAMG,EAAKH,IAC1CH,EAASqB,EAASzB,EAAMO,KAC3BkB,EAAQF,KAAKvB,EAAMO,IAIvB,OAAOkB,IC3JT,MAAMC,EAAYC,OAAOC,aAAa,KAWtC,SAASC,EAAWC,GAClB,OAAOA,GAAQxP,IAAEwP,GAAMC,SAAS,iBAuBlC,SAASC,EAAmBC,GAE1B,OADAA,EAAWA,EAAS/C,cACb,SAAS4C,GACd,OAAOA,GAAQA,EAAKG,SAAS/C,gBAAkB+C,GAYnD,SAASC,EAAOJ,GACd,OAAOA,GAA0B,IAAlBA,EAAKK,SAmBtB,SAASC,EAAON,GACd,OAAOA,GAAQ,2DAA2DrH,KAAKqH,EAAKG,SAAS/C,eAG/F,SAASmD,EAAOP,GACd,OAAID,EAAWC,KAKRA,GAAQ,sBAAsBrH,KAAKqH,EAAKG,SAAS/C,gBAO1D,MAAMoD,EAAQN,EAAmB,OAE3BO,EAAOP,EAAmB,MAMhC,MAAMQ,EAAUR,EAAmB,SAE7BS,EAAST,EAAmB,QAElC,SAASU,EAASZ,GAChB,QAAQa,EAAgBb,IAChBc,EAAOd,IACPe,EAAKf,IACLO,EAAOP,IACPU,EAAQV,IACRgB,EAAahB,IACbW,EAAOX,IAGjB,SAASc,EAAOd,GACd,OAAOA,GAAQ,UAAUrH,KAAKqH,EAAKG,SAAS/C,eAG9C,MAAM2D,EAAOb,EAAmB,MAEhC,SAASe,EAAOjB,GACd,OAAOA,GAAQ,UAAUrH,KAAKqH,EAAKG,SAAS/C,eAG9C,MAAM4D,EAAed,EAAmB,cAExC,SAASW,EAAgBb,GACvB,OAAOiB,EAAOjB,IAASgB,EAAahB,IAASD,EAAWC,GAG1D,MAAMkB,EAAWhB,EAAmB,KAUpC,MAAMiB,EAASjB,EAAmB,QAwClC,MAAMkB,EAAYC,EAAI3I,QAAU2I,EAAIzI,eAAiB,GAAK,SAAW,OASrE,SAAS0I,EAAWtB,GAClB,OAAII,EAAOJ,GACFA,EAAKuB,UAAU/P,OAGpBwO,EACKA,EAAKwB,WAAWhQ,OAGlB,EAuBT,SAAS4N,EAAQY,GACf,MAAMpB,EAAM0C,EAAWtB,GAEvB,OAAY,IAARpB,KAEQwB,EAAOJ,IAAiB,IAARpB,GAAaoB,EAAKyB,YAAcL,MAGjDxL,EAAMiJ,IAAImB,EAAKwB,WAAYpB,IAA8B,KAAnBJ,EAAKyB,YAWxD,SAASC,EAAiB1B,GACnBM,EAAON,IAAUsB,EAAWtB,KAC/BA,EAAKyB,UAAYL,GAUrB,SAASO,EAAS3B,EAAMrB,GACtB,KAAOqB,GAAM,CACX,GAAIrB,EAAKqB,GAAS,OAAOA,EACzB,GAAID,EAAWC,GAAS,MAExBA,EAAOA,EAAK4B,WAEd,OAAO,KA4BT,SAASC,EAAa7B,EAAMrB,GAC1BA,EAAOA,GAAQpB,EAAKjC,KAEpB,MAAMwG,EAAY,GAQlB,OAPAH,EAAS3B,GAAM,SAAS+B,GAKtB,OAJKhC,EAAWgC,IACdD,EAAUrC,KAAKsC,GAGVpD,EAAKoD,MAEPD,EAiDT,SAASE,EAAShC,EAAMrB,GACtBA,EAAOA,GAAQpB,EAAKjC,KAEpB,MAAM2G,EAAQ,GACd,KAAOjC,IACDrB,EAAKqB,IACTiC,EAAMxC,KAAKO,GACXA,EAAOA,EAAKkC,YAEd,OAAOD,EAiDT,SAASE,EAAYnC,EAAMoC,GACzB,MAAM1D,EAAO0D,EAAUF,YACvB,IAAIG,EAASD,EAAUR,WAMvB,OALIlD,EACF2D,EAAOC,aAAatC,EAAMtB,GAE1B2D,EAAOE,YAAYvC,GAEdA,EAST,SAASwC,EAAiBxC,EAAMyC,GAI9B,OAHAjS,IAAEM,KAAK2R,GAAQ,SAAShE,EAAKlN,GAC3ByO,EAAKuC,YAAYhR,MAEZyO,EAST,SAAS0C,EAAgBC,GACvB,OAAwB,IAAjBA,EAAMC,OASf,SAASC,EAAiBF,GACxB,OAAOA,EAAMC,SAAWtB,EAAWqB,EAAM3C,MAS3C,SAAS8C,EAAYH,GACnB,OAAOD,EAAgBC,IAAUE,EAAiBF,GAUpD,SAASI,EAAa/C,EAAM2B,GAC1B,KAAO3B,GAAQA,IAAS2B,GAAU,CAChC,GAAuB,IAAnBqB,GAAShD,GACX,OAAO,EAETA,EAAOA,EAAK4B,WAGd,OAAO,EAUT,SAASqB,GAAcjD,EAAM2B,GAC3B,IAAKA,EACH,OAAO,EAET,KAAO3B,GAAQA,IAAS2B,GAAU,CAChC,GAAIqB,GAAShD,KAAUsB,EAAWtB,EAAK4B,YAAc,EACnD,OAAO,EAET5B,EAAOA,EAAK4B,WAGd,OAAO,EA4BT,SAASoB,GAAShD,GAChB,IAAI4C,EAAS,EACb,KAAQ5C,EAAOA,EAAKkD,iBAClBN,GAAU,EAEZ,OAAOA,EAGT,SAASO,GAAYnD,GACnB,SAAUA,GAAQA,EAAKwB,YAAcxB,EAAKwB,WAAWhQ,QAUvD,SAAS4R,GAAUT,EAAOU,GACxB,IAAIrD,EACA4C,EAEJ,GAAqB,IAAjBD,EAAMC,OAAc,CACtB,GAAI7C,EAAW4C,EAAM3C,MACnB,OAAO,KAGTA,EAAO2C,EAAM3C,KAAK4B,WAClBgB,EAASI,GAASL,EAAM3C,WACfmD,GAAYR,EAAM3C,OAC3BA,EAAO2C,EAAM3C,KAAKwB,WAAWmB,EAAMC,OAAS,GAC5CA,EAAStB,EAAWtB,KAEpBA,EAAO2C,EAAM3C,KACb4C,EAASS,EAAoB,EAAIV,EAAMC,OAAS,GAGlD,MAAO,CACL5C,KAAMA,EACN4C,OAAQA,GAWZ,SAASU,GAAUX,EAAOU,GACxB,IAAIrD,EAAM4C,EAEV,GAAIxD,EAAQuD,EAAM3C,MAChB,OAAO,KAGT,GAAIsB,EAAWqB,EAAM3C,QAAU2C,EAAMC,OAAQ,CAC3C,GAAI7C,EAAW4C,EAAM3C,MACnB,OAAO,KAGTA,EAAO2C,EAAM3C,KAAK4B,WAClBgB,EAASI,GAASL,EAAM3C,MAAQ,OAC3B,GAAImD,GAAYR,EAAM3C,OAG3B,GAFAA,EAAO2C,EAAM3C,KAAKwB,WAAWmB,EAAMC,QACnCA,EAAS,EACLxD,EAAQY,GACV,OAAO,UAMT,GAHAA,EAAO2C,EAAM3C,KACb4C,EAASS,EAAoB/B,EAAWqB,EAAM3C,MAAQ2C,EAAMC,OAAS,EAEjExD,EAAQY,GACV,OAAO,KAIX,MAAO,CACLA,KAAMA,EACN4C,OAAQA,GAWZ,SAASW,GAAYC,EAAQC,GAC3B,OAAOD,EAAOxD,OAASyD,EAAOzD,MAAQwD,EAAOZ,SAAWa,EAAOb,OAiKjE,SAASc,GAAUf,EAAOzS,GACxB,IAAIyT,EAAyBzT,GAAWA,EAAQyT,uBAChD,MAAMC,EAAsB1T,GAAWA,EAAQ0T,oBACzCC,EAAuB3T,GAAWA,EAAQ2T,qBAOhD,GALIA,IACFF,GAAyB,GAIvBb,EAAYH,KAAWvC,EAAOuC,EAAM3C,OAAS4D,GAAsB,CACrE,GAAIlB,EAAgBC,GAClB,OAAOA,EAAM3C,KACR,GAAI6C,EAAiBF,GAC1B,OAAOA,EAAM3C,KAAKkC,YAKtB,GAAI9B,EAAOuC,EAAM3C,MACf,OAAO2C,EAAM3C,KAAK8D,UAAUnB,EAAMC,QAC7B,CACL,MAAMmB,EAAYpB,EAAM3C,KAAKwB,WAAWmB,EAAMC,QACxCoB,EAAQ7B,EAAYQ,EAAM3C,KAAKiE,WAAU,GAAQtB,EAAM3C,MAQ7D,OAPAwC,EAAiBwB,EAAOhC,EAAS+B,IAE5BJ,IACHjC,EAAiBiB,EAAM3C,MACvB0B,EAAiBsC,IAGfH,IACEzE,EAAQuD,EAAM3C,OAChBjM,GAAO4O,EAAM3C,MAEXZ,EAAQ4E,KACVjQ,GAAOiQ,GACArB,EAAM3C,KAAKkC,aAIf8B,GAgBX,SAASE,GAAUjX,EAAM0V,EAAOzS,GAE9B,MAAM4R,EAAYD,EAAac,EAAM3C,KAAMzC,EAAKxC,GAAG9N,IAEnD,OAAK6U,EAAUtQ,OAEiB,IAArBsQ,EAAUtQ,OACZkS,GAAUf,EAAOzS,GAGnB4R,EAAU/C,QAAO,SAASiB,EAAMqC,GAKrC,OAJIrC,IAAS2C,EAAM3C,OACjBA,EAAO0D,GAAUf,EAAOzS,IAGnBwT,GAAU,CACf1D,KAAMqC,EACNO,OAAQ5C,EAAOgD,GAAShD,GAAQsB,EAAWe,IAC1CnS,MAbI,KA0DX,SAASd,GAAO+Q,GACd,OAAO9F,SAASC,cAAc6F,GAehC,SAASpM,GAAOiM,EAAMmE,GACpB,IAAKnE,IAASA,EAAK4B,WAAc,OACjC,GAAI5B,EAAKoE,WAAc,OAAOpE,EAAKoE,WAAWD,GAE9C,MAAM9B,EAASrC,EAAK4B,WACpB,IAAKuC,EAAe,CAClB,MAAMlC,EAAQ,GACd,IAAK,IAAIxU,EAAI,EAAGmR,EAAMoB,EAAKwB,WAAWhQ,OAAQ/D,EAAImR,EAAKnR,IACrDwU,EAAMxC,KAAKO,EAAKwB,WAAW/T,IAG7B,IAAK,IAAIA,EAAI,EAAGmR,EAAMqD,EAAMzQ,OAAQ/D,EAAImR,EAAKnR,IAC3C4U,EAAOC,aAAaL,EAAMxU,GAAIuS,GAIlCqC,EAAOgC,YAAYrE,GAgDrB,MAAMsE,GAAapE,EAAmB,YAMtC,SAASnR,GAAMwB,EAAOgU,GACpB,MAAMC,EAAMF,GAAW/T,EAAM,IAAMA,EAAMiU,MAAQjU,EAAMG,OACvD,OAAI6T,EACKC,EAAIC,QAAQ,UAAW,IAEzBD,EAiEM,QAEb5E,YAEA8E,qBA5hC2B,SA8hC3BC,MAAOvD,EAEPwD,UAAY,MAAKxD,QACjBlB,qBACAH,aACA8E,gBA7gCF,SAAyB7E,GACvB,OAAOA,GAAQxP,IAAEwP,GAAMC,SAAS,wBA6gChCG,SACA0E,UAx+BF,SAAmB9E,GACjB,OAAOA,GAA0B,IAAlBA,EAAKK,UAw+BpBC,SACAC,SACAwE,WA98BF,SAAoB/E,GAClB,OAAOO,EAAOP,KAAUS,EAAKT,IA88B7BgF,UAv9BF,SAAmBhF,GACjB,OAAOA,GAAQ,UAAUrH,KAAKqH,EAAKG,SAAS/C,gBAu9B5CwD,SAlBa,EAmBbqE,QAAS1H,EAAK/B,IAAIoF,GAClBsE,aA16BF,SAAsBlF,GACpB,OAAOY,EAASZ,KAAU2B,EAAS3B,EAAMO,IA06BzCY,SACAgE,aAh7BF,SAAsBnF,GACpB,OAAOY,EAASZ,MAAW2B,EAAS3B,EAAMO,IAg7B1CC,QACAM,SACAJ,UACAC,SACAM,OA3Ba,EA4BbD,eACAH,kBACAK,WACAkE,MAAOlF,EAAmB,OAC1BO,OACA4E,KAAMnF,EAAmB,MACzBoF,OAAQpF,EAAmB,QAC3BqF,IAAKrF,EAAmB,KACxBsF,IAAKtF,EAAmB,KACxBuF,IAAKvF,EAAmB,KACxBwF,IAAKxF,EAAmB,KACxByF,MAAOzF,EAAmB,OAC1BoE,cACAsB,oBAx3BF,SAA6B5F,GAC3B,GACE,GAA+B,OAA3BA,EAAK6F,mBAAmE,KAArC7F,EAAK6F,kBAAkBpE,UAAkB,YACxEzB,EAAOA,EAAK6F,mBAEtB,OAAOzG,EAAQY,IAo3BfZ,QA1Ca,EA2Cb0G,cAAevI,EAAK5B,IAAIuF,EAAU9B,GAClC2G,iBAr7BF,SAA0BC,EAAOC,GAC/B,OAAOD,EAAM9D,cAAgB+D,GACtBD,EAAM9C,kBAAoB+C,GAo7BjCC,oBA16BF,SAA6BlG,EAAMrB,GACjCA,EAAOA,GAAQpB,EAAKlC,GAEpB,MAAM8K,EAAW,GAQjB,OAPInG,EAAKkD,iBAAmBvE,EAAKqB,EAAKkD,kBACpCiD,EAAS1G,KAAKO,EAAKkD,iBAErBiD,EAAS1G,KAAKO,GACVA,EAAKkC,aAAevD,EAAKqB,EAAKkC,cAChCiE,EAAS1G,KAAKO,EAAKkC,aAEdiE,GAg6BP7E,aACAoB,kBACAG,mBACAC,cACAC,eACAE,iBACAmD,kBA1lBF,SAA2BzD,EAAOhB,GAChC,OAAOe,EAAgBC,IAAUI,EAAaJ,EAAM3C,KAAM2B,IA0lB1D0E,mBAjlBF,SAA4B1D,EAAOhB,GACjC,OAAOkB,EAAiBF,IAAUM,GAAcN,EAAM3C,KAAM2B,IAilB5DyB,UAtDa,GAuDbE,UAvDa,GAwDbC,eACA+C,eAreF,SAAwB3D,GACtB,GAAIvC,EAAOuC,EAAM3C,QAAUmD,GAAYR,EAAM3C,OAASZ,EAAQuD,EAAM3C,MAClE,OAAO,EAGT,MAAMuG,EAAW5D,EAAM3C,KAAKwB,WAAWmB,EAAMC,OAAS,GAChD4D,EAAY7D,EAAM3C,KAAKwB,WAAWmB,EAAMC,QAC9C,QAAM2D,IAAYjG,EAAOiG,IAAgBC,IAAalG,EAAOkG,KA+d7DC,eAjdF,SAAwB9D,EAAOhE,GAC7B,KAAOgE,GAAO,CACZ,GAAIhE,EAAKgE,GACP,OAAOA,EAGTA,EAAQS,GAAUT,GAGpB,OAAO,MAycP+D,eA/bF,SAAwB/D,EAAOhE,GAC7B,KAAOgE,GAAO,CACZ,GAAIhE,EAAKgE,GACP,OAAOA,EAGTA,EAAQW,GAAUX,GAGpB,OAAO,MAubPgE,YA9aF,SAAqBhE,GACnB,IAAKvC,EAAOuC,EAAM3C,MAChB,OAAO,EAGT,MAAM4G,EAAKjE,EAAM3C,KAAKuB,UAAUsF,OAAOlE,EAAMC,OAAS,GACtD,OAAOgE,GAAc,MAAPA,GAAcA,IAAOhH,GAyanCkH,aAhaF,SAAsBnE,GACpB,IAAKvC,EAAOuC,EAAM3C,MAChB,OAAO,EAGT,MAAM4G,EAAKjE,EAAM3C,KAAKuB,UAAUsF,OAAOlE,EAAMC,OAAS,GACtD,MAAc,MAAPgE,GAAcA,IAAOhH,GA2Z5BmH,UAhZF,SAAmBC,EAAYC,EAAUC,EAAS7D,GAChD,IAAIV,EAAQqE,EAEZ,KAAOrE,IACLuE,EAAQvE,IAEJY,GAAYZ,EAAOsE,KAHX,CAUZtE,EAAQW,GAAUX,EAHGU,GACF2D,EAAWhH,OAAS2C,EAAM3C,MAC1BiH,EAASjH,OAAS2C,EAAM3C,QAqY7C2B,SA/Da,EAgEbwF,oBAl1BF,SAA6BnH,EAAMrB,GAGjC,IAFAqB,EAAOA,EAAK4B,WAEL5B,GACoB,IAArBsB,EAAWtB,IADJ,CAEX,GAAIrB,EAAKqB,GAAS,OAAOA,EACzB,GAAID,EAAWC,GAAS,MAExBA,EAAOA,EAAK4B,WAEd,OAAO,MAy0BPC,eACAuF,aAhzBF,SAAsBpH,EAAMrB,GAC1B,MAAMmD,EAAYD,EAAa7B,GAC/B,OAAOpK,EAAMuI,KAAK2D,EAAUuF,OAAO1I,KA+yBnCqD,WACAsF,SAzxBF,SAAkBtH,EAAMrB,GACtBA,EAAOA,GAAQpB,EAAKjC,KAEpB,MAAM2G,EAAQ,GACd,KAAOjC,IACDrB,EAAKqB,IACTiC,EAAMxC,KAAKO,GACXA,EAAOA,EAAKkD,gBAEd,OAAOjB,GAixBPsF,eAtvBF,SAAwBvH,EAAMrB,GAC5B,MAAM6I,EAAc,GAapB,OAZA7I,EAAOA,GAAQpB,EAAKlC,GAGpB,SAAUoM,EAAOC,GACX1H,IAAS0H,GAAW/I,EAAK+I,IAC3BF,EAAY/H,KAAKiI,GAEnB,IAAK,IAAIjJ,EAAM,EAAGG,EAAM8I,EAAQlG,WAAWhQ,OAAQiN,EAAMG,EAAKH,IAC5DgJ,EAAOC,EAAQlG,WAAW/C,IAL9B,CAOGuB,GAEIwH,GAyuBPG,eAzyBF,SAAwB3B,EAAOC,GAC7B,MAAMnE,EAAYD,EAAamE,GAC/B,IAAK,IAAIzW,EAAI0W,EAAO1W,EAAGA,EAAIA,EAAEqS,WAC3B,GAAIE,EAAUrI,QAAQlK,IAAM,EAAG,OAAOA,EAExC,OAAO,MAqyBPqY,KAhuBF,SAAc5H,EAAM6H,GAClB,MAAMxF,EAASrC,EAAK4B,WACdkG,EAAUtX,IAAE,IAAMqX,EAAc,KAAK,GAK3C,OAHAxF,EAAOC,aAAawF,EAAS9H,GAC7B8H,EAAQvF,YAAYvC,GAEb8H,GA0tBP3F,cACAK,mBACAQ,SA1Ea,GA2EbG,eACA4E,eArYF,SAAwBpG,EAAU3B,GAEhC,OADkB6B,EAAa7B,EAAMzC,EAAKxC,GAAG4G,IAC5BzE,IAAI8F,IAAUgF,WAoY/BC,eAzXF,SAAwBtG,EAAUuG,GAChC,IAAIR,EAAU/F,EACd,IAAK,IAAIlU,EAAI,EAAGmR,EAAMsJ,EAAQ1W,OAAQ/D,EAAImR,EAAKnR,IAE3Cia,EADEA,EAAQlG,WAAWhQ,QAAU0W,EAAQza,GAC7Bia,EAAQlG,WAAWkG,EAAQlG,WAAWhQ,OAAS,GAE/CkW,EAAQlG,WAAW0G,EAAQza,IAGzC,OAAOia,GAiXPxD,aACAiE,WA7QF,SAAoBxF,EAAO/B,GAIzB,MAAMjC,EAAOiC,EAAWL,EAASM,EAC3BiB,EAAYD,EAAac,EAAM3C,KAAMrB,GACrCyJ,EAAcxS,EAAMuI,KAAK2D,IAAca,EAAM3C,KAEnD,IAAIqI,EAAWC,EACX3J,EAAKyJ,IACPC,EAAYvG,EAAUA,EAAUtQ,OAAS,GACzC8W,EAAYF,IAEZC,EAAYD,EACZE,EAAYD,EAAUzG,YAIxB,IAAI2G,EAAQF,GAAanE,GAAUmE,EAAW1F,EAAO,CACnDgB,uBAAwB/C,EACxBgD,oBAAqBhD,IAQvB,OAJK2H,GAASD,IAAc3F,EAAM3C,OAChCuI,EAAQ5F,EAAM3C,KAAKwB,WAAWmB,EAAMC,SAG/B,CACL4D,UAAW+B,EACXD,UAAWA,IAgPblZ,UACAoZ,WAzOF,SAAoBC,GAClB,OAAOpO,SAASqO,eAAeD,IAyO/B1U,UACA4U,YAtMF,SAAqB3I,EAAMrB,GACzB,KAAOqB,IACDD,EAAWC,IAAUrB,EAAKqB,IADnB,CAKX,MAAMqC,EAASrC,EAAK4B,WACpB7N,GAAOiM,GACPA,EAAOqC,IA+LToC,QAlLF,SAAiBzE,EAAMG,GACrB,GAAIH,EAAKG,SAAS/C,gBAAkB+C,EAAS/C,cAC3C,OAAO4C,EAGT,MAAM4I,EAAUxZ,GAAO+Q,GAUvB,OARIH,EAAK7K,MAAM0T,UACbD,EAAQzT,MAAM0T,QAAU7I,EAAK7K,MAAM0T,SAGrCrG,EAAiBoG,EAAShT,EAAMqJ,KAAKe,EAAKwB,aAC1CW,EAAYyG,EAAS5I,GACrBjM,GAAOiM,GAEA4I,GAoKPlY,KA3IF,SAAcH,EAAOuY,GACnB,IAAI9Y,EAASjB,GAAMwB,GAEnB,GAAIuY,EAAkB,CACpB,MAAMC,EAAW,wCACjB/Y,EAASA,EAAOyU,QAAQsE,GAAU,SAASC,EAAOC,EAAU5a,GAC1DA,EAAOA,EAAK+O,cACZ,MAAM8L,EAAyB,8BAA8BvQ,KAAKtK,MACnC4a,EACzBE,EAAc,4CAA4CxQ,KAAKtK,GAErE,OAAO2a,GAAUE,GAA0BC,EAAe,KAAO,OAEnEnZ,EAASA,EAAOoZ,OAGlB,OAAOpZ,GA4HPjB,MAtFa,GAuFbsa,mBA1HF,SAA4BC,GAC1B,MAAMC,EAAe/Y,IAAE8Y,GACjBE,EAAMD,EAAa3G,SACnBtQ,EAASiX,EAAaE,aAAY,GAExC,MAAO,CACLpT,KAAMmT,EAAInT,KACVoG,IAAK+M,EAAI/M,IAAMnK,IAoHjBoX,aAhHF,SAAsBnZ,EAAOoZ,GAC3Bnb,OAAOob,KAAKD,GAAQrY,SAAQ,SAASjC,GACnCkB,EAAMY,GAAG9B,EAAKsa,EAAOta,QA+GvBwa,aA3GF,SAAsBtZ,EAAOoZ,GAC3Bnb,OAAOob,KAAKD,GAAQrY,SAAQ,SAASjC,GACnCkB,EAAMuZ,IAAIza,EAAKsa,EAAOta,QA0GxB0a,iBA9FF,SAA0B/J,GACxB,OAAOA,IAASI,EAAOJ,IAASpK,EAAM0I,SAAS0B,EAAKgK,UAAW,mBCthClD,MAAMC,GAKnBla,YAAYma,EAAOha,GACjBE,KAAK8Z,MAAQA,EAEb9Z,KAAK+Z,MAAQ,GACb/Z,KAAKpC,QAAU,GACfoC,KAAKga,WAAa,GAClBha,KAAKF,QAAUM,IAAEwB,QAAO,EAAM,GAAI9B,GAGlCM,IAAEsB,WAAWuY,GAAK7Z,IAAEsB,WAAWwY,YAAYla,KAAKF,SAChDE,KAAKia,GAAK7Z,IAAEsB,WAAWuY,GAEvBja,KAAKma,aAMPA,aAIE,OAHAna,KAAKga,WAAaha,KAAKia,GAAGG,aAAapa,KAAK8Z,OAC5C9Z,KAAKqa,cACLra,KAAK8Z,MAAMQ,OACJta,KAMTua,UACEva,KAAKwa,WACLxa,KAAK8Z,MAAMW,WAAW,cACtBza,KAAKia,GAAGS,aAAa1a,KAAK8Z,MAAO9Z,KAAKga,YAMxCpT,QACE,MAAM+T,EAAW3a,KAAK4a,aACtB5a,KAAK6a,KAAKC,GAAItG,WACdxU,KAAKwa,WACLxa,KAAKqa,cAEDM,GACF3a,KAAK+a,UAITV,cAEEra,KAAKF,QAAQmM,GAAKkB,EAAKpB,SAAS3L,IAAE4a,OAElChb,KAAKF,QAAQoY,UAAYlY,KAAKF,QAAQoY,WAAalY,KAAKga,WAAWiB,OAGnE,MAAMC,EAAU9a,IAAEwB,OAAO,GAAI5B,KAAKF,QAAQob,SAC1C9c,OAAOob,KAAK0B,GAASha,QAASjC,IAC5Be,KAAK4O,KAAK,UAAY3P,EAAKic,EAAQjc,MAGrC,MAAMrB,EAAUwC,IAAEwB,OAAO,GAAI5B,KAAKF,QAAQlC,QAASwC,IAAEsB,WAAWyZ,SAAW,IAG3E/c,OAAOob,KAAK5b,GAASsD,QAASjC,IAC5Be,KAAKhD,OAAOiC,EAAKrB,EAAQqB,IAAM,KAGjCb,OAAOob,KAAKxZ,KAAKpC,SAASsD,QAASjC,IACjCe,KAAKob,iBAAiBnc,KAI1Bub,WAEEpc,OAAOob,KAAKxZ,KAAKpC,SAASga,UAAU1W,QAASjC,IAC3Ce,KAAKqb,aAAapc,KAGpBb,OAAOob,KAAKxZ,KAAK+Z,OAAO7Y,QAASjC,IAC/Be,KAAKsb,WAAWrc,KAGlBe,KAAKub,aAAa,UAAWvb,MAG/B6a,KAAKva,GACH,MAAMkb,EAAcxb,KAAK2L,OAAO,wBAEhC,QAAa8P,IAATnb,EAEF,OADAN,KAAK2L,OAAO,iBACL6P,EAAcxb,KAAKga,WAAW0B,QAAQtH,MAAQpU,KAAKga,WAAW2B,SAASrb,OAE1Ekb,EACFxb,KAAKga,WAAW0B,QAAQtH,IAAI9T,GAE5BN,KAAKga,WAAW2B,SAASrb,KAAKA,GAEhCN,KAAK8Z,MAAM1F,IAAI9T,GACfN,KAAKub,aAAa,SAAUjb,EAAMN,KAAKga,WAAW2B,UAItDf,aACE,MAA4D,UAArD5a,KAAKga,WAAW2B,SAAS9a,KAAK,mBAGvC+a,SACE5b,KAAKga,WAAW2B,SAAS9a,KAAK,mBAAmB,GACjDb,KAAK2L,OAAO,oBAAoB,GAChC3L,KAAKub,aAAa,WAAW,GAC7Bvb,KAAKF,QAAQ+b,SAAU,EAGzBd,UAEM/a,KAAK2L,OAAO,yBACd3L,KAAK2L,OAAO,uBAEd3L,KAAKga,WAAW2B,SAAS9a,KAAK,mBAAmB,GACjDb,KAAKF,QAAQ+b,SAAU,EACvB7b,KAAK2L,OAAO,sBAAsB,GAElC3L,KAAKub,aAAa,WAAW,GAG/BA,eACE,MAAM3O,EAAYpH,EAAMqI,KAAKvM,WACvBiM,EAAO/H,EAAMwI,KAAKxI,EAAMqJ,KAAKvN,YAE7BvB,EAAWC,KAAKF,QAAQgc,UAAU3O,EAAKR,iBAAiBC,EAAW,OACrE7M,GACFA,EAASuL,MAAMtL,KAAK8Z,MAAM,GAAIvM,GAEhCvN,KAAK8Z,MAAMiC,QAAQ,cAAgBnP,EAAWW,GAGhD6N,iBAAiBnc,GACf,MAAMjC,EAASgD,KAAKpC,QAAQqB,GAC5BjC,EAAOgf,iBAAmBhf,EAAOgf,kBAAoB7O,EAAKlC,GACrDjO,EAAOgf,qBAKRhf,EAAOmd,YACTnd,EAAOmd,aAILnd,EAAOuc,QACTuB,GAAIxB,aAAatZ,KAAK8Z,MAAO9c,EAAOuc,SAIxCvc,OAAOiC,EAAKgd,EAAaC,GACvB,GAAyB,IAArB5a,UAAUF,OACZ,OAAOpB,KAAKpC,QAAQqB,GAGtBe,KAAKpC,QAAQqB,GAAO,IAAIgd,EAAYjc,MAE/Bkc,GACHlc,KAAKob,iBAAiBnc,GAI1Boc,aAAapc,GACX,MAAMjC,EAASgD,KAAKpC,QAAQqB,GACxBjC,EAAOgf,qBACLhf,EAAOuc,QACTuB,GAAIrB,aAAazZ,KAAK8Z,MAAO9c,EAAOuc,QAGlCvc,EAAOud,SACTvd,EAAOud,kBAIJva,KAAKpC,QAAQqB,GAGtB2P,KAAK3P,EAAK2M,GACR,GAAyB,IAArBtK,UAAUF,OACZ,OAAOpB,KAAK+Z,MAAM9a,GAEpBe,KAAK+Z,MAAM9a,GAAO2M,EAGpB0P,WAAWrc,GACLe,KAAK+Z,MAAM9a,IAAQe,KAAK+Z,MAAM9a,GAAKsb,SACrCva,KAAK+Z,MAAM9a,GAAKsb,iBAGXva,KAAK+Z,MAAM9a,GAMpBkd,kCAAkCvP,EAAWjO,GAC3C,OAAQyd,IACNpc,KAAKqc,oBAAoBzP,EAAWjO,EAApCqB,CAA2Coc,GAC3Cpc,KAAK2L,OAAO,+BAIhB0Q,oBAAoBzP,EAAWjO,GAC7B,OAAQyd,IACNA,EAAME,iBACN,MAAMC,EAAUnc,IAAEgc,EAAMI,QACxBxc,KAAK2L,OAAOiB,EAAWjO,GAAS4d,EAAQE,QAAQ,gBAAgBhc,KAAK,SAAU8b,IAInF5Q,SACE,MAAMiB,EAAYpH,EAAMqI,KAAKvM,WACvBiM,EAAO/H,EAAMwI,KAAKxI,EAAMqJ,KAAKvN,YAE7Bob,EAAS9P,EAAUC,MAAM,KACzB8P,EAAeD,EAAOtb,OAAS,EAC/Bwb,EAAaD,GAAgBnX,EAAMqI,KAAK6O,GACxCG,EAAaF,EAAenX,EAAMuI,KAAK2O,GAAUlX,EAAMqI,KAAK6O,GAE5D1f,EAASgD,KAAKpC,QAAQgf,GAAc,UAC1C,OAAKA,GAAc5c,KAAK6c,GACf7c,KAAK6c,GAAYvR,MAAMtL,KAAMuN,GAC3BvQ,GAAUA,EAAO6f,IAAe7f,EAAOgf,mBACzChf,EAAO6f,GAAYvR,MAAMtO,EAAQuQ,QADnC,GC7NX,SAASuP,GAAiBC,EAAWC,GACnC,IACIxK,EADA0F,EAAY6E,EAAUE,gBAG1B,MAAMC,EAASjT,SAASkT,KAAKC,kBAC7B,IAAIC,EACJ,MAAMjM,EAAa5L,EAAMqJ,KAAKqJ,EAAU9G,YACxC,IAAKoB,EAAS,EAAGA,EAASpB,EAAWhQ,OAAQoR,IAC3C,IAAIsI,GAAI9K,OAAOoB,EAAWoB,IAA1B,CAIA,GADA0K,EAAOI,kBAAkBlM,EAAWoB,IAChC0K,EAAOK,iBAAiB,eAAgBR,IAAc,EACxD,MAEFM,EAAgBjM,EAAWoB,GAG7B,GAAe,IAAXA,GAAgBsI,GAAI9K,OAAOoB,EAAWoB,EAAS,IAAK,CACtD,MAAMgL,EAAiBvT,SAASkT,KAAKC,kBACrC,IAAIK,EAAc,KAClBD,EAAeF,kBAAkBD,GAAiBnF,GAClDsF,EAAeE,UAAUL,GACzBI,EAAcJ,EAAgBA,EAAcvL,YAAcoG,EAAUyF,WAEpE,MAAMC,EAAcb,EAAUc,YAC9BD,EAAYE,YAAY,eAAgBN,GACxC,IAAIO,EAAYH,EAAYvF,KAAKhE,QAAQ,UAAW,IAAIjT,OAExD,KAAO2c,EAAYN,EAAYtM,UAAU/P,QAAUqc,EAAY3L,aAC7DiM,GAAaN,EAAYtM,UAAU/P,OACnCqc,EAAcA,EAAY3L,YAId2L,EAAYtM,UAEtB6L,GAAWS,EAAY3L,aAAegJ,GAAI9K,OAAOyN,EAAY3L,cAC/DiM,IAAcN,EAAYtM,UAAU/P,SACpC2c,GAAaN,EAAYtM,UAAU/P,OACnCqc,EAAcA,EAAY3L,aAG5BoG,EAAYuF,EACZjL,EAASuL,EAGX,MAAO,CACLC,KAAM9F,EACN1F,OAAQA,GASZ,SAASyL,GAAiB1L,GACxB,MAAM2L,EAAgB,SAAShG,EAAW1F,GACxC,IAAI5C,EAAMuO,EAEV,GAAIrD,GAAI9K,OAAOkI,GAAY,CACzB,MAAMkG,EAAgBtD,GAAI5D,SAASgB,EAAW/K,EAAK/B,IAAI0P,GAAI9K,SACrDqN,EAAgB7X,EAAMuI,KAAKqQ,GAAetL,gBAChDlD,EAAOyN,GAAiBnF,EAAU1G,WAClCgB,GAAUhN,EAAMkJ,IAAIlJ,EAAMwI,KAAKoQ,GAAgBtD,GAAI5J,YACnDiN,GAAqBd,MAChB,CAEL,GADAzN,EAAOsI,EAAU9G,WAAWoB,IAAW0F,EACnC4C,GAAI9K,OAAOJ,GACb,OAAOsO,EAActO,EAAM,GAG7B4C,EAAS,EACT2L,GAAoB,EAGtB,MAAO,CACLvO,KAAMA,EACNyO,gBAAiBF,EACjB3L,OAAQA,IAINuK,EAAY9S,SAASkT,KAAKC,kBAC1BkB,EAAOJ,EAAc3L,EAAM3C,KAAM2C,EAAMC,QAK7C,OAHAuK,EAAUO,kBAAkBgB,EAAK1O,MACjCmN,EAAUW,SAASY,EAAKD,iBACxBtB,EAAUwB,UAAU,YAAaD,EAAK9L,QAC/BuK,ECrGT3c,IAAEwJ,GAAGhI,OAAO,CAOVF,WAAY,WACV,MAAM8c,EAAOpe,IAAEoe,KAAKhZ,EAAMqI,KAAKvM,YACzBmd,EAA+B,WAATD,EACtBE,EAA0B,WAATF,EAEjB1e,EAAUM,IAAEwB,OAAO,GAAIxB,IAAEsB,WAAW5B,QAAS4e,EAAiBlZ,EAAMqI,KAAKvM,WAAa,IAG5FxB,EAAQ6e,SAAWve,IAAEwB,QAAO,EAAM,GAAIxB,IAAEsB,WAAWC,KAAK,SAAUvB,IAAEsB,WAAWC,KAAK7B,EAAQ6B,OAC5F7B,EAAQ8e,MAAQxe,IAAEwB,QAAO,EAAM,GAAIxB,IAAEsB,WAAW5B,QAAQ8e,MAAO9e,EAAQ8e,OACvE9e,EAAQ+e,QAA8B,SAApB/e,EAAQ+e,SAAsB5N,EAAIlI,eAAiBjJ,EAAQ+e,QAE7E7e,KAAKU,KAAK,CAAC2N,EAAKyQ,KACd,MAAMhF,EAAQ1Z,IAAE0e,GAChB,IAAKhF,EAAMrZ,KAAK,cAAe,CAC7B,MAAMuJ,EAAU,IAAI6P,GAAQC,EAAOha,GACnCga,EAAMrZ,KAAK,aAAcuJ,GACzB8P,EAAMrZ,KAAK,cAAc8a,aAAa,OAAQvR,EAAQgQ,eAI1D,MAAMF,EAAQ9Z,KAAK+e,QACnB,GAAIjF,EAAM1Y,OAAQ,CAChB,MAAM4I,EAAU8P,EAAMrZ,KAAK,cAC3B,GAAIge,EACF,OAAOzU,EAAQ2B,OAAOL,MAAMtB,EAASxE,EAAMqJ,KAAKvN,YACvCxB,EAAQkf,OACjBhV,EAAQ2B,OAAO,gBAInB,OAAO3L,QD2EX,MAAMif,GACJtf,YAAYuf,EAAIC,EAAIC,EAAIC,GACtBrf,KAAKkf,GAAKA,EACVlf,KAAKmf,GAAKA,EACVnf,KAAKof,GAAKA,EACVpf,KAAKqf,GAAKA,EAGVrf,KAAKsf,aAAetf,KAAKuf,SAASzE,GAAInL,YAEtC3P,KAAKwf,SAAWxf,KAAKuf,SAASzE,GAAIpK,QAElC1Q,KAAKyf,WAAazf,KAAKuf,SAASzE,GAAIhK,UAEpC9Q,KAAK0f,SAAW1f,KAAKuf,SAASzE,GAAIjK,QAElC7Q,KAAK2f,SAAW3f,KAAKuf,SAASzE,GAAIvK,QAIpCqP,cACE,GAAI3O,EAAIzG,kBAAmB,CACzB,MAAMqV,EAAW5V,SAASQ,cAI1B,OAHAoV,EAASC,SAAS9f,KAAKkf,GAAIlf,KAAKkf,GAAGze,MAAQT,KAAKmf,GAAKnf,KAAKkf,GAAGze,KAAKW,OAAS,EAAIpB,KAAKmf,IACpFU,EAASE,OAAO/f,KAAKof,GAAIpf,KAAKkf,GAAGze,KAAOuf,KAAKC,IAAIjgB,KAAKqf,GAAIrf,KAAKkf,GAAGze,KAAKW,QAAUpB,KAAKqf,IAE/EQ,EACF,CACL,MAAM9C,EAAYkB,GAAiB,CACjCrO,KAAM5P,KAAKkf,GACX1M,OAAQxS,KAAKmf,KAQf,OALApC,EAAUe,YAAY,WAAYG,GAAiB,CACjDrO,KAAM5P,KAAKof,GACX5M,OAAQxS,KAAKqf,MAGRtC,GAIXmD,YACE,MAAO,CACLhB,GAAIlf,KAAKkf,GACTC,GAAInf,KAAKmf,GACTC,GAAIpf,KAAKof,GACTC,GAAIrf,KAAKqf,IAIbc,gBACE,MAAO,CACLvQ,KAAM5P,KAAKkf,GACX1M,OAAQxS,KAAKmf,IAIjBiB,cACE,MAAO,CACLxQ,KAAM5P,KAAKof,GACX5M,OAAQxS,KAAKqf,IAOjB1X,SACE,MAAM0Y,EAAYrgB,KAAK4f,cACvB,GAAI3O,EAAIzG,kBAAmB,CACzB,MAAM8V,EAAYrW,SAASsW,eACvBD,EAAUE,WAAa,GACzBF,EAAUG,kBAEZH,EAAUI,SAASL,QAEnBA,EAAU1Y,SAGZ,OAAO3H,KAQT2gB,eAAezI,GACb,MAAMhW,EAAS9B,IAAE8X,GAAWhW,SAK5B,OAJIgW,EAAU5L,UAAYpK,EAASlC,KAAKkf,GAAG0B,YACzC1I,EAAU5L,WAAa0T,KAAKa,IAAI3I,EAAU5L,UAAYpK,EAASlC,KAAKkf,GAAG0B,YAGlE5gB,KAMT8gB,YAOE,MAAMC,EAAkB,SAASxO,EAAOyO,GACtC,IAAKzO,EACH,OAAOA,EAUT,GAAIuI,GAAI5E,eAAe3D,MAChBuI,GAAIpI,YAAYH,IAChBuI,GAAIrI,iBAAiBF,KAAWyO,GAChClG,GAAIxI,gBAAgBC,IAAUyO,GAC9BlG,GAAIrI,iBAAiBF,IAAUyO,GAAiBlG,GAAI5K,OAAOqC,EAAM3C,KAAKkC,cACtEgJ,GAAIxI,gBAAgBC,KAAWyO,GAAiBlG,GAAI5K,OAAOqC,EAAM3C,KAAKkD,kBACtEgI,GAAIjG,QAAQtC,EAAM3C,OAASkL,GAAI9L,QAAQuD,EAAM3C,OAChD,OAAO2C,EAKX,MAAM0O,EAAQnG,GAAIvJ,SAASgB,EAAM3C,KAAMkL,GAAIjG,SAC3C,IAAIqM,GAAe,EAEnB,IAAKA,EAAc,CACjB,MAAMlO,EAAY8H,GAAI9H,UAAUT,IAAU,CAAE3C,KAAM,MAClDsR,GAAgBpG,GAAI9E,kBAAkBzD,EAAO0O,IAAUnG,GAAI5K,OAAO8C,EAAUpD,SAAWoR,EAGzF,IAAIG,GAAc,EAClB,IAAKA,EAAa,CAChB,MAAMjO,EAAY4H,GAAI5H,UAAUX,IAAU,CAAE3C,KAAM,MAClDuR,GAAerG,GAAI7E,mBAAmB1D,EAAO0O,IAAUnG,GAAI5K,OAAOgD,EAAUtD,QAAUoR,EAGxF,GAAIE,GAAgBC,EAAa,CAE/B,GAAIrG,GAAI5E,eAAe3D,GACrB,OAAOA,EAGTyO,GAAiBA,EAKnB,OAFkBA,EAAgBlG,GAAIxE,eAAewE,GAAI5H,UAAUX,GAAQuI,GAAI5E,gBAC3E4E,GAAIzE,eAAeyE,GAAI9H,UAAUT,GAAQuI,GAAI5E,kBAC7B3D,GAGhBsE,EAAWkK,EAAgB/gB,KAAKogB,eAAe,GAC/CxJ,EAAa5W,KAAKohB,cAAgBvK,EAAWkK,EAAgB/gB,KAAKmgB,iBAAiB,GAEzF,OAAO,IAAIlB,GACTrI,EAAWhH,KACXgH,EAAWpE,OACXqE,EAASjH,KACTiH,EAASrE,QAabX,MAAMtD,EAAMzO,GACVyO,EAAOA,GAAQpB,EAAKlC,GAEpB,MAAMoW,EAAkBvhB,GAAWA,EAAQuhB,gBACrCC,EAAgBxhB,GAAWA,EAAQwhB,cAGnC1K,EAAa5W,KAAKmgB,gBAClBtJ,EAAW7W,KAAKogB,cAEhBvO,EAAQ,GACR0P,EAAgB,GA0BtB,OAxBAzG,GAAInE,UAAUC,EAAYC,GAAU,SAAStE,GAC3C,GAAIuI,GAAInL,WAAW4C,EAAM3C,MACvB,OAGF,IAAIA,EACA0R,GACExG,GAAIxI,gBAAgBC,IACtBgP,EAAclS,KAAKkD,EAAM3C,MAEvBkL,GAAIrI,iBAAiBF,IAAU/M,EAAM0I,SAASqT,EAAehP,EAAM3C,QACrEA,EAAO2C,EAAM3C,OAGfA,EADSyR,EACFvG,GAAIvJ,SAASgB,EAAM3C,KAAMrB,GAEzBgE,EAAM3C,KAGXA,GAAQrB,EAAKqB,IACfiC,EAAMxC,KAAKO,MAEZ,GAEIpK,EAAM8J,OAAOuC,GAOtB0F,iBACE,OAAOuD,GAAIvD,eAAevX,KAAKkf,GAAIlf,KAAKof,IAS1CoC,OAAOjT,GACL,MAAMkT,EAAgB3G,GAAIvJ,SAASvR,KAAKkf,GAAI3Q,GACtCmT,EAAc5G,GAAIvJ,SAASvR,KAAKof,GAAI7Q,GAE1C,IAAKkT,IAAkBC,EACrB,OAAO,IAAIzC,GAAajf,KAAKkf,GAAIlf,KAAKmf,GAAInf,KAAKof,GAAIpf,KAAKqf,IAG1D,MAAMsC,EAAiB3hB,KAAKkgB,YAY5B,OAVIuB,IACFE,EAAezC,GAAKuC,EACpBE,EAAexC,GAAK,GAGlBuC,IACFC,EAAevC,GAAKsC,EACpBC,EAAetC,GAAKvE,GAAI5J,WAAWwQ,IAG9B,IAAIzC,GACT0C,EAAezC,GACfyC,EAAexC,GACfwC,EAAevC,GACfuC,EAAetC,IAQnB3B,SAASS,GACP,OAAIA,EACK,IAAIc,GAAajf,KAAKkf,GAAIlf,KAAKmf,GAAInf,KAAKkf,GAAIlf,KAAKmf,IAEjD,IAAIF,GAAajf,KAAKof,GAAIpf,KAAKqf,GAAIrf,KAAKof,GAAIpf,KAAKqf,IAO5D3L,YACE,MAAMkO,EAAkB5hB,KAAKkf,KAAOlf,KAAKof,GACnCuC,EAAiB3hB,KAAKkgB,YAgB5B,OAdIpF,GAAI9K,OAAOhQ,KAAKof,MAAQtE,GAAIpI,YAAY1S,KAAKogB,gBAC/CpgB,KAAKof,GAAG1L,UAAU1T,KAAKqf,IAGrBvE,GAAI9K,OAAOhQ,KAAKkf,MAAQpE,GAAIpI,YAAY1S,KAAKmgB,mBAC/CwB,EAAezC,GAAKlf,KAAKkf,GAAGxL,UAAU1T,KAAKmf,IAC3CwC,EAAexC,GAAK,EAEhByC,IACFD,EAAevC,GAAKuC,EAAezC,GACnCyC,EAAetC,GAAKrf,KAAKqf,GAAKrf,KAAKmf,KAIhC,IAAIF,GACT0C,EAAezC,GACfyC,EAAexC,GACfwC,EAAevC,GACfuC,EAAetC,IAQnBwC,iBACE,GAAI7hB,KAAKohB,cACP,OAAOphB,KAGT,MAAM8hB,EAAM9hB,KAAK0T,YACX7B,EAAQiQ,EAAIjQ,MAAM,KAAM,CAC5ByP,eAAe,IAIX/O,EAAQuI,GAAIzE,eAAeyL,EAAI3B,iBAAiB,SAAS5N,GAC7D,OAAQ/M,EAAM0I,SAAS2D,EAAOU,EAAM3C,SAGhCmS,EAAe,GAerB,OAdA3hB,IAAEM,KAAKmR,GAAO,SAASxD,EAAKuB,GAE1B,MAAMqC,EAASrC,EAAK4B,WAChBe,EAAM3C,OAASqC,GAAqC,IAA3B6I,GAAI5J,WAAWe,IAC1C8P,EAAa1S,KAAK4C,GAEpB6I,GAAInX,OAAOiM,GAAM,MAInBxP,IAAEM,KAAKqhB,GAAc,SAAS1T,EAAKuB,GACjCkL,GAAInX,OAAOiM,GAAM,MAGZ,IAAIqP,GACT1M,EAAM3C,KACN2C,EAAMC,OACND,EAAM3C,KACN2C,EAAMC,QACNsO,YAMJvB,SAAShR,GACP,OAAO,WACL,MAAMgD,EAAWuJ,GAAIvJ,SAASvR,KAAKkf,GAAI3Q,GACvC,QAASgD,GAAaA,IAAauJ,GAAIvJ,SAASvR,KAAKof,GAAI7Q,IAQ7DoE,aAAapE,GACX,IAAKuM,GAAIxI,gBAAgBtS,KAAKmgB,iBAC5B,OAAO,EAGT,MAAMvQ,EAAOkL,GAAIvJ,SAASvR,KAAKkf,GAAI3Q,GACnC,OAAOqB,GAAQkL,GAAInI,aAAa3S,KAAKkf,GAAItP,GAM3CwR,cACE,OAAOphB,KAAKkf,KAAOlf,KAAKof,IAAMpf,KAAKmf,KAAOnf,KAAKqf,GAQjD2C,yBACE,GAAIlH,GAAIrK,gBAAgBzQ,KAAKkf,KAAOpE,GAAI9L,QAAQhP,KAAKkf,IAEnD,OADAlf,KAAKkf,GAAG7N,UAAYyJ,GAAItG,UACjB,IAAIyK,GAAajf,KAAKkf,GAAGvB,WAAY,EAAG3d,KAAKkf,GAAGvB,WAAY,GAQrE,MAAMmE,EAAM9hB,KAAK8gB,YACjB,GAAIhG,GAAI/F,aAAa/U,KAAKkf,KAAOpE,GAAI3K,OAAOnQ,KAAKkf,IAC/C,OAAO4C,EAIT,IAAI9J,EACJ,GAAI8C,GAAItK,SAASsR,EAAI5C,IAAK,CACxB,MAAMxN,EAAYoJ,GAAIrJ,aAAaqQ,EAAI5C,GAAI/R,EAAK/B,IAAI0P,GAAItK,WACxDwH,EAAcxS,EAAMuI,KAAK2D,GACpBoJ,GAAItK,SAASwH,KAChBA,EAActG,EAAUA,EAAUtQ,OAAS,IAAM0gB,EAAI5C,GAAG9N,WAAW0Q,EAAI3C,UAGzEnH,EAAc8J,EAAI5C,GAAG9N,WAAW0Q,EAAI3C,GAAK,EAAI2C,EAAI3C,GAAK,EAAI,GAG5D,GAAInH,EAAa,CAEf,IAAIiK,EAAiBnH,GAAI5D,SAASc,EAAa8C,GAAI/F,cAAc6C,UAIjE,GAHAqK,EAAiBA,EAAeC,OAAOpH,GAAIlJ,SAASoG,EAAYlG,YAAagJ,GAAI/F,eAG7EkN,EAAe7gB,OAAQ,CACzB,MAAM+gB,EAAOrH,GAAItD,KAAKhS,EAAMqI,KAAKoU,GAAiB,KAClDnH,GAAI1I,iBAAiB+P,EAAM3c,EAAMwI,KAAKiU,KAI1C,OAAOjiB,KAAK8gB,YASdsB,WAAWxS,GACT,IAAIkS,EAAM9hB,MAEN8a,GAAI9K,OAAOJ,IAASkL,GAAItK,SAASZ,MACnCkS,EAAM9hB,KAAKgiB,yBAAyBH,kBAGtC,MAAMvD,EAAOxD,GAAI/C,WAAW+J,EAAI3B,gBAAiBrF,GAAItK,SAASZ,IAO9D,OANI0O,EAAKlI,UACPkI,EAAKlI,UAAU5E,WAAWU,aAAatC,EAAM0O,EAAKlI,WAElDkI,EAAKpG,UAAU/F,YAAYvC,GAGtBA,EAMTyS,UAAUziB,GACRA,EAASQ,IAAE4Y,KAAKpZ,GAEhB,MAAM0iB,EAAoBliB,IAAE,eAAeE,KAAKV,GAAQ,GACxD,IAAIwR,EAAa5L,EAAMqJ,KAAKyT,EAAkBlR,YAG9C,MAAM0Q,EAAM9hB,KAWZ,OATI8hB,EAAI3C,IAAM,IACZ/N,EAAaA,EAAWwG,WAE1BxG,EAAaA,EAAWtE,KAAI,SAAS6G,GACnC,OAAOmO,EAAIM,WAAWzO,MAEpBmO,EAAI3C,GAAK,IACX/N,EAAaA,EAAWwG,WAEnBxG,EAQTmR,WACE,MAAMlC,EAAYrgB,KAAK4f,cACvB,OAAO3O,EAAIzG,kBAAoB6V,EAAUkC,WAAalC,EAAUhI,KASlEmK,aAAaC,GACX,IAAI5L,EAAW7W,KAAKogB,cAEpB,IAAKtF,GAAIvE,YAAYM,GACnB,OAAO7W,KAGT,MAAM4W,EAAakE,GAAIzE,eAAeQ,GAAU,SAAStE,GACvD,OAAQuI,GAAIvE,YAAYhE,MAS1B,OANIkQ,IACF5L,EAAWiE,GAAIxE,eAAeO,GAAU,SAAStE,GAC/C,OAAQuI,GAAIvE,YAAYhE,OAIrB,IAAI0M,GACTrI,EAAWhH,KACXgH,EAAWpE,OACXqE,EAASjH,KACTiH,EAASrE,QAUbkQ,cAAcD,GACZ,IAAI5L,EAAW7W,KAAKogB,cAEhBuC,EAAiB,SAASpQ,GAC5B,OAAQuI,GAAIvE,YAAYhE,KAAWuI,GAAIpE,aAAanE,IAGtD,GAAIoQ,EAAe9L,GACjB,OAAO7W,KAGT,IAAI4W,EAAakE,GAAIzE,eAAeQ,EAAU8L,GAM9C,OAJIF,IACF5L,EAAWiE,GAAIxE,eAAeO,EAAU8L,IAGnC,IAAI1D,GACTrI,EAAWhH,KACXgH,EAAWpE,OACXqE,EAASjH,KACTiH,EAASrE,QAeboQ,mBAAmBC,GACjB,IAAIhM,EAAW7W,KAAKogB,cAEhBxJ,EAAakE,GAAIzE,eAAeQ,GAAU,SAAStE,GACrD,IAAKuI,GAAIvE,YAAYhE,KAAWuI,GAAIpE,aAAanE,GAC/C,OAAO,EAET,IAAIuP,EAAM,IAAI7C,GACZ1M,EAAM3C,KACN2C,EAAMC,OACNqE,EAASjH,KACTiH,EAASrE,QAEPzD,EAAS8T,EAAMna,KAAKoZ,EAAIS,YAC5B,OAAOxT,GAA2B,IAAjBA,EAAO+T,SAGtBhB,EAAM,IAAI7C,GACZrI,EAAWhH,KACXgH,EAAWpE,OACXqE,EAASjH,KACTiH,EAASrE,QAGP6F,EAAOyJ,EAAIS,WACXxT,EAAS8T,EAAMna,KAAK2P,GAExB,OAAItJ,GAAUA,EAAO,GAAG3N,SAAWiX,EAAKjX,OAC/B0gB,EAEA,KASXiB,SAASpH,GACP,MAAO,CACLlc,EAAG,CACDujB,KAAMlI,GAAInD,eAAegE,EAAU3b,KAAKkf,IACxC1M,OAAQxS,KAAKmf,IAEf8D,EAAG,CACDD,KAAMlI,GAAInD,eAAegE,EAAU3b,KAAKof,IACxC5M,OAAQxS,KAAKqf,KAUnB6D,aAAaC,GACX,MAAO,CACL1jB,EAAG,CACDujB,KAAMxd,EAAMwI,KAAK8M,GAAInD,eAAenS,EAAMqI,KAAKsV,GAAQnjB,KAAKkf,KAC5D1M,OAAQxS,KAAKmf,IAEf8D,EAAG,CACDD,KAAMxd,EAAMwI,KAAK8M,GAAInD,eAAenS,EAAMuI,KAAKoV,GAAQnjB,KAAKof,KAC5D5M,OAAQxS,KAAKqf,KASnB+D,iBAEE,OADkBpjB,KAAK4f,cACNwD,kBAWN,QAUbpkB,OAAQ,SAASkgB,EAAIC,EAAIC,EAAIC,GAC3B,GAAyB,IAArB/d,UAAUF,OACZ,OAAO,IAAI6d,GAAaC,EAAIC,EAAIC,EAAIC,GAC/B,GAAyB,IAArB/d,UAAUF,OAGnB,OAAO,IAAI6d,GAAaC,EAAIC,EAF5BC,EAAKF,EACLG,EAAKF,GAEA,CACL,IAAIkE,EAAerjB,KAAKsjB,sBAExB,IAAKD,GAAqC,IAArB/hB,UAAUF,OAAc,CAC3C,IAAImiB,EAAcjiB,UAAU,GAI5B,OAHIwZ,GAAInL,WAAW4T,KACjBA,EAAcA,EAAYC,WAErBxjB,KAAKyjB,sBAAsBF,EAAazI,GAAItG,YAAclT,UAAU,GAAG+P,WAEhF,OAAOgS,IAIXI,sBAAuB,SAASF,EAAapF,GAAoB,GAE/D,OADmBne,KAAK0jB,eAAeH,GACnB7F,SAASS,IAG/BmF,oBAAqB,WACnB,IAAIpE,EAAIC,EAAIC,EAAIC,EAChB,GAAIpO,EAAIzG,kBAAmB,CACzB,MAAM8V,EAAYrW,SAASsW,eAC3B,IAAKD,GAAsC,IAAzBA,EAAUE,WAC1B,OAAO,KACF,GAAI1F,GAAI/J,OAAOuP,EAAUqD,YAG9B,OAAO,KAGT,MAAMtD,EAAYC,EAAUsD,WAAW,GACvC1E,EAAKmB,EAAUwD,eACf1E,EAAKkB,EAAUyD,YACf1E,EAAKiB,EAAU0D,aACf1E,EAAKgB,EAAU2D,cACV,CACL,MAAMjH,EAAY9S,SAASqW,UAAU7V,cAC/BwZ,EAAelH,EAAUc,YAC/BoG,EAAavG,UAAS,GACtB,MAAMF,EAAiBT,EACvBS,EAAeE,UAAS,GAExB,IAAI9G,EAAakG,GAAiBU,GAAgB,GAC9C3G,EAAWiG,GAAiBmH,GAAc,GAG1CnJ,GAAI9K,OAAO4G,EAAWhH,OAASkL,GAAIxI,gBAAgBsE,IACrDkE,GAAIoJ,WAAWrN,EAASjH,OAASkL,GAAIrI,iBAAiBoE,IACtDA,EAASjH,KAAKkC,cAAgB8E,EAAWhH,OACzCgH,EAAaC,GAGfqI,EAAKtI,EAAWoH,KAChBmB,EAAKvI,EAAWpE,OAChB4M,EAAKvI,EAASmH,KACdqB,EAAKxI,EAASrE,OAGhB,OAAO,IAAIyM,GAAaC,EAAIC,EAAIC,EAAIC,IAWtCqE,eAAgB,SAAS9T,GACvB,IAAIsP,EAAKtP,EACLuP,EAAK,EACLC,EAAKxP,EACLyP,EAAKvE,GAAI5J,WAAWkO,GAexB,OAZItE,GAAI5K,OAAOgP,KACbC,EAAKrE,GAAI5D,SAASgI,GAAI9d,OAAS,EAC/B8d,EAAKA,EAAG1N,YAENsJ,GAAI7F,KAAKmK,IACXC,EAAKvE,GAAI5D,SAASkI,GAAIhe,OAAS,EAC/Bge,EAAKA,EAAG5N,YACCsJ,GAAI5K,OAAOkP,KACpBC,EAAKvE,GAAI5D,SAASkI,GAAIhe,OACtBge,EAAKA,EAAG5N,YAGHxR,KAAKhB,OAAOkgB,EAAIC,EAAIC,EAAIC,IASjC8E,qBAAsB,SAASvU,GAC7B,OAAO5P,KAAK0jB,eAAe9T,GAAM8N,UAAS,IAS5C0G,oBAAqB,SAASxU,GAC5B,OAAO5P,KAAK0jB,eAAe9T,GAAM8N,YAYnC2G,mBAAoB,SAAS1I,EAAUoH,GACrC,MAAM7D,EAAKpE,GAAIjD,eAAe8D,EAAUoH,EAAStjB,EAAEujB,MAC7C7D,EAAK4D,EAAStjB,EAAE+S,OAChB4M,EAAKtE,GAAIjD,eAAe8D,EAAUoH,EAASE,EAAED,MAC7C3D,EAAK0D,EAASE,EAAEzQ,OACtB,OAAO,IAAIyM,GAAaC,EAAIC,EAAIC,EAAIC,IAYtCiF,uBAAwB,SAASvB,EAAUI,GACzC,MAAMhE,EAAK4D,EAAStjB,EAAE+S,OAChB6M,EAAK0D,EAASE,EAAEzQ,OAChB0M,EAAKpE,GAAIjD,eAAerS,EAAMqI,KAAKsV,GAAQJ,EAAStjB,EAAEujB,MACtD5D,EAAKtE,GAAIjD,eAAerS,EAAMuI,KAAKoV,GAAQJ,EAASE,EAAED,MAE5D,OAAO,IAAI/D,GAAaC,EAAIC,EAAIC,EAAIC,KEn5BxC,MAAMkF,GAAU,CACd,UAAa,EACb,IAAO,EACP,MAAS,GACT,MAAS,GACT,OAAU,GAGV,KAAQ,GACR,GAAM,GACN,MAAS,GACT,KAAQ,GAGR,KAAQ,GACR,KAAQ,GACR,KAAQ,GACR,KAAQ,GACR,KAAQ,GACR,KAAQ,GACR,KAAQ,GACR,KAAQ,GACR,KAAQ,GAGR,EAAK,GACL,EAAK,GACL,EAAK,GACL,EAAK,GACL,EAAK,GACL,EAAK,GACL,EAAK,GACL,EAAK,GACL,EAAK,GACL,EAAK,GACL,EAAK,GACL,EAAK,GAEL,MAAS,IACT,YAAe,IACf,UAAa,IACb,aAAgB,IAGhB,KAAQ,GACR,IAAO,GACP,OAAU,GACV,SAAY,IAWC,QAObC,OAASC,GACAjf,EAAM0I,SAAS,CACpBqW,GAAQG,UACRH,GAAQI,IACRJ,GAAQK,MACRL,GAAQM,MACRN,GAAQO,QACPL,GAQLM,OAASN,GACAjf,EAAM0I,SAAS,CACpBqW,GAAQS,KACRT,GAAQU,GACRV,GAAQW,MACRX,GAAQY,MACPV,GAQLW,aAAeX,GACNjf,EAAM0I,SAAS,CACpBqW,GAAQc,KACRd,GAAQe,IACRf,GAAQgB,OACRhB,GAAQiB,UACPf,GAMLgB,aAActY,EAAKV,aAAa8X,IAChC1J,KAAM0J,IC5GO,MAAMmB,GACnB/lB,YAAYgmB,GACV3lB,KAAK4lB,MAAQ,GACb5lB,KAAK6lB,aAAe,EACpB7lB,KAAK2lB,UAAYA,EACjB3lB,KAAK2b,SAAWgK,EAAU,GAG5BG,eACE,MAAMhE,EAAMiE,GAAM/mB,OAAOgB,KAAK2b,UAG9B,MAAO,CACLtb,SAAUL,KAAK2lB,UAAUrlB,OACzByiB,SAAYjB,GAAOA,EAAIxC,eAAkBwC,EAAIiB,SAAS/iB,KAAK2b,UAJvC,CAAElc,EAAG,CAAEujB,KAAM,GAAIxQ,OAAQ,GAAKyQ,EAAG,CAAED,KAAM,GAAIxQ,OAAQ,KAQ7EwT,cAAcC,GACc,OAAtBA,EAAS5lB,UACXL,KAAK2lB,UAAUrlB,KAAK2lB,EAAS5lB,UAEL,OAAtB4lB,EAASlD,UACXgD,GAAM1B,mBAAmBrkB,KAAK2b,SAAUsK,EAASlD,UAAUpb,SAS/Due,SAEMlmB,KAAK2lB,UAAUrlB,SAAWN,KAAK4lB,MAAM5lB,KAAK6lB,aAAaxlB,UACzDL,KAAKmmB,aAIPnmB,KAAK6lB,YAAc,EAGnB7lB,KAAKgmB,cAAchmB,KAAK4lB,MAAM5lB,KAAK6lB,cAOrCO,SAEEpmB,KAAK4lB,MAAQ,GAGb5lB,KAAK6lB,aAAe,EAGpB7lB,KAAKmmB,aAOPvf,QAEE5G,KAAK4lB,MAAQ,GAGb5lB,KAAK6lB,aAAe,EAGpB7lB,KAAK2lB,UAAUrlB,KAAK,IAGpBN,KAAKmmB,aAMP3e,OAEMxH,KAAK2lB,UAAUrlB,SAAWN,KAAK4lB,MAAM5lB,KAAK6lB,aAAaxlB,UACzDL,KAAKmmB,aAGHnmB,KAAK6lB,YAAc,IACrB7lB,KAAK6lB,cACL7lB,KAAKgmB,cAAchmB,KAAK4lB,MAAM5lB,KAAK6lB,eAOvCpe,OACMzH,KAAK4lB,MAAMxkB,OAAS,EAAIpB,KAAK6lB,cAC/B7lB,KAAK6lB,cACL7lB,KAAKgmB,cAAchmB,KAAK4lB,MAAM5lB,KAAK6lB,eAOvCM,aACEnmB,KAAK6lB,cAGD7lB,KAAK4lB,MAAMxkB,OAASpB,KAAK6lB,cAC3B7lB,KAAK4lB,MAAQ5lB,KAAK4lB,MAAM3X,MAAM,EAAGjO,KAAK6lB,cAIxC7lB,KAAK4lB,MAAMvW,KAAKrP,KAAK8lB,iBC/GV,MAAMO,GAcnBC,UAAUC,EAAMC,GACd,GAAIvV,EAAItH,cAAgB,IAAK,CAC3B,MAAMoF,EAAS,GAIf,OAHA3O,IAAEM,KAAK8lB,EAAe,CAACnY,EAAKoY,KAC1B1X,EAAO0X,GAAgBF,EAAKG,IAAID,KAE3B1X,EAET,OAAOwX,EAAKG,IAAIF,GASlBG,SAASxmB,GACP,MACMymB,EAAY5mB,KAAKsmB,UAAUnmB,EADd,CAAC,cAAe,YAAa,aAAc,kBAAmB,iBAC1B,GAEjD0mB,EAAW1mB,EAAM,GAAG4E,MAAM8hB,UAAYD,EAAU,aAKtD,OAHAA,EAAU,aAAeE,SAASD,EAAU,IAC5CD,EAAU,kBAAoBC,EAASjO,MAAM,YAEtCgO,EASTG,UAAUjF,EAAK8E,GACbxmB,IAAEM,KAAKohB,EAAIjQ,MAAMiJ,GAAI3K,OAAQ,CAC3BkR,iBAAiB,IACf,CAAChT,EAAK8T,KACR/hB,IAAE+hB,GAAMuE,IAAIE,KAchBI,WAAWlF,EAAKhiB,GACdgiB,EAAMA,EAAIpO,YAEV,MAAM3D,EAAYjQ,GAAWA,EAAQiQ,UAAa,OAC5CkX,KAA0BnnB,IAAWA,EAAQmnB,sBAC7CC,KAAyBpnB,IAAWA,EAAQonB,qBAElD,GAAIpF,EAAIV,cACN,MAAO,CAACU,EAAIM,WAAWtH,GAAI9b,OAAO+Q,KAGpC,IAAIxB,EAAOuM,GAAIhL,mBAAmBC,GAClC,MAAM8B,EAAQiQ,EAAIjQ,MAAMiJ,GAAI9K,OAAQ,CAClCsR,eAAe,IACdxU,IAAKuL,GACCyC,GAAI/D,oBAAoBsB,EAAM9J,IAASuM,GAAItD,KAAKa,EAAMtI,IAG/D,GAAIkX,EAAsB,CACxB,GAAIC,EAAqB,CACvB,MAAMC,EAAerF,EAAIjQ,QAEzBtD,EAAOpB,EAAK5B,IAAIgD,EAAOqB,GACdpK,EAAM0I,SAASiZ,EAAcvX,IAIxC,OAAOiC,EAAM/E,IAAK8C,IAChB,MAAMmG,EAAW+E,GAAIhF,oBAAoBlG,EAAMrB,GACzCV,EAAOrI,EAAMqI,KAAKkI,GAClBqR,EAAQ5hB,EAAMwI,KAAK+H,GAKzB,OAJA3V,IAAEM,KAAK0mB,EAAO,CAAC/Y,EAAKgZ,KAClBvM,GAAI1I,iBAAiBvE,EAAMwZ,EAAKjW,YAChC0J,GAAInX,OAAO0jB,KAEN7hB,EAAMqI,KAAKkI,KAGpB,OAAOlE,EAUXyF,QAAQwK,GACN,MAAMwF,EAAQlnB,IAAG0a,GAAIpG,UAAUoN,EAAI5C,IAA0B4C,EAAI5C,GAAxB4C,EAAI5C,GAAG1N,YAChD,IAAIoV,EAAY5mB,KAAK2mB,SAASW,GAI9B,IACEV,EAAYxmB,IAAEwB,OAAOglB,EAAW,CAC9B,YAAa3c,SAASsd,kBAAkB,QAAU,OAAS,SAC3D,cAAetd,SAASsd,kBAAkB,UAAY,SAAW,SACjE,iBAAkBtd,SAASsd,kBAAkB,aAAe,YAAc,SAC1E,iBAAkBtd,SAASsd,kBAAkB,aAAe,YAAc,SAC1E,mBAAoBtd,SAASsd,kBAAkB,eAAiB,cAAgB,SAChF,qBAAsBtd,SAASsd,kBAAkB,iBAAmB,gBAAkB,SACtF,cAAetd,SAASud,kBAAkB,aAAeZ,EAAU,iBAErE,MAAO3D,IAGT,GAAKnB,EAAItC,WAEF,CACL,MACMiI,EADe,CAAC,SAAU,OAAQ,oBAAqB,UAC5Bpe,QAAQud,EAAU,qBAAuB,EAC1EA,EAAU,cAAgBa,EAAc,YAAc,eAJtDb,EAAU,cAAgB,OAO5B,MAAMzE,EAAOrH,GAAIvJ,SAASuQ,EAAI5C,GAAIpE,GAAI3K,QACtC,GAAIgS,GAAQA,EAAKpd,MAAM,eACrB6hB,EAAU,eAAiBzE,EAAKpd,MAAM2iB,eACjC,CACL,MAAMA,EAAaZ,SAASF,EAAU,eAAgB,IAAME,SAASF,EAAU,aAAc,IAC7FA,EAAU,eAAiBc,EAAWC,QAAQ,GAOhD,OAJAf,EAAUgB,OAAS9F,EAAIrC,cAAgB3E,GAAIvJ,SAASuQ,EAAI5C,GAAIpE,GAAIhK,UAChE8V,EAAUlV,UAAYoJ,GAAIrJ,aAAaqQ,EAAI5C,GAAIpE,GAAInL,YACnDiX,EAAUb,MAAQjE,EAEX8E,GC1JI,MAAMiB,GAInBC,kBAAkBnM,GAChB3b,KAAK+nB,WAAW,KAAMpM,GAMxBqM,oBAAoBrM,GAClB3b,KAAK+nB,WAAW,KAAMpM,GAMxB3V,OAAO2V,GACL,MAAMmG,EAAMiE,GAAM/mB,OAAO2c,GAAUqG,yBAE7BmB,EAAQrB,EAAIjQ,MAAMiJ,GAAI3K,OAAQ,CAAEkR,iBAAiB,IACjD4G,EAAaziB,EAAMyJ,UAAUkU,EAAOhW,EAAKpC,KAAK,eAEpD3K,IAAEM,KAAKunB,EAAY,CAAC5Z,EAAK8U,KACvB,MAAMtV,EAAOrI,EAAMqI,KAAKsV,GACxB,GAAIrI,GAAIzK,KAAKxC,GAAO,CAClB,MAAMqa,EAAeloB,KAAKmoB,SAASta,EAAKiF,iBACpCoV,EACF/E,EACGrW,IAAIqV,GAAQ+F,EAAa/V,YAAYgQ,KAExCniB,KAAKooB,SAASjF,EAAOtV,EAAK2D,WAAWzB,UACrCoT,EACGrW,IAAKqV,GAASA,EAAK3Q,YACnB1E,IAAKqV,GAASniB,KAAKqoB,iBAAiBlG,UAGzC/hB,IAAEM,KAAKyiB,EAAO,CAAC9U,EAAK8T,KAClB/hB,IAAE+hB,GAAMuE,IAAI,aAAc,CAACrY,EAAK+F,KACtB0S,SAAS1S,EAAK,KAAO,GAAK,QAM1C0N,EAAIna,SAMN5B,QAAQ4V,GACN,MAAMmG,EAAMiE,GAAM/mB,OAAO2c,GAAUqG,yBAE7BmB,EAAQrB,EAAIjQ,MAAMiJ,GAAI3K,OAAQ,CAAEkR,iBAAiB,IACjD4G,EAAaziB,EAAMyJ,UAAUkU,EAAOhW,EAAKpC,KAAK,eAEpD3K,IAAEM,KAAKunB,EAAY,CAAC5Z,EAAK8U,KACvB,MAAMtV,EAAOrI,EAAMqI,KAAKsV,GACpBrI,GAAIzK,KAAKxC,GACX7N,KAAKsoB,YAAY,CAACnF,IAElB/iB,IAAEM,KAAKyiB,EAAO,CAAC9U,EAAK8T,KAClB/hB,IAAE+hB,GAAMuE,IAAI,aAAc,CAACrY,EAAK+F,KAC9BA,EAAO0S,SAAS1S,EAAK,KAAO,GACf,GAAKA,EAAM,GAAK,QAMrC0N,EAAIna,SAQNogB,WAAWQ,EAAU5M,GACnB,MAAMmG,EAAMiE,GAAM/mB,OAAO2c,GAAUqG,yBAEnC,IAAImB,EAAQrB,EAAIjQ,MAAMiJ,GAAI3K,OAAQ,CAAEkR,iBAAiB,IACrD,MAAM0B,EAAWjB,EAAIoB,aAAaC,GAC5B8E,EAAaziB,EAAMyJ,UAAUkU,EAAOhW,EAAKpC,KAAK,eAGpD,GAAIvF,EAAMvE,KAAKkiB,EAAOrI,GAAInG,YAAa,CACrC,IAAI6T,EAAe,GACnBpoB,IAAEM,KAAKunB,EAAY,CAAC5Z,EAAK8U,KACvBqF,EAAeA,EAAatG,OAAOliB,KAAKooB,SAASjF,EAAOoF,MAE1DpF,EAAQqF,MAEH,CACL,MAAMC,EAAY3G,EAAIjQ,MAAMiJ,GAAIpK,OAAQ,CACtC2Q,iBAAiB,IAChBpK,OAAQyR,IACDtoB,IAAE2P,SAAS2Y,EAAUH,IAG3BE,EAAUrnB,OACZhB,IAAEM,KAAK+nB,EAAW,CAACpa,EAAKqa,KACtB5N,GAAIzG,QAAQqU,EAAUH,KAGxBpF,EAAQnjB,KAAKsoB,YAAYL,GAAY,GAIzClC,GAAMzB,uBAAuBvB,EAAUI,GAAOxb,SAQhDygB,SAASjF,EAAOoF,GACd,MAAM1a,EAAOrI,EAAMqI,KAAKsV,GAClBpV,EAAOvI,EAAMuI,KAAKoV,GAElBwF,EAAW7N,GAAIpK,OAAO7C,EAAKiF,kBAAoBjF,EAAKiF,gBACpD8V,EAAW9N,GAAIpK,OAAO3C,EAAK+D,cAAgB/D,EAAK+D,YAEhD4W,EAAWC,GAAY7N,GAAI/I,YAAY+I,GAAI9b,OAAOupB,GAAY,MAAOxa,GAe3E,OAZAoV,EAAQA,EAAMrW,IAAKqV,GACVrH,GAAInG,WAAWwN,GAAQrH,GAAIzG,QAAQ8N,EAAM,MAAQA,GAI1DrH,GAAI1I,iBAAiBsW,EAAUvF,GAE3ByF,IACF9N,GAAI1I,iBAAiBsW,EAAUljB,EAAMqJ,KAAK+Z,EAASxX,aACnD0J,GAAInX,OAAOilB,IAGNzF,EAUTmF,YAAYL,EAAYY,GACtB,IAAIC,EAAgB,GA+EpB,OA7EA1oB,IAAEM,KAAKunB,EAAY,CAAC5Z,EAAK8U,KACvB,MAAMtV,EAAOrI,EAAMqI,KAAKsV,GAClBpV,EAAOvI,EAAMuI,KAAKoV,GAElB4F,EAAWF,EAAkB/N,GAAI9D,aAAanJ,EAAMiN,GAAIpK,QAAU7C,EAAK2D,WACvEwX,EAAaD,EAASvX,WAE5B,GAAqC,OAAjCuX,EAASvX,WAAWzB,SACtBoT,EAAMrW,IAAIqV,IACR,MAAM8G,EAAUjpB,KAAKkpB,iBAAiB/G,GAElC6G,EAAWlX,YACbkX,EAAWxX,WAAWU,aACpBiQ,EACA6G,EAAWlX,aAGbkX,EAAWxX,WAAWW,YAAYgQ,GAGhC8G,EAAQ7nB,SACVpB,KAAKooB,SAASa,EAASF,EAAShZ,UAChCoS,EAAKhQ,YAAY8W,EAAQ,GAAGzX,eAIC,IAA7BuX,EAASlpB,SAASuB,QACpB4nB,EAAW/U,YAAY8U,GAGY,IAAjCC,EAAW5X,WAAWhQ,QACxB4nB,EAAWxX,WAAWyC,YAAY+U,OAE/B,CACL,MAAMG,EAAWJ,EAAS3X,WAAWhQ,OAAS,EAAI0Z,GAAIhH,UAAUiV,EAAU,CACxEnZ,KAAM7B,EAAKyD,WACXgB,OAAQsI,GAAIlI,SAAS7E,GAAQ,GAC5B,CACDwF,wBAAwB,IACrB,KAEC6V,EAAatO,GAAIhH,UAAUiV,EAAU,CACzCnZ,KAAM/B,EAAK2D,WACXgB,OAAQsI,GAAIlI,SAAS/E,IACpB,CACD0F,wBAAwB,IAG1B4P,EAAQ0F,EAAkB/N,GAAI3D,eAAeiS,EAAYtO,GAAIzK,MACzD7K,EAAMqJ,KAAKua,EAAWhY,YAAY6F,OAAO6D,GAAIzK,OAG7CwY,GAAoB/N,GAAIpK,OAAOqY,EAASvX,cAC1C2R,EAAQA,EAAMrW,IAAKqV,GACVrH,GAAIzG,QAAQ8N,EAAM,OAI7B/hB,IAAEM,KAAK8E,EAAMqJ,KAAKsU,GAAOvL,UAAW,CAACvJ,EAAK8T,KACxCrH,GAAI/I,YAAYoQ,EAAM4G,KAIxB,MAAMM,EAAY7jB,EAAM2J,QAAQ,CAAC4Z,EAAUK,EAAYD,IACvD/oB,IAAEM,KAAK2oB,EAAW,CAAChb,EAAKib,KACtB,MAAMC,EAAY,CAACD,GAAUpH,OAAOpH,GAAI3D,eAAemS,EAAUxO,GAAIpK,SACrEtQ,IAAEM,KAAK6oB,EAAU3R,UAAW,CAACvJ,EAAKqa,KAC3B5N,GAAI5J,WAAWwX,IAClB5N,GAAInX,OAAO+kB,GAAU,OAM7BI,EAAgBA,EAAc5G,OAAOiB,KAGhC2F,EAYTT,iBAAiBzY,GACf,OAAOA,EAAKkD,gBACRgI,GAAI1I,iBAAiBxC,EAAKkD,gBAAiB,CAAClD,IAC5C5P,KAAKooB,SAAS,CAACxY,GAAO,MAW5BuY,SAASvY,GACP,OAAOA,EACHpK,EAAMvE,KAAK2O,EAAK/P,SAAUsB,GAAS,CAAC,KAAM,MAAMkI,QAAQlI,EAAM4O,WAAa,GAC3E,KAWNmZ,iBAAiBtZ,GACf,MAAMmG,EAAW,GACjB,KAAOnG,EAAKkC,aACViE,EAAS1G,KAAKO,EAAKkC,aACnBlC,EAAOA,EAAKkC,YAEd,OAAOiE,GChRI,MAAMyT,GACnB7pB,YAAYqK,GAEVhK,KAAKypB,OAAS,IAAI5B,GAClB7nB,KAAKF,QAAUkK,EAAQlK,QASzB4pB,UAAU5H,EAAK6H,GACb,MAAMC,EAAM9O,GAAI1C,WAAW,IAAI7W,MAAMooB,EAAU,GAAG1c,KAAK6N,GAAItL,aAC3DsS,EAAMA,EAAID,kBACNO,WAAWwH,GAAK,IAEpB9H,EAAMiE,GAAM/mB,OAAO4qB,EAAKD,IACpBhiB,SAcNkiB,gBAAgBlO,EAAUmG,GAOxBA,GAHAA,GAHAA,EAAMA,GAAOiE,GAAM/mB,OAAO2c,IAGhBkG,kBAGAG,yBAGV,MAAM/J,EAAY6C,GAAIvJ,SAASuQ,EAAI5C,GAAIpE,GAAI3K,QAE3C,IAAI2Z,EAEJ,GAAI7R,EAAW,CAEb,GAAI6C,GAAIzK,KAAK4H,KAAe6C,GAAI9L,QAAQiJ,IAAc6C,GAAItF,oBAAoByC,IAG5E,YADAjY,KAAKypB,OAAO1B,WAAW9P,EAAUzG,WAAWzB,UAEvC,CACL,IAAI/K,EAAa,KAOjB,GAN6C,IAAzChF,KAAKF,QAAQiqB,wBACf/kB,EAAa8V,GAAIvJ,SAAS0G,EAAW6C,GAAIlK,cACS,IAAzC5Q,KAAKF,QAAQiqB,0BACtB/kB,EAAa8V,GAAI9D,aAAaiB,EAAW6C,GAAIlK,eAG3C5L,EAAY,CAEd8kB,EAAW1pB,IAAE0a,GAAItG,WAAW,GAGxBsG,GAAIrI,iBAAiBqP,EAAI3B,kBAAoBrF,GAAI7F,KAAK6M,EAAI5C,GAAGpN,cAC/D1R,IAAE0hB,EAAI5C,GAAGpN,aAAanO,SAExB,MAAMkJ,EAAQiO,GAAIhH,UAAU9O,EAAY8c,EAAI3B,gBAAiB,CAAE1M,sBAAsB,IACjF5G,EACFA,EAAM2E,WAAWU,aAAa4X,EAAUjd,GAExCiO,GAAI/I,YAAY+X,EAAU9kB,OAEvB,CACL8kB,EAAWhP,GAAIhH,UAAUmE,EAAW6J,EAAI3B,iBAGxC,IAAI6J,EAAelP,GAAI3D,eAAec,EAAW6C,GAAIpF,eACrDsU,EAAeA,EAAa9H,OAAOpH,GAAI3D,eAAe2S,EAAUhP,GAAIpF,gBAEpEtV,IAAEM,KAAKspB,EAAc,CAAC3b,EAAKuZ,KACzB9M,GAAInX,OAAOikB,MAIR9M,GAAIlG,UAAUkV,IAAahP,GAAI1K,MAAM0Z,IAAahP,GAAInB,iBAAiBmQ,KAAchP,GAAI9L,QAAQ8a,KACpGA,EAAWhP,GAAIzG,QAAQyV,EAAU,YAKlC,CACL,MAAMxb,EAAOwT,EAAI5C,GAAG9N,WAAW0Q,EAAI3C,IACnC2K,EAAW1pB,IAAE0a,GAAItG,WAAW,GACxBlG,EACFwT,EAAI5C,GAAGhN,aAAa4X,EAAUxb,GAE9BwT,EAAI5C,GAAG/M,YAAY2X,GAIvB/D,GAAM/mB,OAAO8qB,EAAU,GAAGhJ,YAAYnZ,SAASgZ,eAAehF,ICtGlE,MAAMsO,GAAoB,SAASrT,EAAYsT,EAAO/iB,EAAQgjB,GAC5D,MAAMC,EAAc,CAAE,OAAU,EAAG,OAAU,GACvCC,EAAgB,GAChBC,EAAkB,GA+BxB,SAASC,EAAwBC,EAAUC,EAAWC,EAASC,EAAUC,EAAWC,EAAWC,GAC7F,MAAMC,EAAc,CAClB,QAAWL,EACX,SAAYC,EACZ,UAAaC,EACb,UAAaC,EACb,UAAaC,GAEVT,EAAcG,KACjBH,EAAcG,GAAY,IAE5BH,EAAcG,GAAUC,GAAaM,EASvC,SAASC,EAAcC,EAAqBC,EAAcC,EAAoBC,GAC5E,MAAO,CACL,SAAYH,EAAoBN,SAChC,OAAUO,EACV,aAAgB,CACd,SAAYC,EACZ,UAAaC,IAWnB,SAASC,EAAiBb,EAAUC,GAClC,IAAKJ,EAAcG,GACjB,OAAOC,EAET,IAAKJ,EAAcG,GAAUC,GAC3B,OAAOA,EAGT,IAAIa,EAAeb,EACnB,KAAOJ,EAAcG,GAAUc,IAE7B,GADAA,KACKjB,EAAcG,GAAUc,GAC3B,OAAOA,EAWb,SAASC,EAAqBC,EAAKC,GACjC,MAAMhB,EAAYY,EAAiBG,EAAIhB,SAAUiB,EAAKhB,WAChDiB,EAAkBD,EAAKE,QAAU,EACjCC,EAAkBH,EAAKI,QAAU,EACjCC,EAAsBN,EAAIhB,WAAaJ,EAAY2B,QAAUN,EAAKhB,YAAcL,EAAY4B,OAClGzB,EAAwBiB,EAAIhB,SAAUC,EAAWe,EAAKC,EAAMG,EAAgBF,GAAgB,GAG5F,MAAMO,EAAgBR,EAAKS,WAAWL,QAAU/E,SAAS2E,EAAKS,WAAWL,QAAQltB,MAAO,IAAM,EAC9F,GAAIstB,EAAgB,EAClB,IAAK,IAAIE,EAAK,EAAGA,EAAKF,EAAeE,IAAM,CACzC,MAAMC,EAAeZ,EAAIhB,SAAW2B,EACpCE,EAAiBD,EAAc3B,EAAWgB,EAAMK,GAChDvB,EAAwB6B,EAAc3B,EAAWe,EAAKC,GAAM,EAAMC,GAAgB,GAKtF,MAAMY,EAAgBb,EAAKS,WAAWP,QAAU7E,SAAS2E,EAAKS,WAAWP,QAAQhtB,MAAO,IAAM,EAC9F,GAAI2tB,EAAgB,EAClB,IAAK,IAAIC,EAAK,EAAGA,EAAKD,EAAeC,IAAM,CACzC,MAAMC,EAAgBnB,EAAiBG,EAAIhB,SAAWC,EAAY8B,GAClEF,EAAiBb,EAAIhB,SAAUgC,EAAef,EAAMK,GACpDvB,EAAwBiB,EAAIhB,SAAUgC,EAAehB,EAAKC,EAAMG,GAAgB,GAAM,IAa5F,SAASS,EAAiB7B,EAAUC,EAAWgB,EAAMgB,GAC/CjC,IAAaJ,EAAY2B,QAAU3B,EAAY4B,QAAUP,EAAKhB,WAAagB,EAAKhB,WAAaA,IAAcgC,GAC7GrC,EAAY4B,SAsBhB,SAASU,EAA4BjB,GACnC,OAAQvB,GACN,KAAKD,GAAkBC,MAAMyC,OAC3B,GAAIlB,EAAKZ,UACP,OAAOZ,GAAkBiB,aAAa0B,kBAExC,MACF,KAAK3C,GAAkBC,MAAM2C,IAC3B,IAAKpB,EAAKqB,WAAarB,EAAKb,UAC1B,OAAOX,GAAkBiB,aAAa6B,QACjC,GAAItB,EAAKb,UACd,OAAOX,GAAkBiB,aAAa0B,kBAI5C,OAAO3C,GAAkBiB,aAAa8B,WAQxC,SAASC,EAAyBxB,GAChC,OAAQvB,GACN,KAAKD,GAAkBC,MAAMyC,OAC3B,GAAIlB,EAAKZ,UACP,OAAOZ,GAAkBiB,aAAagC,aACjC,GAAIzB,EAAKb,WAAaa,EAAKqB,UAChC,OAAO7C,GAAkBiB,aAAaiC,OAExC,MACF,KAAKlD,GAAkBC,MAAM2C,IAC3B,GAAIpB,EAAKb,UACP,OAAOX,GAAkBiB,aAAagC,aACjC,GAAIzB,EAAKZ,WAAaY,EAAKqB,UAChC,OAAO7C,GAAkBiB,aAAaiC,OAI5C,OAAOlD,GAAkBiB,aAAa6B,QAexC/sB,KAAKotB,cAAgB,WACnB,MAAMC,EAAYnD,IAAUD,GAAkBC,MAAM2C,IAAOzC,EAAY2B,QAAU,EAC3EuB,EAAYpD,IAAUD,GAAkBC,MAAMyC,OAAUvC,EAAY4B,QAAU,EAEpF,IAAIuB,EAAiB,EACjBC,GAAc,EAClB,KAAOA,GAAa,CAClB,MAAMC,EAAeJ,GAAY,EAAKA,EAAWE,EAC3CG,EAAeJ,GAAY,EAAKA,EAAWC,EAC3C/B,EAAMnB,EAAcoD,GAC1B,IAAKjC,EAEH,OADAgC,GAAc,EACPlD,EAET,MAAMmB,EAAOD,EAAIkC,GACjB,IAAKjC,EAEH,OADA+B,GAAc,EACPlD,EAIT,IAAIY,EAAejB,GAAkBiB,aAAaiC,OAClD,OAAQhmB,GACN,KAAK8iB,GAAkB0D,cAAcC,IACnC1C,EAAe+B,EAAyBxB,GACxC,MACF,KAAKxB,GAAkB0D,cAAcE,OACnC3C,EAAewB,EAA4BjB,GAG/CnB,EAAgBjb,KAAK2b,EAAcS,EAAMP,EAAcuC,EAAaC,IACpEH,IAGF,OAAOjD,GAtOF1T,GAAeA,EAAWkX,UAAiD,OAArClX,EAAWkX,QAAQ3lB,eAA+D,OAArCyO,EAAWkX,QAAQ3lB,gBAI3GiiB,EAAY4B,OAASpV,EAAW6T,UAC3B7T,EAAWqG,eAAkBrG,EAAWqG,cAAc6Q,SAA8D,OAAnDlX,EAAWqG,cAAc6Q,QAAQ3lB,cAIvGiiB,EAAY2B,OAASnV,EAAWqG,cAAcuN,SAH5CuD,QAAQC,MAAM,0CAA2CpX,IALzDmX,QAAQC,MAAM,2CAA4CpX,GA6H9D,WACE,MAAMqX,EAAO9D,EAAS8D,KACtB,IAAK,IAAIzD,EAAW,EAAGA,EAAWyD,EAAK7sB,OAAQopB,IAAY,CACzD,MAAM0D,EAAQD,EAAKzD,GAAU0D,MAC7B,IAAK,IAAIzD,EAAY,EAAGA,EAAYyD,EAAM9sB,OAAQqpB,IAChDc,EAAqB0C,EAAKzD,GAAW0D,EAAMzD,KAuD/C0D,IAqDJlE,GAAkBC,MAAQ,CAAE,IAAO,EAAG,OAAU,GAKhDD,GAAkB0D,cAAgB,CAAE,IAAO,EAAG,OAAU,GAKxD1D,GAAkBiB,aAAe,CAAE,OAAU,EAAG,kBAAqB,EAAG,WAAc,EAAG,QAAW,EAAG,aAAgB,GASxG,MAAMkD,GAOnBxE,IAAI9H,EAAKuM,GACP,MAAM5C,EAAO3Q,GAAIvJ,SAASuQ,EAAIvK,iBAAkBuD,GAAIjK,QAC9CvM,EAAQwW,GAAIvJ,SAASka,EAAM3Q,GAAIxK,SAC/B4d,EAAQpT,GAAI3D,eAAe7S,EAAOwW,GAAIjK,QAEtCyd,EAAW9oB,EAAM6oB,EAAU,OAAS,QAAQH,EAAOzC,GACrD6C,GACFvI,GAAM/mB,OAAOsvB,EAAU,GAAG3mB,SAW9B4mB,OAAOzM,EAAKlP,GACV,MAAM6Y,EAAO3Q,GAAIvJ,SAASuQ,EAAIvK,iBAAkBuD,GAAIjK,QAE9C2d,EAAYpuB,IAAEqrB,GAAMhP,QAAQ,MAC5BgS,EAAezuB,KAAK0uB,kBAAkBF,GACtCluB,EAAOF,IAAE,MAAQquB,EAAe,UAIhCE,EAFS,IAAI1E,GAAkBwB,EAAMxB,GAAkBC,MAAM2C,IACjE5C,GAAkB0D,cAAcC,IAAKxtB,IAAEouB,GAAW/R,QAAQ,SAAS,IAC9C2Q,gBAEvB,IAAK,IAAIwB,EAAS,EAAGA,EAASD,EAAQvtB,OAAQwtB,IAAU,CACtD,MAAMC,EAAcF,EAAQC,GACtBE,EAAe9uB,KAAK0uB,kBAAkBG,EAAYlE,UACxD,OAAQkE,EAAY1nB,QAClB,KAAK8iB,GAAkBiB,aAAa6B,QAClCzsB,EAAKe,OAAO,MAAQytB,EAAe,IAAMhU,GAAIvG,MAAQ,SACrD,MACF,KAAK0V,GAAkBiB,aAAagC,aAClC,GAAiB,QAAbta,EAAoB,CAGtB,IAFmBic,EAAYlE,SAAS1Y,OACI4c,EAAYlE,SAASlO,QAAQ,MAAM+N,SAAvC,IAAoDgE,EAAU,GAAGhE,SACnF,CACpB,MAAMuE,EAAQ3uB,IAAE,eAAeiB,OAAOjB,IAAE,MAAQ0uB,EAAe,IAAMhU,GAAIvG,MAAQ,SAASya,WAAW,YAAY1uB,OACjHA,EAAKe,OAAO0tB,GACZ,OAGJ,IAAI9C,EAAgBnF,SAAS+H,EAAYlE,SAASkB,QAAS,IAC3DI,IACA4C,EAAYlE,SAASsE,aAAa,UAAWhD,IAKnD,GAAiB,QAAbrZ,EACF4b,EAAUU,OAAO5uB,OACZ,CAEL,GADwBmrB,EAAKI,QAAU,EACnB,CAClB,MAAMsD,EAAcX,EAAU,GAAGhE,UAAYiB,EAAKI,QAAU,GAE5D,YADAzrB,IAAEA,IAAEouB,GAAWvc,SAAShR,KAAK,MAAMkuB,IAAcC,MAAMhvB,IAAEE,IAG3DkuB,EAAUY,MAAM9uB,IAWpB+uB,OAAOvN,EAAKlP,GACV,MAAM6Y,EAAO3Q,GAAIvJ,SAASuQ,EAAIvK,iBAAkBuD,GAAIjK,QAC9C2a,EAAMprB,IAAEqrB,GAAMhP,QAAQ,MACVrc,IAAEorB,GAAKzV,WACf1G,KAAKmc,GAEf,MAEMmD,EAFS,IAAI1E,GAAkBwB,EAAMxB,GAAkBC,MAAMyC,OACjE1C,GAAkB0D,cAAcC,IAAKxtB,IAAEorB,GAAK/O,QAAQ,SAAS,IACxC2Q,gBAEvB,IAAK,IAAIkC,EAAc,EAAGA,EAAcX,EAAQvtB,OAAQkuB,IAAe,CACrE,MAAMT,EAAcF,EAAQW,GACtBR,EAAe9uB,KAAK0uB,kBAAkBG,EAAYlE,UACxD,OAAQkE,EAAY1nB,QAClB,KAAK8iB,GAAkBiB,aAAa6B,QACjB,UAAbna,EACFxS,IAAEyuB,EAAYlE,UAAUyE,MAAM,MAAQN,EAAe,IAAMhU,GAAIvG,MAAQ,SAEvEnU,IAAEyuB,EAAYlE,UAAUuE,OAAO,MAAQJ,EAAe,IAAMhU,GAAIvG,MAAQ,SAE1E,MACF,KAAK0V,GAAkBiB,aAAagC,aAClC,GAAiB,UAAbta,EAAsB,CACxB,IAAI0Z,EAAgBxF,SAAS+H,EAAYlE,SAASgB,QAAS,IAC3DW,IACAuC,EAAYlE,SAASsE,aAAa,UAAW3C,QAE7ClsB,IAAEyuB,EAAYlE,UAAUuE,OAAO,MAAQJ,EAAe,IAAMhU,GAAIvG,MAAQ,WAalFma,kBAAkB/c,GAChB,IAAI4d,EAAY,GAEhB,IAAK5d,EACH,OAAO4d,EAGT,MAAMC,EAAW7d,EAAGua,YAAc,GAElC,IAAK,IAAI7uB,EAAI,EAAGA,EAAImyB,EAASpuB,OAAQ/D,IACI,OAAnCmyB,EAASnyB,GAAGY,KAAKkK,eAIjBqnB,EAASnyB,GAAGoyB,YACdF,GAAa,IAAMC,EAASnyB,GAAGY,KAAO,KAAQuxB,EAASnyB,GAAGsB,MAAQ,KAItE,OAAO4wB,EASTG,UAAU5N,GACR,MAAM2J,EAAO3Q,GAAIvJ,SAASuQ,EAAIvK,iBAAkBuD,GAAIjK,QAC9C2a,EAAMprB,IAAEqrB,GAAMhP,QAAQ,MACtBkT,EAAUnE,EAAI3rB,SAAS,UAAUijB,MAAM1iB,IAAEqrB,IACzCM,EAASP,EAAI,GAAGhB,SAIhBmE,EAFS,IAAI1E,GAAkBwB,EAAMxB,GAAkBC,MAAM2C,IACjE5C,GAAkB0D,cAAcE,OAAQztB,IAAEorB,GAAK/O,QAAQ,SAAS,IAC3C2Q,gBAEvB,IAAK,IAAIkC,EAAc,EAAGA,EAAcX,EAAQvtB,OAAQkuB,IAAe,CACrE,IAAKX,EAAQW,GACX,SAGF,MAAM3E,EAAWgE,EAAQW,GAAa3E,SAChCiF,EAAkBjB,EAAQW,GAAaO,aACvCC,EAAcnF,EAASkB,SAAWlB,EAASkB,QAAU,EAC3D,IAAII,EAAiB6D,EAAchJ,SAAS6D,EAASkB,QAAS,IAAM,EACpE,OAAQ8C,EAAQW,GAAanoB,QAC3B,KAAK8iB,GAAkBiB,aAAaiC,OAClC,SACF,KAAKlD,GAAkBiB,aAAa6B,QAClC,MAAMgD,EAAUvE,EAAIld,KAAK,MAAM,GAC/B,IAAKyhB,EAAW,SAChB,MAAMC,EAAWxE,EAAI,GAAG0C,MAAMyB,GAC1BG,IACE7D,EAAgB,GAClBA,IACA8D,EAAQ7d,aAAa8d,EAAUD,EAAQ7B,MAAMyB,IAC7CI,EAAQ7B,MAAMyB,GAASV,aAAa,UAAWhD,GAC/C8D,EAAQ7B,MAAMyB,GAASte,UAAY,IACR,IAAlB4a,IACT8D,EAAQ7d,aAAa8d,EAAUD,EAAQ7B,MAAMyB,IAC7CI,EAAQ7B,MAAMyB,GAASM,gBAAgB,WACvCF,EAAQ7B,MAAMyB,GAASte,UAAY,KAGvC,SACF,KAAK4Y,GAAkBiB,aAAa0B,kBAC9BkD,IACE7D,EAAgB,GAClBA,IACAtB,EAASsE,aAAa,UAAWhD,GAC7B2D,EAAgBpF,WAAauB,GAAUpB,EAASF,YAAckF,IAAWhF,EAAStZ,UAAY,KACvE,IAAlB4a,IACTtB,EAASsF,gBAAgB,WACrBL,EAAgBpF,WAAauB,GAAUpB,EAASF,YAAckF,IAAWhF,EAAStZ,UAAY,MAGtG,SACF,KAAK4Y,GAAkBiB,aAAa8B,WAElC,UAGNxB,EAAI7nB,SASNusB,UAAUpO,GACR,MAAM2J,EAAO3Q,GAAIvJ,SAASuQ,EAAIvK,iBAAkBuD,GAAIjK,QAC9C2a,EAAMprB,IAAEqrB,GAAMhP,QAAQ,MACtBkT,EAAUnE,EAAI3rB,SAAS,UAAUijB,MAAM1iB,IAAEqrB,IAIzCkD,EAFS,IAAI1E,GAAkBwB,EAAMxB,GAAkBC,MAAMyC,OACjE1C,GAAkB0D,cAAcE,OAAQztB,IAAEorB,GAAK/O,QAAQ,SAAS,IAC3C2Q,gBAEvB,IAAK,IAAIkC,EAAc,EAAGA,EAAcX,EAAQvtB,OAAQkuB,IACtD,GAAKX,EAAQW,GAGb,OAAQX,EAAQW,GAAanoB,QAC3B,KAAK8iB,GAAkBiB,aAAaiC,OAClC,SACF,KAAKlD,GAAkBiB,aAAa0B,kBAClC,MAAMjC,EAAWgE,EAAQW,GAAa3E,SAEtC,GADoBA,EAASgB,SAAWhB,EAASgB,QAAU,EAC3C,CACd,IAAIW,EAAiB3B,EAASgB,QAAW7E,SAAS6D,EAASgB,QAAS,IAAM,EACtEW,EAAgB,GAClBA,IACA3B,EAASsE,aAAa,UAAW3C,GAC7B3B,EAASF,YAAckF,IAAWhF,EAAStZ,UAAY,KAChC,IAAlBib,IACT3B,EAASsF,gBAAgB,WACrBtF,EAASF,YAAckF,IAAWhF,EAAStZ,UAAY,KAG/D,SACF,KAAK4Y,GAAkBiB,aAAa8B,WAClClS,GAAInX,OAAOgrB,EAAQW,GAAa3E,UAAU,GAC1C,UAYRwF,YAAYC,EAAUC,EAAUvwB,GAC9B,MAAMwwB,EAAM,GACZ,IAAIC,EACJ,IAAK,IAAIC,EAAS,EAAGA,EAASJ,EAAUI,IACtCF,EAAIjhB,KAAK,OAASyL,GAAIvG,MAAQ,SAEhCgc,EAASD,EAAIrjB,KAAK,IAElB,MAAMwjB,EAAM,GACZ,IAAIC,EACJ,IAAK,IAAIC,EAAS,EAAGA,EAASN,EAAUM,IACtCF,EAAIphB,KAAK,OAASkhB,EAAS,SAE7BG,EAASD,EAAIxjB,KAAK,IAClB,MAAM2jB,EAASxwB,IAAE,UAAYswB,EAAS,YAKtC,OAJI5wB,GAAWA,EAAQ+wB,gBACrBD,EAAOpwB,SAASV,EAAQ+wB,gBAGnBD,EAAO,GAShBE,YAAYhP,GACV,MAAM2J,EAAO3Q,GAAIvJ,SAASuQ,EAAIvK,iBAAkBuD,GAAIjK,QACpDzQ,IAAEqrB,GAAMhP,QAAQ,SAAS9Y,UCxjB7B,IAAImF,GACAmI,EAAIpI,gBACNC,GAAaxL,OAAOwL,YCDtB,MACMioB,GAAc,iFCwBpB3wB,IAAEsB,WAAatB,IAAEwB,OAAOxB,IAAEsB,WAAY,CACpCsvB,QAAS,SACT7V,QAAS,GAETL,IAAKA,GACLiL,MAAOA,GACPvgB,MAAOA,EAEP1F,QAAS,CACP6e,SAAUve,IAAEsB,WAAWC,KAAK,SAC5Bka,SAAS,EACTje,QAAS,CACP,OCtBS,MACb+B,YAAYqK,GACVhK,KAAKgK,QAAUA,EAEfhK,KAAK8Z,MAAQ9P,EAAQgQ,WAAW8E,KAChC9e,KAAKixB,QAAUjnB,EAAQgQ,WAAWiB,OAClCjb,KAAK2lB,UAAY3b,EAAQgQ,WAAW2B,SACpC3b,KAAKF,QAAUkK,EAAQlK,QACvBE,KAAK2B,KAAO3B,KAAKF,QAAQ6e,SAEzB3e,KAAK2b,SAAW3b,KAAK2lB,UAAU,GAC/B3lB,KAAKkxB,UAAY,KACjBlxB,KAAKimB,SAAW,KAEhBjmB,KAAK+E,MAAQ,IAAIshB,GACjBrmB,KAAKsE,MAAQ,IAAI8pB,GACjBpuB,KAAKmxB,OAAS,IAAI3H,GAAOxf,GACzBhK,KAAKypB,OAAS,IAAI5B,GAClB7nB,KAAKuH,QAAU,IAAIme,GAAQ1lB,KAAK2lB,WAEhC3lB,KAAKgK,QAAQ4E,KAAK,YAAa5O,KAAK2B,KAAKgE,KAAK6B,MAC9CxH,KAAKgK,QAAQ4E,KAAK,YAAa5O,KAAK2B,KAAKgE,KAAK8B,MAC9CzH,KAAKgK,QAAQ4E,KAAK,WAAY5O,KAAK2B,KAAKgE,KAAKikB,KAC7C5pB,KAAKgK,QAAQ4E,KAAK,aAAc5O,KAAK2B,KAAKgE,KAAKyrB,OAC/CpxB,KAAKgK,QAAQ4E,KAAK,uBAAwB5O,KAAK2B,KAAKgE,KAAKkkB,iBACzD7pB,KAAKgK,QAAQ4E,KAAK,yBAA0B5O,KAAK2B,KAAKgE,KAAKmiB,mBAC3D9nB,KAAKgK,QAAQ4E,KAAK,2BAA4B5O,KAAK2B,KAAKgE,KAAKqiB,qBAC7DhoB,KAAKgK,QAAQ4E,KAAK,cAAe5O,KAAK2B,KAAKgE,KAAKK,QAChDhG,KAAKgK,QAAQ4E,KAAK,eAAgB5O,KAAK2B,KAAKgE,KAAKI,SACjD/F,KAAKgK,QAAQ4E,KAAK,kBAAmB5O,KAAK2B,KAAKgE,KAAK0rB,YACpDrxB,KAAKgK,QAAQ4E,KAAK,4BAA6B5O,KAAK2B,KAAKgE,KAAK2rB,sBAC9DtxB,KAAKgK,QAAQ4E,KAAK,gBAAiB5O,KAAK2B,KAAKgE,KAAKsC,UAGlD,MAAMspB,EAAW,CACf,OAAQ,SAAU,YAAa,gBAAiB,cAAe,YAC/D,cAAe,gBAAiB,eAAgB,cAChD,cAAe,eAAgB,aAGjC,IAAK,IAAIljB,EAAM,EAAGG,EAAM+iB,EAASnwB,OAAQiN,EAAMG,EAAKH,IAClDrO,KAAKuxB,EAASljB,IAAQ,CAAEmjB,GACd7yB,IACNqB,KAAKyxB,gBACLxnB,SAASynB,YAAYF,GAAM,EAAO7yB,GAClCqB,KAAK2xB,cAAa,IAJA,CAMnBJ,EAASljB,IACZrO,KAAKgK,QAAQ4E,KAAK,QAAU2iB,EAASljB,GAAMrO,KAAK2B,KAAKgE,KAAK4rB,EAASljB,KAGrErO,KAAKiI,SAAWjI,KAAK4xB,YAAajzB,GACzBqB,KAAK6xB,YAAY,cAAe5gB,EAAIjJ,cAAcrJ,KAG3DqB,KAAK6mB,SAAW7mB,KAAK4xB,YAAajzB,IAChC,MAAMmzB,EAAO9xB,KAAK+xB,eAAe,kBACjC,OAAO/xB,KAAK6xB,YAAY,YAAalzB,EAAQmzB,KAG/C9xB,KAAKgyB,aAAehyB,KAAK4xB,YAAajzB,IACpC,MAAM2D,EAAOtC,KAAK+xB,eAAe,aACjC,OAAO/xB,KAAK6xB,YAAY,YAAavvB,EAAO3D,KAG9C,IAAK,IAAI0P,EAAM,EAAGA,GAAO,EAAGA,IAC1BrO,KAAK,UAAYqO,GAAO,CAAEA,GACjB,KACLrO,KAAKiyB,YAAY,IAAM5jB,IAFH,CAIrBA,GACHrO,KAAKgK,QAAQ4E,KAAK,eAAiBP,EAAKrO,KAAK2B,KAAKgE,KAAK,UAAY0I,IAGrErO,KAAK6pB,gBAAkB7pB,KAAK4xB,YAAY,KACtC5xB,KAAKmxB,OAAOtH,gBAAgB7pB,KAAK2b,YAGnC3b,KAAK8nB,kBAAoB9nB,KAAK4xB,YAAY,KACxC5xB,KAAKypB,OAAO3B,kBAAkB9nB,KAAK2b,YAGrC3b,KAAKgoB,oBAAsBhoB,KAAK4xB,YAAY,KAC1C5xB,KAAKypB,OAAOzB,oBAAoBhoB,KAAK2b,YAGvC3b,KAAKgG,OAAShG,KAAK4xB,YAAY,KAC7B5xB,KAAKypB,OAAOzjB,OAAOhG,KAAK2b,YAG1B3b,KAAK+F,QAAU/F,KAAK4xB,YAAY,KAC9B5xB,KAAKypB,OAAO1jB,QAAQ/F,KAAK2b,YAQ3B3b,KAAKoiB,WAAapiB,KAAK4xB,YAAahiB,IAClC,GAAI5P,KAAKkyB,UAAU9xB,IAAEwP,GAAMyI,OAAOjX,QAChC,OAEUpB,KAAKmyB,eACb/P,WAAWxS,GACf5P,KAAKoyB,aAAarM,GAAM3B,oBAAoBxU,GAAMjI,YAOpD3H,KAAKqyB,WAAaryB,KAAK4xB,YAAavZ,IAClC,GAAIrY,KAAKkyB,UAAU7Z,EAAKjX,QACtB,OAEF,MACMkxB,EADMtyB,KAAKmyB,eACI/P,WAAWtH,GAAI1C,WAAWC,IAC/CrY,KAAKoyB,aAAarM,GAAM/mB,OAAOszB,EAAUxX,GAAI5J,WAAWohB,IAAW3qB,YAOrE3H,KAAKqiB,UAAYriB,KAAK4xB,YAAahyB,IACjC,GAAII,KAAKkyB,UAAUtyB,EAAOwB,QACxB,OAEFxB,EAASI,KAAKgK,QAAQ2B,OAAO,kBAAmB/L,GAChD,MAAMS,EAAWL,KAAKmyB,eAAe9P,UAAUziB,GAC/CI,KAAKoyB,aAAarM,GAAM3B,oBAAoB5e,EAAMuI,KAAK1N,IAAWsH,YAQpE3H,KAAKiyB,YAAcjyB,KAAK4xB,YAAY,CAAC9D,EAASvR,KAC5C,MAAMgW,EAAqBvyB,KAAKF,QAAQgc,UAAUyW,mBAC9CA,EACFA,EAAmB10B,KAAKmC,KAAMuc,EAASvc,KAAKgK,QAAShK,KAAKwyB,eAE1DxyB,KAAKwyB,cAAc1E,EAASvR,KAOhCvc,KAAKsxB,qBAAuBtxB,KAAK4xB,YAAY,KAC3C,MAAMa,EAASzyB,KAAKmyB,eAAe/P,WAAWtH,GAAI9b,OAAO,OACrDyzB,EAAO3gB,aACT9R,KAAKoyB,aAAarM,GAAM/mB,OAAOyzB,EAAO3gB,YAAa,GAAGgP,YAAYnZ,YAQtE3H,KAAK0nB,WAAa1nB,KAAK4xB,YAAajzB,IAClCqB,KAAK+E,MAAMgiB,UAAU/mB,KAAKmyB,eAAgB,CACxCzK,WAAY/oB,MAShBqB,KAAK0yB,WAAa1yB,KAAK4xB,YAAae,IAClC,IAAIC,EAAUD,EAASjvB,IACvB,MAAMmvB,EAAWF,EAASta,KACpBya,EAAcH,EAASG,YACvBC,EAAgBJ,EAASI,cAC/B,IAAIjR,EAAM6Q,EAAS5M,OAAS/lB,KAAKmyB,eACjC,MAAMa,EAAuBH,EAASzxB,OAAS0gB,EAAIS,WAAWnhB,OAC9D,GAAI4xB,EAAuB,GAAKhzB,KAAKkyB,UAAUc,GAC7C,OAEF,MAAMC,EAAgBnR,EAAIS,aAAesQ,EAGlB,iBAAZD,IACTA,EAAUA,EAAQ5Z,QAGhBhZ,KAAKF,QAAQozB,aACfN,EAAU5yB,KAAKF,QAAQozB,aAAaN,GAC3BG,IAETH,EAAU,oCAAoCrqB,KAAKqqB,GAC/CA,EAAU5yB,KAAKF,QAAQqzB,gBAAkBP,GAG/C,IAAIQ,EAAU,GACd,GAAIH,EAAe,CACjBnR,EAAMA,EAAID,iBACV,MAAM+F,EAAS9F,EAAIM,WAAWhiB,IAAE,MAAQyyB,EAAW,QAAQ,IAC3DO,EAAQ/jB,KAAKuY,QAEbwL,EAAUpzB,KAAK+E,MAAMiiB,WAAWlF,EAAK,CACnC/R,SAAU,IACVkX,sBAAsB,EACtBC,qBAAqB,IAIzB9mB,IAAEM,KAAK0yB,EAAS,CAAC/kB,EAAKuZ,KACpBxnB,IAAEwnB,GAAQ/mB,KAAK,OAAQ+xB,GACnBE,EACF1yB,IAAEwnB,GAAQ/mB,KAAK,SAAU,UAEzBT,IAAEwnB,GAAQoH,WAAW,YAIzB,MACMpY,EADamP,GAAM5B,qBAAqB3e,EAAMqI,KAAKulB,IAC3BjT,gBAExBtJ,EADWkP,GAAM3B,oBAAoB5e,EAAMuI,KAAKqlB,IAC5BhT,cAE1BpgB,KAAKoyB,aACHrM,GAAM/mB,OACJ4X,EAAWhH,KACXgH,EAAWpE,OACXqE,EAASjH,KACTiH,EAASrE,QACT7K,YAWN3H,KAAKqG,MAAQrG,KAAK4xB,YAAayB,IAC7B,MAAMC,EAAYD,EAAUC,UACtBC,EAAYF,EAAUE,UAExBD,GAAarpB,SAASynB,YAAY,aAAa,EAAO4B,GACtDC,GAAatpB,SAASynB,YAAY,aAAa,EAAO6B,KAQ5DvzB,KAAKszB,UAAYtzB,KAAK4xB,YAAayB,IACjCppB,SAASynB,YAAY,gBAAgB,GAAO,GAC5CznB,SAASynB,YAAY,aAAa,EAAO2B,KAQ3CrzB,KAAKwzB,YAAcxzB,KAAK4xB,YAAa6B,IACnC,MAAMC,EAAYD,EAAI5mB,MAAM,KAEhB7M,KAAKmyB,eAAetQ,iBAC5BO,WAAWpiB,KAAKsE,MAAM6rB,YAAYuD,EAAU,GAAIA,EAAU,GAAI1zB,KAAKF,YAMzEE,KAAK2zB,YAAc3zB,KAAK4xB,YAAY,KAClC,IAAIrV,EAAUnc,IAAEJ,KAAK4zB,iBAAiB3hB,SAClCsK,EAAQE,QAAQ,UAAUrb,OAC5Bmb,EAAQE,QAAQ,UAAU9Y,SAE1B4Y,EAAUnc,IAAEJ,KAAK4zB,iBAAiBC,SAEpC7zB,KAAKgK,QAAQuR,aAAa,eAAgBgB,EAASvc,KAAK2lB,aAQ1D3lB,KAAK8zB,QAAU9zB,KAAK4xB,YAAajzB,IAC/B,MAAM4d,EAAUnc,IAAEJ,KAAK4zB,iBACvBrX,EAAQwX,YAAY,kBAA6B,SAAVp1B,GACvC4d,EAAQwX,YAAY,mBAA8B,UAAVp1B,GACxC4d,EAAQmK,IAAI,QAAoB,SAAV/nB,EAAmB,GAAKA,KAOhDqB,KAAKg0B,OAASh0B,KAAK4xB,YAAajzB,IAC9B,MAAM4d,EAAUnc,IAAEJ,KAAK4zB,iBAET,KADdj1B,EAAQgK,WAAWhK,IAEjB4d,EAAQmK,IAAI,QAAS,IAErBnK,EAAQmK,IAAI,CACVnc,MAAe,IAAR5L,EAAc,IACrBuD,OAAQ,OAMhBiY,aAEEna,KAAK2lB,UAAU5kB,GAAG,UAAYqb,IAgB5B,GAfIA,EAAMqI,UAAYxlB,GAAI4b,KAAK+J,OAC7B5kB,KAAKgK,QAAQuR,aAAa,QAASa,GAErCpc,KAAKgK,QAAQuR,aAAa,UAAWa,GAGrCpc,KAAKimB,SAAWjmB,KAAKuH,QAAQue,eAExB1J,EAAM6X,uBACLj0B,KAAKF,QAAQkH,UACfhH,KAAKk0B,aAAa9X,GAElBpc,KAAKm0B,gCAAgC/X,IAGrCpc,KAAKkyB,UAAU,EAAG9V,GAAQ,CAC5B,MAAM8U,EAAYlxB,KAAKmyB,eACvB,GAAIjB,EAAU7R,GAAK6R,EAAU/R,IAAO,EAClC,OAAO,EAGXnf,KAAKoyB,iBACJrxB,GAAG,QAAUqb,IACdpc,KAAKoyB,eACLpyB,KAAKgK,QAAQuR,aAAa,QAASa,KAClCrb,GAAG,QAAUqb,IACdpc,KAAKoyB,eACLpyB,KAAKgK,QAAQuR,aAAa,QAASa,KAClCrb,GAAG,OAASqb,IACbpc,KAAKgK,QAAQuR,aAAa,OAAQa,KACjCrb,GAAG,YAAcqb,IAClBpc,KAAKgK,QAAQuR,aAAa,YAAaa,KACtCrb,GAAG,UAAYqb,IAChBpc,KAAKoyB,eACLpyB,KAAKuH,QAAQ4e,aACbnmB,KAAKgK,QAAQuR,aAAa,UAAWa,KACpCrb,GAAG,SAAWqb,IACfpc,KAAKgK,QAAQuR,aAAa,SAAUa,KACnCrb,GAAG,QAAUqb,IACdpc,KAAKoyB,eACLpyB,KAAKgK,QAAQuR,aAAa,QAASa,KAClCrb,GAAG,QAAUqb,IAEVpc,KAAKkyB,UAAU,IAAMlyB,KAAKimB,UAC5BjmB,KAAKuH,QAAQye,cAAchmB,KAAKimB,YAIpCjmB,KAAK2lB,UAAU9kB,KAAK,aAAcb,KAAKF,QAAQs0B,YAE/Cp0B,KAAK2lB,UAAU9kB,KAAK,cAAeb,KAAKF,QAAQs0B,YAE5Cp0B,KAAKF,QAAQu0B,gBACfr0B,KAAK2lB,UAAU9kB,KAAK,cAAc,GAIpCb,KAAK2lB,UAAUrlB,KAAKwa,GAAIxa,KAAKN,KAAK8Z,QAAUgB,GAAItG,WAEhDxU,KAAK2lB,UAAU5kB,GAAGkQ,EAAI/H,eAAgBiE,EAAKD,SAAS,KAClDlN,KAAKgK,QAAQuR,aAAa,SAAUvb,KAAK2lB,UAAUrlB,OAAQN,KAAK2lB,YAC/D,KAEH3lB,KAAKixB,QAAQlwB,GAAG,UAAYqb,IAC1Bpc,KAAKgK,QAAQuR,aAAa,UAAWa,KACpCrb,GAAG,WAAaqb,IACjBpc,KAAKgK,QAAQuR,aAAa,WAAYa,KAGnCpc,KAAKF,QAAQw0B,UACZt0B,KAAKF,QAAQyK,OACfvK,KAAKixB,QAAQsD,WAAWv0B,KAAKF,QAAQyK,OAEnCvK,KAAKF,QAAQoC,QACflC,KAAK2lB,UAAUtM,YAAYrZ,KAAKF,QAAQoC,QAEtClC,KAAKF,QAAQ00B,WACfx0B,KAAK2lB,UAAUe,IAAI,aAAc1mB,KAAKF,QAAQ00B,WAE5Cx0B,KAAKF,QAAQ20B,WACfz0B,KAAK2lB,UAAUe,IAAI,aAAc1mB,KAAKF,QAAQ20B,YAIlDz0B,KAAKuH,QAAQ4e,aACbnmB,KAAKoyB,eAGP7X,UACEva,KAAK2lB,UAAUjM,MAGjBwa,aAAa9X,GACX,MAAMsY,EAAS10B,KAAKF,QAAQ40B,OAAOzjB,EAAI9H,MAAQ,MAAQ,MACjDqQ,EAAO,GAET4C,EAAMuY,SAAWnb,EAAKnK,KAAK,OAC3B+M,EAAMwY,UAAYxY,EAAMyY,QAAUrb,EAAKnK,KAAK,QAC5C+M,EAAM0Y,UAAYtb,EAAKnK,KAAK,SAEhC,MAAM0lB,EAAU91B,GAAIwmB,aAAarJ,EAAMqI,SACnCsQ,GACFvb,EAAKnK,KAAK0lB,GAGZ,MAAMC,EAAYN,EAAOlb,EAAKvM,KAAK,MAEnB,QAAZ8nB,GAAsB/0B,KAAKF,QAAQm1B,WAE5BD,GAC8B,IAAnCh1B,KAAKgK,QAAQ2B,OAAOqpB,IACtB5Y,EAAME,iBAECrd,GAAIulB,OAAOpI,EAAMqI,UAC1BzkB,KAAK2xB,eANL3xB,KAAK2xB,eAUTwC,gCAAgC/X,IAEzBA,EAAMwY,SAAWxY,EAAMuY,UAC1BnvB,EAAM0I,SAAS,CAAC,GAAI,GAAI,IAAKkO,EAAMqI,UACnCrI,EAAME,iBAIV4V,UAAUgD,EAAK9Y,GAGb,OAFA8Y,EAAMA,GAAO,QAEQ,IAAV9Y,KACLnd,GAAI8lB,OAAO3I,EAAMqI,UACjBxlB,GAAImmB,aAAahJ,EAAMqI,UACtBrI,EAAMwY,SAAWxY,EAAMuY,SACxBnvB,EAAM0I,SAAS,CAACjP,GAAI4b,KAAK6J,UAAWzlB,GAAI4b,KAAKiK,QAAS1I,EAAMqI,aAK9DzkB,KAAKF,QAAQq1B,cAAgB,GAC1Bn1B,KAAK2lB,UAAUtN,OAAOjX,OAAS8zB,EAAOl1B,KAAKF,QAAQq1B,eAU5D1qB,cAGE,OAFAzK,KAAKgf,QACLhf,KAAKoyB,eACEpyB,KAAKmyB,eAGdC,aAAatQ,GACPA,EACF9hB,KAAKkxB,UAAYpP,GAEjB9hB,KAAKkxB,UAAYnL,GAAM/mB,OAAOgB,KAAK2b,UAE2B,IAA1Dvb,IAAEJ,KAAKkxB,UAAUhS,IAAIzC,QAAQ,kBAAkBrb,SACjDpB,KAAKkxB,UAAYnL,GAAMtC,sBAAsBzjB,KAAK2b,YAKxDwW,eAIE,OAHKnyB,KAAKkxB,WACRlxB,KAAKoyB,eAEApyB,KAAKkxB,UAUdkE,UAAUC,GACJA,GACFr1B,KAAKmyB,eAAezU,WAAW/V,SASnC2tB,eACMt1B,KAAKkxB,YACPlxB,KAAKkxB,UAAUvpB,SACf3H,KAAKgf,SAITuW,WAAW3lB,GACT5P,KAAK2lB,UAAUllB,KAAK,SAAUmP,GAGhC4lB,cACEx1B,KAAK2lB,UAAUlL,WAAW,UAG5BmZ,gBACE,OAAO5zB,KAAK2lB,UAAUllB,KAAK,UAS7BsxB,eACE,IAAIjQ,EAAMiE,GAAM/mB,SAIhB,OAHI8iB,IACFA,EAAMA,EAAIhB,aAELgB,EAAM9hB,KAAK+E,MAAMuS,QAAQwK,GAAO9hB,KAAK+E,MAAM4hB,SAAS3mB,KAAK2lB,WASlE8P,cAAct1B,GACZ,OAAOH,KAAK+E,MAAM4hB,SAASxmB,GAM7BqH,OACExH,KAAKgK,QAAQuR,aAAa,iBAAkBvb,KAAK2lB,UAAUrlB,QAC3DN,KAAKuH,QAAQC,OACbxH,KAAKgK,QAAQuR,aAAa,SAAUvb,KAAK2lB,UAAUrlB,OAAQN,KAAK2lB,WAMlES,SACEpmB,KAAKgK,QAAQuR,aAAa,iBAAkBvb,KAAK2lB,UAAUrlB,QAC3DN,KAAKuH,QAAQ6e,SACbpmB,KAAKgK,QAAQuR,aAAa,SAAUvb,KAAK2lB,UAAUrlB,OAAQN,KAAK2lB,WAMlEle,OACEzH,KAAKgK,QAAQuR,aAAa,iBAAkBvb,KAAK2lB,UAAUrlB,QAC3DN,KAAKuH,QAAQE,OACbzH,KAAKgK,QAAQuR,aAAa,SAAUvb,KAAK2lB,UAAUrlB,OAAQN,KAAK2lB,WAMlE8L,gBACEzxB,KAAKgK,QAAQuR,aAAa,iBAAkBvb,KAAK2lB,UAAUrlB,QAE3DN,KAAKgf,QAOP2S,aAAa+D,GACX11B,KAAK21B,mBACL31B,KAAKuH,QAAQ4e,aACRuP,GACH11B,KAAKgK,QAAQuR,aAAa,SAAUvb,KAAK2lB,UAAUrlB,OAAQN,KAAK2lB,WAOpEiE,MACE,MAAM9H,EAAM9hB,KAAKmyB,eACjB,GAAIrQ,EAAIV,eAAiBU,EAAIpC,WAC3B1f,KAAKsE,MAAMslB,IAAI9H,OACV,CACL,GAA6B,IAAzB9hB,KAAKF,QAAQ81B,QACf,OAAO,EAGJ51B,KAAKkyB,UAAUlyB,KAAKF,QAAQ81B,WAC/B51B,KAAKyxB,gBACLzxB,KAAKmxB,OAAOzH,UAAU5H,EAAK9hB,KAAKF,QAAQ81B,SACxC51B,KAAK2xB,iBAQXP,QACE,MAAMtP,EAAM9hB,KAAKmyB,eACjB,GAAIrQ,EAAIV,eAAiBU,EAAIpC,WAC3B1f,KAAKsE,MAAMslB,IAAI9H,GAAK,QAEpB,GAA6B,IAAzB9hB,KAAKF,QAAQ81B,QACf,OAAO,EAQbhE,YAAYhoB,GACV,OAAO,WACL5J,KAAKyxB,gBACL7nB,EAAG0B,MAAMtL,KAAMsB,WACftB,KAAK2xB,gBAWTkE,YAAYC,EAAKC,GACf,OCjoBwBryB,EDioBLoyB,EChoBd11B,IAAE41B,SAAUC,IACjB,MAAMC,EAAO91B,IAAE,SAEf81B,EAAKC,IAAI,OAAQ,KACfD,EAAKxc,IAAI,eACTuc,EAASG,QAAQF,KAChBC,IAAI,cAAe,KACpBD,EAAKxc,IAAI,QAAQma,SACjBoC,EAASI,OAAOH,KACfxP,IAAI,CACL4P,QAAS,SACRC,SAAStsB,SAASkT,MAAMtc,KAAK,MAAO6C,KACtC8yB,WDonB8BC,KAAMC,IACnC12B,KAAKyxB,gBAEgB,mBAAVsE,EACTA,EAAMW,IAEe,iBAAVX,GACTW,EAAO71B,KAAK,gBAAiBk1B,GAE/BW,EAAOhQ,IAAI,QAAS1G,KAAKC,IAAIjgB,KAAK2lB,UAAUpb,QAASmsB,EAAOnsB,WAG9DmsB,EAAOC,OACP32B,KAAKmyB,eAAe/P,WAAWsU,EAAO,IACtC12B,KAAKoyB,aAAarM,GAAM3B,oBAAoBsS,EAAO,IAAI/uB,UACvD3H,KAAK2xB,iBACJzmB,KAAM+X,IACPjjB,KAAKgK,QAAQuR,aAAa,qBAAsB0H,KClpB/C,IAAqBvf,ED0pB1BkzB,sBAAsBC,GACpBz2B,IAAEM,KAAKm2B,EAAO,CAACxoB,EAAKyoB,KAClB,MAAMC,EAAWD,EAAK74B,KAClB+B,KAAKF,QAAQk3B,sBAAwBh3B,KAAKF,QAAQk3B,qBAAuBF,EAAKx0B,KAChFtC,KAAKgK,QAAQuR,aAAa,qBAAsBvb,KAAK2B,KAAKa,MAAMiB,sBCprBjE,SAA2BqzB,GAChC,OAAO12B,IAAE41B,SAAUC,IACjB71B,IAAEwB,OAAO,IAAIq1B,WAAc,CACzBC,OAASjU,IACP,MAAMkU,EAAUlU,EAAEzG,OAAOzN,OACzBknB,EAASG,QAAQe,IAEnBC,QAAUC,IACRpB,EAASI,OAAOgB,MAEjBC,cAAcR,KAChBN,UD2qBGe,CAAkBT,GAAML,KAAMU,GACrBn3B,KAAK61B,YAAYsB,EAASJ,IAChC7rB,KAAK,KACNlL,KAAKgK,QAAQuR,aAAa,0BAUlCic,uBAAuBX,GACH72B,KAAKF,QAAQgc,UAEjB2b,cACZz3B,KAAKgK,QAAQuR,aAAa,eAAgBsb,GAG1C72B,KAAK42B,sBAAsBC,GAQ/Ba,kBACE,IAAI5V,EAAM9hB,KAAKmyB,eAOf,OAJIrQ,EAAIrC,eACNqC,EAAMiE,GAAMrC,eAAe5I,GAAIvJ,SAASuQ,EAAI5C,GAAIpE,GAAIhK,YAG/CgR,EAAIS,WAGbiQ,cAAc1E,EAASvR,GAKrB,GAHAtS,SAASynB,YAAY,eAAe,EAAOzgB,EAAI3I,OAAS,IAAMwlB,EAAU,IAAMA,GAG1EvR,GAAWA,EAAQnb,SAEjBmb,EAAQ,GAAGuR,QAAQ9gB,gBAAkB8gB,EAAQ9gB,gBAC/CuP,EAAUA,EAAQtb,KAAK6sB,IAGrBvR,GAAWA,EAAQnb,QAAQ,CAC7B,MAAMb,EAAYgc,EAAQ,GAAGhc,WAAa,GAC1C,GAAIA,EAAW,CACb,MAAMo3B,EAAe33B,KAAKyK,cAEVrK,IAAE,CAACu3B,EAAazY,GAAIyY,EAAavY,KAAK3C,QAAQqR,GACtDttB,SAASD,KAMzB8wB,aACErxB,KAAKiyB,YAAY,KAGnBJ,YAAYrV,EAAQ7d,GAClB,MAAMmjB,EAAM9hB,KAAKmyB,eAEjB,GAAY,KAARrQ,EAAY,CACd,MAAM8V,EAAQ53B,KAAK+E,MAAMiiB,WAAWlF,GAMpC,GALA9hB,KAAKixB,QAAQhwB,KAAK,uBAAuBX,KAAK,IAC9CF,IAAEw3B,GAAOlR,IAAIlK,EAAQ7d,GAIjBmjB,EAAIV,cAAe,CACrB,MAAMyW,EAAYryB,EAAMqI,KAAK+pB,GACzBC,IAAc/c,GAAI5J,WAAW2mB,KAC/BA,EAAUxmB,UAAYyJ,GAAIxG,qBAC1ByR,GAAM3B,oBAAoByT,EAAUla,YAAYhW,SAChD3H,KAAKoyB,eACLpyB,KAAK2lB,UAAUllB,KApwBP,QAowBuBo3B,SAG9B,CACL,MAAMC,EAAmB13B,IAAE4a,MAC3Bhb,KAAKixB,QAAQhwB,KAAK,uBAAuBX,KAAK,+BAAiCw3B,EAAmB,8BAAgC93B,KAAK2B,KAAKiG,OAAOC,YAAc,UACjK8F,YAAW,WAAavN,IAAE,uBAAyB03B,GAAkBn0B,WAAa,MAStFM,SACE,IAAI6d,EAAM9hB,KAAKmyB,eACf,GAAIrQ,EAAIrC,aAAc,CACpB,MAAMmI,EAAS9M,GAAIvJ,SAASuQ,EAAI5C,GAAIpE,GAAIhK,UACxCgR,EAAMiE,GAAMrC,eAAekE,GAC3B9F,EAAIna,SACJ3H,KAAKoyB,eAELpyB,KAAKyxB,gBACLxnB,SAASynB,YAAY,UACrB1xB,KAAK2xB,gBAaToG,cACE,MAAMjW,EAAM9hB,KAAKmyB,eAAe3Q,OAAO1G,GAAIhK,UAErCknB,EAAU53B,IAAEoF,EAAMqI,KAAKiU,EAAIjQ,MAAMiJ,GAAIhK,YACrC6hB,EAAW,CACf5M,MAAOjE,EACPzJ,KAAMyJ,EAAIS,WACV7e,IAAKs0B,EAAQ52B,OAAS42B,EAAQn3B,KAAK,QAAU,IAS/C,OALIm3B,EAAQ52B,SAEVuxB,EAASG,YAAyC,WAA3BkF,EAAQn3B,KAAK,WAG/B8xB,EAGTpE,OAAO3b,GACL,MAAMkP,EAAM9hB,KAAKmyB,aAAanyB,KAAK2lB,WAC/B7D,EAAIV,eAAiBU,EAAIpC,aAC3B1f,KAAKyxB,gBACLzxB,KAAKsE,MAAMiqB,OAAOzM,EAAKlP,GACvB5S,KAAK2xB,gBAITtC,OAAOzc,GACL,MAAMkP,EAAM9hB,KAAKmyB,aAAanyB,KAAK2lB,WAC/B7D,EAAIV,eAAiBU,EAAIpC,aAC3B1f,KAAKyxB,gBACLzxB,KAAKsE,MAAM+qB,OAAOvN,EAAKlP,GACvB5S,KAAK2xB,gBAITjC,YACE,MAAM5N,EAAM9hB,KAAKmyB,aAAanyB,KAAK2lB,WAC/B7D,EAAIV,eAAiBU,EAAIpC,aAC3B1f,KAAKyxB,gBACLzxB,KAAKsE,MAAMorB,UAAU5N,GACrB9hB,KAAK2xB,gBAITzB,YACE,MAAMpO,EAAM9hB,KAAKmyB,aAAanyB,KAAK2lB,WAC/B7D,EAAIV,eAAiBU,EAAIpC,aAC3B1f,KAAKyxB,gBACLzxB,KAAKsE,MAAM4rB,UAAUpO,GACrB9hB,KAAK2xB,gBAITb,cACE,MAAMhP,EAAM9hB,KAAKmyB,aAAanyB,KAAK2lB,WAC/B7D,EAAIV,eAAiBU,EAAIpC,aAC3B1f,KAAKyxB,gBACLzxB,KAAKsE,MAAMwsB,YAAYhP,GACvB9hB,KAAK2xB,gBASTsG,SAAS7e,EAAKmD,EAAS2b,GACrB,IAAIC,EACJ,GAAID,EAAY,CACd,MAAME,EAAWhf,EAAIif,EAAIjf,EAAIkf,EACvBC,EAAQhc,EAAQ9b,KAAK,SAC3B03B,EAAY,CACV5tB,MAAOguB,EAAQH,EAAWhf,EAAIkf,EAAIlf,EAAIif,EAAIE,EAC1Cr2B,OAAQq2B,EAAQH,EAAWhf,EAAIkf,EAAIC,EAAQnf,EAAIif,QAGjDF,EAAY,CACV5tB,MAAO6O,EAAIkf,EACXp2B,OAAQkX,EAAIif,GAIhB9b,EAAQmK,IAAIyR,GAMdK,WACE,OAAOx4B,KAAK2lB,UAAU8S,GAAG,UAM3BzZ,QAGOhf,KAAKw4B,YACRx4B,KAAK2lB,UAAU3G,QAQnBhQ,UACE,OAAO8L,GAAI9L,QAAQhP,KAAK2lB,UAAU,KAAO7K,GAAItG,YAAcxU,KAAK2lB,UAAUrlB,OAM5Eo4B,QACE14B,KAAKgK,QAAQ2B,OAAO,OAAQmP,GAAItG,WAMlCmhB,mBACE31B,KAAK2lB,UAAU,GAAG7E,cD34BhB,UGxCS,MACbnhB,YAAYqK,GACVhK,KAAKgK,QAAUA,EACfhK,KAAK2lB,UAAY3b,EAAQgQ,WAAW2B,SAGtCxB,aACEna,KAAK2lB,UAAU5kB,GAAG,QAASf,KAAK24B,aAAaz5B,KAAKc,OAQpD24B,aAAavc,GACX,MAAMwc,EAAgBxc,EAAMyc,cAAcD,cAC1C,GAAIA,GAAiBA,EAAcE,OAASF,EAAcE,MAAM13B,OAAQ,CACtE,MAAMsK,EAAOktB,EAAcE,MAAM13B,OAAS,EAAIw3B,EAAcE,MAAM,GAAKtzB,EAAMqI,KAAK+qB,EAAcE,OAC9E,SAAdptB,EAAKqtB,OAAoD,IAAjCrtB,EAAK8S,KAAKnV,QAAQ,WAE5CrJ,KAAKgK,QAAQ2B,OAAO,gCAAiC,CAACD,EAAKstB,cAC3D5c,EAAME,iBAENtc,KAAKgK,QAAQ2B,OAAO,wBACG,WAAdD,EAAKqtB,OAEV/4B,KAAKgK,QAAQ2B,OAAO,mBAAoBitB,EAAcK,QAAQ,QAAQ73B,QACxEgb,EAAME,iBAENtc,KAAKgK,QAAQ2B,OAAO,2BHWxB,SIzCS,MACbhM,YAAYqK,GACVhK,KAAKgK,QAAUA,EACfhK,KAAKk5B,eAAiB94B,IAAE6J,UACxBjK,KAAKixB,QAAUjnB,EAAQgQ,WAAWiB,OAClCjb,KAAK2lB,UAAY3b,EAAQgQ,WAAW2B,SACpC3b,KAAKF,QAAUkK,EAAQlK,QACvBE,KAAK2B,KAAO3B,KAAKF,QAAQ6e,SACzB3e,KAAKm5B,sBAAwB,GAE7Bn5B,KAAKo5B,UAAYh5B,IAAE,CACjB,8BACE,uCACF,UACA6M,KAAK,KAAKosB,UAAUr5B,KAAKixB,SAM7B9W,aACMna,KAAKF,QAAQw5B,oBAEft5B,KAAKm5B,sBAAsBI,OAAUtW,IACnCA,EAAE3G,kBAGJtc,KAAKk5B,eAAiBl5B,KAAKo5B,UAC3Bp5B,KAAKk5B,eAAen4B,GAAG,OAAQf,KAAKm5B,sBAAsBI,SAE1Dv5B,KAAKw5B,yBAOTA,yBACE,IAAI1qB,EAAa1O,MACjB,MAAMq5B,EAAmBz5B,KAAKo5B,UAAUn4B,KAAK,0BAE7CjB,KAAKm5B,sBAAsBO,YAAezW,IACxC,MAAM0W,EAAa35B,KAAKgK,QAAQ2B,OAAO,wBACjCiuB,EAAgB55B,KAAKixB,QAAQ1mB,QAAU,GAAKvK,KAAKixB,QAAQ/uB,SAAW,EACrEy3B,GAAe7qB,EAAW1N,SAAUw4B,IACvC55B,KAAKixB,QAAQzwB,SAAS,YACtBR,KAAKo5B,UAAU7uB,MAAMvK,KAAKixB,QAAQ1mB,SAClCvK,KAAKo5B,UAAUl3B,OAAOlC,KAAKixB,QAAQ/uB,UACnCu3B,EAAiBphB,KAAKrY,KAAK2B,KAAKa,MAAMa,gBAExCyL,EAAaA,EAAW+qB,IAAI5W,EAAEzG,SAGhCxc,KAAKm5B,sBAAsBW,YAAe7W,IACxCnU,EAAaA,EAAW1D,IAAI6X,EAAEzG,QAGzB1N,EAAW1N,QAAgC,SAAtB6hB,EAAEzG,OAAOzM,WACjCjB,EAAa1O,MACbJ,KAAKixB,QAAQ8I,YAAY,cAI7B/5B,KAAKm5B,sBAAsBI,OAAS,KAClCzqB,EAAa1O,MACbJ,KAAKixB,QAAQ8I,YAAY,aAK3B/5B,KAAKk5B,eAAen4B,GAAG,YAAaf,KAAKm5B,sBAAsBO,aAC5D34B,GAAG,YAAaf,KAAKm5B,sBAAsBW,aAC3C/4B,GAAG,OAAQf,KAAKm5B,sBAAsBI,QAGzCv5B,KAAKo5B,UAAUr4B,GAAG,YAAa,KAC7Bf,KAAKo5B,UAAU54B,SAAS,SACxBi5B,EAAiBphB,KAAKrY,KAAK2B,KAAKa,MAAMc,aACrCvC,GAAG,YAAa,KACjBf,KAAKo5B,UAAUW,YAAY,SAC3BN,EAAiBphB,KAAKrY,KAAK2B,KAAKa,MAAMa,iBAIxCrD,KAAKo5B,UAAUr4B,GAAG,OAASqb,IACzB,MAAM4d,EAAe5d,EAAMyc,cAAcmB,aAGzC5d,EAAME,iBAEF0d,GAAgBA,EAAanD,OAASmD,EAAanD,MAAMz1B,QAC3DpB,KAAK2lB,UAAU3G,QACfhf,KAAKgK,QAAQ2B,OAAO,gCAAiCquB,EAAanD,QAElEz2B,IAAEM,KAAKs5B,EAAaC,MAAO,CAAC5rB,EAAKmQ,KAE/B,GAAIA,EAAKrW,cAAckB,QAAQ,UAAY,EACzC,OAEF,MAAM6wB,EAAUF,EAAaf,QAAQza,GAEjCA,EAAKrW,cAAckB,QAAQ,SAAW,EACxCrJ,KAAKgK,QAAQ2B,OAAO,mBAAoBuuB,GAExC95B,IAAE85B,GAASx5B,KAAK,CAAC2N,EAAK3C,KACpB1L,KAAKgK,QAAQ2B,OAAO,oBAAqBD,SAKhD3K,GAAG,YAAY,GAGpBwZ,UACEnc,OAAOob,KAAKxZ,KAAKm5B,uBAAuBj4B,QAASjC,IAC/Ce,KAAKk5B,eAAexf,IAAIza,EAAIk7B,OAAO,GAAGhyB,cAAenI,KAAKm5B,sBAAsBl6B,MAElFe,KAAKm5B,sBAAwB,KJ3E3B,SFjCS,MACbx5B,YAAYqK,GACVhK,KAAKgK,QAAUA,EACfhK,KAAKixB,QAAUjnB,EAAQgQ,WAAWiB,OAClCjb,KAAK2lB,UAAY3b,EAAQgQ,WAAW2B,SACpC3b,KAAKo6B,SAAWpwB,EAAQgQ,WAAW0B,QACnC1b,KAAKF,QAAUkK,EAAQlK,QAGzBu6B,OACqBr6B,KAAKwb,eACNvK,EAAIpI,eACpB7I,KAAKo6B,SAAS35B,KAAK,YAAY65B,OAOnC9e,cACE,OAAOxb,KAAKixB,QAAQphB,SAAS,YAM/B0qB,SACMv6B,KAAKwb,cACPxb,KAAKw6B,aAELx6B,KAAKy6B,WAEPz6B,KAAKgK,QAAQuR,aAAa,oBAQ5Bmf,OAAO/7B,GACL,GAAIqB,KAAKF,QAAQ66B,iBAEfh8B,EAAQA,EAAM0V,QAAQrU,KAAKF,QAAQ86B,oBAAqB,IAEpD56B,KAAKF,QAAQ+6B,sBAAsB,CACrC,MAAMC,EAAY96B,KAAKF,QAAQi7B,2BAA2B7Y,OAAOliB,KAAKF,QAAQk7B,gCAC9Er8B,EAAQA,EAAM0V,QAAQ,qCAAqC,SAAS4mB,GAElE,GAAI,uDAAuD1yB,KAAK0yB,GAC9D,MAAO,GAET,IAAK,MAAMnF,KAAOgF,EAEhB,GAAK,IAAII,OAAO,oBAAwBpF,EAAIzhB,QAAQ,yBAA0B,QAAU,UAAY9L,KAAK0yB,GACvG,OAAOA,EAGX,MAAO,MAIb,OAAOt8B,EAMT87B,WASE,GARAz6B,KAAKo6B,SAAShmB,IAAI0G,GAAIxa,KAAKN,KAAK2lB,UAAW3lB,KAAKF,QAAQq7B,eACxDn7B,KAAKo6B,SAASl4B,OAAOlC,KAAK2lB,UAAUzjB,UAEpClC,KAAKgK,QAAQ2B,OAAO,0BAA0B,GAC9C3L,KAAKixB,QAAQzwB,SAAS,YACtBR,KAAKo6B,SAASpb,QAGV/N,EAAIpI,cAAe,CACrB,MAAMuyB,EAAWtyB,GAAWuyB,aAAar7B,KAAKo6B,SAAS,GAAIp6B,KAAKF,QAAQw7B,YAGxE,GAAIt7B,KAAKF,QAAQw7B,WAAWC,KAAM,CAChC,MAAMC,EAAS,IAAI1yB,GAAW2yB,WAAWz7B,KAAKF,QAAQw7B,WAAWC,MACjEH,EAASM,WAAaF,EACtBJ,EAASr6B,GAAG,iBAAmB46B,IAC7BH,EAAOI,eAAeD,KAI1BP,EAASr6B,GAAG,OAASqb,IACnBpc,KAAKgK,QAAQuR,aAAa,gBAAiB6f,EAASS,WAAYzf,KAElEgf,EAASr6B,GAAG,SAAWqb,IACrBpc,KAAKgK,QAAQuR,aAAa,kBAAmB6f,EAASS,WAAYT,KAIpEA,EAASU,QAAQ,KAAM97B,KAAK2lB,UAAUtM,eACtCrZ,KAAKo6B,SAAS35B,KAAK,WAAY26B,QAE/Bp7B,KAAKo6B,SAASr5B,GAAG,OAASqb,IACxBpc,KAAKgK,QAAQuR,aAAa,gBAAiBvb,KAAKo6B,SAAShmB,MAAOgI,KAElEpc,KAAKo6B,SAASr5B,GAAG,QAAUqb,IACzBpc,KAAKgK,QAAQuR,aAAa,kBAAmBvb,KAAKo6B,SAAShmB,MAAOpU,KAAKo6B,YAQ7EI,aAEE,GAAIvpB,EAAIpI,cAAe,CACrB,MAAMuyB,EAAWp7B,KAAKo6B,SAAS35B,KAAK,YACpCT,KAAKo6B,SAAShmB,IAAIgnB,EAASS,YAC3BT,EAASW,aAGX,MAAMp9B,EAAQqB,KAAK06B,OAAO5f,GAAInc,MAAMqB,KAAKo6B,SAAUp6B,KAAKF,QAAQq7B,eAAiBrgB,GAAItG,WAC/EwnB,EAAWh8B,KAAK2lB,UAAUrlB,SAAW3B,EAE3CqB,KAAK2lB,UAAUrlB,KAAK3B,GACpBqB,KAAK2lB,UAAUzjB,OAAOlC,KAAKF,QAAQoC,OAASlC,KAAKo6B,SAASl4B,SAAW,QACrElC,KAAKixB,QAAQ8I,YAAY,YAErBiC,GACFh8B,KAAKgK,QAAQuR,aAAa,SAAUvb,KAAK2lB,UAAUrlB,OAAQN,KAAK2lB,WAGlE3lB,KAAK2lB,UAAU3G,QAEfhf,KAAKgK,QAAQ2B,OAAO,0BAA0B,GAGhD4O,UACMva,KAAKwb,eACPxb,KAAKw6B,eExGL,UK1CS,MACb76B,YAAYqK,GACVhK,KAAKoM,UAAYhM,IAAE6J,UACnBjK,KAAKi8B,WAAajyB,EAAQgQ,WAAWkiB,UACrCl8B,KAAK2lB,UAAY3b,EAAQgQ,WAAW2B,SACpC3b,KAAKF,QAAUkK,EAAQlK,QAGzBqa,aACMna,KAAKF,QAAQw0B,SAAWt0B,KAAKF,QAAQq8B,oBACvCn8B,KAAKua,UAIPva,KAAKi8B,WAAWl7B,GAAG,YAAcqb,IAC/BA,EAAME,iBACNF,EAAMggB,kBAEN,MAAMC,EAAcr8B,KAAK2lB,UAAUnT,SAASnG,IAAMrM,KAAKoM,UAAUE,YAC3DgwB,EAAelgB,IACnB,IAAIla,EAASka,EAAMmgB,SAAWF,EAtBb,IAwBjBn6B,EAAUlC,KAAKF,QAAQ08B,UAAY,EAAKxc,KAAKyc,IAAIv6B,EAAQlC,KAAKF,QAAQ08B,WAAat6B,EACnFA,EAAUlC,KAAKF,QAAQ00B,UAAY,EAAKxU,KAAKC,IAAI/d,EAAQlC,KAAKF,QAAQ00B,WAAatyB,EAEnFlC,KAAK2lB,UAAUzjB,OAAOA,IAGxBlC,KAAKoM,UAAUrL,GAAG,YAAau7B,GAAanG,IAAI,UAAW,KACzDn2B,KAAKoM,UAAUsN,IAAI,YAAa4iB,OAKtC/hB,UACEva,KAAKi8B,WAAWviB,MAChB1Z,KAAKi8B,WAAWz7B,SAAS,YLOvB,WM5CS,MACbb,YAAYqK,GACVhK,KAAKgK,QAAUA,EAEfhK,KAAKixB,QAAUjnB,EAAQgQ,WAAWiB,OAClCjb,KAAK08B,SAAW1yB,EAAQgQ,WAAW2iB,QACnC38B,KAAK2lB,UAAY3b,EAAQgQ,WAAW2B,SACpC3b,KAAKo6B,SAAWpwB,EAAQgQ,WAAW0B,QAEnC1b,KAAK48B,QAAUx8B,IAAE9C,QACjB0C,KAAK68B,WAAaz8B,IAAE,cAEpBJ,KAAK88B,SAAW,KACd98B,KAAKi4B,SAAS,CACZ8E,EAAG/8B,KAAK48B,QAAQ16B,SAAWlC,KAAK08B,SAASrjB,iBAK/C4e,SAAS31B,GACPtC,KAAK2lB,UAAUe,IAAI,SAAUpkB,EAAKy6B,GAClC/8B,KAAKo6B,SAAS1T,IAAI,SAAUpkB,EAAKy6B,GAC7B/8B,KAAKo6B,SAAS35B,KAAK,aACrBT,KAAKo6B,SAAS35B,KAAK,YAAYu8B,QAAQ,KAAM16B,EAAKy6B,GAOtDxC,SACEv6B,KAAKixB,QAAQ8C,YAAY,cACrB/zB,KAAKi9B,gBACPj9B,KAAK2lB,UAAUllB,KAAK,YAAaT,KAAK2lB,UAAUe,IAAI,WACpD1mB,KAAK2lB,UAAUllB,KAAK,eAAgBT,KAAK2lB,UAAUe,IAAI,cACvD1mB,KAAK2lB,UAAUe,IAAI,YAAa,IAChC1mB,KAAK48B,QAAQ77B,GAAG,SAAUf,KAAK88B,UAAU/gB,QAAQ,UACjD/b,KAAK68B,WAAWnW,IAAI,WAAY,YAEhC1mB,KAAK48B,QAAQljB,IAAI,SAAU1Z,KAAK88B,UAChC98B,KAAKi4B,SAAS,CAAE8E,EAAG/8B,KAAK2lB,UAAUllB,KAAK,eACvCT,KAAK2lB,UAAUe,IAAI,YAAa1mB,KAAK2lB,UAAUe,IAAI,iBACnD1mB,KAAK68B,WAAWnW,IAAI,WAAY,YAGlC1mB,KAAKgK,QAAQ2B,OAAO,2BAA4B3L,KAAKi9B,gBAGvDA,eACE,OAAOj9B,KAAKixB,QAAQphB,SAAS,gBNJ3B,OO5CS,MACblQ,YAAYqK,GACVhK,KAAKgK,QAAUA,EACfhK,KAAKoM,UAAYhM,IAAE6J,UACnBjK,KAAKk9B,aAAelzB,EAAQgQ,WAAWmjB,YACvCn9B,KAAKF,QAAUkK,EAAQlK,QACvBE,KAAK2B,KAAO3B,KAAKF,QAAQ6e,SAEzB3e,KAAKuZ,OAAS,CACZ,uBAAwB,CAAC6jB,EAAIna,KACvBjjB,KAAKq9B,OAAOpa,EAAEzG,OAAQyG,IACxBA,EAAE3G,kBAGN,+EAAgF,KAC9Etc,KAAKq9B,UAEP,qCAAsC,KACpCr9B,KAAKsa,QAEP,8BAA+B,KAC7Bta,KAAKq9B,WAKXljB,aACEna,KAAKs9B,QAAUl9B,IAAE,CACf,4BACE,uCACE,gDACA,0DACA,0DACA,0DACA,eACGJ,KAAKF,QAAQy9B,mBAAqB,sBAAwB,sBAC7D,2BACCv9B,KAAKF,QAAQy9B,mBAAqB,GAAK,kDAC1C,SACF,UACAtwB,KAAK,KAAKosB,UAAUr5B,KAAKk9B,cAE3Bl9B,KAAKs9B,QAAQv8B,GAAG,YAAcqb,IAC5B,GAAItB,GAAIrG,gBAAgB2H,EAAMI,QAAS,CACrCJ,EAAME,iBACNF,EAAMggB,kBAEN,MAAM7f,EAAUvc,KAAKs9B,QAAQr8B,KAAK,2BAA2BR,KAAK,UAC5D+8B,EAAWjhB,EAAQ/J,SACnBlG,EAAYtM,KAAKoM,UAAUE,YAE3BgwB,EAAelgB,IACnBpc,KAAKgK,QAAQ2B,OAAO,kBAAmB,CACrC2sB,EAAGlc,EAAMqhB,QAAUD,EAASv3B,KAC5BoyB,EAAGjc,EAAMmgB,SAAWiB,EAASnxB,IAAMC,IAClCiQ,GAAUH,EAAM0Y,UAEnB90B,KAAKq9B,OAAO9gB,EAAQ,GAAIH,IAG1Bpc,KAAKoM,UACFrL,GAAG,YAAau7B,GAChBnG,IAAI,UAAYlT,IACfA,EAAE3G,iBACFtc,KAAKoM,UAAUsN,IAAI,YAAa4iB,GAChCt8B,KAAKgK,QAAQ2B,OAAO,yBAGnB4Q,EAAQ9b,KAAK,UAChB8b,EAAQ9b,KAAK,QAAS8b,EAAQra,SAAWqa,EAAQhS,YAMvDvK,KAAKs9B,QAAQv8B,GAAG,QAAUkiB,IACxBA,EAAE3G,iBACFtc,KAAKq9B,WAIT9iB,UACEva,KAAKs9B,QAAQ35B,SAGf05B,OAAO7gB,EAAQJ,GACb,GAAIpc,KAAKgK,QAAQ4Q,aACf,OAAO,EAGT,MAAM8iB,EAAU5iB,GAAIvF,MAAMiH,GACpBmhB,EAAa39B,KAAKs9B,QAAQr8B,KAAK,2BAIrC,GAFAjB,KAAKgK,QAAQ2B,OAAO,sBAAuB6Q,EAAQJ,GAE/CshB,EAAS,CACX,MAAMhH,EAASt2B,IAAEoc,GACX5J,EAAW8jB,EAAO9jB,WAClBwG,EAAM,CACVnT,KAAM2M,EAAS3M,KAAO6gB,SAAS4P,EAAOhQ,IAAI,cAAe,IACzDra,IAAKuG,EAASvG,IAAMya,SAAS4P,EAAOhQ,IAAI,aAAc,KAIlDyR,EAAY,CAChByF,EAAGlH,EAAOnC,YAAW,GACrBwI,EAAGrG,EAAOrd,aAAY,IAGxBskB,EAAWjX,IAAI,CACb4P,QAAS,QACTrwB,KAAMmT,EAAInT,KACVoG,IAAK+M,EAAI/M,IACT9B,MAAO4tB,EAAUyF,EACjB17B,OAAQi2B,EAAU4E,IACjBt8B,KAAK,SAAUi2B,GAElB,MAAMmH,EAAe,IAAIC,MACzBD,EAAa/H,IAAMY,EAAO71B,KAAK,OAE/B,MAAMk9B,EAAa5F,EAAUyF,EAAI,IAAMzF,EAAU4E,EAAI,KAAO/8B,KAAK2B,KAAKa,MAAMoB,SAAW,KAAOi6B,EAAatzB,MAAQ,IAAMszB,EAAa37B,OAAS,IAC/Iy7B,EAAW18B,KAAK,gCAAgCoX,KAAK0lB,GACrD/9B,KAAKgK,QAAQ2B,OAAO,oBAAqB6Q,QAEzCxc,KAAKsa,OAGP,OAAOojB,EAQTpjB,OACEta,KAAKgK,QAAQ2B,OAAO,sBACpB3L,KAAKs9B,QAAQz9B,WAAWya,SP1FtB,YQzCS,MACb3a,YAAYqK,GACVhK,KAAKgK,QAAUA,EAEfhK,KAAKia,GAAK7Z,IAAEsB,WAAWuY,GACvBja,KAAK2lB,UAAY3b,EAAQgQ,WAAW2B,SACpC3b,KAAKF,QAAUkK,EAAQlK,QACvBE,KAAKg+B,KAAOh+B,KAAKF,QAAQk+B,MAAQ,GACjCh+B,KAAKi+B,UAAYj+B,KAAKF,QAAQo+B,eAAiB,SAC/Cl+B,KAAKm+B,MAAQ58B,MAAMC,QAAQxB,KAAKg+B,MAAQh+B,KAAKg+B,KAAO,CAACh+B,KAAKg+B,MAE1Dh+B,KAAKuZ,OAAS,CACZ,mBAAoB,CAAC6jB,EAAIna,KAClBA,EAAEgR,sBACLj0B,KAAKo+B,YAAYnb,IAGrB,qBAAsB,CAACma,EAAIna,KACzBjjB,KAAKq+B,cAAcpb,IAErB,6DAA8D,KAC5DjjB,KAAKsa,SAKX0B,mBACE,OAAOhc,KAAKm+B,MAAM/8B,OAAS,EAG7B+Y,aACEna,KAAKs+B,cAAgB,KACrBt+B,KAAKu+B,aAAe,KACpBv+B,KAAKw+B,SAAWx+B,KAAKia,GAAGwkB,QAAQ,CAC9Bl+B,UAAW,oBACXm+B,WAAW,EACXT,UAAW,KACVh+B,SAASs2B,SAASv2B,KAAKF,QAAQoY,WAElClY,KAAKw+B,SAASlkB,OACdta,KAAK2+B,SAAW3+B,KAAKw+B,SAASv9B,KAAK,0CACnCjB,KAAK2+B,SAAS59B,GAAG,QAAS,kBAAoBkiB,IAC5CjjB,KAAK2+B,SAAS19B,KAAK,WAAW84B,YAAY,UAC1C35B,IAAE6iB,EAAE2b,eAAep+B,SAAS,UAC5BR,KAAKqU,YAGPrU,KAAKw+B,SAASz9B,GAAG,YAAckiB,IAAQA,EAAE3G,mBAG3C/B,UACEva,KAAKw+B,SAAS76B,SAGhBk7B,WAAWC,GACT9+B,KAAK2+B,SAAS19B,KAAK,WAAW84B,YAAY,UAC1C+E,EAAMt+B,SAAS,UAEfR,KAAK2+B,SAAS,GAAGryB,UAAYwyB,EAAM,GAAGle,UAAa5gB,KAAK2+B,SAASI,cAAgB,EAGnFC,WACE,MAAMC,EAAWj/B,KAAK2+B,SAAS19B,KAAK,0BAC9Bi+B,EAAQD,EAAS3wB,OAEvB,GAAI4wB,EAAM99B,OACRpB,KAAK6+B,WAAWK,OACX,CACL,IAAIC,EAAaF,EAAShtB,SAAS3D,OAE9B6wB,EAAW/9B,SACd+9B,EAAan/B,KAAK2+B,SAAS19B,KAAK,oBAAoB8d,SAGtD/e,KAAK6+B,WAAWM,EAAWl+B,KAAK,mBAAmB8d,UAIvDqgB,SACE,MAAMH,EAAWj/B,KAAK2+B,SAAS19B,KAAK,0BAC9Bo+B,EAAQJ,EAAS7wB,OAEvB,GAAIixB,EAAMj+B,OACRpB,KAAK6+B,WAAWQ,OACX,CACL,IAAIC,EAAaL,EAAShtB,SAAS7D,OAE9BkxB,EAAWl+B,SACdk+B,EAAat/B,KAAK2+B,SAAS19B,KAAK,oBAAoB8M,QAGtD/N,KAAK6+B,WAAWS,EAAWr+B,KAAK,mBAAmB8M,SAIvDsG,UACE,MAAMyqB,EAAQ9+B,KAAK2+B,SAAS19B,KAAK,0BAEjC,GAAI69B,EAAM19B,OAAQ,CAChB,IAAIwO,EAAO5P,KAAKu/B,aAAaT,GAE7B,GAA0B,OAAtB9+B,KAAKu+B,cAAsD,IAA7Bv+B,KAAKu+B,aAAan9B,OAClDpB,KAAKs+B,cAAcnf,GAAKnf,KAAKs+B,cAAcjf,QAEtC,GAA0B,OAAtBrf,KAAKu+B,cAAyBv+B,KAAKu+B,aAAan9B,OAAS,IAAMpB,KAAKs+B,cAAcld,cAAe,CAC1G,IAAIoe,EAAex/B,KAAKs+B,cAAcjf,GAAKrf,KAAKs+B,cAAcnf,GAAKnf,KAAKu+B,aAAan9B,OACjFo+B,EAAe,IACjBx/B,KAAKs+B,cAAcnf,IAAMqgB,GAK7B,GAFAx/B,KAAKs+B,cAAclc,WAAWxS,GAEE,SAA5B5P,KAAKF,QAAQ2/B,WAAuB,CACtC,IAAIlrB,EAAQtK,SAASqO,eAAe,IACpClY,IAAEwP,GAAMwf,MAAM7a,GACdwR,GAAM5B,qBAAqB5P,GAAO5M,cAElCoe,GAAM3B,oBAAoBxU,GAAMjI,SAGlC3H,KAAKs+B,cAAgB,KACrBt+B,KAAKsa,OACLta,KAAKgK,QAAQ2B,OAAO,iBAIxB4zB,aAAaT,GACX,MAAMd,EAAOh+B,KAAKm+B,MAAMW,EAAMr+B,KAAK,UAC7BiL,EAAOozB,EAAMr+B,KAAK,QACxB,IAAImP,EAAOouB,EAAK9D,QAAU8D,EAAK9D,QAAQxuB,GAAQA,EAI/C,MAHoB,iBAATkE,IACTA,EAAOkL,GAAI1C,WAAWxI,IAEjBA,EAGT8vB,oBAAoBC,EAAS7G,GAC3B,MAAMkF,EAAOh+B,KAAKm+B,MAAMwB,GACxB,OAAO7G,EAAMhsB,IAAI,CAACpB,EAAM2C,KACtB,MAAMywB,EAAQ1+B,IAAE,iCAMhB,OALA0+B,EAAMz9B,OAAO28B,EAAK4B,SAAW5B,EAAK4B,SAASl0B,GAAQA,EAAO,IAC1DozB,EAAMr+B,KAAK,CACT,MAASk/B,EACT,KAAQj0B,IAEHozB,IAIXT,cAAcpb,GACPjjB,KAAKw+B,SAAS/F,GAAG,cAIlBxV,EAAEwB,UAAYxlB,GAAI4b,KAAK+J,OACzB3B,EAAE3G,iBACFtc,KAAKqU,WACI4O,EAAEwB,UAAYxlB,GAAI4b,KAAKoK,IAChChC,EAAE3G,iBACFtc,KAAKo/B,UACInc,EAAEwB,UAAYxlB,GAAI4b,KAAKsK,OAChClC,EAAE3G,iBACFtc,KAAKg/B,aAITa,cAAc/c,EAAOgd,EAAS//B,GAC5B,MAAMi+B,EAAOh+B,KAAKm+B,MAAMrb,GACxB,GAAIkb,GAAQA,EAAKplB,MAAMrQ,KAAKu3B,IAAY9B,EAAK+B,OAAQ,CACnD,MAAMt3B,EAAUu1B,EAAKplB,MAAMlQ,KAAKo3B,GAChC9/B,KAAKu+B,aAAe91B,EAAQ,GAC5Bu1B,EAAK+B,OAAOt3B,EAAQ,GAAI1I,QAExBA,IAIJigC,YAAY3xB,EAAKyxB,GACf,MAAMG,EAAS7/B,IAAE,+CAAiDiO,EAAM,OASxE,OARArO,KAAK6/B,cAAcxxB,EAAKyxB,EAAUhH,KAChCA,EAAQA,GAAS,IACP13B,SACR6+B,EAAO3/B,KAAKN,KAAK0/B,oBAAoBrxB,EAAKyqB,IAC1C94B,KAAK22B,UAIFsJ,EAGT7B,YAAYnb,GACV,IAAKzd,EAAM0I,SAAS,CAACjP,GAAI4b,KAAK+J,MAAO3lB,GAAI4b,KAAKoK,GAAIhmB,GAAI4b,KAAKsK,MAAOlC,EAAEwB,SAAU,CAC5E,IACIyb,EAAWJ,EADX/Z,EAAQ/lB,KAAKgK,QAAQ2B,OAAO,uBAEhC,GAA8B,UAA1B3L,KAAKF,QAAQqgC,SAAsB,CAWrC,GAVAD,EAAYna,EAAMrD,cAAcqD,GAChC+Z,EAAUI,EAAU3d,WAEpBviB,KAAKm+B,MAAMj9B,QAAS88B,IAClB,GAAIA,EAAKplB,MAAMrQ,KAAKu3B,GAElB,OADAI,EAAYna,EAAMnD,mBAAmBob,EAAKplB,QACnC,KAINsnB,EAEH,YADAlgC,KAAKsa,OAIPwlB,EAAUI,EAAU3d,gBAEpB2d,EAAYna,EAAMvD,eAClBsd,EAAUI,EAAU3d,WAGtB,GAAIviB,KAAKm+B,MAAM/8B,QAAU0+B,EAAS,CAChC9/B,KAAK2+B,SAASjG,QAEd,MAAM0H,EAAMjzB,EAAKjB,SAAS1G,EAAMuI,KAAKmyB,EAAU9c,mBACzCid,EAAkBjgC,IAAEJ,KAAKF,QAAQoY,WAAW1F,SAC9C4tB,IACFA,EAAI/zB,KAAOg0B,EAAgBh0B,IAC3B+zB,EAAIn6B,MAAQo6B,EAAgBp6B,KAE5BjG,KAAKw+B,SAASlkB,OACdta,KAAKs+B,cAAgB4B,EACrBlgC,KAAKm+B,MAAMj9B,QAAQ,CAAC88B,EAAM3vB,KACpB2vB,EAAKplB,MAAMrQ,KAAKu3B,IAClB9/B,KAAKggC,YAAY3xB,EAAKyxB,GAASvJ,SAASv2B,KAAK2+B,YAIjD3+B,KAAK2+B,SAAS19B,KAAK,yBAAyBT,SAAS,UAG9B,QAAnBR,KAAKi+B,UACPj+B,KAAKw+B,SAAS9X,IAAI,CAChBzgB,KAAMm6B,EAAIn6B,KACVoG,IAAK+zB,EAAI/zB,IAAMrM,KAAKw+B,SAASnlB,cAjPtB,IAoPTrZ,KAAKw+B,SAAS9X,IAAI,CAChBzgB,KAAMm6B,EAAIn6B,KACVoG,IAAK+zB,EAAI/zB,IAAM+zB,EAAIl+B,OAtPZ,UA2PblC,KAAKsa,QAKXqc,OACE32B,KAAKw+B,SAAS7H,OAGhBrc,OACEta,KAAKw+B,SAASlkB,SRzNZ,SD5CS,MACb3a,YAAYqK,GACVhK,KAAKgK,QAAUA,EACfhK,KAAKuZ,OAAS,CACZ,mBAAoB,CAAC6jB,EAAIna,KAClBA,EAAEgR,sBACLj0B,KAAKo+B,YAAYnb,IAGrB,qBAAsB,CAACma,EAAIna,KACzBjjB,KAAKq+B,cAAcpb,KAKzB9I,aACEna,KAAKs+B,cAAgB,KAGvB/jB,UACEva,KAAKs+B,cAAgB,KAGvBjqB,UACE,IAAKrU,KAAKs+B,cACR,OAGF,MAAMwB,EAAU9/B,KAAKs+B,cAAc/b,WAC7B3J,EAAQknB,EAAQlnB,MAAMmY,IAE5B,GAAInY,IAAUA,EAAM,IAAMA,EAAM,IAAK,CACnC,MAAM5U,EAAO4U,EAAM,GAAKknB,EAnCR,UAmCkCA,EAC5CQ,EAAUR,EAAQzrB,QAAQ,wDAAyD,IAAIxH,MAAM,KAAK,GAClG+C,EAAOxP,IAAE,SAASE,KAAKggC,GAASz/B,KAAK,OAAQmD,GAAM,GACrDhE,KAAKgK,QAAQlK,QAAQygC,iBACvBngC,IAAEwP,GAAM/O,KAAK,SAAU,UAGzBb,KAAKs+B,cAAclc,WAAWxS,GAC9B5P,KAAKs+B,cAAgB,KACrBt+B,KAAKgK,QAAQ2B,OAAO,iBAIxB0yB,cAAcpb,GACZ,GAAIzd,EAAM0I,SAAS,CAACjP,GAAI4b,KAAK+J,MAAO3lB,GAAI4b,KAAKgK,OAAQ5B,EAAEwB,SAAU,CAC/D,MAAMyb,EAAYlgC,KAAKgK,QAAQ2B,OAAO,sBAAsB6W,eAC5DxiB,KAAKs+B,cAAgB4B,GAIzB9B,YAAYnb,GACNzd,EAAM0I,SAAS,CAACjP,GAAI4b,KAAK+J,MAAO3lB,GAAI4b,KAAKgK,OAAQ5B,EAAEwB,UACrDzkB,KAAKqU,YCTL,SS/CS,MACb1U,YAAYqK,GACVhK,KAAK8Z,MAAQ9P,EAAQgQ,WAAW8E,KAChC9e,KAAKuZ,OAAS,CACZ,oBAAqB,KACnBvZ,KAAK8Z,MAAM1F,IAAIpK,EAAQ2B,OAAO,WAKpCqQ,mBACE,OAAOlB,GAAI5G,WAAWlU,KAAK8Z,MAAM,MTqC/B,YUjDS,MACbna,YAAYqK,GACVhK,KAAKgK,QAAUA,EACfhK,KAAKF,QAAUkK,EAAQlK,QAAQuU,SAAW,GAE1CrU,KAAKwZ,KAAO,CAACva,GAAI4b,KAAK+J,MAAO3lB,GAAI4b,KAAKgK,MAAO5lB,GAAI4b,KAAK2lB,OAAQvhC,GAAI4b,KAAK4lB,MAAOxhC,GAAI4b,KAAK6lB,UAAWzhC,GAAI4b,KAAK8lB,OAC3G3gC,KAAK4gC,oBAAsB,KAE3B5gC,KAAKuZ,OAAS,CACZ,mBAAoB,CAAC6jB,EAAIna,KAClBA,EAAEgR,sBACLj0B,KAAKo+B,YAAYnb,IAGrB,qBAAsB,CAACma,EAAIna,KACzBjjB,KAAKq+B,cAAcpb,KAKzBjH,mBACE,QAAShc,KAAKF,QAAQ8Y,MAGxBuB,aACEna,KAAK6gC,SAAW,KAGlBtmB,UACEva,KAAK6gC,SAAW,KAGlBxsB,UACE,IAAKrU,KAAK6gC,SACR,OAGF,MAAM11B,EAAOnL,KACP8/B,EAAU9/B,KAAK6gC,SAASte,WAC9BviB,KAAKF,QAAQ8Y,MAAMknB,GAAS,SAASlnB,GACnC,GAAIA,EAAO,CACT,IAAIhJ,EAAO,GAUX,GARqB,iBAAVgJ,EACThJ,EAAOkL,GAAI1C,WAAWQ,GACbA,aAAiBkoB,OAC1BlxB,EAAOgJ,EAAM,GACJA,aAAiBmoB,OAC1BnxB,EAAOgJ,IAGJhJ,EAAM,OACXzE,EAAK01B,SAASze,WAAWxS,GACzBzE,EAAK01B,SAAW,KAChB11B,EAAKnB,QAAQ2B,OAAO,oBAK1B0yB,cAAcpb,GAGZ,GAAIjjB,KAAK4gC,qBAAuBp7B,EAAM0I,SAASlO,KAAKwZ,KAAMxZ,KAAK4gC,qBAC7D5gC,KAAK4gC,oBAAsB3d,EAAEwB,YAD/B,CAKA,GAAIjf,EAAM0I,SAASlO,KAAKwZ,KAAMyJ,EAAEwB,SAAU,CACxC,MAAMyb,EAAYlgC,KAAKgK,QAAQ2B,OAAO,sBAAsB6W,eAC5DxiB,KAAK6gC,SAAWX,EAElBlgC,KAAK4gC,oBAAsB3d,EAAEwB,SAG/B2Z,YAAYnb,GACNzd,EAAM0I,SAASlO,KAAKwZ,KAAMyJ,EAAEwB,UAC9BzkB,KAAKqU,YV1BL,YWrDS,MACb1U,YAAYqK,GACVhK,KAAKgK,QAAUA,EAEfhK,KAAKk9B,aAAelzB,EAAQgQ,WAAWmjB,YACvCn9B,KAAKF,QAAUkK,EAAQlK,SAEiB,IAApCE,KAAKF,QAAQkhC,qBAEfhhC,KAAKF,QAAQoZ,YAAclZ,KAAKgK,QAAQ8P,MAAMjZ,KAAK,gBAAkBb,KAAKF,QAAQoZ,aAGpFlZ,KAAKuZ,OAAS,CACZ,oCAAqC,KACnCvZ,KAAKq9B,UAEP,8BAA+B,KAC7Br9B,KAAKq9B,WAKXrhB,mBACE,QAAShc,KAAKF,QAAQoZ,YAGxBiB,aACEna,KAAKmZ,aAAe/Y,IAAE,kCACtBJ,KAAKmZ,aAAapY,GAAG,QAAS,KAC5Bf,KAAKgK,QAAQ2B,OAAO,WACnBrL,KAAKN,KAAKF,QAAQoZ,aAAamgB,UAAUr5B,KAAKk9B,cAEjDl9B,KAAKq9B,SAGP9iB,UACEva,KAAKmZ,aAAaxV,SAGpB05B,SACE,MAAM4D,GAAUjhC,KAAKgK,QAAQ2B,OAAO,yBAA2B3L,KAAKgK,QAAQ2B,OAAO,kBACnF3L,KAAKmZ,aAAaohB,OAAO0G,KXavB,QYlDS,MACbthC,YAAYqK,GACVhK,KAAKia,GAAK7Z,IAAEsB,WAAWuY,GACvBja,KAAKgK,QAAUA,EACfhK,KAAK08B,SAAW1yB,EAAQgQ,WAAW2iB,QACnC38B,KAAKF,QAAUkK,EAAQlK,QACvBE,KAAK2B,KAAO3B,KAAKF,QAAQ6e,SACzB3e,KAAKkhC,eAAiB/zB,EAAKV,aACzBzM,KAAKF,QAAQ40B,OAAOzjB,EAAI9H,MAAQ,MAAQ,OAI5Cg4B,kBAAkBC,GAChB,IAAIr6B,EAAW/G,KAAKkhC,eAAeE,GACnC,OAAKphC,KAAKF,QAAQkH,WAAcD,GAI5BkK,EAAI9H,QACNpC,EAAWA,EAASsN,QAAQ,MAAO,KAAKA,QAAQ,QAAS,MAG3DtN,EAAWA,EAASsN,QAAQ,YAAa,MACtCA,QAAQ,QAAS,KACjBA,QAAQ,cAAe,KACvBA,QAAQ,eAAgB,KAEpB,KAAOtN,EAAW,KAZhB,GAeXs6B,OAAOljC,GAKL,OAJK6B,KAAKF,QAAQ+e,SAAW1gB,EAAE0gB,gBACtB1gB,EAAE0gB,QAEX1gB,EAAE+Z,UAAYlY,KAAKF,QAAQoY,UACpBlY,KAAKia,GAAGonB,OAAOljC,GAGxBgc,aACEna,KAAKshC,oBACLthC,KAAKuhC,yBACLvhC,KAAKwhC,wBACLxhC,KAAKyhC,yBACLzhC,KAAK0hC,iBAAmB,GAG1BnnB,iBACSva,KAAK0hC,iBAGd53B,gBAAgB7L,GAKd,OAJK+B,KAAK0hC,iBAAiBniC,eAAetB,KACxC+B,KAAK0hC,iBAAiBzjC,GAAQgT,EAAInH,gBAAgB7L,IAChDuH,EAAM0I,SAASlO,KAAKF,QAAQ6hC,qBAAsB1jC,IAE/C+B,KAAK0hC,iBAAiBzjC,GAG/B2jC,oBAAoB3jC,GAElB,MAAiB,MADjBA,EAAOA,EAAKkK,gBACWnI,KAAK8J,gBAAgB7L,KAAoD,IAA3CgT,EAAIlJ,oBAAoBsB,QAAQpL,GAGvF4jC,aAAathC,EAAWse,EAAS0U,EAAWD,GAC1C,OAAOtzB,KAAKia,GAAG6nB,YAAY,CACzBvhC,UAAW,cAAgBA,EAC3BV,SAAU,CACRG,KAAKqhC,OAAO,CACV9gC,UAAW,4BACXF,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAM/c,KAAO,sBACjDgd,QAASA,EACT/d,MAAQmiB,IACN,MAAM+e,EAAU5hC,IAAE6iB,EAAE2b,eAChBrL,GAAaD,EACftzB,KAAKgK,QAAQ2B,OAAO,eAAgB,CAClC4nB,UAAWyO,EAAQnhC,KAAK,kBACxByyB,UAAW0O,EAAQnhC,KAAK,oBAEjB0yB,EACTvzB,KAAKgK,QAAQ2B,OAAO,eAAgB,CAClC4nB,UAAWyO,EAAQnhC,KAAK,oBAEjByyB,GACTtzB,KAAKgK,QAAQ2B,OAAO,eAAgB,CAClC2nB,UAAW0O,EAAQnhC,KAAK,qBAI9Bd,SAAWiiC,IACT,MAAMC,EAAeD,EAAQ/gC,KAAK,sBAC9BsyB,IACF0O,EAAavb,IAAI,mBAAoB1mB,KAAKF,QAAQoiC,YAAY3O,WAC9DyO,EAAQnhC,KAAK,iBAAkBb,KAAKF,QAAQoiC,YAAY3O,YAEtDD,GACF2O,EAAavb,IAAI,QAAS1mB,KAAKF,QAAQoiC,YAAY5O,WACnD0O,EAAQnhC,KAAK,iBAAkBb,KAAKF,QAAQoiC,YAAY5O,YAExD2O,EAAavb,IAAI,QAAS,kBAIhC1mB,KAAKqhC,OAAO,CACV9gC,UAAW,kBACXF,SAAUL,KAAKia,GAAGkoB,uBAAuB,GAAIniC,KAAKF,SAClD+e,QAAS7e,KAAK2B,KAAK0E,MAAME,KACzB9F,KAAM,CACJ85B,OAAQ,cAGZv6B,KAAKia,GAAGmoB,SAAS,CACftJ,OAAQvF,EAAY,CAClB,6BACE,mCAAqCvzB,KAAK2B,KAAK0E,MAAMG,WAAa,SAClE,QACE,4GACExG,KAAK2B,KAAK0E,MAAMK,YAClB,YACF,SACA,oDACA,QACE,6GACE1G,KAAK2B,KAAK0E,MAAMS,SAClB,YACA,0FAA4F9G,KAAKF,QAAQoiC,YAAY3O,UAAY,mCACnI,SACA,iFACF,UACAtmB,KAAK,IAAM,KACZqmB,EAAY,CACX,6BACE,mCAAqCtzB,KAAK2B,KAAK0E,MAAMI,WAAa,SAClE,QACE,iHACEzG,KAAK2B,KAAK0E,MAAMQ,eAClB,YACF,SACA,oDACA,QACE,6GACE7G,KAAK2B,KAAK0E,MAAMS,SAClB,YACA,0FAA4F9G,KAAKF,QAAQoiC,YAAY5O,UAAY,mCACnI,SACA,iFACF,UACArmB,KAAK,IAAM,IACblN,SAAWsiC,IACTA,EAAUphC,KAAK,gBAAgBP,KAAK,CAAC2N,EAAK3C,KACxC,MAAM42B,EAAUliC,IAAEsL,GAClB42B,EAAQjhC,OAAOrB,KAAKia,GAAGsoB,QAAQ,CAC7BC,OAAQxiC,KAAKF,QAAQ0iC,OACrBC,WAAYziC,KAAKF,QAAQ2iC,WACzBzN,UAAWsN,EAAQ7hC,KAAK,SACxByX,UAAWlY,KAAKF,QAAQoY,UACxB2G,QAAS7e,KAAKF,QAAQ+e,UACrB5e,YAGL,IAAIyiC,EAAe,CACjB,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,YAEhFL,EAAUphC,KAAK,uBAAuBP,KAAK,CAAC2N,EAAK3C,KAC/C,MAAM42B,EAAUliC,IAAEsL,GAClB42B,EAAQjhC,OAAOrB,KAAKia,GAAGsoB,QAAQ,CAC7BC,OAAQE,EACRD,WAAYC,EACZ1N,UAAWsN,EAAQ7hC,KAAK,SACxByX,UAAWlY,KAAKF,QAAQoY,UACxB2G,QAAS7e,KAAKF,QAAQ+e,UACrB5e,YAELoiC,EAAUphC,KAAK,qBAAqBP,KAAK,CAAC2N,EAAK3C,KAC7CtL,IAAEsL,GAAMi3B,QAAO,WACb,MAAMC,EAAQP,EAAUphC,KAAK,IAAMb,IAAEJ,MAAMS,KAAK,UAAUQ,KAAK,mBAAmB8d,QAC5E1Y,EAAQrG,KAAKrB,MAAMqO,cACzB41B,EAAMlc,IAAI,mBAAoBrgB,GAC3BxF,KAAK,aAAcwF,GACnBxF,KAAK,aAAcwF,GACnBxF,KAAK,sBAAuBwF,GAC/Bu8B,EAAM9hC,cAIZA,MAAQsb,IACNA,EAAMggB,kBAEN,MAAMl8B,EAAUE,IAAE,IAAMG,GAAWU,KAAK,SAClC+gC,EAAU5hC,IAAEgc,EAAMI,QAClBwY,EAAYgN,EAAQvhC,KAAK,SAC/B,IAAI9B,EAAQqjC,EAAQnhC,KAAK,cAEzB,GAAkB,gBAAdm0B,EAA6B,CAC/B,MAAM6N,EAAU3iC,EAAQe,KAAK,IAAMtC,GAC7BmkC,EAAW1iC,IAAEF,EAAQe,KAAK,IAAM4hC,EAAQpiC,KAAK,UAAUQ,KAAK,mBAAmB,IAG/E2hC,EAAQE,EAAS7hC,KAAK,mBAAmB8M,OAAO8lB,SAGhDxtB,EAAQw8B,EAAQzuB,MACtBwuB,EAAMlc,IAAI,mBAAoBrgB,GAC3BxF,KAAK,aAAcwF,GACnBxF,KAAK,aAAcwF,GACnBxF,KAAK,sBAAuBwF,GAC/By8B,EAASC,QAAQH,GACjBC,EAAQ/hC,aACH,GAAI0E,EAAM0I,SAAS,CAAC,YAAa,aAAc8mB,GAAY,CAChE,MAAM/1B,EAAoB,cAAd+1B,EAA4B,mBAAqB,QACvDgO,EAAShB,EAAQvlB,QAAQ,eAAexb,KAAK,sBAC7CgiC,EAAiBjB,EAAQvlB,QAAQ,eAAexb,KAAK,8BAE3D+hC,EAAOtc,IAAIznB,EAAKN,GAChBskC,EAAepiC,KAAK,QAAUm0B,EAAWr2B,GACzCqB,KAAKgK,QAAQ2B,OAAO,UAAYqpB,EAAWr2B,UAKlDsB,SAGLqhC,oBACEthC,KAAKgK,QAAQ4E,KAAK,eAAgB,IACzB5O,KAAKia,GAAG6nB,YAAY,CACzB9hC,KAAKqhC,OAAO,CACV9gC,UAAW,kBACXF,SAAUL,KAAKia,GAAGkoB,uBAChBniC,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAMskB,OAAQljC,KAAKF,SAE/C+e,QAAS7e,KAAK2B,KAAKoD,MAAMA,MACzBtE,KAAM,CACJ85B,OAAQ,cAGZv6B,KAAKia,GAAGmoB,SAAS,CACf7hC,UAAW,iBACXu4B,MAAO94B,KAAKF,QAAQqjC,UACpBC,MAAOpjC,KAAK2B,KAAKoD,MAAMA,MACvB66B,SAAWl0B,IACW,iBAATA,IACTA,EAAO,CAAEuvB,IAAKvvB,EAAM03B,MAAQpjC,KAAK2B,KAAKoD,MAAMxF,eAAemM,GAAQ1L,KAAK2B,KAAKoD,MAAM2G,GAAQA,IAG7F,MAAMuvB,EAAMvvB,EAAKuvB,IACXmI,EAAQ13B,EAAK03B,MAInB,MAAO,IAAMnI,GAHCvvB,EAAK3G,MAAQ,WAAa2G,EAAK3G,MAAQ,KAAO,KAC1C2G,EAAKnL,UAAY,WAAamL,EAAKnL,UAAY,IAAM,IAEhC,IAAM6iC,EAAQ,KAAOnI,EAAM,KAEpEn6B,MAAOd,KAAKgK,QAAQqS,oBAAoB,0BAEzCpc,UAGL,IAAK,IAAIojC,EAAW,EAAGC,EAAWtjC,KAAKF,QAAQqjC,UAAU/hC,OAAQiiC,EAAWC,EAAUD,IAAY,CAChG,MAAM33B,EAAO1L,KAAKF,QAAQqjC,UAAUE,GAEpCrjC,KAAKgK,QAAQ4E,KAAK,gBAAkBlD,EAAM,IACjC1L,KAAKqhC,OAAO,CACjB9gC,UAAW,kBAAoBmL,EAC/BrL,SAAU,oBAAsBqL,EAAO,KAAOA,EAAKsB,cAAgB,SACnE6R,QAAS7e,KAAK2B,KAAKoD,MAAM2G,GACzB5K,MAAOd,KAAKgK,QAAQqS,oBAAoB,wBACvCpc,UAIPD,KAAKgK,QAAQ4E,KAAK,cAAe,IACxB5O,KAAKqhC,OAAO,CACjB9gC,UAAW,gBACXF,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAM9c,MAC1C+c,QAAS7e,KAAK2B,KAAKE,KAAKC,KAAO9B,KAAKmhC,kBAAkB,QACtDrgC,MAAOd,KAAKgK,QAAQmS,kCAAkC,iBACrDlc,UAGLD,KAAKgK,QAAQ4E,KAAK,gBAAiB,IAC1B5O,KAAKqhC,OAAO,CACjB9gC,UAAW,kBACXF,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAM7c,QAC1C8c,QAAS7e,KAAK2B,KAAKE,KAAKE,OAAS/B,KAAKmhC,kBAAkB,UACxDrgC,MAAOd,KAAKgK,QAAQmS,kCAAkC,mBACrDlc,UAGLD,KAAKgK,QAAQ4E,KAAK,mBAAoB,IAC7B5O,KAAKqhC,OAAO,CACjB9gC,UAAW,qBACXF,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAM5c,WAC1C6c,QAAS7e,KAAK2B,KAAKE,KAAKG,UAAYhC,KAAKmhC,kBAAkB,aAC3DrgC,MAAOd,KAAKgK,QAAQmS,kCAAkC,sBACrDlc,UAGLD,KAAKgK,QAAQ4E,KAAK,eAAgB,IACzB5O,KAAKqhC,OAAO,CACjBhhC,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAM2kB,QAC1C1kB,QAAS7e,KAAK2B,KAAKE,KAAKI,MAAQjC,KAAKmhC,kBAAkB,gBACvDrgC,MAAOd,KAAKgK,QAAQqS,oBAAoB,yBACvCpc,UAGLD,KAAKgK,QAAQ4E,KAAK,uBAAwB,IACjC5O,KAAKqhC,OAAO,CACjB9gC,UAAW,yBACXF,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAMzc,eAC1C0c,QAAS7e,KAAK2B,KAAKE,KAAKM,cAAgBnC,KAAKmhC,kBAAkB,iBAC/DrgC,MAAOd,KAAKgK,QAAQmS,kCAAkC,0BACrDlc,UAGLD,KAAKgK,QAAQ4E,KAAK,qBAAsB,IAC/B5O,KAAKqhC,OAAO,CACjB9gC,UAAW,uBACXF,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAMvc,aAC1Cwc,QAAS7e,KAAK2B,KAAKE,KAAKQ,YACxBvB,MAAOd,KAAKgK,QAAQmS,kCAAkC,wBACrDlc,UAGLD,KAAKgK,QAAQ4E,KAAK,mBAAoB,IAC7B5O,KAAKqhC,OAAO,CACjB9gC,UAAW,qBACXF,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAMxc,WAC1Cyc,QAAS7e,KAAK2B,KAAKE,KAAKO,UACxBtB,MAAOd,KAAKgK,QAAQmS,kCAAkC,sBACrDlc,UAGLD,KAAKgK,QAAQ4E,KAAK,kBAAmB,KACnC,MAAMgY,EAAY5mB,KAAKgK,QAAQ2B,OAAO,uBActC,OAZI3L,KAAKF,QAAQ0jC,iBAEfpjC,IAAEM,KAAKkmB,EAAU,eAAe/Z,MAAM,KAAM,CAACwB,EAAKo1B,KAChDA,EAAWA,EAASzqB,OAAO3E,QAAQ,SAAU,IACzCrU,KAAK4hC,oBAAoB6B,KACuB,IAA9CzjC,KAAKF,QAAQ4jC,UAAUr6B,QAAQo6B,IACjCzjC,KAAKF,QAAQ4jC,UAAUr0B,KAAKo0B,KAM7BzjC,KAAKia,GAAG6nB,YAAY,CACzB9hC,KAAKqhC,OAAO,CACV9gC,UAAW,kBACXF,SAAUL,KAAKia,GAAGkoB,uBAChB,wCAAyCniC,KAAKF,SAEhD+e,QAAS7e,KAAK2B,KAAKE,KAAK5D,KACxBwC,KAAM,CACJ85B,OAAQ,cAGZv6B,KAAKia,GAAG0pB,cAAc,CACpBpjC,UAAW,oBACXqjC,eAAgB5jC,KAAKF,QAAQ8e,MAAMilB,UACnC/K,MAAO94B,KAAKF,QAAQ4jC,UAAUzsB,OAAOjX,KAAK8J,gBAAgB5K,KAAKc,OAC/DojC,MAAOpjC,KAAK2B,KAAKE,KAAK5D,KACtB2hC,SAAWl0B,GACF,6BAA+BuF,EAAIjJ,cAAc0D,GAAQ,KAAOA,EAAO,UAEhF5K,MAAOd,KAAKgK,QAAQmS,kCAAkC,uBAEvDlc,WAGLD,KAAKgK,QAAQ4E,KAAK,kBAAmB,IAC5B5O,KAAKia,GAAG6nB,YAAY,CACzB9hC,KAAKqhC,OAAO,CACV9gC,UAAW,kBACXF,SAAUL,KAAKia,GAAGkoB,uBAAuB,wCAAyCniC,KAAKF,SACvF+e,QAAS7e,KAAK2B,KAAKE,KAAKS,KACxB7B,KAAM,CACJ85B,OAAQ,cAGZv6B,KAAKia,GAAG0pB,cAAc,CACpBpjC,UAAW,oBACXqjC,eAAgB5jC,KAAKF,QAAQ8e,MAAMilB,UACnC/K,MAAO94B,KAAKF,QAAQgkC,UACpBV,MAAOpjC,KAAK2B,KAAKE,KAAKS,KACtBxB,MAAOd,KAAKgK,QAAQmS,kCAAkC,uBAEvDlc,UAGLD,KAAKgK,QAAQ4E,KAAK,sBAAuB,IAChC5O,KAAKia,GAAG6nB,YAAY,CACzB9hC,KAAKqhC,OAAO,CACV9gC,UAAW,kBACXF,SAAUL,KAAKia,GAAGkoB,uBAAuB,4CAA6CniC,KAAKF,SAC3F+e,QAAS7e,KAAK2B,KAAKE,KAAKU,SACxB9B,KAAM,CACJ85B,OAAQ,cAGZv6B,KAAKia,GAAG0pB,cAAc,CACpBpjC,UAAW,wBACXqjC,eAAgB5jC,KAAKF,QAAQ8e,MAAMilB,UACnC/K,MAAO94B,KAAKF,QAAQikC,cACpBX,MAAOpjC,KAAK2B,KAAKE,KAAKU,SACtBzB,MAAOd,KAAKgK,QAAQmS,kCAAkC,2BAEvDlc,UAGLD,KAAKgK,QAAQ4E,KAAK,eAAgB,IACzB5O,KAAK6hC,aAAa,iBAAkB7hC,KAAK2B,KAAK0E,MAAMC,QAAQ,GAAM,IAG3EtG,KAAKgK,QAAQ4E,KAAK,mBAAoB,IAC7B5O,KAAK6hC,aAAa,kBAAmB7hC,KAAK2B,KAAK0E,MAAMI,YAAY,GAAO,IAGjFzG,KAAKgK,QAAQ4E,KAAK,mBAAoB,IAC7B5O,KAAK6hC,aAAa,kBAAmB7hC,KAAK2B,KAAK0E,MAAMG,YAAY,GAAM,IAGhFxG,KAAKgK,QAAQ4E,KAAK,YAAa,IACtB5O,KAAKqhC,OAAO,CACjBhhC,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAMolB,eAC1CnlB,QAAS7e,KAAK2B,KAAK6D,MAAMC,UAAYzF,KAAKmhC,kBAAkB,uBAC5DrgC,MAAOd,KAAKgK,QAAQqS,oBAAoB,gCACvCpc,UAGLD,KAAKgK,QAAQ4E,KAAK,YAAa,IACtB5O,KAAKqhC,OAAO,CACjBhhC,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAMqlB,aAC1CplB,QAAS7e,KAAK2B,KAAK6D,MAAME,QAAU1F,KAAKmhC,kBAAkB,qBAC1DrgC,MAAOd,KAAKgK,QAAQqS,oBAAoB,8BACvCpc,UAGL,MAAMikC,EAAclkC,KAAKqhC,OAAO,CAC9BhhC,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAMulB,WAC1CtlB,QAAS7e,KAAK2B,KAAKmE,UAAUG,KAAOjG,KAAKmhC,kBAAkB,eAC3DrgC,MAAOd,KAAKgK,QAAQqS,oBAAoB,wBAGpC+nB,EAAgBpkC,KAAKqhC,OAAO,CAChChhC,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAMylB,aAC1CxlB,QAAS7e,KAAK2B,KAAKmE,UAAUI,OAASlG,KAAKmhC,kBAAkB,iBAC7DrgC,MAAOd,KAAKgK,QAAQqS,oBAAoB,0BAGpCioB,EAAetkC,KAAKqhC,OAAO,CAC/BhhC,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAM2lB,YAC1C1lB,QAAS7e,KAAK2B,KAAKmE,UAAUK,MAAQnG,KAAKmhC,kBAAkB,gBAC5DrgC,MAAOd,KAAKgK,QAAQqS,oBAAoB,yBAGpCmoB,EAAcxkC,KAAKqhC,OAAO,CAC9BhhC,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAM6lB,cAC1C5lB,QAAS7e,KAAK2B,KAAKmE,UAAUM,QAAUpG,KAAKmhC,kBAAkB,eAC9DrgC,MAAOd,KAAKgK,QAAQqS,oBAAoB,wBAGpCtW,EAAU/F,KAAKqhC,OAAO,CAC1BhhC,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAM7Y,SAC1C8Y,QAAS7e,KAAK2B,KAAKmE,UAAUC,QAAU/F,KAAKmhC,kBAAkB,WAC9DrgC,MAAOd,KAAKgK,QAAQqS,oBAAoB,oBAGpCrW,EAAShG,KAAKqhC,OAAO,CACzBhhC,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAM5Y,QAC1C6Y,QAAS7e,KAAK2B,KAAKmE,UAAUE,OAAShG,KAAKmhC,kBAAkB,UAC7DrgC,MAAOd,KAAKgK,QAAQqS,oBAAoB,mBAG1Crc,KAAKgK,QAAQ4E,KAAK,qBAAsBzB,EAAKxB,OAAOu4B,EAAa,WACjElkC,KAAKgK,QAAQ4E,KAAK,uBAAwBzB,EAAKxB,OAAOy4B,EAAe,WACrEpkC,KAAKgK,QAAQ4E,KAAK,sBAAuBzB,EAAKxB,OAAO24B,EAAc,WACnEtkC,KAAKgK,QAAQ4E,KAAK,qBAAsBzB,EAAKxB,OAAO64B,EAAa,WACjExkC,KAAKgK,QAAQ4E,KAAK,iBAAkBzB,EAAKxB,OAAO5F,EAAS,WACzD/F,KAAKgK,QAAQ4E,KAAK,gBAAiBzB,EAAKxB,OAAO3F,EAAQ,WAEvDhG,KAAKgK,QAAQ4E,KAAK,mBAAoB,IAC7B5O,KAAKia,GAAG6nB,YAAY,CACzB9hC,KAAKqhC,OAAO,CACV9gC,UAAW,kBACXF,SAAUL,KAAKia,GAAGkoB,uBAAuBniC,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAMulB,WAAYnkC,KAAKF,SAC1F+e,QAAS7e,KAAK2B,KAAKmE,UAAUA,UAC7BrF,KAAM,CACJ85B,OAAQ,cAGZv6B,KAAKia,GAAGmoB,SAAS,CACfpiC,KAAKia,GAAG6nB,YAAY,CAClBvhC,UAAW,aACXV,SAAU,CAACqkC,EAAaE,EAAeE,EAAcE,KAEvDxkC,KAAKia,GAAG6nB,YAAY,CAClBvhC,UAAW,YACXV,SAAU,CAACkG,EAASC,SAGvB/F,UAGLD,KAAKgK,QAAQ4E,KAAK,gBAAiB,IAC1B5O,KAAKia,GAAG6nB,YAAY,CACzB9hC,KAAKqhC,OAAO,CACV9gC,UAAW,kBACXF,SAAUL,KAAKia,GAAGkoB,uBAAuBniC,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAM8lB,YAAa1kC,KAAKF,SAC3F+e,QAAS7e,KAAK2B,KAAKE,KAAKK,OACxBzB,KAAM,CACJ85B,OAAQ,cAGZv6B,KAAKia,GAAG0pB,cAAc,CACpB7K,MAAO94B,KAAKF,QAAQ6kC,YACpBf,eAAgB5jC,KAAKF,QAAQ8e,MAAMilB,UACnCtjC,UAAW,uBACX6iC,MAAOpjC,KAAK2B,KAAKE,KAAKK,OACtBpB,MAAOd,KAAKgK,QAAQqS,oBAAoB,yBAEzCpc,UAGLD,KAAKgK,QAAQ4E,KAAK,eAAgB,IACzB5O,KAAKia,GAAG6nB,YAAY,CACzB9hC,KAAKqhC,OAAO,CACV9gC,UAAW,kBACXF,SAAUL,KAAKia,GAAGkoB,uBAAuBniC,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAMta,OAAQtE,KAAKF,SACtF+e,QAAS7e,KAAK2B,KAAK2C,MAAMA,MACzB7D,KAAM,CACJ85B,OAAQ,cAGZv6B,KAAKia,GAAGmoB,SAAS,CACfgB,MAAOpjC,KAAK2B,KAAK2C,MAAMA,MACvB/D,UAAW,aACXu4B,MAAO,CACL,sCACE,8FACA,mDACA,qDACF,SACA,mDACA7rB,KAAK,OAER,CACDlN,SAAWI,IACQA,EAAMc,KAAK,uCACnBylB,IAAI,CACXnc,MAAOvK,KAAKF,QAAQ8kC,mBAAmBC,IAAM,KAC7C3iC,OAAQlC,KAAKF,QAAQ8kC,mBAAmBpZ,IAAM,OAC7CsZ,UAAU9kC,KAAKgK,QAAQqS,oBAAoB,uBAC3Ctb,GAAG,YAAaf,KAAK+kC,iBAAiB7lC,KAAKc,UAE/CC,UAGLD,KAAKgK,QAAQ4E,KAAK,cAAe,IACxB5O,KAAKqhC,OAAO,CACjBhhC,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAM5a,MAC1C6a,QAAS7e,KAAK2B,KAAKqC,KAAKA,KAAOhE,KAAKmhC,kBAAkB,mBACtDrgC,MAAOd,KAAKgK,QAAQqS,oBAAoB,qBACvCpc,UAGLD,KAAKgK,QAAQ4E,KAAK,iBAAkB,IAC3B5O,KAAKqhC,OAAO,CACjBhhC,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAMomB,SAC1CnmB,QAAS7e,KAAK2B,KAAKa,MAAMA,MACzB1B,MAAOd,KAAKgK,QAAQqS,oBAAoB,sBACvCpc,UAGLD,KAAKgK,QAAQ4E,KAAK,eAAgB,IACzB5O,KAAKqhC,OAAO,CACjBhhC,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAM/a,OAC1Cgb,QAAS7e,KAAK2B,KAAKkC,MAAMA,MACzB/C,MAAOd,KAAKgK,QAAQqS,oBAAoB,sBACvCpc,UAGLD,KAAKgK,QAAQ4E,KAAK,YAAa,IACtB5O,KAAKqhC,OAAO,CACjBhhC,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAMqmB,OAC1CpmB,QAAS7e,KAAK2B,KAAKmD,GAAGrC,OAASzC,KAAKmhC,kBAAkB,wBACtDrgC,MAAOd,KAAKgK,QAAQqS,oBAAoB,iCACvCpc,UAGLD,KAAKgK,QAAQ4E,KAAK,oBAAqB,IAC9B5O,KAAKqhC,OAAO,CACjB9gC,UAAW,iBACXF,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAMsmB,WAC1CrmB,QAAS7e,KAAK2B,KAAK7B,QAAQ8F,WAC3B9E,MAAOd,KAAKgK,QAAQqS,oBAAoB,uBACvCpc,UAGLD,KAAKgK,QAAQ4E,KAAK,kBAAmB,IAC5B5O,KAAKqhC,OAAO,CACjB9gC,UAAW,eACXF,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAM/D,MAC1CgE,QAAS7e,KAAK2B,KAAK7B,QAAQ+F,SAC3B/E,MAAOd,KAAKgK,QAAQqS,oBAAoB,qBACvCpc,UAGLD,KAAKgK,QAAQ4E,KAAK,cAAe,IACxB5O,KAAKqhC,OAAO,CACjBhhC,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAMnX,MAC1CoX,QAAS7e,KAAK2B,KAAK4F,QAAQE,KAAOzH,KAAKmhC,kBAAkB,QACzDrgC,MAAOd,KAAKgK,QAAQqS,oBAAoB,iBACvCpc,UAGLD,KAAKgK,QAAQ4E,KAAK,cAAe,IACxB5O,KAAKqhC,OAAO,CACjBhhC,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAMpX,MAC1CqX,QAAS7e,KAAK2B,KAAK4F,QAAQC,KAAOxH,KAAKmhC,kBAAkB,QACzDrgC,MAAOd,KAAKgK,QAAQqS,oBAAoB,iBACvCpc,UAGLD,KAAKgK,QAAQ4E,KAAK,cAAe,IACxB5O,KAAKqhC,OAAO,CACjBhhC,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAMumB,UAC1CtmB,QAAS7e,KAAK2B,KAAK7B,QAAQ6F,KAC3B7E,MAAOd,KAAKgK,QAAQqS,oBAAoB,qBACvCpc,UAWPshC,yBAEEvhC,KAAKgK,QAAQ4E,KAAK,oBAAqB,IAC9B5O,KAAKqhC,OAAO,CACjBhhC,SAAU,6CACVwe,QAAS7e,KAAK2B,KAAKa,MAAME,WACzB5B,MAAOd,KAAKgK,QAAQqS,oBAAoB,gBAAiB,OACxDpc,UAELD,KAAKgK,QAAQ4E,KAAK,oBAAqB,IAC9B5O,KAAKqhC,OAAO,CACjBhhC,SAAU,4CACVwe,QAAS7e,KAAK2B,KAAKa,MAAMG,WACzB7B,MAAOd,KAAKgK,QAAQqS,oBAAoB,gBAAiB,SACxDpc,UAELD,KAAKgK,QAAQ4E,KAAK,uBAAwB,IACjC5O,KAAKqhC,OAAO,CACjBhhC,SAAU,4CACVwe,QAAS7e,KAAK2B,KAAKa,MAAMI,cACzB9B,MAAOd,KAAKgK,QAAQqS,oBAAoB,gBAAiB,UACxDpc,UAELD,KAAKgK,QAAQ4E,KAAK,oBAAqB,IAC9B5O,KAAKqhC,OAAO,CACjBhhC,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAMwmB,UAC1CvmB,QAAS7e,KAAK2B,KAAKa,MAAMK,WACzB/B,MAAOd,KAAKgK,QAAQqS,oBAAoB,gBAAiB,OACxDpc,UAILD,KAAKgK,QAAQ4E,KAAK,mBAAoB,IAC7B5O,KAAKqhC,OAAO,CACjBhhC,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAM9b,WAC1C+b,QAAS7e,KAAK2B,KAAKa,MAAMM,UACzBhC,MAAOd,KAAKgK,QAAQqS,oBAAoB,iBAAkB,UACzDpc,UAGLD,KAAKgK,QAAQ4E,KAAK,oBAAqB,IAC9B5O,KAAKqhC,OAAO,CACjBhhC,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAM7b,YAC1C8b,QAAS7e,KAAK2B,KAAKa,MAAMO,WACzBjC,MAAOd,KAAKgK,QAAQqS,oBAAoB,iBAAkB,WACzDpc,UAGLD,KAAKgK,QAAQ4E,KAAK,mBAAoB,IAC7B5O,KAAKqhC,OAAO,CACjBhhC,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAMwmB,UAC1CvmB,QAAS7e,KAAK2B,KAAKa,MAAMQ,UACzBlC,MAAOd,KAAKgK,QAAQqS,oBAAoB,iBAAkB,UACzDpc,UAILD,KAAKgK,QAAQ4E,KAAK,qBAAsB,IAC/B5O,KAAKqhC,OAAO,CACjBhhC,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAMymB,OAC1CxmB,QAAS7e,KAAK2B,KAAKa,MAAMmB,OACzB7C,MAAOd,KAAKgK,QAAQqS,oBAAoB,wBACvCpc,UAIPuhC,wBACExhC,KAAKgK,QAAQ4E,KAAK,wBAAyB,IAClC5O,KAAKqhC,OAAO,CACjBhhC,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAM5a,MAC1C6a,QAAS7e,KAAK2B,KAAKqC,KAAKE,KACxBpD,MAAOd,KAAKgK,QAAQqS,oBAAoB,qBACvCpc,UAGLD,KAAKgK,QAAQ4E,KAAK,gBAAiB,IAC1B5O,KAAKqhC,OAAO,CACjBhhC,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAM3a,QAC1C4a,QAAS7e,KAAK2B,KAAKqC,KAAKC,OACxBnD,MAAOd,KAAKgK,QAAQqS,oBAAoB,mBACvCpc,UAUPwhC,yBACEzhC,KAAKgK,QAAQ4E,KAAK,kBAAmB,IAC5B5O,KAAKqhC,OAAO,CACjB9gC,UAAW,SACXF,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAM0mB,UAC1CzmB,QAAS7e,KAAK2B,KAAK2C,MAAMC,YACzBzD,MAAOd,KAAKgK,QAAQqS,oBAAoB,gBAAiB,SACxDpc,UAELD,KAAKgK,QAAQ4E,KAAK,oBAAqB,IAC9B5O,KAAKqhC,OAAO,CACjB9gC,UAAW,SACXF,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAM2mB,UAC1C1mB,QAAS7e,KAAK2B,KAAK2C,MAAME,YACzB1D,MAAOd,KAAKgK,QAAQqS,oBAAoB,gBAAiB,YACxDpc,UAELD,KAAKgK,QAAQ4E,KAAK,oBAAqB,IAC9B5O,KAAKqhC,OAAO,CACjB9gC,UAAW,SACXF,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAM4mB,WAC1C3mB,QAAS7e,KAAK2B,KAAK2C,MAAMG,WACzB3D,MAAOd,KAAKgK,QAAQqS,oBAAoB,gBAAiB,UACxDpc,UAELD,KAAKgK,QAAQ4E,KAAK,qBAAsB,IAC/B5O,KAAKqhC,OAAO,CACjB9gC,UAAW,SACXF,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAM6mB,UAC1C5mB,QAAS7e,KAAK2B,KAAK2C,MAAMI,YACzB5D,MAAOd,KAAKgK,QAAQqS,oBAAoB,gBAAiB,WACxDpc,UAELD,KAAKgK,QAAQ4E,KAAK,mBAAoB,IAC7B5O,KAAKqhC,OAAO,CACjB9gC,UAAW,SACXF,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAM8mB,WAC1C7mB,QAAS7e,KAAK2B,KAAK2C,MAAMK,OACzB7D,MAAOd,KAAKgK,QAAQqS,oBAAoB,sBACvCpc,UAELD,KAAKgK,QAAQ4E,KAAK,mBAAoB,IAC7B5O,KAAKqhC,OAAO,CACjB9gC,UAAW,SACXF,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAM+mB,WAC1C9mB,QAAS7e,KAAK2B,KAAK2C,MAAMM,OACzB9D,MAAOd,KAAKgK,QAAQqS,oBAAoB,sBACvCpc,UAELD,KAAKgK,QAAQ4E,KAAK,qBAAsB,IAC/B5O,KAAKqhC,OAAO,CACjB9gC,UAAW,SACXF,SAAUL,KAAKia,GAAG8nB,KAAK/hC,KAAKF,QAAQ8e,MAAMymB,OAC1CxmB,QAAS7e,KAAK2B,KAAK2C,MAAMO,SACzB/D,MAAOd,KAAKgK,QAAQqS,oBAAoB,wBACvCpc,UAIP2lC,MAAM5kC,EAAY6kC,GAChB,IAAK,IAAIC,EAAW,EAAGC,EAAWF,EAAOzkC,OAAQ0kC,EAAWC,EAAUD,IAAY,CAChF,MAAME,EAAQH,EAAOC,GACfG,EAAY1kC,MAAMC,QAAQwkC,GAASA,EAAM,GAAKA,EAC9C9qB,EAAU3Z,MAAMC,QAAQwkC,GAA4B,IAAjBA,EAAM5kC,OAAgB,CAAC4kC,EAAM,IAAMA,EAAM,GAAM,CAACA,GAEnF/F,EAASjgC,KAAKia,GAAG6nB,YAAY,CACjCvhC,UAAW,QAAU0lC,IACpBhmC,SAEH,IAAK,IAAIoO,EAAM,EAAGG,EAAM0M,EAAQ9Z,OAAQiN,EAAMG,EAAKH,IAAO,CACxD,MAAM63B,EAAMlmC,KAAKgK,QAAQ4E,KAAK,UAAYsM,EAAQ7M,IAC9C63B,GACFjG,EAAO5+B,OAAsB,mBAAR6kC,EAAqBA,EAAIlmC,KAAKgK,SAAWk8B,GAGlEjG,EAAO1J,SAASv1B,IAOpBmlC,mBAAmBnlC,GACjB,MAAMsmB,EAAQtmB,GAAchB,KAAK08B,SAE3B9V,EAAY5mB,KAAKgK,QAAQ2B,OAAO,uBAsBtC,GArBA3L,KAAKomC,gBAAgB9e,EAAO,CAC1B,iBAAkB,IACkB,SAA3BV,EAAU,aAEnB,mBAAoB,IACkB,WAA7BA,EAAU,eAEnB,sBAAuB,IACkB,cAAhCA,EAAU,kBAEnB,sBAAuB,IACkB,cAAhCA,EAAU,kBAEnB,wBAAyB,IACkB,gBAAlCA,EAAU,oBAEnB,0BAA2B,IACkB,kBAApCA,EAAU,wBAIjBA,EAAU,eAAgB,CAC5B,MAAM8c,EAAY9c,EAAU,eAAe/Z,MAAM,KAAKC,IAAK7O,GAClDA,EAAKoW,QAAQ,UAAW,IAC5BA,QAAQ,OAAQ,IAChBA,QAAQ,OAAQ,KAEfpM,EAAWzC,EAAMvE,KAAKyiC,EAAW1jC,KAAK8J,gBAAgB5K,KAAKc,OAEjEsnB,EAAMrmB,KAAK,wBAAwBP,KAAK,CAAC2N,EAAK3C,KAC5C,MAAMozB,EAAQ1+B,IAAEsL,GAEV26B,EAAavH,EAAMr+B,KAAK,SAAW,IAASwH,EAAW,GAC7D62B,EAAM/K,YAAY,UAAWsS,KAE/B/e,EAAMrmB,KAAK,0BAA0BoX,KAAKpQ,GAAUye,IAAI,cAAeze,GAGzE,GAAI2e,EAAU,aAAc,CAC1B,MAAMC,EAAWD,EAAU,aAC3BU,EAAMrmB,KAAK,wBAAwBP,KAAK,CAAC2N,EAAK3C,KAC5C,MAAMozB,EAAQ1+B,IAAEsL,GAEV26B,EAAavH,EAAMr+B,KAAK,SAAW,IAASomB,EAAW,GAC7DiY,EAAM/K,YAAY,UAAWsS,KAE/B/e,EAAMrmB,KAAK,0BAA0BoX,KAAKwO,GAE1C,MAAMmL,EAAepL,EAAU,kBAC/BU,EAAMrmB,KAAK,4BAA4BP,KAAK,CAAC2N,EAAK3C,KAChD,MAAMozB,EAAQ1+B,IAAEsL,GACV26B,EAAavH,EAAMr+B,KAAK,SAAW,IAASuxB,EAAe,GACjE8M,EAAM/K,YAAY,UAAWsS,KAE/B/e,EAAMrmB,KAAK,8BAA8BoX,KAAK2Z,GAGhD,GAAIpL,EAAU,eAAgB,CAC5B,MAAMc,EAAad,EAAU,eAC7BU,EAAMrmB,KAAK,8BAA8BP,KAAK,CAAC2N,EAAK3C,KAElD,MAAM26B,EAAajmC,IAAEsL,GAAMjL,KAAK,SAAW,IAASinB,EAAa,GACjE1nB,KAAKO,UAAY8lC,EAAY,UAAY,MAK/CD,gBAAgBplC,EAAYslC,GAC1BlmC,IAAEM,KAAK4lC,EAAO,CAACC,EAAUh4B,KACvBvO,KAAKia,GAAGusB,gBAAgBxlC,EAAWC,KAAKslC,GAAWh4B,OAIvDw2B,iBAAiB3oB,GACf,MACMymB,EAAUziC,IAAEgc,EAAMI,OAAOhL,YACzBi1B,EAAoB5D,EAAQv0B,OAC5Bo4B,EAAW7D,EAAQ5hC,KAAK,uCACxB0lC,EAAe9D,EAAQ5hC,KAAK,sCAC5B2lC,EAAiB/D,EAAQ5hC,KAAK,wCAEpC,IAAI4lC,EAEJ,QAAsBprB,IAAlBW,EAAM0qB,QAAuB,CAC/B,MAAMC,EAAa3mC,IAAEgc,EAAMI,QAAQhK,SACnCq0B,EAAY,CACVvO,EAAGlc,EAAM4qB,MAAQD,EAAW9gC,KAC5BoyB,EAAGjc,EAAM6qB,MAAQF,EAAW16B,UAG9Bw6B,EAAY,CACVvO,EAAGlc,EAAM0qB,QACTzO,EAAGjc,EAAM8qB,SAIb,MAAMzT,EACDzT,KAAKmnB,KAAKN,EAAUvO,EAvBP,KAuByB,EADrC7E,EAEDzT,KAAKmnB,KAAKN,EAAUxO,EAxBP,KAwByB,EAG3CsO,EAAajgB,IAAI,CAAEnc,MAAOkpB,EAAQ,KAAMvxB,OAAQuxB,EAAQ,OACxDiT,EAASjmC,KAAK,QAASgzB,EAAQ,IAAMA,GAEjCA,EAAQ,GAAKA,EAAQzzB,KAAKF,QAAQ8kC,mBAAmBC,KACvD+B,EAAelgB,IAAI,CAAEnc,MAAOkpB,EAAQ,EAAI,OAGtCA,EAAQ,GAAKA,EAAQzzB,KAAKF,QAAQ8kC,mBAAmBpZ,KACvDob,EAAelgB,IAAI,CAAExkB,OAAQuxB,EAAQ,EAAI,OAG3CgT,EAAkBnmC,KAAKmzB,EAAQ,MAAQA,KZ72BrC,QavDS,MACb9zB,YAAYqK,GACVhK,KAAKgK,QAAUA,EAEfhK,KAAK48B,QAAUx8B,IAAE9C,QACjB0C,KAAKoM,UAAYhM,IAAE6J,UAEnBjK,KAAKia,GAAK7Z,IAAEsB,WAAWuY,GACvBja,KAAK8Z,MAAQ9P,EAAQgQ,WAAW8E,KAChC9e,KAAKixB,QAAUjnB,EAAQgQ,WAAWiB,OAClCjb,KAAK08B,SAAW1yB,EAAQgQ,WAAW2iB,QACnC38B,KAAK2lB,UAAY3b,EAAQgQ,WAAW2B,SACpC3b,KAAKi8B,WAAajyB,EAAQgQ,WAAWkiB,UACrCl8B,KAAKF,QAAUkK,EAAQlK,QAEvBE,KAAKonC,aAAc,EACnBpnC,KAAKqnC,aAAernC,KAAKqnC,aAAanoC,KAAKc,MAG7Cgc,mBACE,OAAQhc,KAAKF,QAAQw0B,QAGvBna,aACEna,KAAKF,QAAQ68B,QAAU38B,KAAKF,QAAQ68B,SAAW,GAE1C38B,KAAKF,QAAQ68B,QAAQv7B,OAGxBpB,KAAKgK,QAAQ2B,OAAO,gBAAiB3L,KAAK08B,SAAU18B,KAAKF,QAAQ68B,SAFjE38B,KAAK08B,SAASpiB,OAKZta,KAAKF,QAAQwnC,kBACftnC,KAAK08B,SAASnG,SAASv2B,KAAKF,QAAQwnC,kBAGtCtnC,KAAKunC,iBAAgB,GAErBvnC,KAAK8Z,MAAM/Y,GAAG,wDAAyD,KACrEf,KAAKgK,QAAQ2B,OAAO,gCAGtB3L,KAAKgK,QAAQ2B,OAAO,8BAChB3L,KAAKF,QAAQ0nC,kBACfxnC,KAAK48B,QAAQ77B,GAAG,gBAAiBf,KAAKqnC,cAI1C9sB,UACEva,KAAK08B,SAAS78B,WAAW8D,SAErB3D,KAAKF,QAAQ0nC,kBACfxnC,KAAK48B,QAAQljB,IAAI,gBAAiB1Z,KAAKqnC,cAI3CA,eACE,GAAIrnC,KAAKixB,QAAQphB,SAAS,cACxB,OAAO,EAGT,MAAM43B,EAAeznC,KAAKixB,QAAQ5X,cAC5BquB,EAAc1nC,KAAKixB,QAAQ1mB,QAC3Bo9B,EAAgB3nC,KAAK08B,SAASx6B,SAC9B0lC,EAAkB5nC,KAAKi8B,WAAW/5B,SAGxC,IAAI2lC,EAAiB,EACjB7nC,KAAKF,QAAQgoC,iBACfD,EAAiBznC,IAAEJ,KAAKF,QAAQgoC,gBAAgBzuB,eAGlD,MAAM0uB,EAAgB/nC,KAAKoM,UAAUE,YAC/B07B,EAAkBhoC,KAAKixB,QAAQze,SAASnG,IAExC47B,EAAiBD,EAAkBH,EACnCK,EAFqBF,EAAkBP,EAEOI,EAAiBF,EAAgBC,GAEhF5nC,KAAKonC,aACPW,EAAgBE,GAAoBF,EAAgBG,EAAyBP,GAC9E3nC,KAAKonC,aAAc,EACnBpnC,KAAK08B,SAAShW,IAAI,CAChB9T,SAAU,QACVvG,IAAKw7B,EACLt9B,MAAOm9B,EACPS,OAAQ,MAEVnoC,KAAK2lB,UAAUe,IAAI,CACjB0hB,UAAWpoC,KAAK08B,SAASx6B,SAAW,KAE7BlC,KAAKonC,cACZW,EAAgBE,GAAoBF,EAAgBG,KACtDloC,KAAKonC,aAAc,EACnBpnC,KAAK08B,SAAShW,IAAI,CAChB9T,SAAU,WACVvG,IAAK,EACL9B,MAAO,OACP49B,OAAQ,SAEVnoC,KAAK2lB,UAAUe,IAAI,CACjB0hB,UAAW,MAKjBb,gBAAgBtK,GACVA,EACFj9B,KAAK08B,SAASrD,UAAUr5B,KAAKixB,SAEzBjxB,KAAKF,QAAQwnC,kBACftnC,KAAK08B,SAASnG,SAASv2B,KAAKF,QAAQwnC,kBAGpCtnC,KAAKF,QAAQ0nC,kBACfxnC,KAAKqnC,eAITgB,iBAAiBpL,GACfj9B,KAAKia,GAAGusB,gBAAgBxmC,KAAK08B,SAASz7B,KAAK,mBAAoBg8B,GAE/Dj9B,KAAKunC,gBAAgBtK,GAGvBqL,eAAe3O,GACb35B,KAAKia,GAAGusB,gBAAgBxmC,KAAK08B,SAASz7B,KAAK,iBAAkB04B,GACzDA,EACF35B,KAAKw6B,aAELx6B,KAAKy6B,WAITA,SAAS8N,GACP,IAAIC,EAAOxoC,KAAK08B,SAASz7B,KAAK,UACzBsnC,IACHC,EAAOA,EAAKp9B,IAAI,iBAAiBA,IAAI,oBAEvCpL,KAAKia,GAAGwuB,UAAUD,GAAM,GAG1BhO,WAAW+N,GACT,IAAIC,EAAOxoC,KAAK08B,SAASz7B,KAAK,UACzBsnC,IACHC,EAAOA,EAAKp9B,IAAI,iBAAiBA,IAAI,oBAEvCpL,KAAKia,GAAGwuB,UAAUD,GAAM,Kb1FtB,WcpDS,MACb7oC,YAAYqK,GACVhK,KAAKgK,QAAUA,EAEfhK,KAAKia,GAAK7Z,IAAEsB,WAAWuY,GACvBja,KAAK0oC,MAAQtoC,IAAE6J,SAASkT,MACxBnd,KAAKixB,QAAUjnB,EAAQgQ,WAAWiB,OAClCjb,KAAKF,QAAUkK,EAAQlK,QACvBE,KAAK2B,KAAO3B,KAAKF,QAAQ6e,SAEzB3U,EAAQ4E,KAAK,uBAAwB5O,KAAKF,QAAQ6e,SAAShZ,KAAK,oBAGlEwU,aACE,MAAMnZ,EAAahB,KAAKF,QAAQ6oC,cAAgB3oC,KAAK0oC,MAAQ1oC,KAAKF,QAAQoY,UACpEiF,EAAO,CACX,2CACG,oCAAmCnd,KAAKF,QAAQmM,+BAA+BjM,KAAK2B,KAAKqC,KAAKG,wBAC9F,mCAAkCnE,KAAKF,QAAQmM,qFAClD,SACA,2CACG,oCAAmCjM,KAAKF,QAAQmM,+BAA+BjM,KAAK2B,KAAKqC,KAAKN,cAC9F,mCAAkC1D,KAAKF,QAAQmM,oGAClD,SACCjM,KAAKF,QAAQ8oC,kBAMV,GALAxoC,IAAE,UAAUiB,OAAOrB,KAAKia,GAAG4uB,SAAS,CACpCtoC,UAAW,iCACX8X,KAAMrY,KAAK2B,KAAKqC,KAAKI,gBACrB0kC,SAAS,IACR7oC,UAAUK,OAEfF,IAAE,UAAUiB,OAAOrB,KAAKia,GAAG4uB,SAAS,CAClCtoC,UAAW,2BACX8X,KAAMrY,KAAK2B,KAAKqC,KAAKK,YACrBykC,SAAS,IACR7oC,UAAUK,QACb2M,KAAK,IAGD87B,EAAU,wGAA8D/oC,KAAK2B,KAAKqC,KAAKvB,oBAE7FzC,KAAKgpC,QAAUhpC,KAAKia,GAAGgvB,OAAO,CAC5B1oC,UAAW,cACX6iC,MAAOpjC,KAAK2B,KAAKqC,KAAKvB,OACtBymC,KAAMlpC,KAAKF,QAAQqpC,YACnBhsB,KAAMA,EACN4rB,OAAQA,IACP9oC,SAASs2B,SAASv1B,GAGvBuZ,UACEva,KAAKia,GAAGmvB,WAAWppC,KAAKgpC,SACxBhpC,KAAKgpC,QAAQrlC,SAGf0lC,aAAaC,EAAQd,GACnBc,EAAOvoC,GAAG,WAAaqb,IACjBA,EAAMqI,UAAYxlB,GAAI4b,KAAK+J,QAC7BxI,EAAME,iBACNksB,EAAKzsB,QAAQ,YAQnBwtB,cAAcC,EAAUC,EAAWC,GACjC1pC,KAAKia,GAAGwuB,UAAUe,EAAUC,EAAUr1B,OAASs1B,EAASt1B,OAS1Du1B,eAAehX,GACb,OAAOvyB,IAAE41B,SAAUC,IACjB,MAAMwT,EAAYzpC,KAAKgpC,QAAQ/nC,KAAK,mBAC9ByoC,EAAW1pC,KAAKgpC,QAAQ/nC,KAAK,kBAC7BuoC,EAAWxpC,KAAKgpC,QAAQ/nC,KAAK,kBAC7B2oC,EAAmB5pC,KAAKgpC,QAC3B/nC,KAAK,wDACF4oC,EAAe7pC,KAAKgpC,QACvB/nC,KAAK,kDAERjB,KAAKia,GAAG6vB,cAAc9pC,KAAKgpC,QAAS,KAClChpC,KAAKgK,QAAQuR,aAAa,iBAGrBoX,EAASjvB,KAAOyJ,EAAKS,WAAW+kB,EAASta,QAC5Csa,EAASjvB,IAAMivB,EAASta,MAG1BoxB,EAAU1oC,GAAG,6BAA8B,KAGzC4xB,EAASta,KAAOoxB,EAAUr1B,MAC1BpU,KAAKupC,cAAcC,EAAUC,EAAWC,KACvCt1B,IAAIue,EAASta,MAEhBqxB,EAAS3oC,GAAG,6BAA8B,KAGnC4xB,EAASta,MACZoxB,EAAUr1B,IAAIs1B,EAASt1B,OAEzBpU,KAAKupC,cAAcC,EAAUC,EAAWC,KACvCt1B,IAAIue,EAASjvB,KAEXuN,EAAIlI,gBACP2gC,EAAS3tB,QAAQ,SAGnB/b,KAAKupC,cAAcC,EAAUC,EAAWC,GACxC1pC,KAAKqpC,aAAaK,EAAUF,GAC5BxpC,KAAKqpC,aAAaI,EAAWD,GAE7B,MAAMO,OAA8CtuB,IAAzBkX,EAASG,YAChCH,EAASG,YAAc9yB,KAAKgK,QAAQlK,QAAQygC,gBAEhDqJ,EAAiBI,KAAK,UAAWD,GAEjC,MAAME,GAAqBtX,EAASjvB,KACxB1D,KAAKgK,QAAQlK,QAAQuE,YAEjCwlC,EAAaG,KAAK,UAAWC,GAE7BT,EAASrT,IAAI,QAAU/Z,IACrBA,EAAME,iBAEN2Z,EAASG,QAAQ,CACfrQ,MAAO4M,EAAS5M,MAChBriB,IAAKgmC,EAASt1B,MACdiE,KAAMoxB,EAAUr1B,MAChB0e,YAAa8W,EAAiBnR,GAAG,YACjC1F,cAAe8W,EAAapR,GAAG,cAEjCz4B,KAAKia,GAAGmvB,WAAWppC,KAAKgpC,aAI5BhpC,KAAKia,GAAGiwB,eAAelqC,KAAKgpC,QAAS,KAEnCS,EAAU/vB,MACVgwB,EAAShwB,MACT8vB,EAAS9vB,MAEgB,YAArBuc,EAASkU,SACXlU,EAASI,WAIbr2B,KAAKia,GAAGmwB,WAAWpqC,KAAKgpC,WACvBxS,UAMLG,OACE,MAAMhE,EAAW3yB,KAAKgK,QAAQ2B,OAAO,sBAErC3L,KAAKgK,QAAQ2B,OAAO,oBACpB3L,KAAK2pC,eAAehX,GAAU8D,KAAM9D,IAClC3yB,KAAKgK,QAAQ2B,OAAO,uBACpB3L,KAAKgK,QAAQ2B,OAAO,oBAAqBgnB,KACxCznB,KAAK,KACNlL,KAAKgK,QAAQ2B,OAAO,2BdpHpB,YetDS,MACbhM,YAAYqK,GACVhK,KAAKgK,QAAUA,EAEfhK,KAAKia,GAAK7Z,IAAEsB,WAAWuY,GACvBja,KAAKF,QAAUkK,EAAQlK,QACvBE,KAAKuZ,OAAS,CACZ,0EAA2E,KACzEvZ,KAAKq9B,UAEP,6DAA8D,KAC5Dr9B,KAAKsa,SAKX0B,mBACE,OAAQxW,EAAMwJ,QAAQhP,KAAKF,QAAQ2+B,QAAQz6B,MAG7CmW,aACEna,KAAKw+B,SAAWx+B,KAAKia,GAAGwkB,QAAQ,CAC9Bl+B,UAAW,oBACXR,SAAWI,IACQA,EAAMc,KAAK,0CACnB8hC,QAAQ,iDAElB9iC,SAASs2B,SAASv2B,KAAKF,QAAQoY,WAClC,MAAMymB,EAAW3+B,KAAKw+B,SAASv9B,KAAK,0CAEpCjB,KAAKgK,QAAQ2B,OAAO,gBAAiBgzB,EAAU3+B,KAAKF,QAAQ2+B,QAAQz6B,MAEpEhE,KAAKw+B,SAASz9B,GAAG,YAAckiB,IAAQA,EAAE3G,mBAG3C/B,UACEva,KAAKw+B,SAAS76B,SAGhB05B,SAEE,IAAKr9B,KAAKgK,QAAQ2B,OAAO,mBAEvB,YADA3L,KAAKsa,OAIP,MAAMwH,EAAM9hB,KAAKgK,QAAQ2B,OAAO,uBAChC,GAAImW,EAAIV,eAAiBU,EAAIrC,aAAc,CACzC,MAAMmI,EAAS9M,GAAIvJ,SAASuQ,EAAI5C,GAAIpE,GAAIhK,UAClCu5B,EAAOjqC,IAAEwnB,GAAQ/mB,KAAK,QAC5Bb,KAAKw+B,SAASv9B,KAAK,KAAKJ,KAAK,OAAQwpC,GAAM/pC,KAAK+pC,GAEhD,MAAMjxB,EAAM0B,GAAI7B,mBAAmB2O,GAC7ByY,EAAkBjgC,IAAEJ,KAAKF,QAAQoY,WAAW1F,SAClD4G,EAAI/M,KAAOg0B,EAAgBh0B,IAC3B+M,EAAInT,MAAQo6B,EAAgBp6B,KAE5BjG,KAAKw+B,SAAS9X,IAAI,CAChB4P,QAAS,QACTrwB,KAAMmT,EAAInT,KACVoG,IAAK+M,EAAI/M,WAGXrM,KAAKsa,OAITA,OACEta,KAAKw+B,SAASlkB,SfbZ,YgBvDS,MACb3a,YAAYqK,GACVhK,KAAKgK,QAAUA,EACfhK,KAAKia,GAAK7Z,IAAEsB,WAAWuY,GACvBja,KAAK0oC,MAAQtoC,IAAE6J,SAASkT,MACxBnd,KAAKixB,QAAUjnB,EAAQgQ,WAAWiB,OAClCjb,KAAKF,QAAUkK,EAAQlK,QACvBE,KAAK2B,KAAO3B,KAAKF,QAAQ6e,SAG3BxE,aACE,IAAImwB,EAAkB,GACtB,GAAItqC,KAAKF,QAAQk3B,qBAAsB,CACrC,MAAMlF,EAAO9R,KAAKuqB,MAAMvqB,KAAKwqB,IAAIxqC,KAAKF,QAAQk3B,sBAAwBhX,KAAKwqB,IAAI,OACzEC,EAAuF,GAAvEzqC,KAAKF,QAAQk3B,qBAAuBhX,KAAK0qB,IAAI,KAAM5Y,IAAOnK,QAAQ,GACrE,IAAM,SAASmK,GAAQ,IAC1CwY,EAAmB,UAAStqC,KAAK2B,KAAKa,MAAMgB,gBAAkB,MAAQinC,YAGxE,MAAMzpC,EAAahB,KAAKF,QAAQ6oC,cAAgB3oC,KAAK0oC,MAAQ1oC,KAAKF,QAAQoY,UACpEiF,EAAO,CACX,wEACE,sCAAwCnd,KAAKF,QAAQmM,GAAK,6BAA+BjM,KAAK2B,KAAKa,MAAMe,gBAAkB,WAC3H,qCAAuCvD,KAAKF,QAAQmM,GAAK,6EACzD,mEACAq+B,EACF,SACA,gDACE,qCAAuCtqC,KAAKF,QAAQmM,GAAK,6BAA+BjM,KAAK2B,KAAKa,MAAMkB,IAAM,WAC9G,oCAAsC1D,KAAKF,QAAQmM,GAAK,mFAC1D,UACAgB,KAAK,IAED87B,EAAU,yGAA8D/oC,KAAK2B,KAAKa,MAAMC,oBAE9FzC,KAAKgpC,QAAUhpC,KAAKia,GAAGgvB,OAAO,CAC5B7F,MAAOpjC,KAAK2B,KAAKa,MAAMC,OACvBymC,KAAMlpC,KAAKF,QAAQqpC,YACnBhsB,KAAMA,EACN4rB,OAAQA,IACP9oC,SAASs2B,SAASv1B,GAGvBuZ,UACEva,KAAKia,GAAGmvB,WAAWppC,KAAKgpC,SACxBhpC,KAAKgpC,QAAQrlC,SAGf0lC,aAAaC,EAAQd,GACnBc,EAAOvoC,GAAG,WAAaqb,IACjBA,EAAMqI,UAAYxlB,GAAI4b,KAAK+J,QAC7BxI,EAAME,iBACNksB,EAAKzsB,QAAQ,YAKnB4a,OACE32B,KAAKgK,QAAQ2B,OAAO,oBACpB3L,KAAK2qC,kBAAkBlU,KAAMh2B,IAE3BT,KAAKia,GAAGmvB,WAAWppC,KAAKgpC,SACxBhpC,KAAKgK,QAAQ2B,OAAO,uBAEA,iBAATlL,EAELT,KAAKF,QAAQgc,UAAU8uB,kBACzB5qC,KAAKgK,QAAQuR,aAAa,oBAAqB9a,GAE/CT,KAAKgK,QAAQ2B,OAAO,qBAAsBlL,GAG5CT,KAAKgK,QAAQ2B,OAAO,gCAAiClL,KAEtDyK,KAAK,KACNlL,KAAKgK,QAAQ2B,OAAO,yBAUxBg/B,kBACE,OAAOvqC,IAAE41B,SAAUC,IACjB,MAAM4U,EAAc7qC,KAAKgpC,QAAQ/nC,KAAK,qBAChC6pC,EAAY9qC,KAAKgpC,QAAQ/nC,KAAK,mBAC9B8pC,EAAY/qC,KAAKgpC,QAAQ/nC,KAAK,mBAEpCjB,KAAKia,GAAG6vB,cAAc9pC,KAAKgpC,QAAS,KAClChpC,KAAKgK,QAAQuR,aAAa,gBAG1BsvB,EAAYG,YAAYH,EAAYj3B,QAAQ7S,GAAG,SAAWqb,IACxD6Z,EAASG,QAAQha,EAAMI,OAAOqa,OAASza,EAAMI,OAAO7d,SACnDyV,IAAI,KAEP02B,EAAU/pC,GAAG,6BAA8B,KACzCf,KAAKia,GAAGwuB,UAAUsC,EAAWD,EAAU12B,SACtCA,IAAI,IAEFnD,EAAIlI,gBACP+hC,EAAU/uB,QAAQ,SAGpBgvB,EAAUjqC,MAAOsb,IACfA,EAAME,iBACN2Z,EAASG,QAAQ0U,EAAU12B,SAG7BpU,KAAKqpC,aAAayB,EAAWC,KAG/B/qC,KAAKia,GAAGiwB,eAAelqC,KAAKgpC,QAAS,KACnC6B,EAAYnxB,MACZoxB,EAAUpxB,MACVqxB,EAAUrxB,MAEe,YAArBuc,EAASkU,SACXlU,EAASI,WAIbr2B,KAAKia,GAAGmwB,WAAWpqC,KAAKgpC,ahBrExB,aiBnDS,MACbrpC,YAAYqK,GACVhK,KAAKgK,QAAUA,EACfhK,KAAKia,GAAK7Z,IAAEsB,WAAWuY,GAEvBja,KAAK2b,SAAW3R,EAAQgQ,WAAW2B,SAAS,GAC5C3b,KAAKF,QAAUkK,EAAQlK,QAEvBE,KAAKuZ,OAAS,CACZ,qCAAsC,KACpCvZ,KAAKsa,SAKX0B,mBACE,OAAQxW,EAAMwJ,QAAQhP,KAAKF,QAAQ2+B,QAAQj8B,OAG7C2X,aACEna,KAAKw+B,SAAWx+B,KAAKia,GAAGwkB,QAAQ,CAC9Bl+B,UAAW,uBACVN,SAASs2B,SAASv2B,KAAKF,QAAQoY,WAClC,MAAMymB,EAAW3+B,KAAKw+B,SAASv9B,KAAK,0CACpCjB,KAAKgK,QAAQ2B,OAAO,gBAAiBgzB,EAAU3+B,KAAKF,QAAQ2+B,QAAQj8B,OAEpExC,KAAKw+B,SAASz9B,GAAG,YAAckiB,IAAQA,EAAE3G,mBAG3C/B,UACEva,KAAKw+B,SAAS76B,SAGhB05B,OAAO7gB,EAAQJ,GACb,GAAItB,GAAIvF,MAAMiH,GAAS,CACrB,MAAM5J,EAAWxS,IAAEoc,GAAQhK,SACrB6tB,EAAkBjgC,IAAEJ,KAAKF,QAAQoY,WAAW1F,SAClD,IAAI4G,EAAM,GACNpZ,KAAKF,QAAQmrC,YACf7xB,EAAInT,KAAOmW,EAAM4qB,MAAQ,GACzB5tB,EAAI/M,IAAM+P,EAAM6qB,OAEhB7tB,EAAMxG,EAERwG,EAAI/M,KAAOg0B,EAAgBh0B,IAC3B+M,EAAInT,MAAQo6B,EAAgBp6B,KAE5BjG,KAAKw+B,SAAS9X,IAAI,CAChB4P,QAAS,QACTrwB,KAAMmT,EAAInT,KACVoG,IAAK+M,EAAI/M,WAGXrM,KAAKsa,OAITA,OACEta,KAAKw+B,SAASlkB,SjBNZ,akBxDS,MACb3a,YAAYqK,GACVhK,KAAKgK,QAAUA,EAEfhK,KAAKia,GAAK7Z,IAAEsB,WAAWuY,GACvBja,KAAKF,QAAUkK,EAAQlK,QACvBE,KAAKuZ,OAAS,CACZ,uBAAwB,CAAC6jB,EAAIna,KAC3BjjB,KAAKq9B,OAAOpa,EAAEzG,SAEhB,uDAAwD,KACtDxc,KAAKq9B,UAEP,qCAAsC,KACpCr9B,KAAKsa,SAKX0B,mBACE,OAAQxW,EAAMwJ,QAAQhP,KAAKF,QAAQ2+B,QAAQn6B,OAG7C6V,aACEna,KAAKw+B,SAAWx+B,KAAKia,GAAGwkB,QAAQ,CAC9Bl+B,UAAW,uBACVN,SAASs2B,SAASv2B,KAAKF,QAAQoY,WAClC,MAAMymB,EAAW3+B,KAAKw+B,SAASv9B,KAAK,0CAEpCjB,KAAKgK,QAAQ2B,OAAO,gBAAiBgzB,EAAU3+B,KAAKF,QAAQ2+B,QAAQn6B,OAGhE2M,EAAI3H,MACNW,SAASynB,YAAY,4BAA4B,GAAO,GAG1D1xB,KAAKw+B,SAASz9B,GAAG,YAAckiB,IAAQA,EAAE3G,mBAG3C/B,UACEva,KAAKw+B,SAAS76B,SAGhB05B,OAAO7gB,GACL,GAAIxc,KAAKgK,QAAQ4Q,aACf,OAAO,EAGT,MAAM/J,EAASiK,GAAIjK,OAAO2L,GAE1B,GAAI3L,EAAQ,CACV,MAAMuI,EAAM0B,GAAI7B,mBAAmBuD,GAC7B6jB,EAAkBjgC,IAAEJ,KAAKF,QAAQoY,WAAW1F,SAClD4G,EAAI/M,KAAOg0B,EAAgBh0B,IAC3B+M,EAAInT,MAAQo6B,EAAgBp6B,KAE5BjG,KAAKw+B,SAAS9X,IAAI,CAChB4P,QAAS,QACTrwB,KAAMmT,EAAInT,KACVoG,IAAK+M,EAAI/M,WAGXrM,KAAKsa,OAGP,OAAOzJ,EAGTyJ,OACEta,KAAKw+B,SAASlkB,SlBZZ,YmB1DS,MACb3a,YAAYqK,GACVhK,KAAKgK,QAAUA,EAEfhK,KAAKia,GAAK7Z,IAAEsB,WAAWuY,GACvBja,KAAK0oC,MAAQtoC,IAAE6J,SAASkT,MACxBnd,KAAKixB,QAAUjnB,EAAQgQ,WAAWiB,OAClCjb,KAAKF,QAAUkK,EAAQlK,QACvBE,KAAK2B,KAAO3B,KAAKF,QAAQ6e,SAG3BxE,aACE,MAAMnZ,EAAahB,KAAKF,QAAQ6oC,cAAgB3oC,KAAK0oC,MAAQ1oC,KAAKF,QAAQoY,UACpEiF,EAAO,CACX,qDACG,qCAAoCnd,KAAKF,QAAQmM,+BAA+BjM,KAAK2B,KAAKkC,MAAMH,iCAAiC1D,KAAK2B,KAAKkC,MAAME,4BACjJ,oCAAmC/D,KAAKF,QAAQmM,qFACnD,UACAgB,KAAK,IAED87B,EAAU,yGAA8D/oC,KAAK2B,KAAKkC,MAAMpB,oBAE9FzC,KAAKgpC,QAAUhpC,KAAKia,GAAGgvB,OAAO,CAC5B7F,MAAOpjC,KAAK2B,KAAKkC,MAAMpB,OACvBymC,KAAMlpC,KAAKF,QAAQqpC,YACnBhsB,KAAMA,EACN4rB,OAAQA,IACP9oC,SAASs2B,SAASv1B,GAGvBuZ,UACEva,KAAKia,GAAGmvB,WAAWppC,KAAKgpC,SACxBhpC,KAAKgpC,QAAQrlC,SAGf0lC,aAAaC,EAAQd,GACnBc,EAAOvoC,GAAG,WAAaqb,IACjBA,EAAMqI,UAAYxlB,GAAI4b,KAAK+J,QAC7BxI,EAAME,iBACNksB,EAAKzsB,QAAQ,YAKnBmvB,gBAAgBxnC,GAEd,MACMynC,EAAmB,sCACnBC,EAAU1nC,EAAIkV,MAFH,wHAKXyyB,EAAU3nC,EAAIkV,MADH,sDAIX0yB,EAAS5nC,EAAIkV,MADH,mCAIV2yB,EAAW7nC,EAAIkV,MADH,qDAIZ4yB,EAAU9nC,EAAIkV,MADH,kEAIX6yB,EAAa/nC,EAAIkV,MADH,+CAId8yB,EAAUhoC,EAAIkV,MADH,6BAIX+yB,EAAWjoC,EAAIkV,MADH,6DAIZgzB,EAAWloC,EAAIkV,MADH,kBAIZizB,EAAWnoC,EAAIkV,MADH,kBAIZkzB,EAAYpoC,EAAIkV,MADH,eAIbmzB,EAAUroC,EAAIkV,MADH,2DAGjB,IAAIozB,EACJ,GAAIZ,GAAiC,KAAtBA,EAAQ,GAAGhqC,OAAe,CACvC,MAAM6qC,EAAYb,EAAQ,GAC1B,IAAIc,EAAQ,EACZ,QAA0B,IAAfd,EAAQ,GAAoB,CACrC,MAAMe,EAAkBf,EAAQ,GAAGxyB,MAAMuyB,GACzC,GAAIgB,EACF,IAAK,IAAIhtC,EAAI,CAAC,KAAM,GAAI,GAAI9B,EAAI,EAAGmB,EAAIW,EAAEiC,OAAQ/D,EAAImB,EAAGnB,IACtD6uC,QAA4C,IAA3BC,EAAgB9uC,EAAI,GAAqB8B,EAAE9B,GAAKypB,SAASqlB,EAAgB9uC,EAAI,GAAI,IAAM,EAI9G2uC,EAAS5rC,IAAE,YACRS,KAAK,cAAe,GACpBA,KAAK,MAAO,2BAA6BorC,GAAaC,EAAQ,EAAI,UAAYA,EAAQ,KACtFrrC,KAAK,QAAS,OAAOA,KAAK,SAAU,YAClC,GAAIwqC,GAAWA,EAAQ,GAAGjqC,OAC/B4qC,EAAS5rC,IAAE,YACRS,KAAK,cAAe,GACpBA,KAAK,MAAO,2BAA6BwqC,EAAQ,GAAK,WACtDxqC,KAAK,QAAS,OAAOA,KAAK,SAAU,OACpCA,KAAK,YAAa,MAClBA,KAAK,oBAAqB,aACxB,GAAIyqC,GAAUA,EAAO,GAAGlqC,OAC7B4qC,EAAS5rC,IAAE,YACRS,KAAK,cAAe,GACpBA,KAAK,MAAOyqC,EAAO,GAAK,iBACxBzqC,KAAK,QAAS,OAAOA,KAAK,SAAU,OACpCA,KAAK,QAAS,mBACZ,GAAI0qC,GAAYA,EAAS,GAAGnqC,OACjC4qC,EAAS5rC,IAAE,qEACRS,KAAK,cAAe,GACpBA,KAAK,MAAO,4BAA8B0qC,EAAS,IACnD1qC,KAAK,QAAS,OAAOA,KAAK,SAAU,YAClC,GAAI2qC,GAAWA,EAAQ,GAAGpqC,OAC/B4qC,EAAS5rC,IAAE,YACRS,KAAK,cAAe,GACpBA,KAAK,MAAO,qCAAuC2qC,EAAQ,IAC3D3qC,KAAK,QAAS,OAAOA,KAAK,SAAU,YAClC,GAAI4qC,GAAcA,EAAW,GAAGrqC,OACrC4qC,EAAS5rC,IAAE,qEACRS,KAAK,cAAe,GACpBA,KAAK,SAAU,OACfA,KAAK,QAAS,OACdA,KAAK,MAAO,4BAA8B4qC,EAAW,SACnD,GAAKC,GAAWA,EAAQ,GAAGtqC,QAAYuqC,GAAYA,EAAS,GAAGvqC,OAAS,CAC7E,MAAMgrC,EAAQV,GAAWA,EAAQ,GAAGtqC,OAAUsqC,EAAQ,GAAKC,EAAS,GACpEK,EAAS5rC,IAAE,qEACRS,KAAK,cAAe,GACpBA,KAAK,SAAU,OACfA,KAAK,QAAS,OACdA,KAAK,MAAO,2CAA6CurC,EAAM,oBAC7D,GAAIR,GAAYC,GAAYC,EACjCE,EAAS5rC,IAAE,oBACRS,KAAK,MAAO6C,GACZ7C,KAAK,QAAS,OAAOA,KAAK,SAAU,WAClC,KAAIkrC,IAAWA,EAAQ,GAAG3qC,OAS/B,OAAO,EARP4qC,EAAS5rC,IAAE,YACRS,KAAK,cAAe,GACpBA,KAAK,MAAO,mDAAqDwrC,mBAAmBN,EAAQ,IAAM,0BAClGlrC,KAAK,QAAS,OAAOA,KAAK,SAAU,OACpCA,KAAK,YAAa,MAClBA,KAAK,oBAAqB,QAQ/B,OAFAmrC,EAAOxrC,SAAS,mBAETwrC,EAAO,GAGhBrV,OACE,MAAMte,EAAOrY,KAAKgK,QAAQ2B,OAAO,0BACjC3L,KAAKgK,QAAQ2B,OAAO,oBACpB3L,KAAKssC,gBAAgBj0B,GAAMoe,KAAM/yB,IAE/B1D,KAAKia,GAAGmvB,WAAWppC,KAAKgpC,SACxBhpC,KAAKgK,QAAQ2B,OAAO,uBAGpB,MAAMxL,EAAQH,KAAKkrC,gBAAgBxnC,GAE/BvD,GAEFH,KAAKgK,QAAQ2B,OAAO,oBAAqBxL,KAE1C+K,KAAK,KACNlL,KAAKgK,QAAQ2B,OAAO,yBAUxB2gC,gBAAgBj0B,GACd,OAAOjY,IAAE41B,SAAUC,IACjB,MAAMsW,EAAYvsC,KAAKgpC,QAAQ/nC,KAAK,mBAC9BurC,EAAYxsC,KAAKgpC,QAAQ/nC,KAAK,mBAEpCjB,KAAKia,GAAG6vB,cAAc9pC,KAAKgpC,QAAS,KAClChpC,KAAKgK,QAAQuR,aAAa,gBAE1BgxB,EAAUxrC,GAAG,6BAA8B,KACzCf,KAAKia,GAAGwuB,UAAU+D,EAAWD,EAAUn4B,SAGpCnD,EAAIlI,gBACPwjC,EAAUxwB,QAAQ,SAGpBywB,EAAU1rC,MAAOsb,IACfA,EAAME,iBACN2Z,EAASG,QAAQmW,EAAUn4B,SAG7BpU,KAAKqpC,aAAakD,EAAWC,KAG/BxsC,KAAKia,GAAGiwB,eAAelqC,KAAKgpC,QAAS,KACnCuD,EAAU7yB,MACV8yB,EAAU9yB,MAEe,YAArBuc,EAASkU,SACXlU,EAASI,WAIbr2B,KAAKia,GAAGmwB,WAAWpqC,KAAKgpC,anB5JxB,WoB5DS,MACbrpC,YAAYqK,GACVhK,KAAKgK,QAAUA,EAEfhK,KAAKia,GAAK7Z,IAAEsB,WAAWuY,GACvBja,KAAK0oC,MAAQtoC,IAAE6J,SAASkT,MACxBnd,KAAKixB,QAAUjnB,EAAQgQ,WAAWiB,OAClCjb,KAAKF,QAAUkK,EAAQlK,QACvBE,KAAK2B,KAAO3B,KAAKF,QAAQ6e,SAG3BxE,aACE,MAAMnZ,EAAahB,KAAKF,QAAQ6oC,cAAgB3oC,KAAK0oC,MAAQ1oC,KAAKF,QAAQoY,UACpEiF,EAAO,CACX,0BACE,gKACA,uFACA,QACF,KACAlQ,IAEFjN,KAAKgpC,QAAUhpC,KAAKia,GAAGgvB,OAAO,CAC5B7F,MAAOpjC,KAAK2B,KAAK7B,QAAQ6F,KACzBujC,KAAMlpC,KAAKF,QAAQqpC,YACnBhsB,KAAMnd,KAAKysC,qBACX1D,OAAQ5rB,EACRpd,SAAWI,IACTA,EAAMc,KAAK,gCAAgCylB,IAAI,CAC7C,aAAc,IACd,SAAY,cAGfzmB,SAASs2B,SAASv1B,GAGvBuZ,UACEva,KAAKia,GAAGmvB,WAAWppC,KAAKgpC,SACxBhpC,KAAKgpC,QAAQrlC,SAGf8oC,qBACE,MAAM/X,EAAS10B,KAAKF,QAAQ40B,OAAOzjB,EAAI9H,MAAQ,MAAQ,MACvD,OAAO/K,OAAOob,KAAKkb,GAAQ5nB,IAAK7N,IAC9B,MAAMytC,EAAUhY,EAAOz1B,GACjB0tC,EAAOvsC,IAAE,4CAKf,OAJAusC,EAAKtrC,OAAOjB,IAAE,eAAiBnB,EAAM,kBAAkBynB,IAAI,CACzD,MAAS,IACT,eAAgB,MACdrlB,OAAOjB,IAAE,WAAWE,KAAKN,KAAKgK,QAAQ4E,KAAK,QAAU89B,IAAYA,IAC9DC,EAAKrsC,SACX2M,KAAK,IAQV2/B,iBACE,OAAOxsC,IAAE41B,SAAUC,IACjBj2B,KAAKia,GAAG6vB,cAAc9pC,KAAKgpC,QAAS,KAClChpC,KAAKgK,QAAQuR,aAAa,gBAC1B0a,EAASG,YAEXp2B,KAAKia,GAAGmwB,WAAWpqC,KAAKgpC,WACvBxS,UAGLG,OACE32B,KAAKgK,QAAQ2B,OAAO,oBACpB3L,KAAK4sC,iBAAiBnW,KAAK,KACzBz2B,KAAKgK,QAAQ2B,OAAO,2BpBVpB,WqB1DS,MACbhM,YAAYqK,GACVhK,KAAKgK,QAAUA,EACfhK,KAAKia,GAAK7Z,IAAEsB,WAAWuY,GACvBja,KAAKF,QAAUkK,EAAQlK,QAEvBE,KAAK6sC,SAAU,EAEf7sC,KAAKuZ,OAAS,CACZ,wDAAyD,KACnDvZ,KAAKF,QAAQ+b,SACf7b,KAAKq9B,UAGT,+EAAgF,KAC9Er9B,KAAKsa,QAEP,sBAAuB,CAAC8iB,EAAIna,KACrBjjB,KAAKw+B,SAAS/F,GAAG,mBACpBz4B,KAAKsa,SAMb0B,mBACE,OAAOhc,KAAKF,QAAQw0B,UAAY9uB,EAAMwJ,QAAQhP,KAAKF,QAAQ2+B,QAAQqO,KAGrE3yB,aACEna,KAAKw+B,SAAWx+B,KAAKia,GAAGwkB,QAAQ,CAC9Bl+B,UAAW,qBACVN,SAASs2B,SAASv2B,KAAKF,QAAQoY,WAClC,MAAMymB,EAAW3+B,KAAKw+B,SAASv9B,KAAK,oBAEpCjB,KAAKgK,QAAQ2B,OAAO,gBAAiBgzB,EAAU3+B,KAAKF,QAAQ2+B,QAAQqO,KAGpE9sC,KAAKw+B,SAASz9B,GAAG,YAAa,KAAQf,KAAK6sC,SAAU,IAErD7sC,KAAKw+B,SAASz9B,GAAG,UAAW,KAAQf,KAAK6sC,SAAU,IAGrDtyB,UACEva,KAAKw+B,SAAS76B,SAGhB05B,SACE,MAAMzW,EAAY5mB,KAAKgK,QAAQ2B,OAAO,uBACtC,GAAIib,EAAUb,QAAUa,EAAUb,MAAM3E,cAAe,CACrD,MAAMjV,EAAO3G,EAAMuI,KAAK6Y,EAAUb,MAAM3C,kBACxC,GAAIjX,EAAM,CACR,MAAMi0B,EAAMjzB,EAAKjB,SAASC,GAE1BnM,KAAKw+B,SAAS9X,IAAI,CAChB4P,QAAS,QACTrwB,KAAM+Z,KAAKyc,IAAI2D,EAAIn6B,KAAOm6B,EAAI71B,MAAQ,EAAG,GA1DjB,GA2DxB8B,IAAK+zB,EAAI/zB,IAAM+zB,EAAIl+B,SAErBlC,KAAKgK,QAAQ2B,OAAO,6BAA8B3L,KAAKw+B,gBAGzDx+B,KAAKsa,OAITA,OACMta,KAAK6sC,SACP7sC,KAAKw+B,SAASlkB,UrBPhBY,QAAS,GAETvZ,KAAM,QAEN6lC,kBAAkB,EAClBuF,gBAAiB,MACjBjF,eAAgB,GAGhBnL,QAAS,CACP,CAAC,QAAS,CAAC,UACX,CAAC,OAAQ,CAAC,OAAQ,YAAa,UAC/B,CAAC,WAAY,CAAC,aACd,CAAC,QAAS,CAAC,UACX,CAAC,OAAQ,CAAC,KAAM,KAAM,cACtB,CAAC,QAAS,CAAC,UACX,CAAC,SAAU,CAAC,OAAQ,UAAW,UAC/B,CAAC,OAAQ,CAAC,aAAc,WAAY,UAItCsO,YAAY,EACZxM,QAAS,CACPj8B,MAAO,CACL,CAAC,SAAU,CAAC,aAAc,aAAc,gBAAiB,eACzD,CAAC,QAAS,CAAC,YAAa,aAAc,cACtC,CAAC,SAAU,CAAC,iBAEdwB,KAAM,CACJ,CAAC,OAAQ,CAAC,iBAAkB,YAE9BM,MAAO,CACL,CAAC,MAAO,CAAC,aAAc,WAAY,aAAc,gBACjD,CAAC,SAAU,CAAC,YAAa,YAAa,iBAExCwoC,IAAK,CACH,CAAC,QAAS,CAAC,UACX,CAAC,OAAQ,CAAC,OAAQ,YAAa,UAC/B,CAAC,OAAQ,CAAC,KAAM,cAChB,CAAC,QAAS,CAAC,UACX,CAAC,SAAU,CAAC,OAAQ,YACpB,CAAC,OAAQ,CAAC,aAAc,eAK5BxY,SAAS,EAET/pB,MAAO,KACPrI,OAAQ,KACRq+B,iBAAiB,EACjBl8B,aAAa,EACb8uB,gBAAiB,UAEjBnU,OAAO,EACPguB,aAAa,EACbpX,QAAS,EACTqX,eAAe,EACfjmC,WAAW,EACXkmC,kBAAkB,EAClBruB,QAAS,OACT3G,UAAW,KACXid,cAAe,EACfpL,wBAAyB,EACzBqK,YAAY,EACZC,gBAAgB,EAChBnb,YAAa,KACb8nB,oBAAoB,EAGpBb,SAAU,OACVV,WAAY,QACZvB,cAAe,SAEfiF,UAAW,CAAC,IAAK,aAAc,MAAO,KAAM,KAAM,KAAM,KAAM,KAAM,MAEpEO,UAAW,CACT,QAAS,cAAe,gBAAiB,cACzC,iBAAkB,YAAa,SAAU,gBACzC,SAAU,kBAAmB,WAE/B/B,qBAAsB,GACtB6B,iBAAiB,EAEjBM,UAAW,CAAC,IAAK,IAAK,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAE1DC,cAAe,CAAC,KAAM,MAGtBvB,OAAQ,CACN,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,WAC9E,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,WAC9E,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,WAC9E,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,WAC9E,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,WAC9E,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,WAC9E,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,WAC9E,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,YAIhFC,WAAY,CACV,CAAC,QAAS,UAAW,YAAa,YAAa,aAAc,UAAW,YAAa,SACrF,CAAC,MAAO,cAAe,SAAU,QAAS,OAAQ,OAAQ,kBAAmB,WAC7E,CAAC,SAAU,QAAS,YAAa,QAAS,aAAc,gBAAiB,UAAW,YACpF,CAAC,aAAc,eAAgB,eAAgB,SAAU,SAAU,SAAU,cAAe,eAC5F,CAAC,QAAS,QAAS,YAAa,UAAW,cAAe,SAAU,kBAAmB,QACvF,CAAC,gBAAiB,YAAa,eAAgB,mBAAoB,aAAc,cAAe,iBAAkB,YAClH,CAAC,UAAW,UAAW,cAAe,eAAgB,OAAQ,cAAe,YAAa,UAC1F,CAAC,WAAY,WAAY,QAAS,UAAW,QAAS,gBAAiB,YAAa,WAGtFP,YAAa,CACX5O,UAAW,UACXC,UAAW,WAGboR,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OAE/D9T,eAAgB,uBAEhB+T,mBAAoB,CAClBC,IAAK,GACLrZ,IAAK,IAIPmd,eAAe,EACfQ,aAAa,EAEbnS,qBAAsB,KAEtBlb,UAAW,CACTqxB,gBAAiB,KACjBC,OAAQ,KACRC,eAAgB,KAChBC,SAAU,KACVC,iBAAkB,KAClBzD,cAAe,KACf0D,QAAS,KACTC,QAAS,KACT7C,kBAAmB,KACnBnT,cAAe,KACfiW,mBAAoB,KACpBC,OAAQ,KACRC,UAAW,KACXC,QAAS,KACTC,YAAa,KACbC,UAAW,KACXC,QAAS,KACTC,SAAU,MAGZ3S,WAAY,CACVz8B,KAAM,YACNqvC,UAAU,EACVC,aAAa,GAGfxT,gBAAgB,EAChBC,oBAAqB,0IACrBC,sBAAsB,EACtBE,2BAA4B,GAC5BC,+BAAgC,CAC9B,kBACA,2BACA,mBACA,UACA,gBACA,mBACA,sBACA,mBACA,YAGFtG,OAAQ,CACN0Z,GAAI,CACF,MAAS,kBACT,SAAU,OACV,SAAU,OACV,IAAO,MACP,YAAa,QACb,SAAU,OACV,SAAU,SACV,SAAU,YACV,eAAgB,gBAChB,iBAAkB,eAClB,eAAgB,cAChB,eAAgB,gBAChB,eAAgB,eAChB,eAAgB,cAChB,kBAAmB,sBACnB,kBAAmB,oBACnB,mBAAoB,UACpB,oBAAqB,SACrB,YAAa,aACb,YAAa,WACb,YAAa,WACb,YAAa,WACb,YAAa,WACb,YAAa,WACb,YAAa,WACb,aAAc,uBACd,SAAU,mBAGZC,IAAK,CACH,MAAS,kBACT,QAAS,OACT,cAAe,OACf,IAAO,MACP,YAAa,QACb,QAAS,OACT,QAAS,SACT,QAAS,YACT,cAAe,gBACf,gBAAiB,eACjB,cAAe,cACf,cAAe,gBACf,cAAe,eACf,cAAe,cACf,iBAAkB,sBAClB,iBAAkB,oBAClB,kBAAmB,UACnB,mBAAoB,SACpB,WAAY,aACZ,WAAY,WACZ,WAAY,WACZ,WAAY,WACZ,WAAY,WACZ,WAAY,WACZ,WAAY,WACZ,YAAa,uBACb,QAAS,oBAGbzvB,MAAO,CACL,MAAS,kBACT,YAAe,yBACf,aAAgB,0BAChB,UAAa,uBACb,WAAc,wBACd,SAAY,sBACZ,UAAa,uBACb,SAAY,sBACZ,SAAY,sBACZ,UAAa,uBACb,UAAa,uBACb,OAAU,yBACV,QAAW,0BACX,UAAa,uBACb,KAAQ,iBACR,MAAS,kBACT,OAAU,mBACV,MAAS,kBACT,KAAQ,iBACR,OAAU,mBACV,UAAa,uBACb,WAAc,wBACd,KAAQ,iBACR,MAAS,kBACT,OAAU,mBACV,KAAQ,iBACR,OAAU,yBACV,MAAS,kBACT,UAAa,uBACb,MAAS,kBACT,YAAe,wBACf,OAAU,mBACV,QAAW,oBACX,SAAY,qBACZ,KAAQ,iBACR,SAAY,qBACZ,OAAU,mBACV,cAAiB,0BACjB,UAAa,sBACb,YAAe,wBACf,MAAS,kBACT,WAAc,wBACd,MAAS,kBACT,UAAa,sBACb,KAAQ,iBACR,cAAiB,0BACjB,MAAS,uB,kEsB1PA0vB,MAlGf,MACE3uC,YAAYQ,EAAOL,GAkBjB,GAjBAE,KAAKG,MAAQA,EACbH,KAAKF,QAAUM,IAAEwB,OAAO,GAAI,CAC1BwhC,MAAO,GACP5mB,OAAQ1c,EAAQoY,UAChB6D,QAAS,cACTwyB,UAAW,UACVzuC,GAGHE,KAAKwuC,SAAWpuC,IAAE,CAChB,6BACE,oCACA,sCACF,UACA6M,KAAK,KAGsB,WAAzBjN,KAAKF,QAAQic,QAAsB,CACrC,MAAM0yB,EAAezuC,KAAK22B,KAAKz3B,KAAKc,MAC9B0uC,EAAe1uC,KAAKsa,KAAKpb,KAAKc,MAC9B2uC,EAAiB3uC,KAAKu6B,OAAOr7B,KAAKc,MAExCA,KAAKF,QAAQic,QAAQlP,MAAM,KAAK3L,SAAQ,SAAS8zB,GAC7B,UAAdA,GACF70B,EAAMuZ,IAAI,yBACVvZ,EAAMY,GAAG,aAAc0tC,GAAc1tC,GAAG,aAAc2tC,IAC/B,UAAd1Z,EACT70B,EAAMY,GAAG,QAAS4tC,GACK,UAAd3Z,GACT70B,EAAMY,GAAG,QAAS0tC,GAAc1tC,GAAG,OAAQ2tC,OAMnD/X,OACE,MAAMx2B,EAAQH,KAAKG,MACbqS,EAASrS,EAAMqS,SACfo8B,EAAexuC,IAAEJ,KAAKF,QAAQ0c,QAAQhK,SAC5CA,EAAOnG,KAAOuiC,EAAaviC,IAC3BmG,EAAOvM,MAAQ2oC,EAAa3oC,KAE5B,MAAMuoC,EAAWxuC,KAAKwuC,SAChBpL,EAAQpjC,KAAKF,QAAQsjC,OAASjjC,EAAMU,KAAK,UAAYV,EAAMM,KAAK,SAChE8tC,EAAYvuC,KAAKF,QAAQyuC,WAAapuC,EAAMM,KAAK,aAEvD+tC,EAAShuC,SAAS+tC,GAClBC,EAASvtC,KAAK,yBAAyBoX,KAAK+qB,GAC5CoL,EAASjY,SAASv2B,KAAKF,QAAQ0c,QAE/B,MAAMqyB,EAAY1uC,EAAMo0B,aAClBua,EAAa3uC,EAAMkZ,cACnB01B,EAAeP,EAASja,aACxBya,EAAgBR,EAASn1B,cAEb,WAAdk1B,EACFC,EAAS9nB,IAAI,CACXra,IAAKmG,EAAOnG,IAAMyiC,EAClB7oC,KAAMuM,EAAOvM,MAAQ4oC,EAAY,EAAIE,EAAe,KAE/B,QAAdR,EACTC,EAAS9nB,IAAI,CACXra,IAAKmG,EAAOnG,IAAM2iC,EAClB/oC,KAAMuM,EAAOvM,MAAQ4oC,EAAY,EAAIE,EAAe,KAE/B,SAAdR,EACTC,EAAS9nB,IAAI,CACXra,IAAKmG,EAAOnG,KAAOyiC,EAAa,EAAIE,EAAgB,GACpD/oC,KAAMuM,EAAOvM,KAAO8oC,IAEC,UAAdR,GACTC,EAAS9nB,IAAI,CACXra,IAAKmG,EAAOnG,KAAOyiC,EAAa,EAAIE,EAAgB,GACpD/oC,KAAMuM,EAAOvM,KAAO4oC,IAIxBL,EAAShuC,SAAS,MAGpB8Z,OACEta,KAAKwuC,SAASzU,YAAY,MAC1BpsB,WAAW,KACT3N,KAAKwuC,SAAS7qC,UACb,KAGL42B,SACMv6B,KAAKwuC,SAAS3+B,SAAS,MACzB7P,KAAKsa,OAELta,KAAK22B,SCpCXv2B,IAAE6J,UAAUlJ,GAAG,SAAS,SAASkiB,GAC1B7iB,IAAE6iB,EAAEzG,QAAQC,QAAQ,mBAAmBrb,SAC1ChB,IAAE,wBAAwB25B,YAAY,QACtC35B,IAAE,oCAAoC25B,YAAY,cAItD35B,IAAE6J,UAAUlJ,GAAG,4BAA4B,SAASkiB,GAClD7iB,IAAE6iB,EAAEzG,QAAQC,QAAQ,uBAAuBxK,SAAS8nB,YAAY,QAChE35B,IAAE6iB,EAAEzG,QAAQC,QAAQ,uBAAuBxK,SAAShR,KAAK,oBAAoB84B,YAAY,aAG5EkV,MArEf,MACEtvC,YAAYQ,EAAOL,GACjBE,KAAKgiC,QAAU7hC,EACfH,KAAKF,QAAUM,IAAEwB,OAAO,GAAI,CAC1B4a,OAAQ1c,EAAQoY,WACfpY,GACHE,KAAKkvC,WAGPA,WACElvC,KAAKgiC,QAAQjhC,GAAG,QAAUkiB,IACxBjjB,KAAKu6B,SACLtX,EAAEksB,6BAINltC,QACE,IAAI/B,EAAUE,IAAE,wBAChBF,EAAQe,KAAK,oBAAoB84B,YAAY,UAC7C75B,EAAQ65B,YAAY,QAGtBpD,OACE32B,KAAKgiC,QAAQxhC,SAAS,UACtBR,KAAKgiC,QAAQ/vB,SAASzR,SAAS,QAE/B,IAAI6hC,EAAYriC,KAAKgiC,QAAQ1zB,OACzBkE,EAAS6vB,EAAU7vB,SACnBjI,EAAQ83B,EAAU9N,aAClB6a,EAAchvC,IAAE9C,QAAQiN,QACxB8kC,EAAoB1mC,WAAWvI,IAAEJ,KAAKF,QAAQ0c,QAAQkK,IAAI,iBAE1DlU,EAAOvM,KAAOsE,EAAQ6kC,EAAcC,EACtChN,EAAU3b,IAAI,cAAe0oB,EAAcC,GAAqB78B,EAAOvM,KAAOsE,IAE9E83B,EAAU3b,IAAI,cAAe,IAIjCpM,OACEta,KAAKgiC,QAAQjI,YAAY,UACzB/5B,KAAKgiC,QAAQ/vB,SAAS8nB,YAAY,QAGpCQ,SACE,IAAI+U,EAAWtvC,KAAKgiC,QAAQ/vB,SAASpC,SAAS,QAE9C7P,KAAKiC,QAEDqtC,EACFtvC,KAAKsa,OAELta,KAAK22B,SCzBI4Y,MA3Bf,MACE5vC,YAAYQ,EAAOL,GACjBE,KAAKwvC,OAASrvC,EACdH,KAAKyvC,UAAYrvC,IAAE,sCAGrBu2B,OACE32B,KAAKyvC,UAAUlZ,SAAStsB,SAASkT,MAAMwZ,OACvC32B,KAAKwvC,OAAOhvC,SAAS,QAAQm2B,OAC7B32B,KAAKwvC,OAAOzzB,QAAQ,mBACpB/b,KAAKwvC,OAAO91B,IAAI,QAAS,UAAU3Y,GAAG,QAAS,SAAUf,KAAKsa,KAAKpb,KAAKc,OACxEA,KAAKwvC,OAAOzuC,GAAG,UAAYqb,IACL,KAAhBA,EAAMszB,QACRtzB,EAAME,iBACNtc,KAAKsa,UAKXA,OACEta,KAAKwvC,OAAOzV,YAAY,QAAQzf,OAChCta,KAAKyvC,UAAUn1B,OACfta,KAAKwvC,OAAOzzB,QAAQ,mBACpB/b,KAAKwvC,OAAO91B,IAAI,aCnBpB,MAAMuB,EAAS00B,IAAS3wC,OAAO,yCACzB29B,EAAUgT,IAAS3wC,OAAO,8CAC1Bm+B,EAAcwS,IAAS3wC,OAAO,oCAC9B0c,EAAUi0B,IAAS3wC,OAAO,0DAC1B2c,EAAWg0B,IAAS3wC,OAAO,4FAC3Bk9B,EAAYyT,IAAS3wC,OAAO,CAChC,wEACA,6CACE,mDACE,+BACA,+BACA,+BACF,SACF,UACAiO,KAAK,KAED2iC,EAAYD,IAAS3wC,OAAO,4CAC5B6wC,EAAcF,IAAS3wC,OAAO,CAClC,2FACA,yEACAiO,KAAK,KAED60B,EAAc6N,IAAS3wC,OAAO,gCAC9BqiC,EAASsO,IAAS3wC,OAAO,yDAAyD,SAASmB,EAAOL,GAElGA,GAAWA,EAAQ+e,UACrB1e,EAAMU,KAAK,CACT,aAAcf,EAAQ+e,UAExB1e,EAAMM,KAAK,gBAAiB,IAAI6tC,EAAUnuC,EAAO,CAC/CijC,MAAOtjC,EAAQ+e,QACf3G,UAAWpY,EAAQoY,aACjBnX,GAAG,QAAUkiB,IACf7iB,IAAE6iB,EAAE2b,eAAen+B,KAAK,iBAAiB6Z,UAGzCxa,EAAQO,UACVF,EAAMG,KAAKR,EAAQO,UAGjBP,GAAWA,EAAQW,MAAgC,aAAxBX,EAAQW,KAAK85B,QAC1Cp6B,EAAMM,KAAK,iBAAkB,IAAIwuC,EAAW9uC,EAAO,CACjD+X,UAAWpY,EAAQoY,gBAKnBkqB,EAAWuN,IAAS3wC,OAAO,gDAAgD,SAASmB,EAAOL,GAC/F,MAAMF,EAAS2B,MAAMC,QAAQ1B,EAAQg5B,OAASh5B,EAAQg5B,MAAMhsB,KAAI,SAASpB,GACvE,MAAM/M,EAAyB,iBAAT+M,EAAqBA,EAAQA,EAAK/M,OAAS,GAC3Du7B,EAAUp6B,EAAQ8/B,SAAW9/B,EAAQ8/B,SAASl0B,GAAQA,EACtDokC,EAAQ1vC,IAAE,sDAAwDzB,EAAQ,iCAAmCA,EAAQ,UAI3H,OAFAmxC,EAAMxvC,KAAK45B,GAASz5B,KAAK,OAAQiL,GAE1BokC,KACJhwC,EAAQg5B,MAEb34B,EAAMG,KAAKV,GAAQiB,KAAK,CAAE,aAAcf,EAAQsjC,QAEhDjjC,EAAMY,GAAG,QAAS,yBAAyB,SAASkiB,GAClD,MAAM8sB,EAAK3vC,IAAEJ,MAEP0L,EAAOqkC,EAAGtvC,KAAK,QACf9B,EAAQoxC,EAAGtvC,KAAK,SAElBiL,EAAK5K,MACP4K,EAAK5K,MAAMivC,GACFjwC,EAAQkwC,WACjBlwC,EAAQkwC,UAAU/sB,EAAGvX,EAAM/M,SAK3BglC,EAAgBgM,IAAS3wC,OAAO,2DAA2D,SAASmB,EAAOL,GAC/G,MAAMF,EAAS2B,MAAMC,QAAQ1B,EAAQg5B,OAASh5B,EAAQg5B,MAAMhsB,KAAI,SAASpB,GACvE,MAAM/M,EAAyB,iBAAT+M,EAAqBA,EAAQA,EAAK/M,OAAS,GAC3Du7B,EAAUp6B,EAAQ8/B,SAAW9/B,EAAQ8/B,SAASl0B,GAAQA,EAEtDokC,EAAQ1vC,IAAE,sDAAwDzB,EAAQ,iCAAmC+M,EAAO,UAE1H,OADAokC,EAAMxvC,KAAK,CAACyhC,EAAKjiC,EAAQ8jC,gBAAiB,IAAK1J,IAAUz5B,KAAK,OAAQiL,GAC/DokC,KACJhwC,EAAQg5B,MAEb34B,EAAMG,KAAKV,GAAQiB,KAAK,CAAE,aAAcf,EAAQsjC,QAEhDjjC,EAAMY,GAAG,QAAS,yBAAyB,SAASkiB,GAClD,MAAM8sB,EAAK3vC,IAAEJ,MAEP0L,EAAOqkC,EAAGtvC,KAAK,QACf9B,EAAQoxC,EAAGtvC,KAAK,SAElBiL,EAAK5K,MACP4K,EAAK5K,MAAMivC,GACFjwC,EAAQkwC,WACjBlwC,EAAQkwC,UAAU/sB,EAAGvX,EAAM/M,SAK3BwjC,EAAyB,SAAS9hC,EAAUP,GAChD,OAAOO,EAAW,IAAM0hC,EAAKjiC,EAAQ8e,MAAMqxB,MAAO,SAG9CC,EAAiB,SAASC,EAAKpwC,GACnC,OAAO+hC,EAAY,CACjBT,EAAO,CACL9gC,UAAW,kBACXF,SAAU8vC,EAAI/M,MAAQ,IAAMrB,EAAK,mBACjCljB,QAASsxB,EAAItxB,QACbpe,KAAM,CACJ85B,OAAQ,cAGZ6H,EAAS,CACP7hC,UAAW4vC,EAAI5vC,UACfu4B,MAAOqX,EAAIrX,MACX8G,SAAUuQ,EAAIvQ,SACdoQ,UAAWG,EAAIH,aAEhB,CAAEjwC,SAAUA,IAAYE,UAGvBmwC,EAAsB,SAASD,EAAKpwC,GACxC,OAAO+hC,EAAY,CACjBT,EAAO,CACL9gC,UAAW,kBACXF,SAAU8vC,EAAI/M,MAAQ,IAAMrB,EAAK,mBACjCljB,QAASsxB,EAAItxB,QACbpe,KAAM,CACJ85B,OAAQ,cAGZoJ,EAAc,CACZpjC,UAAW4vC,EAAI5vC,UACfqjC,eAAgBuM,EAAIvM,eACpB9K,MAAOqX,EAAIrX,MACX8G,SAAUuQ,EAAIvQ,SACdoQ,UAAWG,EAAIH,aAEhB,CAAEjwC,SAAUA,IAAYE,UAGvBowC,EAA0B,SAASF,GACvC,OAAOrO,EAAY,CACjBT,EAAO,CACL9gC,UAAW,kBACXF,SAAU8vC,EAAI/M,MAAQ,IAAMrB,EAAK,mBACjCljB,QAASsxB,EAAItxB,QACbpe,KAAM,CACJ85B,OAAQ,cAGZ6H,EAAS,CACPN,EAAY,CACVvhC,UAAW,aACXV,SAAUswC,EAAIrX,MAAM,KAEtBgJ,EAAY,CACVvhC,UAAW,YACXV,SAAUswC,EAAIrX,MAAM,SAGvB74B,UA6CCqwC,EAAsB,SAASH,GACnC,OAAOrO,EAAY,CACjBT,EAAO,CACL9gC,UAAW,kBACXF,SAAU8vC,EAAI/M,MAAQ,IAAMrB,EAAK,mBACjCljB,QAASsxB,EAAItxB,QACbpe,KAAM,CACJ85B,OAAQ,cAGZ6H,EAAS,CACP7hC,UAAW,aACXu4B,MAAO,CACL,sCACE,8FACA,mDACA,qDACF,SACA,mDACA7rB,KAAK,OAER,CACDlN,SAAU,SAASI,GACAA,EAAMc,KAAK,uCACnBylB,IAAI,CACXnc,MAAO4lC,EAAItL,IAAM,KACjB3iC,OAAQiuC,EAAI3kB,IAAM,OAEjBsZ,UAAUqL,EAAIH,WACdO,WAAU,SAASttB,IAvEH,SAAS7G,EAAOyoB,EAAKrZ,GAC5C,MACMqX,EAAUziC,IAAEgc,EAAMI,OAAOhL,YACzBi1B,EAAoB5D,EAAQv0B,OAC5Bo4B,EAAW7D,EAAQ5hC,KAAK,uCACxB0lC,EAAe9D,EAAQ5hC,KAAK,sCAC5B2lC,EAAiB/D,EAAQ5hC,KAAK,wCAEpC,IAAI4lC,EAEJ,QAAsBprB,IAAlBW,EAAM0qB,QAAuB,CAC/B,MAAMC,EAAa3mC,IAAEgc,EAAMI,QAAQhK,SACnCq0B,EAAY,CACVvO,EAAGlc,EAAM4qB,MAAQD,EAAW9gC,KAC5BoyB,EAAGjc,EAAM6qB,MAAQF,EAAW16B,UAG9Bw6B,EAAY,CACVvO,EAAGlc,EAAM0qB,QACTzO,EAAGjc,EAAM8qB,SAIb,MAAMzT,EACDzT,KAAKmnB,KAAKN,EAAUvO,EAvBP,KAuByB,EADrC7E,EAEDzT,KAAKmnB,KAAKN,EAAUxO,EAxBP,KAwByB,EAG3CsO,EAAajgB,IAAI,CAAEnc,MAAOkpB,EAAQ,KAAMvxB,OAAQuxB,EAAQ,OACxDiT,EAASjmC,KAAK,QAASgzB,EAAQ,IAAMA,GAEjCA,EAAQ,GAAKA,EAAQoR,GACvB+B,EAAelgB,IAAI,CAAEnc,MAAOkpB,EAAQ,EAAI,OAGtCA,EAAQ,GAAKA,EAAQjI,GACvBob,EAAelgB,IAAI,CAAExkB,OAAQuxB,EAAQ,EAAI,OAG3CgT,EAAkBnmC,KAAKmzB,EAAQ,MAAQA,GAiC/BsR,CAAiB9hB,EAAGktB,EAAItL,IAAKsL,EAAI3kB,WAGtCvrB,UAGCsiC,EAAUoN,IAAS3wC,OAAO,qCAAqC,SAASmB,EAAOL,GACnF,MAAMO,EAAW,GACjB,IAAK,IAAImrB,EAAM,EAAGglB,EAAU1wC,EAAQ0iC,OAAOphC,OAAQoqB,EAAMglB,EAAShlB,IAAO,CACvE,MAAMwJ,EAAYl1B,EAAQk1B,UACpBwN,EAAS1iC,EAAQ0iC,OAAOhX,GACxBiX,EAAa3iC,EAAQ2iC,WAAWjX,GAChCtQ,EAAU,GAChB,IAAK,IAAI2pB,EAAM,EAAG4L,EAAUjO,EAAOphC,OAAQyjC,EAAM4L,EAAS5L,IAAO,CAC/D,MAAMx+B,EAAQm8B,EAAOqC,GACf6L,EAAYjO,EAAWoC,GAC7B3pB,EAAQ7L,KAAK,CACX,wDACA,2BAA4BhJ,EAAO,KACnC,eAAgB2uB,EAAW,KAC3B,eAAgB3uB,EAAO,KACvB,eAAgBqqC,EAAW,KAC3B,eAAgBA,EAAW,KAC3B,gDACAzjC,KAAK,KAET5M,EAASgP,KAAK,+BAAiC6L,EAAQjO,KAAK,IAAM,UAEpE9M,EAAMG,KAAKD,EAAS4M,KAAK,KAEzB9M,EAAMc,KAAK,mBAAmBP,MAAK,WACjCN,IAAEJ,MAAMS,KAAK,gBAAiB,IAAI6tC,EAAUluC,IAAEJ,MAAO,CACnDkY,UAAWpY,EAAQoY,mBAKnBy4B,EAAsB,SAASR,EAAK3xB,GACxC,OAAOsjB,EAAY,CACjBvhC,UAAW,aACXV,SAAU,CACRwhC,EAAO,CACL9gC,UAAW,4BACXF,SAAU8vC,EAAI/M,MACdvkB,QAASsxB,EAAIxuC,KAAK0E,MAAMC,OACxBxF,MAAOqvC,EAAIS,aACX7wC,SAAU,SAASiiC,GACjB,MAAMC,EAAeD,EAAQ/gC,KAAK,sBAErB,cAATud,IACFyjB,EAAavb,IAAI,mBAAoB,WACrCsb,EAAQnhC,KAAK,iBAAkB,eAIrCwgC,EAAO,CACL9gC,UAAW,kBACXF,SAAU0hC,EAAK,mBACfljB,QAASsxB,EAAIxuC,KAAK0E,MAAME,KACxB9F,KAAM,CACJ85B,OAAQ,cAGZ6H,EAAS,CACPtJ,MAAO,CACL,QACE,oDACE,mCAAqCqX,EAAIxuC,KAAK0E,MAAMG,WAAa,SACnE,QACA,sHACE2pC,EAAIxuC,KAAK0E,MAAMK,YACjB,YACF,SACA,oDACE,uBACE,sHACA,sGACEypC,EAAIxuC,KAAK0E,MAAMS,SACjB,YACF,SACF,SACA,oDACE,mCAAqCqpC,EAAIxuC,KAAK0E,MAAMI,WAAa,SACjE,QACE,2HACE0pC,EAAIxuC,KAAK0E,MAAMQ,eACjB,YACF,SACA,oDACE,uBACE,sHACA,sGACEspC,EAAIxuC,KAAK0E,MAAMS,SACjB,YACF,SACF,SACF,UACAmG,KAAK,IACPlN,SAAU,SAASsiC,GACjBA,EAAUphC,KAAK,gBAAgBP,MAAK,WAClC,MAAM4hC,EAAUliC,IAAEJ,MAClBsiC,EAAQjhC,OAAOkhC,EAAQ,CACrBC,OAAQ2N,EAAI3N,OACZxN,UAAWsN,EAAQ7hC,KAAK,WACvBR,aAGQ,SAATue,GACF6jB,EAAUphC,KAAK,yBAAyBqZ,OACxC+nB,EAAU3b,IAAI,CAAE,YAAa,WACX,SAATlI,IACT6jB,EAAUphC,KAAK,yBAAyBqZ,OACxC+nB,EAAU3b,IAAI,CAAE,YAAa,YAGjC5lB,MAAO,SAASsb,GACd,MAAM4lB,EAAU5hC,IAAEgc,EAAMI,QAClBwY,EAAYgN,EAAQvhC,KAAK,SAC/B,IAAI9B,EAAQqjC,EAAQvhC,KAAK,SACzB,MAAMowC,EAAY5mC,SAAS6mC,eAAe,YAAYnyC,MAChDoyC,EAAY9mC,SAAS6mC,eAAe,YAAYnyC,MAStD,GARc,OAAVA,EACFyd,EAAMggB,kBACa,gBAAVz9B,EACTA,EAAQoyC,EACW,gBAAVpyC,IACTA,EAAQkyC,GAGN7b,GAAar2B,EAAO,CACtB,MAAMM,EAAoB,cAAd+1B,EAA4B,mBAAqB,QACvDgO,EAAShB,EAAQvlB,QAAQ,eAAexb,KAAK,sBAC7CgiC,EAAiBjB,EAAQvlB,QAAQ,eAAexb,KAAK,8BAE3D+hC,EAAOtc,IAAIznB,EAAKN,GAChBskC,EAAepiC,KAAK,QAAUm0B,EAAWr2B,GAE5B,SAAT6f,EACF2xB,EAAIH,UAAU,YAAarxC,GACT,SAAT6f,EACT2xB,EAAIH,UAAU,YAAarxC,GAE3BwxC,EAAIH,UAAUhb,EAAWr2B,UAMlCsB,UAGCgpC,EAAS0G,IAAS3wC,OAAO,6EAA6E,SAASmB,EAAOL,GACtHA,EAAQopC,MACV/oC,EAAMK,SAAS,QAEjBL,EAAMU,KAAK,CACT,aAAcf,EAAQsjC,QAExBjjC,EAAMG,KAAK,CACT,mCACGR,EAAQsjC,MAAQ,iLAAmLtjC,EAAQsjC,MAAQ,cAAgB,GACpO,gCAAkCtjC,EAAQqd,KAAO,SAChDrd,EAAQipC,OAAS,kCAAoCjpC,EAAQipC,OAAS,SAAW,GACpF,UACA97B,KAAK,KAEP9M,EAAMM,KAAK,QAAS,IAAI8uC,EAAQpvC,EAAOL,OAGnCkxC,EAAc,SAASb,GAC3B,MAAMhzB,EAAO,kEAC4BgzB,EAAIlkC,GAAK,6BAA+BkkC,EAAIxuC,KAAKkC,MAAMH,IAAM,8BAAgCysC,EAAIxuC,KAAKkC,MAAME,UAAY,oDACzHosC,EAAIlkC,GAAK,0DAE3C88B,EAAS,CACb,qGACEoH,EAAIxuC,KAAKkC,MAAMpB,OACjB,aACAwK,KAAK,IAEP,OAAOg8B,EAAO,CACZ7F,MAAO+M,EAAIxuC,KAAKkC,MAAMpB,OACtBymC,KAAMiH,EAAIjH,KACV/rB,KAAMA,EACN4rB,OAAQA,IACP9oC,UAGCgxC,EAAc,SAASd,GAC3B,MAAMhzB,EAAO,gGAC6BgzB,EAAIlkC,GAAK,6BAA+BkkC,EAAIxuC,KAAKa,MAAMe,gBAAkB,6CAC1E4sC,EAAIlkC,GAAK,6GAChDkkC,EAAI7F,gBACN,wEAEyC6F,EAAIlkC,GAAK,6BAA+BkkC,EAAIxuC,KAAKa,MAAMkB,IAAM,4CAC9DysC,EAAIlkC,GAAK,0DAE3C88B,EAAS,CACb,oHACEoH,EAAIxuC,KAAKa,MAAMC,OACjB,aACAwK,KAAK,IAEP,OAAOg8B,EAAO,CACZ7F,MAAO+M,EAAIxuC,KAAKa,MAAMC,OACtBymC,KAAMiH,EAAIjH,KACV/rB,KAAMA,EACN4rB,OAAQA,IACP9oC,UAGCixC,EAAa,SAASf,GAC1B,MAAMhzB,EAAO,iEAC2BgzB,EAAIlkC,GAAK,6BAA+BkkC,EAAIxuC,KAAKqC,KAAKG,cAAgB,2CACvEgsC,EAAIlkC,GAAK,wHAGRkkC,EAAIlkC,GAAK,6BAA+BkkC,EAAIxuC,KAAKqC,KAAKN,IAAM,2CAC7DysC,EAAIlkC,GAAK,0EAE9CkkC,EAAIvH,kBAA0N,GAAtM,yDAA2DuH,EAAIlkC,GAAK,oCAAsCkkC,EAAIlkC,GAAK,8BAAgCkkC,EAAIxuC,KAAKqC,KAAKI,gBAAkB,kBAC7M,yDAA2D+rC,EAAIlkC,GAAK,oCAAsCkkC,EAAIlkC,GAAK,8BAAgCkkC,EAAIxuC,KAAKqC,KAAKK,YAAc,iBACzK0kC,EAAS,CACb,oGACEoH,EAAIxuC,KAAKqC,KAAKvB,OAChB,aACAwK,KAAK,IAEP,OAAOg8B,EAAO,CACZ1oC,UAAW,cACX6iC,MAAO+M,EAAIxuC,KAAKqC,KAAKvB,OACrBymC,KAAMiH,EAAIjH,KACV/rB,KAAMA,EACN4rB,OAAQA,IACP9oC,UAGCw+B,EAAUkR,IAAS3wC,OAAO,CAC9B,oCACE,oCACA,yDACF,UACAiO,KAAK,KAAK,SAAS9M,EAAOL,GAC1B,MAAMm+B,OAAyC,IAAtBn+B,EAAQm+B,UAA4Bn+B,EAAQm+B,UAAY,SAEjF99B,EAAMK,SAASy9B,GAAW3jB,OAEtBxa,EAAQ4+B,WACVv+B,EAAMc,KAAK,uBAAuBqZ,UAIhCuuB,EAAW8G,IAAS3wC,OAAO,gCAAgC,SAASmB,EAAOL,GAC/EK,EAAMG,KAAK,CACT,UAAYR,EAAQmM,GAAK,cAAgBnM,EAAQmM,GAAK,IAAM,IAAM,IAChE,0CAA4CnM,EAAQmM,GAAK,aAAenM,EAAQmM,GAAK,IAAM,IAC1FnM,EAAQgpC,QAAU,WAAa,GAChC,mBAAqBhpC,EAAQgpC,QAAU,OAAS,SAAW,MAC1DhpC,EAAQuY,KAAOvY,EAAQuY,KAAO,GACjC,YACApL,KAAK,QAGH80B,EAAO,SAASoP,EAAerjB,GAEnC,MAAO,KADPA,EAAUA,GAAW,KACE,WAAaqjB,EAAgB,OAkIvCl3B,MA/HJ,SAASm3B,GAClB,MAAO,CACLn2B,OAAQA,EACR0hB,QAASA,EACTQ,YAAaA,EACbzhB,QAASA,EACTC,SAAUA,EACVugB,UAAWA,EACX0T,UAAWA,EACXC,YAAaA,EACb/N,YAAaA,EACbT,OAAQA,EACRe,SAAUA,EACVuB,cAAeA,EACfuM,eAAgBA,EAChB/N,uBAAwBA,EACxBiO,oBAAqBA,EACrBC,wBAAyBA,EACzBC,oBAAqBA,EACrBK,oBAAqBA,EACrBpO,QAASA,EACT0G,OAAQA,EACR+H,YAAaA,EACbC,YAAaA,EACbC,WAAYA,EACZzS,QAASA,EACToK,SAAUA,EACV9G,KAAMA,EACNjiC,QAASsxC,EAET3I,UAAW,SAASD,EAAM6I,GACxB7I,EAAKzU,YAAY,YAAasd,GAC9B7I,EAAK3nC,KAAK,YAAawwC,IAGzB7K,gBAAiB,SAASgC,EAAM8I,GAC9B9I,EAAKzU,YAAY,SAAUud,IAG7BC,MAAO,SAASC,EAAM7yC,GACpB6yC,EAAKvwC,KAAK,YAAY84B,YAAY,WAClCyX,EAAKvwC,KAAK,gBAAkBtC,EAAQ,MAAM6B,SAAS,YAGrDspC,cAAe,SAASd,EAASlyB,GAC/BkyB,EAAQ7S,IAAI,kBAAmBrf,IAGjCozB,eAAgB,SAASlB,EAASlyB,GAChCkyB,EAAQ7S,IAAI,kBAAmBrf,IAGjCszB,WAAY,SAASpB,GACnBA,EAAQvoC,KAAK,SAASk2B,QAGxByS,WAAY,SAASJ,GACnBA,EAAQvoC,KAAK,SAAS6Z,QASxBm3B,kBAAmB,SAASjT,GAC1B,OAAOA,EAASv9B,KAAK,0BASvBywC,cAAe,SAAS1I,GACtB,OAAOA,EAAQ/nC,KAAK,qBAGtBmZ,aAAc,SAASN,GACrB,MAAMmX,GAAWmgB,EAAc9c,QAAUsb,EAAU,CACjDzS,EAAY,CACVzhB,IACAm0B,QAEoC,WAAlCuB,EAAcrE,gBAChB9xB,EAAO,CACPkiB,EAAY,CACVzhB,IACAC,MAEFghB,IACAT,MAEAjhB,EAAO,CACP0hB,IACAQ,EAAY,CACVzhB,IACAC,MAEFugB,OAEDj8B,SAIH,OAFAgxB,EAAQlf,YAAY+H,GAEb,CACLgF,KAAMhF,EACNmB,OAAQgW,EACR0L,QAAS1L,EAAQhwB,KAAK,iBACtBk8B,YAAalM,EAAQhwB,KAAK,sBAC1B0a,SAAUsV,EAAQhwB,KAAK,kBACvBya,QAASuV,EAAQhwB,KAAK,iBACtBi7B,UAAWjL,EAAQhwB,KAAK,qBAI5ByZ,aAAc,SAASZ,EAAOE,GAC5BF,EAAMxZ,KAAK0Z,EAAW2B,SAASrb,QAC/B0Z,EAAWiB,OAAOtX,SAClBmW,EAAMJ,IAAI,cACVI,EAAM6c,U,UCrnBZv2B,IAAEsB,WAAatB,IAAEwB,OAAOxB,IAAEsB,WAAY,CACpCwY,YAAaD,EACb03B,UAAW,U", "file": "summernote-lite.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"jquery\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"jquery\"], factory);\n\telse {\n\t\tvar a = typeof exports === 'object' ? factory(require(\"jquery\")) : factory(root[\"jQuery\"]);\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(window, function(__WEBPACK_EXTERNAL_MODULE__0__) {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 50);\n", "module.exports = __WEBPACK_EXTERNAL_MODULE__0__;", "import $ from 'jquery';\n\nclass Renderer {\n  constructor(markup, children, options, callback) {\n    this.markup = markup;\n    this.children = children;\n    this.options = options;\n    this.callback = callback;\n  }\n\n  render($parent) {\n    const $node = $(this.markup);\n\n    if (this.options && this.options.contents) {\n      $node.html(this.options.contents);\n    }\n\n    if (this.options && this.options.className) {\n      $node.addClass(this.options.className);\n    }\n\n    if (this.options && this.options.data) {\n      $.each(this.options.data, (k, v) => {\n        $node.attr('data-' + k, v);\n      });\n    }\n\n    if (this.options && this.options.click) {\n      $node.on('click', this.options.click);\n    }\n\n    if (this.children) {\n      const $container = $node.find('.note-children-container');\n      this.children.forEach((child) => {\n        child.render($container.length ? $container : $node);\n      });\n    }\n\n    if (this.callback) {\n      this.callback($node, this.options);\n    }\n\n    if (this.options && this.options.callback) {\n      this.options.callback($node);\n    }\n\n    if ($parent) {\n      $parent.append($node);\n    }\n\n    return $node;\n  }\n}\n\nexport default {\n  create: (markup, callback) => {\n    return function() {\n      const options = typeof arguments[1] === 'object' ? arguments[1] : arguments[0];\n      let children = Array.isArray(arguments[0]) ? arguments[0] : [];\n      if (options && options.children) {\n        children = options.children;\n      }\n      return new Renderer(markup, children, options, callback);\n    };\n  },\n};\n", "/* globals __webpack_amd_options__ */\nmodule.exports = __webpack_amd_options__;\n", "import $ from 'jquery';\n\n$.summernote = $.summernote || {\n  lang: {},\n};\n\n$.extend($.summernote.lang, {\n  'en-US': {\n    font: {\n      bold: 'Bold',\n      italic: 'Italic',\n      underline: 'Underline',\n      clear: 'Remove Font Style',\n      height: 'Line Height',\n      name: 'Font Family',\n      strikethrough: 'Strikethrough',\n      subscript: 'Subscript',\n      superscript: 'Superscript',\n      size: 'Font Size',\n      sizeunit: 'Font Size Unit',\n    },\n    image: {\n      image: 'Picture',\n      insert: 'Insert Image',\n      resizeFull: 'Resize full',\n      resizeHalf: 'Resize half',\n      resizeQuarter: 'Resize quarter',\n      resizeNone: 'Original size',\n      floatLeft: 'Float Left',\n      floatRight: 'Float Right',\n      floatNone: 'Remove float',\n      shapeRounded: 'Shape: Rounded',\n      shapeCircle: 'Shape: Circle',\n      shapeThumbnail: 'Shape: Thumbnail',\n      shapeNone: 'Shape: None',\n      dragImageHere: 'Drag image or text here',\n      dropImage: 'Drop image or Text',\n      selectFromFiles: 'Select from files',\n      maximumFileSize: 'Maximum file size',\n      maximumFileSizeError: 'Maximum file size exceeded.',\n      url: 'Image URL',\n      remove: 'Remove Image',\n      original: 'Original',\n    },\n    video: {\n      video: 'Video',\n      videoLink: 'Video Link',\n      insert: 'Insert Video',\n      url: 'Video URL',\n      providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion or Youku)',\n    },\n    link: {\n      link: 'Link',\n      insert: 'Insert Link',\n      unlink: 'Unlink',\n      edit: 'Edit',\n      textToDisplay: 'Text to display',\n      url: 'To what URL should this link go?',\n      openInNewWindow: 'Open in new window',\n      useProtocol: 'Use default protocol',\n    },\n    table: {\n      table: 'Table',\n      addRowAbove: 'Add row above',\n      addRowBelow: 'Add row below',\n      addColLeft: 'Add column left',\n      addColRight: 'Add column right',\n      delRow: 'Delete row',\n      delCol: 'Delete column',\n      delTable: 'Delete table',\n    },\n    hr: {\n      insert: 'Insert Horizontal Rule',\n    },\n    style: {\n      style: 'Style',\n      p: 'Normal',\n      blockquote: 'Quote',\n      pre: 'Code',\n      h1: 'Header 1',\n      h2: 'Header 2',\n      h3: 'Header 3',\n      h4: 'Header 4',\n      h5: 'Header 5',\n      h6: 'Header 6',\n    },\n    lists: {\n      unordered: 'Unordered list',\n      ordered: 'Ordered list',\n    },\n    options: {\n      help: 'Help',\n      fullscreen: 'Full Screen',\n      codeview: 'Code View',\n    },\n    paragraph: {\n      paragraph: 'Paragraph',\n      outdent: 'Outdent',\n      indent: 'Indent',\n      left: 'Align left',\n      center: 'Align center',\n      right: 'Align right',\n      justify: 'Justify full',\n    },\n    color: {\n      recent: 'Recent Color',\n      more: 'More Color',\n      background: 'Background Color',\n      foreground: 'Text Color',\n      transparent: 'Transparent',\n      setTransparent: 'Set transparent',\n      reset: 'Reset',\n      resetToDefault: 'Reset to default',\n      cpSelect: 'Select',\n    },\n    shortcut: {\n      shortcuts: 'Keyboard shortcuts',\n      close: 'Close',\n      textFormatting: 'Text formatting',\n      action: 'Action',\n      paragraphFormatting: 'Paragraph formatting',\n      documentStyle: 'Document Style',\n      extraKeys: 'Extra keys',\n    },\n    help: {\n      'insertParagraph': 'Insert Paragraph',\n      'undo': 'Undoes the last command',\n      'redo': 'Redoes the last command',\n      'tab': 'Tab',\n      'untab': 'Untab',\n      'bold': 'Set a bold style',\n      'italic': 'Set a italic style',\n      'underline': 'Set a underline style',\n      'strikethrough': 'Set a strikethrough style',\n      'removeFormat': 'Clean a style',\n      'justifyLeft': 'Set left align',\n      'justifyCenter': 'Set center align',\n      'justifyRight': 'Set right align',\n      'justifyFull': 'Set full align',\n      'insertUnorderedList': 'Toggle unordered list',\n      'insertOrderedList': 'Toggle ordered list',\n      'outdent': 'Outdent on current paragraph',\n      'indent': 'Indent on current paragraph',\n      'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n      'formatH1': 'Change current block\\'s format as H1',\n      'formatH2': 'Change current block\\'s format as H2',\n      'formatH3': 'Change current block\\'s format as H3',\n      'formatH4': 'Change current block\\'s format as H4',\n      'formatH5': 'Change current block\\'s format as H5',\n      'formatH6': 'Change current block\\'s format as H6',\n      'insertHorizontalRule': 'Insert horizontal rule',\n      'linkDialog.show': 'Show Link Dialog',\n    },\n    history: {\n      undo: 'Undo',\n      redo: 'Redo',\n    },\n    specialChar: {\n      specialChar: 'SPECIAL CHARACTERS',\n      select: 'Select Special characters',\n    },\n    output: {\n      noSelection: 'No Selection Made!',\n    },\n  },\n});\n", "import $ from 'jquery';\nconst isSupportAmd = typeof define === 'function' && define.amd; // eslint-disable-line\n\n/**\n * returns whether font is installed or not.\n *\n * @param {String} fontName\n * @return {Boolean}\n */\nconst genericFontFamilies = ['sans-serif', 'serif', 'monospace', 'cursive', 'fantasy'];\n\nfunction validFontName(fontName) {\n  return ($.inArray(fontName.toLowerCase(), genericFontFamilies) === -1) ? `'${fontName}'` : fontName;\n}\n\nfunction isFontInstalled(fontName) {\n  const testFontName = fontName === 'Comic Sans MS' ? 'Courier New' : 'Comic Sans MS';\n  const testText = 'mmmmmmmmmmwwwww';\n  const testSize = '200px';\n\n  var canvas = document.createElement('canvas');\n  var context = canvas.getContext('2d');\n\n  context.font = testSize + \" '\" + testFontName + \"'\";\n  const originalWidth = context.measureText(testText).width;\n\n  context.font = testSize + ' ' + validFontName(fontName) + ', \"' + testFontName + '\"';\n  const width = context.measureText(testText).width;\n\n  return originalWidth !== width;\n}\n\nconst userAgent = navigator.userAgent;\nconst isMSIE = /MSIE|Trident/i.test(userAgent);\nlet browserVersion;\nif (isMSIE) {\n  let matches = /MSIE (\\d+[.]\\d+)/.exec(userAgent);\n  if (matches) {\n    browserVersion = parseFloat(matches[1]);\n  }\n  matches = /Trident\\/.*rv:([0-9]{1,}[.0-9]{0,})/.exec(userAgent);\n  if (matches) {\n    browserVersion = parseFloat(matches[1]);\n  }\n}\n\nconst isEdge = /Edge\\/\\d+/.test(userAgent);\n\nlet hasCodeMirror = !!window.CodeMirror;\n\nconst isSupportTouch =\n  (('ontouchstart' in window) ||\n   (navigator.MaxTouchPoints > 0) ||\n   (navigator.msMaxTouchPoints > 0));\n\n// [workaround] IE doesn't have input events for contentEditable\n// - see: https://goo.gl/4bfIvA\nconst inputEventName = (isMSIE) ? 'DOMCharacterDataModified DOMSubtreeModified DOMNodeInserted' : 'input';\n\n/**\n * @class core.env\n *\n * Object which check platform and agent\n *\n * @singleton\n * @alternateClassName env\n */\nexport default {\n  isMac: navigator.appVersion.indexOf('Mac') > -1,\n  isMSIE,\n  isEdge,\n  isFF: !isEdge && /firefox/i.test(userAgent),\n  isPhantom: /PhantomJS/i.test(userAgent),\n  isWebkit: !isEdge && /webkit/i.test(userAgent),\n  isChrome: !isEdge && /chrome/i.test(userAgent),\n  isSafari: !isEdge && /safari/i.test(userAgent) && (!/chrome/i.test(userAgent)),\n  browserVersion,\n  jqueryVersion: parseFloat($.fn.jquery),\n  isSupportAmd,\n  isSupportTouch,\n  hasCodeMirror,\n  isFontInstalled,\n  isW3CRangeSupport: !!document.createRange,\n  inputEventName,\n  genericFontFamilies,\n  validFontName,\n};\n", "import $ from 'jquery';\n\n/**\n * @class core.func\n *\n * func utils (for high-order func's arg)\n *\n * @singleton\n * @alternateClassName func\n */\nfunction eq(itemA) {\n  return function(itemB) {\n    return itemA === itemB;\n  };\n}\n\nfunction eq2(itemA, itemB) {\n  return itemA === itemB;\n}\n\nfunction peq2(propName) {\n  return function(itemA, itemB) {\n    return itemA[propName] === itemB[propName];\n  };\n}\n\nfunction ok() {\n  return true;\n}\n\nfunction fail() {\n  return false;\n}\n\nfunction not(f) {\n  return function() {\n    return !f.apply(f, arguments);\n  };\n}\n\nfunction and(fA, fB) {\n  return function(item) {\n    return fA(item) && fB(item);\n  };\n}\n\nfunction self(a) {\n  return a;\n}\n\nfunction invoke(obj, method) {\n  return function() {\n    return obj[method].apply(obj, arguments);\n  };\n}\n\nlet idCounter = 0;\n\n/**\n * reset globally-unique id\n *\n */\nfunction resetUniqueId() {\n  idCounter = 0;\n}\n\n/**\n * generate a globally-unique id\n *\n * @param {String} [prefix]\n */\nfunction uniqueId(prefix) {\n  const id = ++idCounter + '';\n  return prefix ? prefix + id : id;\n}\n\n/**\n * returns bnd (bounds) from rect\n *\n * - IE Compatibility Issue: http://goo.gl/sRLOAo\n * - Scroll Issue: http://goo.gl/sNjUc\n *\n * @param {Rect} rect\n * @return {Object} bounds\n * @return {Number} bounds.top\n * @return {Number} bounds.left\n * @return {Number} bounds.width\n * @return {Number} bounds.height\n */\nfunction rect2bnd(rect) {\n  const $document = $(document);\n  return {\n    top: rect.top + $document.scrollTop(),\n    left: rect.left + $document.scrollLeft(),\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top,\n  };\n}\n\n/**\n * returns a copy of the object where the keys have become the values and the values the keys.\n * @param {Object} obj\n * @return {Object}\n */\nfunction invertObject(obj) {\n  const inverted = {};\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      inverted[obj[key]] = key;\n    }\n  }\n  return inverted;\n}\n\n/**\n * @param {String} namespace\n * @param {String} [prefix]\n * @return {String}\n */\nfunction namespaceToCamel(namespace, prefix) {\n  prefix = prefix || '';\n  return prefix + namespace.split('.').map(function(name) {\n    return name.substring(0, 1).toUpperCase() + name.substring(1);\n  }).join('');\n}\n\n/**\n * Returns a function, that, as long as it continues to be invoked, will not\n * be triggered. The function will be called after it stops being called for\n * N milliseconds. If `immediate` is passed, trigger the function on the\n * leading edge, instead of the trailing.\n * @param {Function} func\n * @param {Number} wait\n * @param {Boolean} immediate\n * @return {Function}\n */\nfunction debounce(func, wait, immediate) {\n  let timeout;\n  return function() {\n    const context = this;\n    const args = arguments;\n    const later = () => {\n      timeout = null;\n      if (!immediate) {\n        func.apply(context, args);\n      }\n    };\n    const callNow = immediate && !timeout;\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n    if (callNow) {\n      func.apply(context, args);\n    }\n  };\n}\n\n/**\n *\n * @param {String} url\n * @return {Boolean}\n */\nfunction isValidUrl(url) {\n  const expression = /[-a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,6}\\b([-a-zA-Z0-9@:%_\\+.~#?&//=]*)/gi;\n  return expression.test(url);\n}\n\nexport default {\n  eq,\n  eq2,\n  peq2,\n  ok,\n  fail,\n  self,\n  not,\n  and,\n  invoke,\n  resetUniqueId,\n  uniqueId,\n  rect2bnd,\n  invertObject,\n  namespaceToCamel,\n  debounce,\n  isValidUrl,\n};\n", "import func from './func';\n\n/**\n * returns the first item of an array.\n *\n * @param {Array} array\n */\nfunction head(array) {\n  return array[0];\n}\n\n/**\n * returns the last item of an array.\n *\n * @param {Array} array\n */\nfunction last(array) {\n  return array[array.length - 1];\n}\n\n/**\n * returns everything but the last entry of the array.\n *\n * @param {Array} array\n */\nfunction initial(array) {\n  return array.slice(0, array.length - 1);\n}\n\n/**\n * returns the rest of the items in an array.\n *\n * @param {Array} array\n */\nfunction tail(array) {\n  return array.slice(1);\n}\n\n/**\n * returns item of array\n */\nfunction find(array, pred) {\n  for (let idx = 0, len = array.length; idx < len; idx++) {\n    const item = array[idx];\n    if (pred(item)) {\n      return item;\n    }\n  }\n}\n\n/**\n * returns true if all of the values in the array pass the predicate truth test.\n */\nfunction all(array, pred) {\n  for (let idx = 0, len = array.length; idx < len; idx++) {\n    if (!pred(array[idx])) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * returns true if the value is present in the list.\n */\nfunction contains(array, item) {\n  if (array && array.length && item) {\n    if (array.indexOf) {\n      return array.indexOf(item) !== -1;\n    } else if (array.contains) {\n      // `DOMTokenList` doesn't implement `.indexOf`, but it implements `.contains`\n      return array.contains(item);\n    }\n  }\n  return false;\n}\n\n/**\n * get sum from a list\n *\n * @param {Array} array - array\n * @param {Function} fn - iterator\n */\nfunction sum(array, fn) {\n  fn = fn || func.self;\n  return array.reduce(function(memo, v) {\n    return memo + fn(v);\n  }, 0);\n}\n\n/**\n * returns a copy of the collection with array type.\n * @param {Collection} collection - collection eg) node.childNodes, ...\n */\nfunction from(collection) {\n  const result = [];\n  const length = collection.length;\n  let idx = -1;\n  while (++idx < length) {\n    result[idx] = collection[idx];\n  }\n  return result;\n}\n\n/**\n * returns whether list is empty or not\n */\nfunction isEmpty(array) {\n  return !array || !array.length;\n}\n\n/**\n * cluster elements by predicate function.\n *\n * @param {Array} array - array\n * @param {Function} fn - predicate function for cluster rule\n * @param {Array[]}\n */\nfunction clusterBy(array, fn) {\n  if (!array.length) { return []; }\n  const aTail = tail(array);\n  return aTail.reduce(function(memo, v) {\n    const aLast = last(memo);\n    if (fn(last(aLast), v)) {\n      aLast[aLast.length] = v;\n    } else {\n      memo[memo.length] = [v];\n    }\n    return memo;\n  }, [[head(array)]]);\n}\n\n/**\n * returns a copy of the array with all false values removed\n *\n * @param {Array} array - array\n * @param {Function} fn - predicate function for cluster rule\n */\nfunction compact(array) {\n  const aResult = [];\n  for (let idx = 0, len = array.length; idx < len; idx++) {\n    if (array[idx]) { aResult.push(array[idx]); }\n  }\n  return aResult;\n}\n\n/**\n * produces a duplicate-free version of the array\n *\n * @param {Array} array\n */\nfunction unique(array) {\n  const results = [];\n\n  for (let idx = 0, len = array.length; idx < len; idx++) {\n    if (!contains(results, array[idx])) {\n      results.push(array[idx]);\n    }\n  }\n\n  return results;\n}\n\n/**\n * returns next item.\n * @param {Array} array\n */\nfunction next(array, item) {\n  if (array && array.length && item) {\n    const idx = array.indexOf(item);\n    return idx === -1 ? null : array[idx + 1];\n  }\n  return null;\n}\n\n/**\n * returns prev item.\n * @param {Array} array\n */\nfunction prev(array, item) {\n  if (array && array.length && item) {\n    const idx = array.indexOf(item);\n    return idx === -1 ? null : array[idx - 1];\n  }\n  return null;\n}\n\n/**\n * @class core.list\n *\n * list utils\n *\n * @singleton\n * @alternateClassName list\n */\nexport default {\n  head,\n  last,\n  initial,\n  tail,\n  prev,\n  next,\n  find,\n  contains,\n  all,\n  sum,\n  from,\n  isEmpty,\n  clusterBy,\n  compact,\n  unique,\n};\n", "import $ from 'jquery';\nimport func from './func';\nimport lists from './lists';\nimport env from './env';\n\nconst NBSP_CHAR = String.fromCharCode(160);\nconst ZERO_WIDTH_NBSP_CHAR = '\\ufeff';\n\n/**\n * @method isEditable\n *\n * returns whether node is `note-editable` or not.\n *\n * @param {Node} node\n * @return {Boolean}\n */\nfunction isEditable(node) {\n  return node && $(node).hasClass('note-editable');\n}\n\n/**\n * @method isControlSizing\n *\n * returns whether node is `note-control-sizing` or not.\n *\n * @param {Node} node\n * @return {Boolean}\n */\nfunction isControlSizing(node) {\n  return node && $(node).hasClass('note-control-sizing');\n}\n\n/**\n * @method makePredByNodeName\n *\n * returns predicate which judge whether nodeName is same\n *\n * @param {String} nodeName\n * @return {Function}\n */\nfunction makePredByNodeName(nodeName) {\n  nodeName = nodeName.toUpperCase();\n  return function(node) {\n    return node && node.nodeName.toUpperCase() === nodeName;\n  };\n}\n\n/**\n * @method isText\n *\n *\n *\n * @param {Node} node\n * @return {Boolean} true if node's type is text(3)\n */\nfunction isText(node) {\n  return node && node.nodeType === 3;\n}\n\n/**\n * @method isElement\n *\n *\n *\n * @param {Node} node\n * @return {Boolean} true if node's type is element(1)\n */\nfunction isElement(node) {\n  return node && node.nodeType === 1;\n}\n\n/**\n * ex) br, col, embed, hr, img, input, ...\n * @see http://www.w3.org/html/wg/drafts/html/master/syntax.html#void-elements\n */\nfunction isVoid(node) {\n  return node && /^BR|^IMG|^HR|^IFRAME|^BUTTON|^INPUT|^AUDIO|^VIDEO|^EMBED/.test(node.nodeName.toUpperCase());\n}\n\nfunction isPara(node) {\n  if (isEditable(node)) {\n    return false;\n  }\n\n  // Chrome(v31.0), FF(v25.0.1) use DIV for paragraph\n  return node && /^DIV|^P|^LI|^H[1-7]/.test(node.nodeName.toUpperCase());\n}\n\nfunction isHeading(node) {\n  return node && /^H[1-7]/.test(node.nodeName.toUpperCase());\n}\n\nconst isPre = makePredByNodeName('PRE');\n\nconst isLi = makePredByNodeName('LI');\n\nfunction isPurePara(node) {\n  return isPara(node) && !isLi(node);\n}\n\nconst isTable = makePredByNodeName('TABLE');\n\nconst isData = makePredByNodeName('DATA');\n\nfunction isInline(node) {\n  return !isBodyContainer(node) &&\n         !isList(node) &&\n         !isHr(node) &&\n         !isPara(node) &&\n         !isTable(node) &&\n         !isBlockquote(node) &&\n         !isData(node);\n}\n\nfunction isList(node) {\n  return node && /^UL|^OL/.test(node.nodeName.toUpperCase());\n}\n\nconst isHr = makePredByNodeName('HR');\n\nfunction isCell(node) {\n  return node && /^TD|^TH/.test(node.nodeName.toUpperCase());\n}\n\nconst isBlockquote = makePredByNodeName('BLOCKQUOTE');\n\nfunction isBodyContainer(node) {\n  return isCell(node) || isBlockquote(node) || isEditable(node);\n}\n\nconst isAnchor = makePredByNodeName('A');\n\nfunction isParaInline(node) {\n  return isInline(node) && !!ancestor(node, isPara);\n}\n\nfunction isBodyInline(node) {\n  return isInline(node) && !ancestor(node, isPara);\n}\n\nconst isBody = makePredByNodeName('BODY');\n\n/**\n * returns whether nodeB is closest sibling of nodeA\n *\n * @param {Node} nodeA\n * @param {Node} nodeB\n * @return {Boolean}\n */\nfunction isClosestSibling(nodeA, nodeB) {\n  return nodeA.nextSibling === nodeB ||\n         nodeA.previousSibling === nodeB;\n}\n\n/**\n * returns array of closest siblings with node\n *\n * @param {Node} node\n * @param {function} [pred] - predicate function\n * @return {Node[]}\n */\nfunction withClosestSiblings(node, pred) {\n  pred = pred || func.ok;\n\n  const siblings = [];\n  if (node.previousSibling && pred(node.previousSibling)) {\n    siblings.push(node.previousSibling);\n  }\n  siblings.push(node);\n  if (node.nextSibling && pred(node.nextSibling)) {\n    siblings.push(node.nextSibling);\n  }\n  return siblings;\n}\n\n/**\n * blank HTML for cursor position\n * - [workaround] old IE only works with &nbsp;\n * - [workaround] IE11 and other browser works with bogus br\n */\nconst blankHTML = env.isMSIE && env.browserVersion < 11 ? '&nbsp;' : '<br>';\n\n/**\n * @method nodeLength\n *\n * returns #text's text size or element's childNodes size\n *\n * @param {Node} node\n */\nfunction nodeLength(node) {\n  if (isText(node)) {\n    return node.nodeValue.length;\n  }\n\n  if (node) {\n    return node.childNodes.length;\n  }\n\n  return 0;\n}\n\n/**\n * returns whether deepest child node is empty or not.\n *\n * @param {Node} node\n * @return {Boolean}\n */\nfunction deepestChildIsEmpty(node) {\n  do {\n    if (node.firstElementChild === null || node.firstElementChild.innerHTML === '') break;\n  } while ((node = node.firstElementChild));\n\n  return isEmpty(node);\n}\n\n/**\n * returns whether node is empty or not.\n *\n * @param {Node} node\n * @return {Boolean}\n */\nfunction isEmpty(node) {\n  const len = nodeLength(node);\n\n  if (len === 0) {\n    return true;\n  } else if (!isText(node) && len === 1 && node.innerHTML === blankHTML) {\n    // ex) <p><br></p>, <span><br></span>\n    return true;\n  } else if (lists.all(node.childNodes, isText) && node.innerHTML === '') {\n    // ex) <p></p>, <span></span>\n    return true;\n  }\n\n  return false;\n}\n\n/**\n * padding blankHTML if node is empty (for cursor position)\n */\nfunction paddingBlankHTML(node) {\n  if (!isVoid(node) && !nodeLength(node)) {\n    node.innerHTML = blankHTML;\n  }\n}\n\n/**\n * find nearest ancestor predicate hit\n *\n * @param {Node} node\n * @param {Function} pred - predicate function\n */\nfunction ancestor(node, pred) {\n  while (node) {\n    if (pred(node)) { return node; }\n    if (isEditable(node)) { break; }\n\n    node = node.parentNode;\n  }\n  return null;\n}\n\n/**\n * find nearest ancestor only single child blood line and predicate hit\n *\n * @param {Node} node\n * @param {Function} pred - predicate function\n */\nfunction singleChildAncestor(node, pred) {\n  node = node.parentNode;\n\n  while (node) {\n    if (nodeLength(node) !== 1) { break; }\n    if (pred(node)) { return node; }\n    if (isEditable(node)) { break; }\n\n    node = node.parentNode;\n  }\n  return null;\n}\n\n/**\n * returns new array of ancestor nodes (until predicate hit).\n *\n * @param {Node} node\n * @param {Function} [optional] pred - predicate function\n */\nfunction listAncestor(node, pred) {\n  pred = pred || func.fail;\n\n  const ancestors = [];\n  ancestor(node, function(el) {\n    if (!isEditable(el)) {\n      ancestors.push(el);\n    }\n\n    return pred(el);\n  });\n  return ancestors;\n}\n\n/**\n * find farthest ancestor predicate hit\n */\nfunction lastAncestor(node, pred) {\n  const ancestors = listAncestor(node);\n  return lists.last(ancestors.filter(pred));\n}\n\n/**\n * returns common ancestor node between two nodes.\n *\n * @param {Node} nodeA\n * @param {Node} nodeB\n */\nfunction commonAncestor(nodeA, nodeB) {\n  const ancestors = listAncestor(nodeA);\n  for (let n = nodeB; n; n = n.parentNode) {\n    if (ancestors.indexOf(n) > -1) return n;\n  }\n  return null; // difference document area\n}\n\n/**\n * listing all previous siblings (until predicate hit).\n *\n * @param {Node} node\n * @param {Function} [optional] pred - predicate function\n */\nfunction listPrev(node, pred) {\n  pred = pred || func.fail;\n\n  const nodes = [];\n  while (node) {\n    if (pred(node)) { break; }\n    nodes.push(node);\n    node = node.previousSibling;\n  }\n  return nodes;\n}\n\n/**\n * listing next siblings (until predicate hit).\n *\n * @param {Node} node\n * @param {Function} [pred] - predicate function\n */\nfunction listNext(node, pred) {\n  pred = pred || func.fail;\n\n  const nodes = [];\n  while (node) {\n    if (pred(node)) { break; }\n    nodes.push(node);\n    node = node.nextSibling;\n  }\n  return nodes;\n}\n\n/**\n * listing descendant nodes\n *\n * @param {Node} node\n * @param {Function} [pred] - predicate function\n */\nfunction listDescendant(node, pred) {\n  const descendants = [];\n  pred = pred || func.ok;\n\n  // start DFS(depth first search) with node\n  (function fnWalk(current) {\n    if (node !== current && pred(current)) {\n      descendants.push(current);\n    }\n    for (let idx = 0, len = current.childNodes.length; idx < len; idx++) {\n      fnWalk(current.childNodes[idx]);\n    }\n  })(node);\n\n  return descendants;\n}\n\n/**\n * wrap node with new tag.\n *\n * @param {Node} node\n * @param {Node} tagName of wrapper\n * @return {Node} - wrapper\n */\nfunction wrap(node, wrapperName) {\n  const parent = node.parentNode;\n  const wrapper = $('<' + wrapperName + '>')[0];\n\n  parent.insertBefore(wrapper, node);\n  wrapper.appendChild(node);\n\n  return wrapper;\n}\n\n/**\n * insert node after preceding\n *\n * @param {Node} node\n * @param {Node} preceding - predicate function\n */\nfunction insertAfter(node, preceding) {\n  const next = preceding.nextSibling;\n  let parent = preceding.parentNode;\n  if (next) {\n    parent.insertBefore(node, next);\n  } else {\n    parent.appendChild(node);\n  }\n  return node;\n}\n\n/**\n * append elements.\n *\n * @param {Node} node\n * @param {Collection} aChild\n */\nfunction appendChildNodes(node, aChild) {\n  $.each(aChild, function(idx, child) {\n    node.appendChild(child);\n  });\n  return node;\n}\n\n/**\n * returns whether boundaryPoint is left edge or not.\n *\n * @param {BoundaryPoint} point\n * @return {Boolean}\n */\nfunction isLeftEdgePoint(point) {\n  return point.offset === 0;\n}\n\n/**\n * returns whether boundaryPoint is right edge or not.\n *\n * @param {BoundaryPoint} point\n * @return {Boolean}\n */\nfunction isRightEdgePoint(point) {\n  return point.offset === nodeLength(point.node);\n}\n\n/**\n * returns whether boundaryPoint is edge or not.\n *\n * @param {BoundaryPoint} point\n * @return {Boolean}\n */\nfunction isEdgePoint(point) {\n  return isLeftEdgePoint(point) || isRightEdgePoint(point);\n}\n\n/**\n * returns whether node is left edge of ancestor or not.\n *\n * @param {Node} node\n * @param {Node} ancestor\n * @return {Boolean}\n */\nfunction isLeftEdgeOf(node, ancestor) {\n  while (node && node !== ancestor) {\n    if (position(node) !== 0) {\n      return false;\n    }\n    node = node.parentNode;\n  }\n\n  return true;\n}\n\n/**\n * returns whether node is right edge of ancestor or not.\n *\n * @param {Node} node\n * @param {Node} ancestor\n * @return {Boolean}\n */\nfunction isRightEdgeOf(node, ancestor) {\n  if (!ancestor) {\n    return false;\n  }\n  while (node && node !== ancestor) {\n    if (position(node) !== nodeLength(node.parentNode) - 1) {\n      return false;\n    }\n    node = node.parentNode;\n  }\n\n  return true;\n}\n\n/**\n * returns whether point is left edge of ancestor or not.\n * @param {BoundaryPoint} point\n * @param {Node} ancestor\n * @return {Boolean}\n */\nfunction isLeftEdgePointOf(point, ancestor) {\n  return isLeftEdgePoint(point) && isLeftEdgeOf(point.node, ancestor);\n}\n\n/**\n * returns whether point is right edge of ancestor or not.\n * @param {BoundaryPoint} point\n * @param {Node} ancestor\n * @return {Boolean}\n */\nfunction isRightEdgePointOf(point, ancestor) {\n  return isRightEdgePoint(point) && isRightEdgeOf(point.node, ancestor);\n}\n\n/**\n * returns offset from parent.\n *\n * @param {Node} node\n */\nfunction position(node) {\n  let offset = 0;\n  while ((node = node.previousSibling)) {\n    offset += 1;\n  }\n  return offset;\n}\n\nfunction hasChildren(node) {\n  return !!(node && node.childNodes && node.childNodes.length);\n}\n\n/**\n * returns previous boundaryPoint\n *\n * @param {BoundaryPoint} point\n * @param {Boolean} isSkipInnerOffset\n * @return {BoundaryPoint}\n */\nfunction prevPoint(point, isSkipInnerOffset) {\n  let node;\n  let offset;\n\n  if (point.offset === 0) {\n    if (isEditable(point.node)) {\n      return null;\n    }\n\n    node = point.node.parentNode;\n    offset = position(point.node);\n  } else if (hasChildren(point.node)) {\n    node = point.node.childNodes[point.offset - 1];\n    offset = nodeLength(node);\n  } else {\n    node = point.node;\n    offset = isSkipInnerOffset ? 0 : point.offset - 1;\n  }\n\n  return {\n    node: node,\n    offset: offset,\n  };\n}\n\n/**\n * returns next boundaryPoint\n *\n * @param {BoundaryPoint} point\n * @param {Boolean} isSkipInnerOffset\n * @return {BoundaryPoint}\n */\nfunction nextPoint(point, isSkipInnerOffset) {\n  let node, offset;\n\n  if (isEmpty(point.node)) {\n    return null;\n  }\n\n  if (nodeLength(point.node) === point.offset) {\n    if (isEditable(point.node)) {\n      return null;\n    }\n\n    node = point.node.parentNode;\n    offset = position(point.node) + 1;\n  } else if (hasChildren(point.node)) {\n    node = point.node.childNodes[point.offset];\n    offset = 0;\n    if (isEmpty(node)) {\n      return null;\n    }\n  } else {\n    node = point.node;\n    offset = isSkipInnerOffset ? nodeLength(point.node) : point.offset + 1;\n\n    if (isEmpty(node)) {\n      return null;\n    }\n  }\n\n  return {\n    node: node,\n    offset: offset,\n  };\n}\n\n/**\n * returns whether pointA and pointB is same or not.\n *\n * @param {BoundaryPoint} pointA\n * @param {BoundaryPoint} pointB\n * @return {Boolean}\n */\nfunction isSamePoint(pointA, pointB) {\n  return pointA.node === pointB.node && pointA.offset === pointB.offset;\n}\n\n/**\n * returns whether point is visible (can set cursor) or not.\n *\n * @param {BoundaryPoint} point\n * @return {Boolean}\n */\nfunction isVisiblePoint(point) {\n  if (isText(point.node) || !hasChildren(point.node) || isEmpty(point.node)) {\n    return true;\n  }\n\n  const leftNode = point.node.childNodes[point.offset - 1];\n  const rightNode = point.node.childNodes[point.offset];\n  if ((!leftNode || isVoid(leftNode)) && (!rightNode || isVoid(rightNode))) {\n    return true;\n  }\n\n  return false;\n}\n\n/**\n * @method prevPointUtil\n *\n * @param {BoundaryPoint} point\n * @param {Function} pred\n * @return {BoundaryPoint}\n */\nfunction prevPointUntil(point, pred) {\n  while (point) {\n    if (pred(point)) {\n      return point;\n    }\n\n    point = prevPoint(point);\n  }\n\n  return null;\n}\n\n/**\n * @method nextPointUntil\n *\n * @param {BoundaryPoint} point\n * @param {Function} pred\n * @return {BoundaryPoint}\n */\nfunction nextPointUntil(point, pred) {\n  while (point) {\n    if (pred(point)) {\n      return point;\n    }\n\n    point = nextPoint(point);\n  }\n\n  return null;\n}\n\n/**\n * returns whether point has character or not.\n *\n * @param {Point} point\n * @return {Boolean}\n */\nfunction isCharPoint(point) {\n  if (!isText(point.node)) {\n    return false;\n  }\n\n  const ch = point.node.nodeValue.charAt(point.offset - 1);\n  return ch && (ch !== ' ' && ch !== NBSP_CHAR);\n}\n\n/**\n * returns whether point has space or not.\n *\n * @param {Point} point\n * @return {Boolean}\n */\nfunction isSpacePoint(point) {\n  if (!isText(point.node)) {\n    return false;\n  }\n\n  const ch = point.node.nodeValue.charAt(point.offset - 1);\n  return ch === ' ' || ch === NBSP_CHAR;\n};\n\n/**\n * @method walkPoint\n *\n * @param {BoundaryPoint} startPoint\n * @param {BoundaryPoint} endPoint\n * @param {Function} handler\n * @param {Boolean} isSkipInnerOffset\n */\nfunction walkPoint(startPoint, endPoint, handler, isSkipInnerOffset) {\n  let point = startPoint;\n\n  while (point) {\n    handler(point);\n\n    if (isSamePoint(point, endPoint)) {\n      break;\n    }\n\n    const isSkipOffset = isSkipInnerOffset &&\n                       startPoint.node !== point.node &&\n                       endPoint.node !== point.node;\n    point = nextPoint(point, isSkipOffset);\n  }\n}\n\n/**\n * @method makeOffsetPath\n *\n * return offsetPath(array of offset) from ancestor\n *\n * @param {Node} ancestor - ancestor node\n * @param {Node} node\n */\nfunction makeOffsetPath(ancestor, node) {\n  const ancestors = listAncestor(node, func.eq(ancestor));\n  return ancestors.map(position).reverse();\n}\n\n/**\n * @method fromOffsetPath\n *\n * return element from offsetPath(array of offset)\n *\n * @param {Node} ancestor - ancestor node\n * @param {array} offsets - offsetPath\n */\nfunction fromOffsetPath(ancestor, offsets) {\n  let current = ancestor;\n  for (let i = 0, len = offsets.length; i < len; i++) {\n    if (current.childNodes.length <= offsets[i]) {\n      current = current.childNodes[current.childNodes.length - 1];\n    } else {\n      current = current.childNodes[offsets[i]];\n    }\n  }\n  return current;\n}\n\n/**\n * @method splitNode\n *\n * split element or #text\n *\n * @param {BoundaryPoint} point\n * @param {Object} [options]\n * @param {Boolean} [options.isSkipPaddingBlankHTML] - default: false\n * @param {Boolean} [options.isNotSplitEdgePoint] - default: false\n * @param {Boolean} [options.isDiscardEmptySplits] - default: false\n * @return {Node} right node of boundaryPoint\n */\nfunction splitNode(point, options) {\n  let isSkipPaddingBlankHTML = options && options.isSkipPaddingBlankHTML;\n  const isNotSplitEdgePoint = options && options.isNotSplitEdgePoint;\n  const isDiscardEmptySplits = options && options.isDiscardEmptySplits;\n\n  if (isDiscardEmptySplits) {\n    isSkipPaddingBlankHTML = true;\n  }\n\n  // edge case\n  if (isEdgePoint(point) && (isText(point.node) || isNotSplitEdgePoint)) {\n    if (isLeftEdgePoint(point)) {\n      return point.node;\n    } else if (isRightEdgePoint(point)) {\n      return point.node.nextSibling;\n    }\n  }\n\n  // split #text\n  if (isText(point.node)) {\n    return point.node.splitText(point.offset);\n  } else {\n    const childNode = point.node.childNodes[point.offset];\n    const clone = insertAfter(point.node.cloneNode(false), point.node);\n    appendChildNodes(clone, listNext(childNode));\n\n    if (!isSkipPaddingBlankHTML) {\n      paddingBlankHTML(point.node);\n      paddingBlankHTML(clone);\n    }\n\n    if (isDiscardEmptySplits) {\n      if (isEmpty(point.node)) {\n        remove(point.node);\n      }\n      if (isEmpty(clone)) {\n        remove(clone);\n        return point.node.nextSibling;\n      }\n    }\n\n    return clone;\n  }\n}\n\n/**\n * @method splitTree\n *\n * split tree by point\n *\n * @param {Node} root - split root\n * @param {BoundaryPoint} point\n * @param {Object} [options]\n * @param {Boolean} [options.isSkipPaddingBlankHTML] - default: false\n * @param {Boolean} [options.isNotSplitEdgePoint] - default: false\n * @return {Node} right node of boundaryPoint\n */\nfunction splitTree(root, point, options) {\n  // ex) [#text, <span>, <p>]\n  const ancestors = listAncestor(point.node, func.eq(root));\n\n  if (!ancestors.length) {\n    return null;\n  } else if (ancestors.length === 1) {\n    return splitNode(point, options);\n  }\n\n  return ancestors.reduce(function(node, parent) {\n    if (node === point.node) {\n      node = splitNode(point, options);\n    }\n\n    return splitNode({\n      node: parent,\n      offset: node ? position(node) : nodeLength(parent),\n    }, options);\n  });\n}\n\n/**\n * split point\n *\n * @param {Point} point\n * @param {Boolean} isInline\n * @return {Object}\n */\nfunction splitPoint(point, isInline) {\n  // find splitRoot, container\n  //  - inline: splitRoot is a child of paragraph\n  //  - block: splitRoot is a child of bodyContainer\n  const pred = isInline ? isPara : isBodyContainer;\n  const ancestors = listAncestor(point.node, pred);\n  const topAncestor = lists.last(ancestors) || point.node;\n\n  let splitRoot, container;\n  if (pred(topAncestor)) {\n    splitRoot = ancestors[ancestors.length - 2];\n    container = topAncestor;\n  } else {\n    splitRoot = topAncestor;\n    container = splitRoot.parentNode;\n  }\n\n  // if splitRoot is exists, split with splitTree\n  let pivot = splitRoot && splitTree(splitRoot, point, {\n    isSkipPaddingBlankHTML: isInline,\n    isNotSplitEdgePoint: isInline,\n  });\n\n  // if container is point.node, find pivot with point.offset\n  if (!pivot && container === point.node) {\n    pivot = point.node.childNodes[point.offset];\n  }\n\n  return {\n    rightNode: pivot,\n    container: container,\n  };\n}\n\nfunction create(nodeName) {\n  return document.createElement(nodeName);\n}\n\nfunction createText(text) {\n  return document.createTextNode(text);\n}\n\n/**\n * @method remove\n *\n * remove node, (isRemoveChild: remove child or not)\n *\n * @param {Node} node\n * @param {Boolean} isRemoveChild\n */\nfunction remove(node, isRemoveChild) {\n  if (!node || !node.parentNode) { return; }\n  if (node.removeNode) { return node.removeNode(isRemoveChild); }\n\n  const parent = node.parentNode;\n  if (!isRemoveChild) {\n    const nodes = [];\n    for (let i = 0, len = node.childNodes.length; i < len; i++) {\n      nodes.push(node.childNodes[i]);\n    }\n\n    for (let i = 0, len = nodes.length; i < len; i++) {\n      parent.insertBefore(nodes[i], node);\n    }\n  }\n\n  parent.removeChild(node);\n}\n\n/**\n * @method removeWhile\n *\n * @param {Node} node\n * @param {Function} pred\n */\nfunction removeWhile(node, pred) {\n  while (node) {\n    if (isEditable(node) || !pred(node)) {\n      break;\n    }\n\n    const parent = node.parentNode;\n    remove(node);\n    node = parent;\n  }\n}\n\n/**\n * @method replace\n *\n * replace node with provided nodeName\n *\n * @param {Node} node\n * @param {String} nodeName\n * @return {Node} - new node\n */\nfunction replace(node, nodeName) {\n  if (node.nodeName.toUpperCase() === nodeName.toUpperCase()) {\n    return node;\n  }\n\n  const newNode = create(nodeName);\n\n  if (node.style.cssText) {\n    newNode.style.cssText = node.style.cssText;\n  }\n\n  appendChildNodes(newNode, lists.from(node.childNodes));\n  insertAfter(newNode, node);\n  remove(node);\n\n  return newNode;\n}\n\nconst isTextarea = makePredByNodeName('TEXTAREA');\n\n/**\n * @param {jQuery} $node\n * @param {Boolean} [stripLinebreaks] - default: false\n */\nfunction value($node, stripLinebreaks) {\n  const val = isTextarea($node[0]) ? $node.val() : $node.html();\n  if (stripLinebreaks) {\n    return val.replace(/[\\n\\r]/g, '');\n  }\n  return val;\n}\n\n/**\n * @method html\n *\n * get the HTML contents of node\n *\n * @param {jQuery} $node\n * @param {Boolean} [isNewlineOnBlock]\n */\nfunction html($node, isNewlineOnBlock) {\n  let markup = value($node);\n\n  if (isNewlineOnBlock) {\n    const regexTag = /<(\\/?)(\\b(?!!)[^>\\s]*)(.*?)(\\s*\\/?>)/g;\n    markup = markup.replace(regexTag, function(match, endSlash, name) {\n      name = name.toUpperCase();\n      const isEndOfInlineContainer = /^DIV|^TD|^TH|^P|^LI|^H[1-7]/.test(name) &&\n                                   !!endSlash;\n      const isBlockNode = /^BLOCKQUOTE|^TABLE|^TBODY|^TR|^HR|^UL|^OL/.test(name);\n\n      return match + ((isEndOfInlineContainer || isBlockNode) ? '\\n' : '');\n    });\n    markup = markup.trim();\n  }\n\n  return markup;\n}\n\nfunction posFromPlaceholder(placeholder) {\n  const $placeholder = $(placeholder);\n  const pos = $placeholder.offset();\n  const height = $placeholder.outerHeight(true); // include margin\n\n  return {\n    left: pos.left,\n    top: pos.top + height,\n  };\n}\n\nfunction attachEvents($node, events) {\n  Object.keys(events).forEach(function(key) {\n    $node.on(key, events[key]);\n  });\n}\n\nfunction detachEvents($node, events) {\n  Object.keys(events).forEach(function(key) {\n    $node.off(key, events[key]);\n  });\n}\n\n/**\n * @method isCustomStyleTag\n *\n * assert if a node contains a \"note-styletag\" class,\n * which implies that's a custom-made style tag node\n *\n * @param {Node} an HTML DOM node\n */\nfunction isCustomStyleTag(node) {\n  return node && !isText(node) && lists.contains(node.classList, 'note-styletag');\n}\n\nexport default {\n  /** @property {String} NBSP_CHAR */\n  NBSP_CHAR,\n  /** @property {String} ZERO_WIDTH_NBSP_CHAR */\n  ZERO_WIDTH_NBSP_CHAR,\n  /** @property {String} blank */\n  blank: blankHTML,\n  /** @property {String} emptyPara */\n  emptyPara: `<p>${blankHTML}</p>`,\n  makePredByNodeName,\n  isEditable,\n  isControlSizing,\n  isText,\n  isElement,\n  isVoid,\n  isPara,\n  isPurePara,\n  isHeading,\n  isInline,\n  isBlock: func.not(isInline),\n  isBodyInline,\n  isBody,\n  isParaInline,\n  isPre,\n  isList,\n  isTable,\n  isData,\n  isCell,\n  isBlockquote,\n  isBodyContainer,\n  isAnchor,\n  isDiv: makePredByNodeName('DIV'),\n  isLi,\n  isBR: makePredByNodeName('BR'),\n  isSpan: makePredByNodeName('SPAN'),\n  isB: makePredByNodeName('B'),\n  isU: makePredByNodeName('U'),\n  isS: makePredByNodeName('S'),\n  isI: makePredByNodeName('I'),\n  isImg: makePredByNodeName('IMG'),\n  isTextarea,\n  deepestChildIsEmpty,\n  isEmpty,\n  isEmptyAnchor: func.and(isAnchor, isEmpty),\n  isClosestSibling,\n  withClosestSiblings,\n  nodeLength,\n  isLeftEdgePoint,\n  isRightEdgePoint,\n  isEdgePoint,\n  isLeftEdgeOf,\n  isRightEdgeOf,\n  isLeftEdgePointOf,\n  isRightEdgePointOf,\n  prevPoint,\n  nextPoint,\n  isSamePoint,\n  isVisiblePoint,\n  prevPointUntil,\n  nextPointUntil,\n  isCharPoint,\n  isSpacePoint,\n  walkPoint,\n  ancestor,\n  singleChildAncestor,\n  listAncestor,\n  lastAncestor,\n  listNext,\n  listPrev,\n  listDescendant,\n  commonAncestor,\n  wrap,\n  insertAfter,\n  appendChildNodes,\n  position,\n  hasChildren,\n  makeOffsetPath,\n  fromOffsetPath,\n  splitTree,\n  splitPoint,\n  create,\n  createText,\n  remove,\n  removeWhile,\n  replace,\n  html,\n  value,\n  posFromPlaceholder,\n  attachEvents,\n  detachEvents,\n  isCustomStyleTag,\n};\n", "import $ from 'jquery';\nimport func from './core/func';\nimport lists from './core/lists';\nimport dom from './core/dom';\n\nexport default class Context {\n  /**\n   * @param {jQuery} $note\n   * @param {Object} options\n   */\n  constructor($note, options) {\n    this.$note = $note;\n\n    this.memos = {};\n    this.modules = {};\n    this.layoutInfo = {};\n    this.options = $.extend(true, {}, options);\n\n    // init ui with options\n    $.summernote.ui = $.summernote.ui_template(this.options);\n    this.ui = $.summernote.ui;\n\n    this.initialize();\n  }\n\n  /**\n   * create layout and initialize modules and other resources\n   */\n  initialize() {\n    this.layoutInfo = this.ui.createLayout(this.$note);\n    this._initialize();\n    this.$note.hide();\n    return this;\n  }\n\n  /**\n   * destroy modules and other resources and remove layout\n   */\n  destroy() {\n    this._destroy();\n    this.$note.removeData('summernote');\n    this.ui.removeLayout(this.$note, this.layoutInfo);\n  }\n\n  /**\n   * destory modules and other resources and initialize it again\n   */\n  reset() {\n    const disabled = this.isDisabled();\n    this.code(dom.emptyPara);\n    this._destroy();\n    this._initialize();\n\n    if (disabled) {\n      this.disable();\n    }\n  }\n\n  _initialize() {\n    // set own id\n    this.options.id = func.uniqueId($.now());\n    // set default container for tooltips, popovers, and dialogs\n    this.options.container = this.options.container || this.layoutInfo.editor;\n\n    // add optional buttons\n    const buttons = $.extend({}, this.options.buttons);\n    Object.keys(buttons).forEach((key) => {\n      this.memo('button.' + key, buttons[key]);\n    });\n\n    const modules = $.extend({}, this.options.modules, $.summernote.plugins || {});\n\n    // add and initialize modules\n    Object.keys(modules).forEach((key) => {\n      this.module(key, modules[key], true);\n    });\n\n    Object.keys(this.modules).forEach((key) => {\n      this.initializeModule(key);\n    });\n  }\n\n  _destroy() {\n    // destroy modules with reversed order\n    Object.keys(this.modules).reverse().forEach((key) => {\n      this.removeModule(key);\n    });\n\n    Object.keys(this.memos).forEach((key) => {\n      this.removeMemo(key);\n    });\n    // trigger custom onDestroy callback\n    this.triggerEvent('destroy', this);\n  }\n\n  code(html) {\n    const isActivated = this.invoke('codeview.isActivated');\n\n    if (html === undefined) {\n      this.invoke('codeview.sync');\n      return isActivated ? this.layoutInfo.codable.val() : this.layoutInfo.editable.html();\n    } else {\n      if (isActivated) {\n        this.layoutInfo.codable.val(html);\n      } else {\n        this.layoutInfo.editable.html(html);\n      }\n      this.$note.val(html);\n      this.triggerEvent('change', html, this.layoutInfo.editable);\n    }\n  }\n\n  isDisabled() {\n    return this.layoutInfo.editable.attr('contenteditable') === 'false';\n  }\n\n  enable() {\n    this.layoutInfo.editable.attr('contenteditable', true);\n    this.invoke('toolbar.activate', true);\n    this.triggerEvent('disable', false);\n    this.options.editing = true;\n  }\n\n  disable() {\n    // close codeview if codeview is opend\n    if (this.invoke('codeview.isActivated')) {\n      this.invoke('codeview.deactivate');\n    }\n    this.layoutInfo.editable.attr('contenteditable', false);\n    this.options.editing = false;\n    this.invoke('toolbar.deactivate', true);\n\n    this.triggerEvent('disable', true);\n  }\n\n  triggerEvent() {\n    const namespace = lists.head(arguments);\n    const args = lists.tail(lists.from(arguments));\n\n    const callback = this.options.callbacks[func.namespaceToCamel(namespace, 'on')];\n    if (callback) {\n      callback.apply(this.$note[0], args);\n    }\n    this.$note.trigger('summernote.' + namespace, args);\n  }\n\n  initializeModule(key) {\n    const module = this.modules[key];\n    module.shouldInitialize = module.shouldInitialize || func.ok;\n    if (!module.shouldInitialize()) {\n      return;\n    }\n\n    // initialize module\n    if (module.initialize) {\n      module.initialize();\n    }\n\n    // attach events\n    if (module.events) {\n      dom.attachEvents(this.$note, module.events);\n    }\n  }\n\n  module(key, ModuleClass, withoutIntialize) {\n    if (arguments.length === 1) {\n      return this.modules[key];\n    }\n\n    this.modules[key] = new ModuleClass(this);\n\n    if (!withoutIntialize) {\n      this.initializeModule(key);\n    }\n  }\n\n  removeModule(key) {\n    const module = this.modules[key];\n    if (module.shouldInitialize()) {\n      if (module.events) {\n        dom.detachEvents(this.$note, module.events);\n      }\n\n      if (module.destroy) {\n        module.destroy();\n      }\n    }\n\n    delete this.modules[key];\n  }\n\n  memo(key, obj) {\n    if (arguments.length === 1) {\n      return this.memos[key];\n    }\n    this.memos[key] = obj;\n  }\n\n  removeMemo(key) {\n    if (this.memos[key] && this.memos[key].destroy) {\n      this.memos[key].destroy();\n    }\n\n    delete this.memos[key];\n  }\n\n  /**\n   * Some buttons need to change their visual style immediately once they get pressed\n   */\n  createInvokeHandlerAndUpdateState(namespace, value) {\n    return (event) => {\n      this.createInvokeHandler(namespace, value)(event);\n      this.invoke('buttons.updateCurrentStyle');\n    };\n  }\n\n  createInvokeHandler(namespace, value) {\n    return (event) => {\n      event.preventDefault();\n      const $target = $(event.target);\n      this.invoke(namespace, value || $target.closest('[data-value]').data('value'), $target);\n    };\n  }\n\n  invoke() {\n    const namespace = lists.head(arguments);\n    const args = lists.tail(lists.from(arguments));\n\n    const splits = namespace.split('.');\n    const hasSeparator = splits.length > 1;\n    const moduleName = hasSeparator && lists.head(splits);\n    const methodName = hasSeparator ? lists.last(splits) : lists.head(splits);\n\n    const module = this.modules[moduleName || 'editor'];\n    if (!moduleName && this[methodName]) {\n      return this[methodName].apply(this, args);\n    } else if (module && module[methodName] && module.shouldInitialize()) {\n      return module[methodName].apply(module, args);\n    }\n  }\n}\n", "import $ from 'jquery';\nimport env from './env';\nimport func from './func';\nimport lists from './lists';\nimport dom from './dom';\n\n/**\n * return boundaryPoint from TextRange, inspired by <PERSON>'s HuskyRange.js\n *\n * @param {TextRange} textRange\n * @param {Boolean} isStart\n * @return {BoundaryPoint}\n *\n * @see http://msdn.microsoft.com/en-us/library/ie/ms535872(v=vs.85).aspx\n */\nfunction textRangeToPoint(textRange, isStart) {\n  let container = textRange.parentElement();\n  let offset;\n\n  const tester = document.body.createTextRange();\n  let prevContainer;\n  const childNodes = lists.from(container.childNodes);\n  for (offset = 0; offset < childNodes.length; offset++) {\n    if (dom.isText(childNodes[offset])) {\n      continue;\n    }\n    tester.moveToElementText(childNodes[offset]);\n    if (tester.compareEndPoints('StartToStart', textRange) >= 0) {\n      break;\n    }\n    prevContainer = childNodes[offset];\n  }\n\n  if (offset !== 0 && dom.isText(childNodes[offset - 1])) {\n    const textRangeStart = document.body.createTextRange();\n    let curTextNode = null;\n    textRangeStart.moveToElementText(prevContainer || container);\n    textRangeStart.collapse(!prevContainer);\n    curTextNode = prevContainer ? prevContainer.nextSibling : container.firstChild;\n\n    const pointTester = textRange.duplicate();\n    pointTester.setEndPoint('StartToStart', textRangeStart);\n    let textCount = pointTester.text.replace(/[\\r\\n]/g, '').length;\n\n    while (textCount > curTextNode.nodeValue.length && curTextNode.nextSibling) {\n      textCount -= curTextNode.nodeValue.length;\n      curTextNode = curTextNode.nextSibling;\n    }\n\n    // [workaround] enforce IE to re-reference curTextNode, hack\n    const dummy = curTextNode.nodeValue; // eslint-disable-line\n\n    if (isStart && curTextNode.nextSibling && dom.isText(curTextNode.nextSibling) &&\n      textCount === curTextNode.nodeValue.length) {\n      textCount -= curTextNode.nodeValue.length;\n      curTextNode = curTextNode.nextSibling;\n    }\n\n    container = curTextNode;\n    offset = textCount;\n  }\n\n  return {\n    cont: container,\n    offset: offset,\n  };\n}\n\n/**\n * return TextRange from boundary point (inspired by google closure-library)\n * @param {BoundaryPoint} point\n * @return {TextRange}\n */\nfunction pointToTextRange(point) {\n  const textRangeInfo = function(container, offset) {\n    let node, isCollapseToStart;\n\n    if (dom.isText(container)) {\n      const prevTextNodes = dom.listPrev(container, func.not(dom.isText));\n      const prevContainer = lists.last(prevTextNodes).previousSibling;\n      node = prevContainer || container.parentNode;\n      offset += lists.sum(lists.tail(prevTextNodes), dom.nodeLength);\n      isCollapseToStart = !prevContainer;\n    } else {\n      node = container.childNodes[offset] || container;\n      if (dom.isText(node)) {\n        return textRangeInfo(node, 0);\n      }\n\n      offset = 0;\n      isCollapseToStart = false;\n    }\n\n    return {\n      node: node,\n      collapseToStart: isCollapseToStart,\n      offset: offset,\n    };\n  };\n\n  const textRange = document.body.createTextRange();\n  const info = textRangeInfo(point.node, point.offset);\n\n  textRange.moveToElementText(info.node);\n  textRange.collapse(info.collapseToStart);\n  textRange.moveStart('character', info.offset);\n  return textRange;\n}\n\n/**\n   * Wrapped Range\n   *\n   * @constructor\n   * @param {Node} sc - start container\n   * @param {Number} so - start offset\n   * @param {Node} ec - end container\n   * @param {Number} eo - end offset\n   */\nclass WrappedRange {\n  constructor(sc, so, ec, eo) {\n    this.sc = sc;\n    this.so = so;\n    this.ec = ec;\n    this.eo = eo;\n\n    // isOnEditable: judge whether range is on editable or not\n    this.isOnEditable = this.makeIsOn(dom.isEditable);\n    // isOnList: judge whether range is on list node or not\n    this.isOnList = this.makeIsOn(dom.isList);\n    // isOnAnchor: judge whether range is on anchor node or not\n    this.isOnAnchor = this.makeIsOn(dom.isAnchor);\n    // isOnCell: judge whether range is on cell node or not\n    this.isOnCell = this.makeIsOn(dom.isCell);\n    // isOnData: judge whether range is on data node or not\n    this.isOnData = this.makeIsOn(dom.isData);\n  }\n\n  // nativeRange: get nativeRange from sc, so, ec, eo\n  nativeRange() {\n    if (env.isW3CRangeSupport) {\n      const w3cRange = document.createRange();\n      w3cRange.setStart(this.sc, this.sc.data && this.so > this.sc.data.length ? 0 : this.so);\n      w3cRange.setEnd(this.ec, this.sc.data ? Math.min(this.eo, this.sc.data.length) : this.eo);\n\n      return w3cRange;\n    } else {\n      const textRange = pointToTextRange({\n        node: this.sc,\n        offset: this.so,\n      });\n\n      textRange.setEndPoint('EndToEnd', pointToTextRange({\n        node: this.ec,\n        offset: this.eo,\n      }));\n\n      return textRange;\n    }\n  }\n\n  getPoints() {\n    return {\n      sc: this.sc,\n      so: this.so,\n      ec: this.ec,\n      eo: this.eo,\n    };\n  }\n\n  getStartPoint() {\n    return {\n      node: this.sc,\n      offset: this.so,\n    };\n  }\n\n  getEndPoint() {\n    return {\n      node: this.ec,\n      offset: this.eo,\n    };\n  }\n\n  /**\n   * select update visible range\n   */\n  select() {\n    const nativeRng = this.nativeRange();\n    if (env.isW3CRangeSupport) {\n      const selection = document.getSelection();\n      if (selection.rangeCount > 0) {\n        selection.removeAllRanges();\n      }\n      selection.addRange(nativeRng);\n    } else {\n      nativeRng.select();\n    }\n\n    return this;\n  }\n\n  /**\n   * Moves the scrollbar to start container(sc) of current range\n   *\n   * @return {WrappedRange}\n   */\n  scrollIntoView(container) {\n    const height = $(container).height();\n    if (container.scrollTop + height < this.sc.offsetTop) {\n      container.scrollTop += Math.abs(container.scrollTop + height - this.sc.offsetTop);\n    }\n\n    return this;\n  }\n\n  /**\n   * @return {WrappedRange}\n   */\n  normalize() {\n    /**\n     * @param {BoundaryPoint} point\n     * @param {Boolean} isLeftToRight - true: prefer to choose right node\n     *                                - false: prefer to choose left node\n     * @return {BoundaryPoint}\n     */\n    const getVisiblePoint = function(point, isLeftToRight) {\n      if (!point) {\n        return point;\n      }\n\n      // Just use the given point [XXX:Adhoc]\n      //  - case 01. if the point is on the middle of the node\n      //  - case 02. if the point is on the right edge and prefer to choose left node\n      //  - case 03. if the point is on the left edge and prefer to choose right node\n      //  - case 04. if the point is on the right edge and prefer to choose right node but the node is void\n      //  - case 05. if the point is on the left edge and prefer to choose left node but the node is void\n      //  - case 06. if the point is on the block node and there is no children\n      if (dom.isVisiblePoint(point)) {\n        if (!dom.isEdgePoint(point) ||\n            (dom.isRightEdgePoint(point) && !isLeftToRight) ||\n            (dom.isLeftEdgePoint(point) && isLeftToRight) ||\n            (dom.isRightEdgePoint(point) && isLeftToRight && dom.isVoid(point.node.nextSibling)) ||\n            (dom.isLeftEdgePoint(point) && !isLeftToRight && dom.isVoid(point.node.previousSibling)) ||\n            (dom.isBlock(point.node) && dom.isEmpty(point.node))) {\n          return point;\n        }\n      }\n\n      // point on block's edge\n      const block = dom.ancestor(point.node, dom.isBlock);\n      let hasRightNode = false;\n\n      if (!hasRightNode) {\n        const prevPoint = dom.prevPoint(point) || { node: null };\n        hasRightNode = (dom.isLeftEdgePointOf(point, block) || dom.isVoid(prevPoint.node)) && !isLeftToRight;\n      }\n\n      let hasLeftNode = false;\n      if (!hasLeftNode) {\n        const nextPoint = dom.nextPoint(point) || { node: null };\n        hasLeftNode = (dom.isRightEdgePointOf(point, block) || dom.isVoid(nextPoint.node)) && isLeftToRight;\n      }\n\n      if (hasRightNode || hasLeftNode) {\n        // returns point already on visible point\n        if (dom.isVisiblePoint(point)) {\n          return point;\n        }\n        // reverse direction\n        isLeftToRight = !isLeftToRight;\n      }\n\n      const nextPoint = isLeftToRight ? dom.nextPointUntil(dom.nextPoint(point), dom.isVisiblePoint)\n        : dom.prevPointUntil(dom.prevPoint(point), dom.isVisiblePoint);\n      return nextPoint || point;\n    };\n\n    const endPoint = getVisiblePoint(this.getEndPoint(), false);\n    const startPoint = this.isCollapsed() ? endPoint : getVisiblePoint(this.getStartPoint(), true);\n\n    return new WrappedRange(\n      startPoint.node,\n      startPoint.offset,\n      endPoint.node,\n      endPoint.offset\n    );\n  }\n\n  /**\n   * returns matched nodes on range\n   *\n   * @param {Function} [pred] - predicate function\n   * @param {Object} [options]\n   * @param {Boolean} [options.includeAncestor]\n   * @param {Boolean} [options.fullyContains]\n   * @return {Node[]}\n   */\n  nodes(pred, options) {\n    pred = pred || func.ok;\n\n    const includeAncestor = options && options.includeAncestor;\n    const fullyContains = options && options.fullyContains;\n\n    // TODO compare points and sort\n    const startPoint = this.getStartPoint();\n    const endPoint = this.getEndPoint();\n\n    const nodes = [];\n    const leftEdgeNodes = [];\n\n    dom.walkPoint(startPoint, endPoint, function(point) {\n      if (dom.isEditable(point.node)) {\n        return;\n      }\n\n      let node;\n      if (fullyContains) {\n        if (dom.isLeftEdgePoint(point)) {\n          leftEdgeNodes.push(point.node);\n        }\n        if (dom.isRightEdgePoint(point) && lists.contains(leftEdgeNodes, point.node)) {\n          node = point.node;\n        }\n      } else if (includeAncestor) {\n        node = dom.ancestor(point.node, pred);\n      } else {\n        node = point.node;\n      }\n\n      if (node && pred(node)) {\n        nodes.push(node);\n      }\n    }, true);\n\n    return lists.unique(nodes);\n  }\n\n  /**\n   * returns commonAncestor of range\n   * @return {Element} - commonAncestor\n   */\n  commonAncestor() {\n    return dom.commonAncestor(this.sc, this.ec);\n  }\n\n  /**\n   * returns expanded range by pred\n   *\n   * @param {Function} pred - predicate function\n   * @return {WrappedRange}\n   */\n  expand(pred) {\n    const startAncestor = dom.ancestor(this.sc, pred);\n    const endAncestor = dom.ancestor(this.ec, pred);\n\n    if (!startAncestor && !endAncestor) {\n      return new WrappedRange(this.sc, this.so, this.ec, this.eo);\n    }\n\n    const boundaryPoints = this.getPoints();\n\n    if (startAncestor) {\n      boundaryPoints.sc = startAncestor;\n      boundaryPoints.so = 0;\n    }\n\n    if (endAncestor) {\n      boundaryPoints.ec = endAncestor;\n      boundaryPoints.eo = dom.nodeLength(endAncestor);\n    }\n\n    return new WrappedRange(\n      boundaryPoints.sc,\n      boundaryPoints.so,\n      boundaryPoints.ec,\n      boundaryPoints.eo\n    );\n  }\n\n  /**\n   * @param {Boolean} isCollapseToStart\n   * @return {WrappedRange}\n   */\n  collapse(isCollapseToStart) {\n    if (isCollapseToStart) {\n      return new WrappedRange(this.sc, this.so, this.sc, this.so);\n    } else {\n      return new WrappedRange(this.ec, this.eo, this.ec, this.eo);\n    }\n  }\n\n  /**\n   * splitText on range\n   */\n  splitText() {\n    const isSameContainer = this.sc === this.ec;\n    const boundaryPoints = this.getPoints();\n\n    if (dom.isText(this.ec) && !dom.isEdgePoint(this.getEndPoint())) {\n      this.ec.splitText(this.eo);\n    }\n\n    if (dom.isText(this.sc) && !dom.isEdgePoint(this.getStartPoint())) {\n      boundaryPoints.sc = this.sc.splitText(this.so);\n      boundaryPoints.so = 0;\n\n      if (isSameContainer) {\n        boundaryPoints.ec = boundaryPoints.sc;\n        boundaryPoints.eo = this.eo - this.so;\n      }\n    }\n\n    return new WrappedRange(\n      boundaryPoints.sc,\n      boundaryPoints.so,\n      boundaryPoints.ec,\n      boundaryPoints.eo\n    );\n  }\n\n  /**\n   * delete contents on range\n   * @return {WrappedRange}\n   */\n  deleteContents() {\n    if (this.isCollapsed()) {\n      return this;\n    }\n\n    const rng = this.splitText();\n    const nodes = rng.nodes(null, {\n      fullyContains: true,\n    });\n\n    // find new cursor point\n    const point = dom.prevPointUntil(rng.getStartPoint(), function(point) {\n      return !lists.contains(nodes, point.node);\n    });\n\n    const emptyParents = [];\n    $.each(nodes, function(idx, node) {\n      // find empty parents\n      const parent = node.parentNode;\n      if (point.node !== parent && dom.nodeLength(parent) === 1) {\n        emptyParents.push(parent);\n      }\n      dom.remove(node, false);\n    });\n\n    // remove empty parents\n    $.each(emptyParents, function(idx, node) {\n      dom.remove(node, false);\n    });\n\n    return new WrappedRange(\n      point.node,\n      point.offset,\n      point.node,\n      point.offset\n    ).normalize();\n  }\n\n  /**\n   * makeIsOn: return isOn(pred) function\n   */\n  makeIsOn(pred) {\n    return function() {\n      const ancestor = dom.ancestor(this.sc, pred);\n      return !!ancestor && (ancestor === dom.ancestor(this.ec, pred));\n    };\n  }\n\n  /**\n   * @param {Function} pred\n   * @return {Boolean}\n   */\n  isLeftEdgeOf(pred) {\n    if (!dom.isLeftEdgePoint(this.getStartPoint())) {\n      return false;\n    }\n\n    const node = dom.ancestor(this.sc, pred);\n    return node && dom.isLeftEdgeOf(this.sc, node);\n  }\n\n  /**\n   * returns whether range was collapsed or not\n   */\n  isCollapsed() {\n    return this.sc === this.ec && this.so === this.eo;\n  }\n\n  /**\n   * wrap inline nodes which children of body with paragraph\n   *\n   * @return {WrappedRange}\n   */\n  wrapBodyInlineWithPara() {\n    if (dom.isBodyContainer(this.sc) && dom.isEmpty(this.sc)) {\n      this.sc.innerHTML = dom.emptyPara;\n      return new WrappedRange(this.sc.firstChild, 0, this.sc.firstChild, 0);\n    }\n\n    /**\n     * [workaround] firefox often create range on not visible point. so normalize here.\n     *  - firefox: |<p>text</p>|\n     *  - chrome: <p>|text|</p>\n     */\n    const rng = this.normalize();\n    if (dom.isParaInline(this.sc) || dom.isPara(this.sc)) {\n      return rng;\n    }\n\n    // find inline top ancestor\n    let topAncestor;\n    if (dom.isInline(rng.sc)) {\n      const ancestors = dom.listAncestor(rng.sc, func.not(dom.isInline));\n      topAncestor = lists.last(ancestors);\n      if (!dom.isInline(topAncestor)) {\n        topAncestor = ancestors[ancestors.length - 2] || rng.sc.childNodes[rng.so];\n      }\n    } else {\n      topAncestor = rng.sc.childNodes[rng.so > 0 ? rng.so - 1 : 0];\n    }\n\n    if (topAncestor) {\n      // siblings not in paragraph\n      let inlineSiblings = dom.listPrev(topAncestor, dom.isParaInline).reverse();\n      inlineSiblings = inlineSiblings.concat(dom.listNext(topAncestor.nextSibling, dom.isParaInline));\n\n      // wrap with paragraph\n      if (inlineSiblings.length) {\n        const para = dom.wrap(lists.head(inlineSiblings), 'p');\n        dom.appendChildNodes(para, lists.tail(inlineSiblings));\n      }\n    }\n\n    return this.normalize();\n  }\n\n  /**\n   * insert node at current cursor\n   *\n   * @param {Node} node\n   * @return {Node}\n   */\n  insertNode(node) {\n    let rng = this;\n\n    if (dom.isText(node) || dom.isInline(node)) {\n      rng = this.wrapBodyInlineWithPara().deleteContents();\n    }\n\n    const info = dom.splitPoint(rng.getStartPoint(), dom.isInline(node));\n    if (info.rightNode) {\n      info.rightNode.parentNode.insertBefore(node, info.rightNode);\n    } else {\n      info.container.appendChild(node);\n    }\n\n    return node;\n  }\n\n  /**\n   * insert html at current cursor\n   */\n  pasteHTML(markup) {\n    markup = $.trim(markup);\n\n    const contentsContainer = $('<div></div>').html(markup)[0];\n    let childNodes = lists.from(contentsContainer.childNodes);\n\n    // const rng = this.wrapBodyInlineWithPara().deleteContents();\n    const rng = this;\n\n    if (rng.so >= 0) {\n      childNodes = childNodes.reverse();\n    }\n    childNodes = childNodes.map(function(childNode) {\n      return rng.insertNode(childNode);\n    });\n    if (rng.so > 0) {\n      childNodes = childNodes.reverse();\n    }\n    return childNodes;\n  }\n\n  /**\n   * returns text in range\n   *\n   * @return {String}\n   */\n  toString() {\n    const nativeRng = this.nativeRange();\n    return env.isW3CRangeSupport ? nativeRng.toString() : nativeRng.text;\n  }\n\n  /**\n   * returns range for word before cursor\n   *\n   * @param {Boolean} [findAfter] - find after cursor, default: false\n   * @return {WrappedRange}\n   */\n  getWordRange(findAfter) {\n    let endPoint = this.getEndPoint();\n\n    if (!dom.isCharPoint(endPoint)) {\n      return this;\n    }\n\n    const startPoint = dom.prevPointUntil(endPoint, function(point) {\n      return !dom.isCharPoint(point);\n    });\n\n    if (findAfter) {\n      endPoint = dom.nextPointUntil(endPoint, function(point) {\n        return !dom.isCharPoint(point);\n      });\n    }\n\n    return new WrappedRange(\n      startPoint.node,\n      startPoint.offset,\n      endPoint.node,\n      endPoint.offset\n    );\n  }\n\n  /**\n   * returns range for words before cursor\n   *\n   * @param {Boolean} [findAfter] - find after cursor, default: false\n   * @return {WrappedRange}\n   */\n  getWordsRange(findAfter) {\n    var endPoint = this.getEndPoint();\n\n    var isNotTextPoint = function(point) {\n      return !dom.isCharPoint(point) && !dom.isSpacePoint(point);\n    };\n\n    if (isNotTextPoint(endPoint)) {\n      return this;\n    }\n\n    var startPoint = dom.prevPointUntil(endPoint, isNotTextPoint);\n\n    if (findAfter) {\n      endPoint = dom.nextPointUntil(endPoint, isNotTextPoint);\n    }\n\n    return new WrappedRange(\n      startPoint.node,\n      startPoint.offset,\n      endPoint.node,\n      endPoint.offset\n    );\n  };\n\n  /**\n   * returns range for words before cursor that match with a Regex\n   *\n   * example:\n   *  range: 'hi @Peter Pan'\n   *  regex: '/@[a-z ]+/i'\n   *  return range: '@Peter Pan'\n   *\n   * @param {RegExp} [regex]\n   * @return {WrappedRange|null}\n   */\n  getWordsMatchRange(regex) {\n    var endPoint = this.getEndPoint();\n\n    var startPoint = dom.prevPointUntil(endPoint, function(point) {\n      if (!dom.isCharPoint(point) && !dom.isSpacePoint(point)) {\n        return true;\n      }\n      var rng = new WrappedRange(\n        point.node,\n        point.offset,\n        endPoint.node,\n        endPoint.offset\n      );\n      var result = regex.exec(rng.toString());\n      return result && result.index === 0;\n    });\n\n    var rng = new WrappedRange(\n      startPoint.node,\n      startPoint.offset,\n      endPoint.node,\n      endPoint.offset\n    );\n\n    var text = rng.toString();\n    var result = regex.exec(text);\n\n    if (result && result[0].length === text.length) {\n      return rng;\n    } else {\n      return null;\n    }\n  };\n\n  /**\n   * create offsetPath bookmark\n   *\n   * @param {Node} editable\n   */\n  bookmark(editable) {\n    return {\n      s: {\n        path: dom.makeOffsetPath(editable, this.sc),\n        offset: this.so,\n      },\n      e: {\n        path: dom.makeOffsetPath(editable, this.ec),\n        offset: this.eo,\n      },\n    };\n  }\n\n  /**\n   * create offsetPath bookmark base on paragraph\n   *\n   * @param {Node[]} paras\n   */\n  paraBookmark(paras) {\n    return {\n      s: {\n        path: lists.tail(dom.makeOffsetPath(lists.head(paras), this.sc)),\n        offset: this.so,\n      },\n      e: {\n        path: lists.tail(dom.makeOffsetPath(lists.last(paras), this.ec)),\n        offset: this.eo,\n      },\n    };\n  }\n\n  /**\n   * getClientRects\n   * @return {Rect[]}\n   */\n  getClientRects() {\n    const nativeRng = this.nativeRange();\n    return nativeRng.getClientRects();\n  }\n}\n\n/**\n * Data structure\n *  * BoundaryPoint: a point of dom tree\n *  * BoundaryPoints: two boundaryPoints corresponding to the start and the end of the Range\n *\n * See to http://www.w3.org/TR/DOM-Level-2-Traversal-Range/ranges.html#Level-2-Range-Position\n */\nexport default {\n  /**\n   * create Range Object From arguments or Browser Selection\n   *\n   * @param {Node} sc - start container\n   * @param {Number} so - start offset\n   * @param {Node} ec - end container\n   * @param {Number} eo - end offset\n   * @return {WrappedRange}\n   */\n  create: function(sc, so, ec, eo) {\n    if (arguments.length === 4) {\n      return new WrappedRange(sc, so, ec, eo);\n    } else if (arguments.length === 2) { // collapsed\n      ec = sc;\n      eo = so;\n      return new WrappedRange(sc, so, ec, eo);\n    } else {\n      let wrappedRange = this.createFromSelection();\n\n      if (!wrappedRange && arguments.length === 1) {\n        let bodyElement = arguments[0];\n        if (dom.isEditable(bodyElement)) {\n          bodyElement = bodyElement.lastChild;\n        }\n        return this.createFromBodyElement(bodyElement, dom.emptyPara === arguments[0].innerHTML);\n      }\n      return wrappedRange;\n    }\n  },\n\n  createFromBodyElement: function(bodyElement, isCollapseToStart = false) {\n    var wrappedRange = this.createFromNode(bodyElement);\n    return wrappedRange.collapse(isCollapseToStart);\n  },\n\n  createFromSelection: function() {\n    let sc, so, ec, eo;\n    if (env.isW3CRangeSupport) {\n      const selection = document.getSelection();\n      if (!selection || selection.rangeCount === 0) {\n        return null;\n      } else if (dom.isBody(selection.anchorNode)) {\n        // Firefox: returns entire body as range on initialization.\n        // We won't never need it.\n        return null;\n      }\n\n      const nativeRng = selection.getRangeAt(0);\n      sc = nativeRng.startContainer;\n      so = nativeRng.startOffset;\n      ec = nativeRng.endContainer;\n      eo = nativeRng.endOffset;\n    } else { // IE8: TextRange\n      const textRange = document.selection.createRange();\n      const textRangeEnd = textRange.duplicate();\n      textRangeEnd.collapse(false);\n      const textRangeStart = textRange;\n      textRangeStart.collapse(true);\n\n      let startPoint = textRangeToPoint(textRangeStart, true);\n      let endPoint = textRangeToPoint(textRangeEnd, false);\n\n      // same visible point case: range was collapsed.\n      if (dom.isText(startPoint.node) && dom.isLeftEdgePoint(startPoint) &&\n        dom.isTextNode(endPoint.node) && dom.isRightEdgePoint(endPoint) &&\n        endPoint.node.nextSibling === startPoint.node) {\n        startPoint = endPoint;\n      }\n\n      sc = startPoint.cont;\n      so = startPoint.offset;\n      ec = endPoint.cont;\n      eo = endPoint.offset;\n    }\n\n    return new WrappedRange(sc, so, ec, eo);\n  },\n\n  /**\n   * @method\n   *\n   * create WrappedRange from node\n   *\n   * @param {Node} node\n   * @return {WrappedRange}\n   */\n  createFromNode: function(node) {\n    let sc = node;\n    let so = 0;\n    let ec = node;\n    let eo = dom.nodeLength(ec);\n\n    // browsers can't target a picture or void node\n    if (dom.isVoid(sc)) {\n      so = dom.listPrev(sc).length - 1;\n      sc = sc.parentNode;\n    }\n    if (dom.isBR(ec)) {\n      eo = dom.listPrev(ec).length - 1;\n      ec = ec.parentNode;\n    } else if (dom.isVoid(ec)) {\n      eo = dom.listPrev(ec).length;\n      ec = ec.parentNode;\n    }\n\n    return this.create(sc, so, ec, eo);\n  },\n\n  /**\n   * create WrappedRange from node after position\n   *\n   * @param {Node} node\n   * @return {WrappedRange}\n   */\n  createFromNodeBefore: function(node) {\n    return this.createFromNode(node).collapse(true);\n  },\n\n  /**\n   * create WrappedRange from node after position\n   *\n   * @param {Node} node\n   * @return {WrappedRange}\n   */\n  createFromNodeAfter: function(node) {\n    return this.createFromNode(node).collapse();\n  },\n\n  /**\n   * @method\n   *\n   * create WrappedRange from bookmark\n   *\n   * @param {Node} editable\n   * @param {Object} bookmark\n   * @return {WrappedRange}\n   */\n  createFromBookmark: function(editable, bookmark) {\n    const sc = dom.fromOffsetPath(editable, bookmark.s.path);\n    const so = bookmark.s.offset;\n    const ec = dom.fromOffsetPath(editable, bookmark.e.path);\n    const eo = bookmark.e.offset;\n    return new WrappedRange(sc, so, ec, eo);\n  },\n\n  /**\n   * @method\n   *\n   * create WrappedRange from paraBookmark\n   *\n   * @param {Object} bookmark\n   * @param {Node[]} paras\n   * @return {WrappedRange}\n   */\n  createFromParaBookmark: function(bookmark, paras) {\n    const so = bookmark.s.offset;\n    const eo = bookmark.e.offset;\n    const sc = dom.fromOffsetPath(lists.head(paras), bookmark.s.path);\n    const ec = dom.fromOffsetPath(lists.last(paras), bookmark.e.path);\n\n    return new WrappedRange(sc, so, ec, eo);\n  },\n};\n", "import $ from 'jquery';\nimport env from './base/core/env';\nimport lists from './base/core/lists';\nimport Context from './base/Context';\n\n$.fn.extend({\n  /**\n   * Summernote API\n   *\n   * @param {Object|String}\n   * @return {this}\n   */\n  summernote: function() {\n    const type = $.type(lists.head(arguments));\n    const isExternalAPICalled = type === 'string';\n    const hasInitOptions = type === 'object';\n\n    const options = $.extend({}, $.summernote.options, hasInitOptions ? lists.head(arguments) : {});\n\n    // Update options\n    options.langInfo = $.extend(true, {}, $.summernote.lang['en-US'], $.summernote.lang[options.lang]);\n    options.icons = $.extend(true, {}, $.summernote.options.icons, options.icons);\n    options.tooltip = options.tooltip === 'auto' ? !env.isSupportTouch : options.tooltip;\n\n    this.each((idx, note) => {\n      const $note = $(note);\n      if (!$note.data('summernote')) {\n        const context = new Context($note, options);\n        $note.data('summernote', context);\n        $note.data('summernote').triggerEvent('init', context.layoutInfo);\n      }\n    });\n\n    const $note = this.first();\n    if ($note.length) {\n      const context = $note.data('summernote');\n      if (isExternalAPICalled) {\n        return context.invoke.apply(context, lists.from(arguments));\n      } else if (options.focus) {\n        context.invoke('editor.focus');\n      }\n    }\n\n    return this;\n  },\n});\n", "import lists from './lists';\nimport func from './func';\n\nconst KEY_MAP = {\n  'BACKSPACE': 8,\n  'TAB': 9,\n  'ENTER': 13,\n  'SPACE': 32,\n  'DELETE': 46,\n\n  // Arrow\n  'LEFT': 37,\n  'UP': 38,\n  'RIGHT': 39,\n  'DOWN': 40,\n\n  // Number: 0-9\n  'NUM0': 48,\n  'NUM1': 49,\n  'NUM2': 50,\n  'NUM3': 51,\n  'NUM4': 52,\n  'NUM5': 53,\n  'NUM6': 54,\n  'NUM7': 55,\n  'NUM8': 56,\n\n  // Alphabet: a-z\n  'B': 66,\n  'E': 69,\n  'I': 73,\n  'J': 74,\n  'K': 75,\n  'L': 76,\n  'R': 82,\n  'S': 83,\n  'U': 85,\n  'V': 86,\n  'Y': 89,\n  'Z': 90,\n\n  'SLASH': 191,\n  'LEFTBRACKET': 219,\n  'BACKSLASH': 220,\n  'RIGHTBRACKET': 221,\n\n  // Navigation\n  'HOME': 36,\n  'END': 35,\n  'PAGEUP': 33,\n  'PAGEDOWN': 34,\n};\n\n/**\n * @class core.key\n *\n * Object for keycodes.\n *\n * @singleton\n * @alternateClassName key\n */\nexport default {\n  /**\n   * @method isEdit\n   *\n   * @param {Number} keyCode\n   * @return {Boolean}\n   */\n  isEdit: (keyCode) => {\n    return lists.contains([\n      KEY_MAP.BACKSPACE,\n      KEY_MAP.TAB,\n      KEY_MAP.ENTER,\n      KEY_MAP.SPACE,\n      KEY_MAP.DELETE,\n    ], keyCode);\n  },\n  /**\n   * @method isMove\n   *\n   * @param {Number} keyCode\n   * @return {Boolean}\n   */\n  isMove: (keyCode) => {\n    return lists.contains([\n      KEY_MAP.LEFT,\n      KEY_MAP.UP,\n      KEY_MAP.RIGHT,\n      KEY_MAP.DOWN,\n    ], keyCode);\n  },\n  /**\n   * @method isNavigation\n   *\n   * @param {Number} keyCode\n   * @return {Boolean}\n   */\n  isNavigation: (keyCode) => {\n    return lists.contains([\n      KEY_MAP.HOME,\n      KEY_MAP.END,\n      KEY_MAP.PAGEUP,\n      KEY_MAP.PAGEDOWN,\n    ], keyCode);\n  },\n  /**\n   * @property {Object} nameFromCode\n   * @property {String} nameFromCode.8 \"BACKSPACE\"\n   */\n  nameFromCode: func.invertObject(KEY_MAP),\n  code: KEY_MAP,\n};\n", "import range from '../core/range';\n\nexport default class History {\n  constructor($editable) {\n    this.stack = [];\n    this.stackOffset = -1;\n    this.$editable = $editable;\n    this.editable = $editable[0];\n  }\n\n  makeSnapshot() {\n    const rng = range.create(this.editable);\n    const emptyBookmark = { s: { path: [], offset: 0 }, e: { path: [], offset: 0 } };\n\n    return {\n      contents: this.$editable.html(),\n      bookmark: ((rng && rng.isOnEditable()) ? rng.bookmark(this.editable) : emptyBookmark),\n    };\n  }\n\n  applySnapshot(snapshot) {\n    if (snapshot.contents !== null) {\n      this.$editable.html(snapshot.contents);\n    }\n    if (snapshot.bookmark !== null) {\n      range.createFromBookmark(this.editable, snapshot.bookmark).select();\n    }\n  }\n\n  /**\n  * @method rewind\n  * Rewinds the history stack back to the first snapshot taken.\n  * Leaves the stack intact, so that \"Redo\" can still be used.\n  */\n  rewind() {\n    // Create snap shot if not yet recorded\n    if (this.$editable.html() !== this.stack[this.stackOffset].contents) {\n      this.recordUndo();\n    }\n\n    // Return to the first available snapshot.\n    this.stackOffset = 0;\n\n    // Apply that snapshot.\n    this.applySnapshot(this.stack[this.stackOffset]);\n  }\n\n  /**\n  *  @method commit\n  *  Resets history stack, but keeps current editor's content.\n  */\n  commit() {\n    // Clear the stack.\n    this.stack = [];\n\n    // Restore stackOffset to its original value.\n    this.stackOffset = -1;\n\n    // Record our first snapshot (of nothing).\n    this.recordUndo();\n  }\n\n  /**\n  * @method reset\n  * Resets the history stack completely; reverting to an empty editor.\n  */\n  reset() {\n    // Clear the stack.\n    this.stack = [];\n\n    // Restore stackOffset to its original value.\n    this.stackOffset = -1;\n\n    // Clear the editable area.\n    this.$editable.html('');\n\n    // Record our first snapshot (of nothing).\n    this.recordUndo();\n  }\n\n  /**\n   * undo\n   */\n  undo() {\n    // Create snap shot if not yet recorded\n    if (this.$editable.html() !== this.stack[this.stackOffset].contents) {\n      this.recordUndo();\n    }\n\n    if (this.stackOffset > 0) {\n      this.stackOffset--;\n      this.applySnapshot(this.stack[this.stackOffset]);\n    }\n  }\n\n  /**\n   * redo\n   */\n  redo() {\n    if (this.stack.length - 1 > this.stackOffset) {\n      this.stackOffset++;\n      this.applySnapshot(this.stack[this.stackOffset]);\n    }\n  }\n\n  /**\n   * recorded undo\n   */\n  recordUndo() {\n    this.stackOffset++;\n\n    // Wash out stack after stackOffset\n    if (this.stack.length > this.stackOffset) {\n      this.stack = this.stack.slice(0, this.stackOffset);\n    }\n\n    // Create new snapshot and push it to the end\n    this.stack.push(this.makeSnapshot());\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport func from '../core/func';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\n\nexport default class Style {\n  /**\n   * @method jQueryCSS\n   *\n   * [workaround] for old jQuery\n   * passing an array of style properties to .css()\n   * will result in an object of property-value pairs.\n   * (compability with version < 1.9)\n   *\n   * @private\n   * @param  {jQuery} $obj\n   * @param  {Array} propertyNames - An array of one or more CSS properties.\n   * @return {Object}\n   */\n  jQueryCSS($obj, propertyNames) {\n    if (env.jqueryVersion < 1.9) {\n      const result = {};\n      $.each(propertyNames, (idx, propertyName) => {\n        result[propertyName] = $obj.css(propertyName);\n      });\n      return result;\n    }\n    return $obj.css(propertyNames);\n  }\n\n  /**\n   * returns style object from node\n   *\n   * @param {jQuery} $node\n   * @return {Object}\n   */\n  fromNode($node) {\n    const properties = ['font-family', 'font-size', 'text-align', 'list-style-type', 'line-height'];\n    const styleInfo = this.jQueryCSS($node, properties) || {};\n\n    const fontSize = $node[0].style.fontSize || styleInfo['font-size'];\n\n    styleInfo['font-size'] = parseInt(fontSize, 10);\n    styleInfo['font-size-unit'] = fontSize.match(/[a-z%]+$/);\n\n    return styleInfo;\n  }\n\n  /**\n   * paragraph level style\n   *\n   * @param {WrappedRange} rng\n   * @param {Object} styleInfo\n   */\n  stylePara(rng, styleInfo) {\n    $.each(rng.nodes(dom.isPara, {\n      includeAncestor: true,\n    }), (idx, para) => {\n      $(para).css(styleInfo);\n    });\n  }\n\n  /**\n   * insert and returns styleNodes on range.\n   *\n   * @param {WrappedRange} rng\n   * @param {Object} [options] - options for styleNodes\n   * @param {String} [options.nodeName] - default: `SPAN`\n   * @param {Boolean} [options.expandClosestSibling] - default: `false`\n   * @param {Boolean} [options.onlyPartialContains] - default: `false`\n   * @return {Node[]}\n   */\n  styleNodes(rng, options) {\n    rng = rng.splitText();\n\n    const nodeName = (options && options.nodeName) || 'SPAN';\n    const expandClosestSibling = !!(options && options.expandClosestSibling);\n    const onlyPartialContains = !!(options && options.onlyPartialContains);\n\n    if (rng.isCollapsed()) {\n      return [rng.insertNode(dom.create(nodeName))];\n    }\n\n    let pred = dom.makePredByNodeName(nodeName);\n    const nodes = rng.nodes(dom.isText, {\n      fullyContains: true,\n    }).map((text) => {\n      return dom.singleChildAncestor(text, pred) || dom.wrap(text, nodeName);\n    });\n\n    if (expandClosestSibling) {\n      if (onlyPartialContains) {\n        const nodesInRange = rng.nodes();\n        // compose with partial contains predication\n        pred = func.and(pred, (node) => {\n          return lists.contains(nodesInRange, node);\n        });\n      }\n\n      return nodes.map((node) => {\n        const siblings = dom.withClosestSiblings(node, pred);\n        const head = lists.head(siblings);\n        const tails = lists.tail(siblings);\n        $.each(tails, (idx, elem) => {\n          dom.appendChildNodes(head, elem.childNodes);\n          dom.remove(elem);\n        });\n        return lists.head(siblings);\n      });\n    } else {\n      return nodes;\n    }\n  }\n\n  /**\n   * get current style on cursor\n   *\n   * @param {WrappedRange} rng\n   * @return {Object} - object contains style properties.\n   */\n  current(rng) {\n    const $cont = $(!dom.isElement(rng.sc) ? rng.sc.parentNode : rng.sc);\n    let styleInfo = this.fromNode($cont);\n\n    // document.queryCommandState for toggle state\n    // [workaround] prevent Firefox nsresult: \"0x80004005 (NS_ERROR_FAILURE)\"\n    try {\n      styleInfo = $.extend(styleInfo, {\n        'font-bold': document.queryCommandState('bold') ? 'bold' : 'normal',\n        'font-italic': document.queryCommandState('italic') ? 'italic' : 'normal',\n        'font-underline': document.queryCommandState('underline') ? 'underline' : 'normal',\n        'font-subscript': document.queryCommandState('subscript') ? 'subscript' : 'normal',\n        'font-superscript': document.queryCommandState('superscript') ? 'superscript' : 'normal',\n        'font-strikethrough': document.queryCommandState('strikethrough') ? 'strikethrough' : 'normal',\n        'font-family': document.queryCommandValue('fontname') || styleInfo['font-family'],\n      });\n    } catch (e) {}\n\n    // list-style-type to list-style(unordered, ordered)\n    if (!rng.isOnList()) {\n      styleInfo['list-style'] = 'none';\n    } else {\n      const orderedTypes = ['circle', 'disc', 'disc-leading-zero', 'square'];\n      const isUnordered = orderedTypes.indexOf(styleInfo['list-style-type']) > -1;\n      styleInfo['list-style'] = isUnordered ? 'unordered' : 'ordered';\n    }\n\n    const para = dom.ancestor(rng.sc, dom.isPara);\n    if (para && para.style['line-height']) {\n      styleInfo['line-height'] = para.style.lineHeight;\n    } else {\n      const lineHeight = parseInt(styleInfo['line-height'], 10) / parseInt(styleInfo['font-size'], 10);\n      styleInfo['line-height'] = lineHeight.toFixed(1);\n    }\n\n    styleInfo.anchor = rng.isOnAnchor() && dom.ancestor(rng.sc, dom.isAnchor);\n    styleInfo.ancestors = dom.listAncestor(rng.sc, dom.isEditable);\n    styleInfo.range = rng;\n\n    return styleInfo;\n  }\n}\n", "import $ from 'jquery';\nimport lists from '../core/lists';\nimport func from '../core/func';\nimport dom from '../core/dom';\nimport range from '../core/range';\n\nexport default class Bullet {\n  /**\n   * toggle ordered list\n   */\n  insertOrderedList(editable) {\n    this.toggleList('OL', editable);\n  }\n\n  /**\n   * toggle unordered list\n   */\n  insertUnorderedList(editable) {\n    this.toggleList('UL', editable);\n  }\n\n  /**\n   * indent\n   */\n  indent(editable) {\n    const rng = range.create(editable).wrapBodyInlineWithPara();\n\n    const paras = rng.nodes(dom.isPara, { includeAncestor: true });\n    const clustereds = lists.clusterBy(paras, func.peq2('parentNode'));\n\n    $.each(clustereds, (idx, paras) => {\n      const head = lists.head(paras);\n      if (dom.isLi(head)) {\n        const previousList = this.findList(head.previousSibling);\n        if (previousList) {\n          paras\n            .map(para => previousList.appendChild(para));\n        } else {\n          this.wrapList(paras, head.parentNode.nodeName);\n          paras\n            .map((para) => para.parentNode)\n            .map((para) => this.appendToPrevious(para));\n        }\n      } else {\n        $.each(paras, (idx, para) => {\n          $(para).css('marginLeft', (idx, val) => {\n            return (parseInt(val, 10) || 0) + 25;\n          });\n        });\n      }\n    });\n\n    rng.select();\n  }\n\n  /**\n   * outdent\n   */\n  outdent(editable) {\n    const rng = range.create(editable).wrapBodyInlineWithPara();\n\n    const paras = rng.nodes(dom.isPara, { includeAncestor: true });\n    const clustereds = lists.clusterBy(paras, func.peq2('parentNode'));\n\n    $.each(clustereds, (idx, paras) => {\n      const head = lists.head(paras);\n      if (dom.isLi(head)) {\n        this.releaseList([paras]);\n      } else {\n        $.each(paras, (idx, para) => {\n          $(para).css('marginLeft', (idx, val) => {\n            val = (parseInt(val, 10) || 0);\n            return val > 25 ? val - 25 : '';\n          });\n        });\n      }\n    });\n\n    rng.select();\n  }\n\n  /**\n   * toggle list\n   *\n   * @param {String} listName - OL or UL\n   */\n  toggleList(listName, editable) {\n    const rng = range.create(editable).wrapBodyInlineWithPara();\n\n    let paras = rng.nodes(dom.isPara, { includeAncestor: true });\n    const bookmark = rng.paraBookmark(paras);\n    const clustereds = lists.clusterBy(paras, func.peq2('parentNode'));\n\n    // paragraph to list\n    if (lists.find(paras, dom.isPurePara)) {\n      let wrappedParas = [];\n      $.each(clustereds, (idx, paras) => {\n        wrappedParas = wrappedParas.concat(this.wrapList(paras, listName));\n      });\n      paras = wrappedParas;\n    // list to paragraph or change list style\n    } else {\n      const diffLists = rng.nodes(dom.isList, {\n        includeAncestor: true,\n      }).filter((listNode) => {\n        return !$.nodeName(listNode, listName);\n      });\n\n      if (diffLists.length) {\n        $.each(diffLists, (idx, listNode) => {\n          dom.replace(listNode, listName);\n        });\n      } else {\n        paras = this.releaseList(clustereds, true);\n      }\n    }\n\n    range.createFromParaBookmark(bookmark, paras).select();\n  }\n\n  /**\n   * @param {Node[]} paras\n   * @param {String} listName\n   * @return {Node[]}\n   */\n  wrapList(paras, listName) {\n    const head = lists.head(paras);\n    const last = lists.last(paras);\n\n    const prevList = dom.isList(head.previousSibling) && head.previousSibling;\n    const nextList = dom.isList(last.nextSibling) && last.nextSibling;\n\n    const listNode = prevList || dom.insertAfter(dom.create(listName || 'UL'), last);\n\n    // P to LI\n    paras = paras.map((para) => {\n      return dom.isPurePara(para) ? dom.replace(para, 'LI') : para;\n    });\n\n    // append to list(<ul>, <ol>)\n    dom.appendChildNodes(listNode, paras);\n\n    if (nextList) {\n      dom.appendChildNodes(listNode, lists.from(nextList.childNodes));\n      dom.remove(nextList);\n    }\n\n    return paras;\n  }\n\n  /**\n   * @method releaseList\n   *\n   * @param {Array[]} clustereds\n   * @param {Boolean} isEscapseToBody\n   * @return {Node[]}\n   */\n  releaseList(clustereds, isEscapseToBody) {\n    let releasedParas = [];\n\n    $.each(clustereds, (idx, paras) => {\n      const head = lists.head(paras);\n      const last = lists.last(paras);\n\n      const headList = isEscapseToBody ? dom.lastAncestor(head, dom.isList) : head.parentNode;\n      const parentItem = headList.parentNode;\n\n      if (headList.parentNode.nodeName === 'LI') {\n        paras.map(para => {\n          const newList = this.findNextSiblings(para);\n\n          if (parentItem.nextSibling) {\n            parentItem.parentNode.insertBefore(\n              para,\n              parentItem.nextSibling\n            );\n          } else {\n            parentItem.parentNode.appendChild(para);\n          }\n\n          if (newList.length) {\n            this.wrapList(newList, headList.nodeName);\n            para.appendChild(newList[0].parentNode);\n          }\n        });\n\n        if (headList.children.length === 0) {\n          parentItem.removeChild(headList);\n        }\n\n        if (parentItem.childNodes.length === 0) {\n          parentItem.parentNode.removeChild(parentItem);\n        }\n      } else {\n        const lastList = headList.childNodes.length > 1 ? dom.splitTree(headList, {\n          node: last.parentNode,\n          offset: dom.position(last) + 1,\n        }, {\n          isSkipPaddingBlankHTML: true,\n        }) : null;\n\n        const middleList = dom.splitTree(headList, {\n          node: head.parentNode,\n          offset: dom.position(head),\n        }, {\n          isSkipPaddingBlankHTML: true,\n        });\n\n        paras = isEscapseToBody ? dom.listDescendant(middleList, dom.isLi)\n          : lists.from(middleList.childNodes).filter(dom.isLi);\n\n        // LI to P\n        if (isEscapseToBody || !dom.isList(headList.parentNode)) {\n          paras = paras.map((para) => {\n            return dom.replace(para, 'P');\n          });\n        }\n\n        $.each(lists.from(paras).reverse(), (idx, para) => {\n          dom.insertAfter(para, headList);\n        });\n\n        // remove empty lists\n        const rootLists = lists.compact([headList, middleList, lastList]);\n        $.each(rootLists, (idx, rootList) => {\n          const listNodes = [rootList].concat(dom.listDescendant(rootList, dom.isList));\n          $.each(listNodes.reverse(), (idx, listNode) => {\n            if (!dom.nodeLength(listNode)) {\n              dom.remove(listNode, true);\n            }\n          });\n        });\n      }\n\n      releasedParas = releasedParas.concat(paras);\n    });\n\n    return releasedParas;\n  }\n\n  /**\n   * @method appendToPrevious\n   *\n   * Appends list to previous list item, if\n   * none exist it wraps the list in a new list item.\n   *\n   * @param {HTMLNode} ListItem\n   * @return {HTMLNode}\n   */\n  appendToPrevious(node) {\n    return node.previousSibling\n      ? dom.appendChildNodes(node.previousSibling, [node])\n      : this.wrapList([node], 'LI');\n  }\n\n  /**\n   * @method findList\n   *\n   * Finds an existing list in list item\n   *\n   * @param {HTMLNode} ListItem\n   * @return {Array[]}\n   */\n  findList(node) {\n    return node\n      ? lists.find(node.children, child => ['OL', 'UL'].indexOf(child.nodeName) > -1)\n      : null;\n  }\n\n  /**\n   * @method findNextSiblings\n   *\n   * Finds all list item siblings that follow it\n   *\n   * @param {HTMLNode} ListItem\n   * @return {HTMLNode}\n   */\n  findNextSiblings(node) {\n    const siblings = [];\n    while (node.nextSibling) {\n      siblings.push(node.nextSibling);\n      node = node.nextSibling;\n    }\n    return siblings;\n  }\n}\n", "import $ from 'jquery';\nimport dom from '../core/dom';\nimport range from '../core/range';\nimport Bullet from '../editing/Bullet';\n\n/**\n * @class editing.Typing\n *\n * Typing\n *\n */\nexport default class Typing {\n  constructor(context) {\n    // a Bullet instance to toggle lists off\n    this.bullet = new Bullet();\n    this.options = context.options;\n  }\n\n  /**\n   * insert tab\n   *\n   * @param {WrappedRange} rng\n   * @param {Number} tabsize\n   */\n  insertTab(rng, tabsize) {\n    const tab = dom.createText(new Array(tabsize + 1).join(dom.NBSP_CHAR));\n    rng = rng.deleteContents();\n    rng.insertNode(tab, true);\n\n    rng = range.create(tab, tabsize);\n    rng.select();\n  }\n\n  /**\n   * insert paragraph\n   *\n   * @param {jQuery} $editable\n   * @param {WrappedRange} rng Can be used in unit tests to \"mock\" the range\n   *\n   * blockquoteBreakingLevel\n   *   0 - No break, the new paragraph remains inside the quote\n   *   1 - Break the first blockquote in the ancestors list\n   *   2 - Break all blockquotes, so that the new paragraph is not quoted (this is the default)\n   */\n  insertParagraph(editable, rng) {\n    rng = rng || range.create(editable);\n\n    // deleteContents on range.\n    rng = rng.deleteContents();\n\n    // Wrap range if it needs to be wrapped by paragraph\n    rng = rng.wrapBodyInlineWithPara();\n\n    // finding paragraph\n    const splitRoot = dom.ancestor(rng.sc, dom.isPara);\n\n    let nextPara;\n    // on paragraph: split paragraph\n    if (splitRoot) {\n      // if it is an empty line with li\n      if (dom.isLi(splitRoot) && (dom.isEmpty(splitRoot) || dom.deepestChildIsEmpty(splitRoot))) {\n        // toogle UL/OL and escape\n        this.bullet.toggleList(splitRoot.parentNode.nodeName);\n        return;\n      } else {\n        let blockquote = null;\n        if (this.options.blockquoteBreakingLevel === 1) {\n          blockquote = dom.ancestor(splitRoot, dom.isBlockquote);\n        } else if (this.options.blockquoteBreakingLevel === 2) {\n          blockquote = dom.lastAncestor(splitRoot, dom.isBlockquote);\n        }\n\n        if (blockquote) {\n          // We're inside a blockquote and options ask us to break it\n          nextPara = $(dom.emptyPara)[0];\n          // If the split is right before a <br>, remove it so that there's no \"empty line\"\n          // after the split in the new blockquote created\n          if (dom.isRightEdgePoint(rng.getStartPoint()) && dom.isBR(rng.sc.nextSibling)) {\n            $(rng.sc.nextSibling).remove();\n          }\n          const split = dom.splitTree(blockquote, rng.getStartPoint(), { isDiscardEmptySplits: true });\n          if (split) {\n            split.parentNode.insertBefore(nextPara, split);\n          } else {\n            dom.insertAfter(nextPara, blockquote); // There's no split if we were at the end of the blockquote\n          }\n        } else {\n          nextPara = dom.splitTree(splitRoot, rng.getStartPoint());\n\n          // not a blockquote, just insert the paragraph\n          let emptyAnchors = dom.listDescendant(splitRoot, dom.isEmptyAnchor);\n          emptyAnchors = emptyAnchors.concat(dom.listDescendant(nextPara, dom.isEmptyAnchor));\n\n          $.each(emptyAnchors, (idx, anchor) => {\n            dom.remove(anchor);\n          });\n\n          // replace empty heading, pre or custom-made styleTag with P tag\n          if ((dom.isHeading(nextPara) || dom.isPre(nextPara) || dom.isCustomStyleTag(nextPara)) && dom.isEmpty(nextPara)) {\n            nextPara = dom.replace(nextPara, 'p');\n          }\n        }\n      }\n    // no paragraph: insert empty paragraph\n    } else {\n      const next = rng.sc.childNodes[rng.so];\n      nextPara = $(dom.emptyPara)[0];\n      if (next) {\n        rng.sc.insertBefore(nextPara, next);\n      } else {\n        rng.sc.appendChild(nextPara);\n      }\n    }\n\n    range.create(nextPara, 0).normalize().select().scrollIntoView(editable);\n  }\n}\n", "import $ from 'jquery';\nimport dom from '../core/dom';\nimport range from '../core/range';\nimport lists from '../core/lists';\n\n/**\n * @class Create a virtual table to create what actions to do in change.\n * @param {object} startPoint Cell selected to apply change.\n * @param {enum} where  Where change will be applied Row or Col. Use enum: TableResultAction.where\n * @param {enum} action Action to be applied. Use enum: TableResultAction.requestAction\n * @param {object} domTable Dom element of table to make changes.\n */\nconst TableResultAction = function(startPoint, where, action, domTable) {\n  const _startPoint = { 'colPos': 0, 'rowPos': 0 };\n  const _virtualTable = [];\n  const _actionCellList = [];\n\n  /// ///////////////////////////////////////////\n  // Private functions\n  /// ///////////////////////////////////////////\n\n  /**\n   * Set the startPoint of action.\n   */\n  function setStartPoint() {\n    if (!startPoint || !startPoint.tagName || (startPoint.tagName.toLowerCase() !== 'td' && startPoint.tagName.toLowerCase() !== 'th')) {\n      console.error('Impossible to identify start Cell point.', startPoint);\n      return;\n    }\n    _startPoint.colPos = startPoint.cellIndex;\n    if (!startPoint.parentElement || !startPoint.parentElement.tagName || startPoint.parentElement.tagName.toLowerCase() !== 'tr') {\n      console.error('Impossible to identify start Row point.', startPoint);\n      return;\n    }\n    _startPoint.rowPos = startPoint.parentElement.rowIndex;\n  }\n\n  /**\n   * Define virtual table position info object.\n   *\n   * @param {int} rowIndex Index position in line of virtual table.\n   * @param {int} cellIndex Index position in column of virtual table.\n   * @param {object} baseRow Row affected by this position.\n   * @param {object} baseCell Cell affected by this position.\n   * @param {bool} isSpan Inform if it is an span cell/row.\n   */\n  function setVirtualTablePosition(rowIndex, cellIndex, baseRow, baseCell, isRowSpan, isColSpan, isVirtualCell) {\n    const objPosition = {\n      'baseRow': baseRow,\n      'baseCell': baseCell,\n      'isRowSpan': isRowSpan,\n      'isColSpan': isColSpan,\n      'isVirtual': isVirtualCell,\n    };\n    if (!_virtualTable[rowIndex]) {\n      _virtualTable[rowIndex] = [];\n    }\n    _virtualTable[rowIndex][cellIndex] = objPosition;\n  }\n\n  /**\n   * Create action cell object.\n   *\n   * @param {object} virtualTableCellObj Object of specific position on virtual table.\n   * @param {enum} resultAction Action to be applied in that item.\n   */\n  function getActionCell(virtualTableCellObj, resultAction, virtualRowPosition, virtualColPosition) {\n    return {\n      'baseCell': virtualTableCellObj.baseCell,\n      'action': resultAction,\n      'virtualTable': {\n        'rowIndex': virtualRowPosition,\n        'cellIndex': virtualColPosition,\n      },\n    };\n  }\n\n  /**\n   * Recover free index of row to append Cell.\n   *\n   * @param {int} rowIndex Index of row to find free space.\n   * @param {int} cellIndex Index of cell to find free space in table.\n   */\n  function recoverCellIndex(rowIndex, cellIndex) {\n    if (!_virtualTable[rowIndex]) {\n      return cellIndex;\n    }\n    if (!_virtualTable[rowIndex][cellIndex]) {\n      return cellIndex;\n    }\n\n    let newCellIndex = cellIndex;\n    while (_virtualTable[rowIndex][newCellIndex]) {\n      newCellIndex++;\n      if (!_virtualTable[rowIndex][newCellIndex]) {\n        return newCellIndex;\n      }\n    }\n  }\n\n  /**\n   * Recover info about row and cell and add information to virtual table.\n   *\n   * @param {object} row Row to recover information.\n   * @param {object} cell Cell to recover information.\n   */\n  function addCellInfoToVirtual(row, cell) {\n    const cellIndex = recoverCellIndex(row.rowIndex, cell.cellIndex);\n    const cellHasColspan = (cell.colSpan > 1);\n    const cellHasRowspan = (cell.rowSpan > 1);\n    const isThisSelectedCell = (row.rowIndex === _startPoint.rowPos && cell.cellIndex === _startPoint.colPos);\n    setVirtualTablePosition(row.rowIndex, cellIndex, row, cell, cellHasRowspan, cellHasColspan, false);\n\n    // Add span rows to virtual Table.\n    const rowspanNumber = cell.attributes.rowSpan ? parseInt(cell.attributes.rowSpan.value, 10) : 0;\n    if (rowspanNumber > 1) {\n      for (let rp = 1; rp < rowspanNumber; rp++) {\n        const rowspanIndex = row.rowIndex + rp;\n        adjustStartPoint(rowspanIndex, cellIndex, cell, isThisSelectedCell);\n        setVirtualTablePosition(rowspanIndex, cellIndex, row, cell, true, cellHasColspan, true);\n      }\n    }\n\n    // Add span cols to virtual table.\n    const colspanNumber = cell.attributes.colSpan ? parseInt(cell.attributes.colSpan.value, 10) : 0;\n    if (colspanNumber > 1) {\n      for (let cp = 1; cp < colspanNumber; cp++) {\n        const cellspanIndex = recoverCellIndex(row.rowIndex, (cellIndex + cp));\n        adjustStartPoint(row.rowIndex, cellspanIndex, cell, isThisSelectedCell);\n        setVirtualTablePosition(row.rowIndex, cellspanIndex, row, cell, cellHasRowspan, true, true);\n      }\n    }\n  }\n\n  /**\n   * Process validation and adjust of start point if needed\n   *\n   * @param {int} rowIndex\n   * @param {int} cellIndex\n   * @param {object} cell\n   * @param {bool} isSelectedCell\n   */\n  function adjustStartPoint(rowIndex, cellIndex, cell, isSelectedCell) {\n    if (rowIndex === _startPoint.rowPos && _startPoint.colPos >= cell.cellIndex && cell.cellIndex <= cellIndex && !isSelectedCell) {\n      _startPoint.colPos++;\n    }\n  }\n\n  /**\n   * Create virtual table of cells with all cells, including span cells.\n   */\n  function createVirtualTable() {\n    const rows = domTable.rows;\n    for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {\n      const cells = rows[rowIndex].cells;\n      for (let cellIndex = 0; cellIndex < cells.length; cellIndex++) {\n        addCellInfoToVirtual(rows[rowIndex], cells[cellIndex]);\n      }\n    }\n  }\n\n  /**\n   * Get action to be applied on the cell.\n   *\n   * @param {object} cell virtual table cell to apply action\n   */\n  function getDeleteResultActionToCell(cell) {\n    switch (where) {\n      case TableResultAction.where.Column:\n        if (cell.isColSpan) {\n          return TableResultAction.resultAction.SubtractSpanCount;\n        }\n        break;\n      case TableResultAction.where.Row:\n        if (!cell.isVirtual && cell.isRowSpan) {\n          return TableResultAction.resultAction.AddCell;\n        } else if (cell.isRowSpan) {\n          return TableResultAction.resultAction.SubtractSpanCount;\n        }\n        break;\n    }\n    return TableResultAction.resultAction.RemoveCell;\n  }\n\n  /**\n   * Get action to be applied on the cell.\n   *\n   * @param {object} cell virtual table cell to apply action\n   */\n  function getAddResultActionToCell(cell) {\n    switch (where) {\n      case TableResultAction.where.Column:\n        if (cell.isColSpan) {\n          return TableResultAction.resultAction.SumSpanCount;\n        } else if (cell.isRowSpan && cell.isVirtual) {\n          return TableResultAction.resultAction.Ignore;\n        }\n        break;\n      case TableResultAction.where.Row:\n        if (cell.isRowSpan) {\n          return TableResultAction.resultAction.SumSpanCount;\n        } else if (cell.isColSpan && cell.isVirtual) {\n          return TableResultAction.resultAction.Ignore;\n        }\n        break;\n    }\n    return TableResultAction.resultAction.AddCell;\n  }\n\n  function init() {\n    setStartPoint();\n    createVirtualTable();\n  }\n\n  /// ///////////////////////////////////////////\n  // Public functions\n  /// ///////////////////////////////////////////\n\n  /**\n   * Recover array os what to do in table.\n   */\n  this.getActionList = function() {\n    const fixedRow = (where === TableResultAction.where.Row) ? _startPoint.rowPos : -1;\n    const fixedCol = (where === TableResultAction.where.Column) ? _startPoint.colPos : -1;\n\n    let actualPosition = 0;\n    let canContinue = true;\n    while (canContinue) {\n      const rowPosition = (fixedRow >= 0) ? fixedRow : actualPosition;\n      const colPosition = (fixedCol >= 0) ? fixedCol : actualPosition;\n      const row = _virtualTable[rowPosition];\n      if (!row) {\n        canContinue = false;\n        return _actionCellList;\n      }\n      const cell = row[colPosition];\n      if (!cell) {\n        canContinue = false;\n        return _actionCellList;\n      }\n\n      // Define action to be applied in this cell\n      let resultAction = TableResultAction.resultAction.Ignore;\n      switch (action) {\n        case TableResultAction.requestAction.Add:\n          resultAction = getAddResultActionToCell(cell);\n          break;\n        case TableResultAction.requestAction.Delete:\n          resultAction = getDeleteResultActionToCell(cell);\n          break;\n      }\n      _actionCellList.push(getActionCell(cell, resultAction, rowPosition, colPosition));\n      actualPosition++;\n    }\n\n    return _actionCellList;\n  };\n\n  init();\n};\n/**\n*\n* Where action occours enum.\n*/\nTableResultAction.where = { 'Row': 0, 'Column': 1 };\n/**\n*\n* Requested action to apply enum.\n*/\nTableResultAction.requestAction = { 'Add': 0, 'Delete': 1 };\n/**\n*\n* Result action to be executed enum.\n*/\nTableResultAction.resultAction = { 'Ignore': 0, 'SubtractSpanCount': 1, 'RemoveCell': 2, 'AddCell': 3, 'SumSpanCount': 4 };\n\n/**\n *\n * @class editing.Table\n *\n * Table\n *\n */\nexport default class Table {\n  /**\n   * handle tab key\n   *\n   * @param {WrappedRange} rng\n   * @param {Boolean} isShift\n   */\n  tab(rng, isShift) {\n    const cell = dom.ancestor(rng.commonAncestor(), dom.isCell);\n    const table = dom.ancestor(cell, dom.isTable);\n    const cells = dom.listDescendant(table, dom.isCell);\n\n    const nextCell = lists[isShift ? 'prev' : 'next'](cells, cell);\n    if (nextCell) {\n      range.create(nextCell, 0).select();\n    }\n  }\n\n  /**\n   * Add a new row\n   *\n   * @param {WrappedRange} rng\n   * @param {String} position (top/bottom)\n   * @return {Node}\n   */\n  addRow(rng, position) {\n    const cell = dom.ancestor(rng.commonAncestor(), dom.isCell);\n\n    const currentTr = $(cell).closest('tr');\n    const trAttributes = this.recoverAttributes(currentTr);\n    const html = $('<tr' + trAttributes + '></tr>');\n\n    const vTable = new TableResultAction(cell, TableResultAction.where.Row,\n      TableResultAction.requestAction.Add, $(currentTr).closest('table')[0]);\n    const actions = vTable.getActionList();\n\n    for (let idCell = 0; idCell < actions.length; idCell++) {\n      const currentCell = actions[idCell];\n      const tdAttributes = this.recoverAttributes(currentCell.baseCell);\n      switch (currentCell.action) {\n        case TableResultAction.resultAction.AddCell:\n          html.append('<td' + tdAttributes + '>' + dom.blank + '</td>');\n          break;\n        case TableResultAction.resultAction.SumSpanCount:\n          if (position === 'top') {\n            const baseCellTr = currentCell.baseCell.parent;\n            const isTopFromRowSpan = (!baseCellTr ? 0 : currentCell.baseCell.closest('tr').rowIndex) <= currentTr[0].rowIndex;\n            if (isTopFromRowSpan) {\n              const newTd = $('<div></div>').append($('<td' + tdAttributes + '>' + dom.blank + '</td>').removeAttr('rowspan')).html();\n              html.append(newTd);\n              break;\n            }\n          }\n          let rowspanNumber = parseInt(currentCell.baseCell.rowSpan, 10);\n          rowspanNumber++;\n          currentCell.baseCell.setAttribute('rowSpan', rowspanNumber);\n          break;\n      }\n    }\n\n    if (position === 'top') {\n      currentTr.before(html);\n    } else {\n      const cellHasRowspan = (cell.rowSpan > 1);\n      if (cellHasRowspan) {\n        const lastTrIndex = currentTr[0].rowIndex + (cell.rowSpan - 2);\n        $($(currentTr).parent().find('tr')[lastTrIndex]).after($(html));\n        return;\n      }\n      currentTr.after(html);\n    }\n  }\n\n  /**\n   * Add a new col\n   *\n   * @param {WrappedRange} rng\n   * @param {String} position (left/right)\n   * @return {Node}\n   */\n  addCol(rng, position) {\n    const cell = dom.ancestor(rng.commonAncestor(), dom.isCell);\n    const row = $(cell).closest('tr');\n    const rowsGroup = $(row).siblings();\n    rowsGroup.push(row);\n\n    const vTable = new TableResultAction(cell, TableResultAction.where.Column,\n      TableResultAction.requestAction.Add, $(row).closest('table')[0]);\n    const actions = vTable.getActionList();\n\n    for (let actionIndex = 0; actionIndex < actions.length; actionIndex++) {\n      const currentCell = actions[actionIndex];\n      const tdAttributes = this.recoverAttributes(currentCell.baseCell);\n      switch (currentCell.action) {\n        case TableResultAction.resultAction.AddCell:\n          if (position === 'right') {\n            $(currentCell.baseCell).after('<td' + tdAttributes + '>' + dom.blank + '</td>');\n          } else {\n            $(currentCell.baseCell).before('<td' + tdAttributes + '>' + dom.blank + '</td>');\n          }\n          break;\n        case TableResultAction.resultAction.SumSpanCount:\n          if (position === 'right') {\n            let colspanNumber = parseInt(currentCell.baseCell.colSpan, 10);\n            colspanNumber++;\n            currentCell.baseCell.setAttribute('colSpan', colspanNumber);\n          } else {\n            $(currentCell.baseCell).before('<td' + tdAttributes + '>' + dom.blank + '</td>');\n          }\n          break;\n      }\n    }\n  }\n\n  /*\n  * Copy attributes from element.\n  *\n  * @param {object} Element to recover attributes.\n  * @return {string} Copied string elements.\n  */\n  recoverAttributes(el) {\n    let resultStr = '';\n\n    if (!el) {\n      return resultStr;\n    }\n\n    const attrList = el.attributes || [];\n\n    for (let i = 0; i < attrList.length; i++) {\n      if (attrList[i].name.toLowerCase() === 'id') {\n        continue;\n      }\n\n      if (attrList[i].specified) {\n        resultStr += ' ' + attrList[i].name + '=\\'' + attrList[i].value + '\\'';\n      }\n    }\n\n    return resultStr;\n  }\n\n  /**\n   * Delete current row\n   *\n   * @param {WrappedRange} rng\n   * @return {Node}\n   */\n  deleteRow(rng) {\n    const cell = dom.ancestor(rng.commonAncestor(), dom.isCell);\n    const row = $(cell).closest('tr');\n    const cellPos = row.children('td, th').index($(cell));\n    const rowPos = row[0].rowIndex;\n\n    const vTable = new TableResultAction(cell, TableResultAction.where.Row,\n      TableResultAction.requestAction.Delete, $(row).closest('table')[0]);\n    const actions = vTable.getActionList();\n\n    for (let actionIndex = 0; actionIndex < actions.length; actionIndex++) {\n      if (!actions[actionIndex]) {\n        continue;\n      }\n\n      const baseCell = actions[actionIndex].baseCell;\n      const virtualPosition = actions[actionIndex].virtualTable;\n      const hasRowspan = (baseCell.rowSpan && baseCell.rowSpan > 1);\n      let rowspanNumber = (hasRowspan) ? parseInt(baseCell.rowSpan, 10) : 0;\n      switch (actions[actionIndex].action) {\n        case TableResultAction.resultAction.Ignore:\n          continue;\n        case TableResultAction.resultAction.AddCell:\n          const nextRow = row.next('tr')[0];\n          if (!nextRow) { continue; }\n          const cloneRow = row[0].cells[cellPos];\n          if (hasRowspan) {\n            if (rowspanNumber > 2) {\n              rowspanNumber--;\n              nextRow.insertBefore(cloneRow, nextRow.cells[cellPos]);\n              nextRow.cells[cellPos].setAttribute('rowSpan', rowspanNumber);\n              nextRow.cells[cellPos].innerHTML = '';\n            } else if (rowspanNumber === 2) {\n              nextRow.insertBefore(cloneRow, nextRow.cells[cellPos]);\n              nextRow.cells[cellPos].removeAttribute('rowSpan');\n              nextRow.cells[cellPos].innerHTML = '';\n            }\n          }\n          continue;\n        case TableResultAction.resultAction.SubtractSpanCount:\n          if (hasRowspan) {\n            if (rowspanNumber > 2) {\n              rowspanNumber--;\n              baseCell.setAttribute('rowSpan', rowspanNumber);\n              if (virtualPosition.rowIndex !== rowPos && baseCell.cellIndex === cellPos) { baseCell.innerHTML = ''; }\n            } else if (rowspanNumber === 2) {\n              baseCell.removeAttribute('rowSpan');\n              if (virtualPosition.rowIndex !== rowPos && baseCell.cellIndex === cellPos) { baseCell.innerHTML = ''; }\n            }\n          }\n          continue;\n        case TableResultAction.resultAction.RemoveCell:\n          // Do not need remove cell because row will be deleted.\n          continue;\n      }\n    }\n    row.remove();\n  }\n\n  /**\n   * Delete current col\n   *\n   * @param {WrappedRange} rng\n   * @return {Node}\n   */\n  deleteCol(rng) {\n    const cell = dom.ancestor(rng.commonAncestor(), dom.isCell);\n    const row = $(cell).closest('tr');\n    const cellPos = row.children('td, th').index($(cell));\n\n    const vTable = new TableResultAction(cell, TableResultAction.where.Column,\n      TableResultAction.requestAction.Delete, $(row).closest('table')[0]);\n    const actions = vTable.getActionList();\n\n    for (let actionIndex = 0; actionIndex < actions.length; actionIndex++) {\n      if (!actions[actionIndex]) {\n        continue;\n      }\n      switch (actions[actionIndex].action) {\n        case TableResultAction.resultAction.Ignore:\n          continue;\n        case TableResultAction.resultAction.SubtractSpanCount:\n          const baseCell = actions[actionIndex].baseCell;\n          const hasColspan = (baseCell.colSpan && baseCell.colSpan > 1);\n          if (hasColspan) {\n            let colspanNumber = (baseCell.colSpan) ? parseInt(baseCell.colSpan, 10) : 0;\n            if (colspanNumber > 2) {\n              colspanNumber--;\n              baseCell.setAttribute('colSpan', colspanNumber);\n              if (baseCell.cellIndex === cellPos) { baseCell.innerHTML = ''; }\n            } else if (colspanNumber === 2) {\n              baseCell.removeAttribute('colSpan');\n              if (baseCell.cellIndex === cellPos) { baseCell.innerHTML = ''; }\n            }\n          }\n          continue;\n        case TableResultAction.resultAction.RemoveCell:\n          dom.remove(actions[actionIndex].baseCell, true);\n          continue;\n      }\n    }\n  }\n\n  /**\n   * create empty table element\n   *\n   * @param {Number} rowCount\n   * @param {Number} colCount\n   * @return {Node}\n   */\n  createTable(colCount, rowCount, options) {\n    const tds = [];\n    let tdHTML;\n    for (let idxCol = 0; idxCol < colCount; idxCol++) {\n      tds.push('<td>' + dom.blank + '</td>');\n    }\n    tdHTML = tds.join('');\n\n    const trs = [];\n    let trHTML;\n    for (let idxRow = 0; idxRow < rowCount; idxRow++) {\n      trs.push('<tr>' + tdHTML + '</tr>');\n    }\n    trHTML = trs.join('');\n    const $table = $('<table>' + trHTML + '</table>');\n    if (options && options.tableClassName) {\n      $table.addClass(options.tableClassName);\n    }\n\n    return $table[0];\n  }\n\n  /**\n   * Delete current table\n   *\n   * @param {WrappedRange} rng\n   * @return {Node}\n   */\n  deleteTable(rng) {\n    const cell = dom.ancestor(rng.commonAncestor(), dom.isCell);\n    $(cell).closest('table').remove();\n  }\n}\n", "import env from '../core/env';\nimport dom from '../core/dom';\n\nlet CodeMirror;\nif (env.hasCodeMirror) {\n  CodeMirror = window.CodeMirror;\n}\n\n/**\n * @class Codeview\n */\nexport default class CodeView {\n  constructor(context) {\n    this.context = context;\n    this.$editor = context.layoutInfo.editor;\n    this.$editable = context.layoutInfo.editable;\n    this.$codable = context.layoutInfo.codable;\n    this.options = context.options;\n  }\n\n  sync() {\n    const isCodeview = this.isActivated();\n    if (isCodeview && env.hasCodeMirror) {\n      this.$codable.data('cmEditor').save();\n    }\n  }\n\n  /**\n   * @return {Boolean}\n   */\n  isActivated() {\n    return this.$editor.hasClass('codeview');\n  }\n\n  /**\n   * toggle codeview\n   */\n  toggle() {\n    if (this.isActivated()) {\n      this.deactivate();\n    } else {\n      this.activate();\n    }\n    this.context.triggerEvent('codeview.toggled');\n  }\n\n  /**\n   * purify input value\n   * @param value\n   * @returns {*}\n   */\n  purify(value) {\n    if (this.options.codeviewFilter) {\n      // filter code view regex\n      value = value.replace(this.options.codeviewFilterRegex, '');\n      // allow specific iframe tag\n      if (this.options.codeviewIframeFilter) {\n        const whitelist = this.options.codeviewIframeWhitelistSrc.concat(this.options.codeviewIframeWhitelistSrcBase);\n        value = value.replace(/(<iframe.*?>.*?(?:<\\/iframe>)?)/gi, function(tag) {\n          // remove if src attribute is duplicated\n          if (/<.+src(?==?('|\"|\\s)?)[\\s\\S]+src(?=('|\"|\\s)?)[^>]*?>/i.test(tag)) {\n            return '';\n          }\n          for (const src of whitelist) {\n            // pass if src is trusted\n            if ((new RegExp('src=\"(https?:)?\\/\\/' + src.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&') + '\\/(.+)\"')).test(tag)) {\n              return tag;\n            }\n          }\n          return '';\n        });\n      }\n    }\n    return value;\n  }\n\n  /**\n   * activate code view\n   */\n  activate() {\n    this.$codable.val(dom.html(this.$editable, this.options.prettifyHtml));\n    this.$codable.height(this.$editable.height());\n\n    this.context.invoke('toolbar.updateCodeview', true);\n    this.$editor.addClass('codeview');\n    this.$codable.focus();\n\n    // activate CodeMirror as codable\n    if (env.hasCodeMirror) {\n      const cmEditor = CodeMirror.fromTextArea(this.$codable[0], this.options.codemirror);\n\n      // CodeMirror TernServer\n      if (this.options.codemirror.tern) {\n        const server = new CodeMirror.TernServer(this.options.codemirror.tern);\n        cmEditor.ternServer = server;\n        cmEditor.on('cursorActivity', (cm) => {\n          server.updateArgHints(cm);\n        });\n      }\n\n      cmEditor.on('blur', (event) => {\n        this.context.triggerEvent('blur.codeview', cmEditor.getValue(), event);\n      });\n      cmEditor.on('change', (event) => {\n        this.context.triggerEvent('change.codeview', cmEditor.getValue(), cmEditor);\n      });\n\n      // CodeMirror hasn't Padding.\n      cmEditor.setSize(null, this.$editable.outerHeight());\n      this.$codable.data('cmEditor', cmEditor);\n    } else {\n      this.$codable.on('blur', (event) => {\n        this.context.triggerEvent('blur.codeview', this.$codable.val(), event);\n      });\n      this.$codable.on('input', (event) => {\n        this.context.triggerEvent('change.codeview', this.$codable.val(), this.$codable);\n      });\n    }\n  }\n\n  /**\n   * deactivate code view\n   */\n  deactivate() {\n    // deactivate CodeMirror as codable\n    if (env.hasCodeMirror) {\n      const cmEditor = this.$codable.data('cmEditor');\n      this.$codable.val(cmEditor.getValue());\n      cmEditor.toTextArea();\n    }\n\n    const value = this.purify(dom.value(this.$codable, this.options.prettifyHtml) || dom.emptyPara);\n    const isChange = this.$editable.html() !== value;\n\n    this.$editable.html(value);\n    this.$editable.height(this.options.height ? this.$codable.height() : 'auto');\n    this.$editor.removeClass('codeview');\n\n    if (isChange) {\n      this.context.triggerEvent('change', this.$editable.html(), this.$editable);\n    }\n\n    this.$editable.focus();\n\n    this.context.invoke('toolbar.updateCodeview', false);\n  }\n\n  destroy() {\n    if (this.isActivated()) {\n      this.deactivate();\n    }\n  }\n}\n", "import $ from 'jquery';\nimport lists from '../core/lists';\nimport key from '../core/key';\n\nconst defaultScheme = 'http://';\nconst linkPattern = /^([A-Za-z][A-Za-z0-9+-.]*\\:[\\/]{2}|tel:|mailto:[A-Z0-9._%+-]+@)?(www\\.)?(.+)$/i;\n\nexport default class AutoLink {\n  constructor(context) {\n    this.context = context;\n    this.events = {\n      'summernote.keyup': (we, e) => {\n        if (!e.isDefaultPrevented()) {\n          this.handleKeyup(e);\n        }\n      },\n      'summernote.keydown': (we, e) => {\n        this.handleKeydown(e);\n      },\n    };\n  }\n\n  initialize() {\n    this.lastWordRange = null;\n  }\n\n  destroy() {\n    this.lastWordRange = null;\n  }\n\n  replace() {\n    if (!this.lastWordRange) {\n      return;\n    }\n\n    const keyword = this.lastWordRange.toString();\n    const match = keyword.match(linkPattern);\n\n    if (match && (match[1] || match[2])) {\n      const link = match[1] ? keyword : defaultScheme + keyword;\n      const urlText = keyword.replace(/^(?:https?:\\/\\/)?(?:tel?:?)?(?:mailto?:?)?(?:www\\.)?/i, '').split('/')[0];\n      const node = $('<a />').html(urlText).attr('href', link)[0];\n      if (this.context.options.linkTargetBlank) {\n        $(node).attr('target', '_blank');\n      }\n\n      this.lastWordRange.insertNode(node);\n      this.lastWordRange = null;\n      this.context.invoke('editor.focus');\n    }\n  }\n\n  handleKeydown(e) {\n    if (lists.contains([key.code.ENTER, key.code.SPACE], e.keyCode)) {\n      const wordRange = this.context.invoke('editor.createRange').getWordRange();\n      this.lastWordRange = wordRange;\n    }\n  }\n\n  handleKeyup(e) {\n    if (lists.contains([key.code.ENTER, key.code.SPACE], e.keyCode)) {\n      this.replace();\n    }\n  }\n}\n", "import $ from 'jquery';\nimport './summernote-en-US';\nimport '../summernote';\nimport dom from './core/dom';\nimport range from './core/range';\nimport lists from './core/lists';\nimport Editor from './module/Editor';\nimport Clipboard from './module/Clipboard';\nimport Dropzone from './module/Dropzone';\nimport Codeview from './module/Codeview';\nimport Statusbar from './module/Statusbar';\nimport Fullscreen from './module/Fullscreen';\nimport Handle from './module/Handle';\nimport AutoLink from './module/AutoLink';\nimport AutoSync from './module/AutoSync';\nimport AutoReplace from './module/AutoReplace';\nimport Placeholder from './module/Placeholder';\nimport Buttons from './module/Buttons';\nimport Toolbar from './module/Toolbar';\nimport LinkDialog from './module/LinkDialog';\nimport LinkPopover from './module/LinkPopover';\nimport ImageDialog from './module/ImageDialog';\nimport ImagePopover from './module/ImagePopover';\nimport TablePopover from './module/TablePopover';\nimport VideoDialog from './module/VideoDialog';\nimport HelpDialog from './module/HelpDialog';\nimport AirPopover from './module/AirPopover';\nimport HintPopover from './module/HintPopover';\n\n$.summernote = $.extend($.summernote, {\n  version: '@@VERSION@@',\n  plugins: {},\n\n  dom: dom,\n  range: range,\n  lists: lists,\n\n  options: {\n    langInfo: $.summernote.lang['en-US'],\n    editing: true,\n    modules: {\n      'editor': Editor,\n      'clipboard': Clipboard,\n      'dropzone': Dropzone,\n      'codeview': Codeview,\n      'statusbar': Statusbar,\n      'fullscreen': Fullscreen,\n      'handle': Handle,\n      // FIXME: HintPopover must be front of autolink\n      //  - Script error about range when Enter key is pressed on hint popover\n      'hintPopover': HintPopover,\n      'autoLink': AutoLink,\n      'autoSync': AutoSync,\n      'autoReplace': AutoReplace,\n      'placeholder': Placeholder,\n      'buttons': Buttons,\n      'toolbar': Toolbar,\n      'linkDialog': LinkDialog,\n      'linkPopover': LinkPopover,\n      'imageDialog': ImageDialog,\n      'imagePopover': ImagePopover,\n      'tablePopover': TablePopover,\n      'videoDialog': VideoDialog,\n      'helpDialog': HelpDialog,\n      'airPopover': AirPopover,\n    },\n\n    buttons: {},\n\n    lang: 'en-US',\n\n    followingToolbar: false,\n    toolbarPosition: 'top',\n    otherStaticBar: '',\n\n    // toolbar\n    toolbar: [\n      ['style', ['style']],\n      ['font', ['bold', 'underline', 'clear']],\n      ['fontname', ['fontname']],\n      ['color', ['color']],\n      ['para', ['ul', 'ol', 'paragraph']],\n      ['table', ['table']],\n      ['insert', ['link', 'picture', 'video']],\n      ['view', ['fullscreen', 'codeview', 'help']],\n    ],\n\n    // popover\n    popatmouse: true,\n    popover: {\n      image: [\n        ['resize', ['resizeFull', 'resizeHalf', 'resizeQuarter', 'resizeNone']],\n        ['float', ['floatLeft', 'floatRight', 'floatNone']],\n        ['remove', ['removeMedia']],\n      ],\n      link: [\n        ['link', ['linkDialogShow', 'unlink']],\n      ],\n      table: [\n        ['add', ['addRowDown', 'addRowUp', 'addColLeft', 'addColRight']],\n        ['delete', ['deleteRow', 'deleteCol', 'deleteTable']],\n      ],\n      air: [\n        ['color', ['color']],\n        ['font', ['bold', 'underline', 'clear']],\n        ['para', ['ul', 'paragraph']],\n        ['table', ['table']],\n        ['insert', ['link', 'picture']],\n        ['view', ['fullscreen', 'codeview']],\n      ],\n    },\n\n    // air mode: inline editor\n    airMode: false,\n\n    width: null,\n    height: null,\n    linkTargetBlank: true,\n    useProtocol: true,\n    defaultProtocol: 'http://',\n\n    focus: false,\n    tabDisabled: false,\n    tabSize: 4,\n    styleWithSpan: true,\n    shortcuts: true,\n    textareaAutoSync: true,\n    tooltip: 'auto',\n    container: null,\n    maxTextLength: 0,\n    blockquoteBreakingLevel: 2,\n    spellCheck: true,\n    disableGrammar: false,\n    placeholder: null,\n    inheritPlaceholder: false,\n\n    // TODO: need to be documented\n    hintMode: 'word',\n    hintSelect: 'after',\n    hintDirection: 'bottom',\n\n    styleTags: ['p', 'blockquote', 'pre', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],\n\n    fontNames: [\n      'Arial', 'Arial Black', 'Comic Sans MS', 'Courier New',\n      'Helvetica Neue', 'Helvetica', 'Impact', 'Lucida Grande',\n      'Tahoma', 'Times New Roman', 'Verdana',\n    ],\n    fontNamesIgnoreCheck: [],\n    addDefaultFonts: true,\n\n    fontSizes: ['8', '9', '10', '11', '12', '14', '18', '24', '36'],\n\n    fontSizeUnits: ['px', 'pt'],\n\n    // pallete colors(n x n)\n    colors: [\n      ['#000000', '#424242', '#636363', '#9C9C94', '#CEC6CE', '#EFEFEF', '#F7F7F7', '#FFFFFF'],\n      ['#FF0000', '#FF9C00', '#FFFF00', '#00FF00', '#00FFFF', '#0000FF', '#9C00FF', '#FF00FF'],\n      ['#F7C6CE', '#FFE7CE', '#FFEFC6', '#D6EFD6', '#CEDEE7', '#CEE7F7', '#D6D6E7', '#E7D6DE'],\n      ['#E79C9C', '#FFC69C', '#FFE79C', '#B5D6A5', '#A5C6CE', '#9CC6EF', '#B5A5D6', '#D6A5BD'],\n      ['#E76363', '#F7AD6B', '#FFD663', '#94BD7B', '#73A5AD', '#6BADDE', '#8C7BC6', '#C67BA5'],\n      ['#CE0000', '#E79439', '#EFC631', '#6BA54A', '#4A7B8C', '#3984C6', '#634AA5', '#A54A7B'],\n      ['#9C0000', '#B56308', '#BD9400', '#397B21', '#104A5A', '#085294', '#311873', '#731842'],\n      ['#630000', '#7B3900', '#846300', '#295218', '#083139', '#003163', '#21104A', '#4A1031'],\n    ],\n\n    // http://chir.ag/projects/name-that-color/\n    colorsName: [\n      ['Black', 'Tundora', 'Dove Gray', 'Star Dust', 'Pale Slate', 'Gallery', 'Alabaster', 'White'],\n      ['Red', 'Orange Peel', 'Yellow', 'Green', 'Cyan', 'Blue', 'Electric Violet', 'Magenta'],\n      ['Azalea', 'Karry', 'Egg White', 'Zanah', 'Botticelli', 'Tropical Blue', 'Mischka', 'Twilight'],\n      ['Tonys Pink', 'Peach Orange', 'Cream Brulee', 'Sprout', 'Casper', 'Perano', 'Cold Purple', 'Careys Pink'],\n      ['Mandy', 'Rajah', 'Dandelion', 'Olivine', 'Gulf Stream', 'Viking', 'Blue Marguerite', 'Puce'],\n      ['Guardsman Red', 'Fire Bush', 'Golden Dream', 'Chelsea Cucumber', 'Smalt Blue', 'Boston Blue', 'Butterfly Bush', 'Cadillac'],\n      ['Sangria', 'Mai Tai', 'Buddha Gold', 'Forest Green', 'Eden', 'Venice Blue', 'Meteorite', 'Claret'],\n      ['Rosewood', 'Cinnamon', 'Olive', 'Parsley', 'Tiber', 'Midnight Blue', 'Valentino', 'Loulou'],\n    ],\n\n    colorButton: {\n      foreColor: '#000000',\n      backColor: '#FFFF00',\n    },\n\n    lineHeights: ['1.0', '1.2', '1.4', '1.5', '1.6', '1.8', '2.0', '3.0'],\n\n    tableClassName: 'table table-bordered',\n\n    insertTableMaxSize: {\n      col: 10,\n      row: 10,\n    },\n\n    // By default, dialogs are attached in container.\n    dialogsInBody: false,\n    dialogsFade: false,\n\n    maximumImageFileSize: null,\n\n    callbacks: {\n      onBeforeCommand: null,\n      onBlur: null,\n      onBlurCodeview: null,\n      onChange: null,\n      onChangeCodeview: null,\n      onDialogShown: null,\n      onEnter: null,\n      onFocus: null,\n      onImageLinkInsert: null,\n      onImageUpload: null,\n      onImageUploadError: null,\n      onInit: null,\n      onKeydown: null,\n      onKeyup: null,\n      onMousedown: null,\n      onMouseup: null,\n      onPaste: null,\n      onScroll: null,\n    },\n\n    codemirror: {\n      mode: 'text/html',\n      htmlMode: true,\n      lineNumbers: true,\n    },\n\n    codeviewFilter: false,\n    codeviewFilterRegex: /<\\/*(?:applet|b(?:ase|gsound|link)|embed|frame(?:set)?|ilayer|l(?:ayer|ink)|meta|object|s(?:cript|tyle)|t(?:itle|extarea)|xml)[^>]*?>/gi,\n    codeviewIframeFilter: true,\n    codeviewIframeWhitelistSrc: [],\n    codeviewIframeWhitelistSrcBase: [\n      'www.youtube.com',\n      'www.youtube-nocookie.com',\n      'www.facebook.com',\n      'vine.co',\n      'instagram.com',\n      'player.vimeo.com',\n      'www.dailymotion.com',\n      'player.youku.com',\n      'v.qq.com',\n    ],\n\n    keyMap: {\n      pc: {\n        'ENTER': 'insertParagraph',\n        'CTRL+Z': 'undo',\n        'CTRL+Y': 'redo',\n        'TAB': 'tab',\n        'SHIFT+TAB': 'untab',\n        'CTRL+B': 'bold',\n        'CTRL+I': 'italic',\n        'CTRL+U': 'underline',\n        'CTRL+SHIFT+S': 'strikethrough',\n        'CTRL+BACKSLASH': 'removeFormat',\n        'CTRL+SHIFT+L': 'justifyLeft',\n        'CTRL+SHIFT+E': 'justifyCenter',\n        'CTRL+SHIFT+R': 'justifyRight',\n        'CTRL+SHIFT+J': 'justifyFull',\n        'CTRL+SHIFT+NUM7': 'insertUnorderedList',\n        'CTRL+SHIFT+NUM8': 'insertOrderedList',\n        'CTRL+LEFTBRACKET': 'outdent',\n        'CTRL+RIGHTBRACKET': 'indent',\n        'CTRL+NUM0': 'formatPara',\n        'CTRL+NUM1': 'formatH1',\n        'CTRL+NUM2': 'formatH2',\n        'CTRL+NUM3': 'formatH3',\n        'CTRL+NUM4': 'formatH4',\n        'CTRL+NUM5': 'formatH5',\n        'CTRL+NUM6': 'formatH6',\n        'CTRL+ENTER': 'insertHorizontalRule',\n        'CTRL+K': 'linkDialog.show',\n      },\n\n      mac: {\n        'ENTER': 'insertParagraph',\n        'CMD+Z': 'undo',\n        'CMD+SHIFT+Z': 'redo',\n        'TAB': 'tab',\n        'SHIFT+TAB': 'untab',\n        'CMD+B': 'bold',\n        'CMD+I': 'italic',\n        'CMD+U': 'underline',\n        'CMD+SHIFT+S': 'strikethrough',\n        'CMD+BACKSLASH': 'removeFormat',\n        'CMD+SHIFT+L': 'justifyLeft',\n        'CMD+SHIFT+E': 'justifyCenter',\n        'CMD+SHIFT+R': 'justifyRight',\n        'CMD+SHIFT+J': 'justifyFull',\n        'CMD+SHIFT+NUM7': 'insertUnorderedList',\n        'CMD+SHIFT+NUM8': 'insertOrderedList',\n        'CMD+LEFTBRACKET': 'outdent',\n        'CMD+RIGHTBRACKET': 'indent',\n        'CMD+NUM0': 'formatPara',\n        'CMD+NUM1': 'formatH1',\n        'CMD+NUM2': 'formatH2',\n        'CMD+NUM3': 'formatH3',\n        'CMD+NUM4': 'formatH4',\n        'CMD+NUM5': 'formatH5',\n        'CMD+NUM6': 'formatH6',\n        'CMD+ENTER': 'insertHorizontalRule',\n        'CMD+K': 'linkDialog.show',\n      },\n    },\n    icons: {\n      'align': 'note-icon-align',\n      'alignCenter': 'note-icon-align-center',\n      'alignJustify': 'note-icon-align-justify',\n      'alignLeft': 'note-icon-align-left',\n      'alignRight': 'note-icon-align-right',\n      'rowBelow': 'note-icon-row-below',\n      'colBefore': 'note-icon-col-before',\n      'colAfter': 'note-icon-col-after',\n      'rowAbove': 'note-icon-row-above',\n      'rowRemove': 'note-icon-row-remove',\n      'colRemove': 'note-icon-col-remove',\n      'indent': 'note-icon-align-indent',\n      'outdent': 'note-icon-align-outdent',\n      'arrowsAlt': 'note-icon-arrows-alt',\n      'bold': 'note-icon-bold',\n      'caret': 'note-icon-caret',\n      'circle': 'note-icon-circle',\n      'close': 'note-icon-close',\n      'code': 'note-icon-code',\n      'eraser': 'note-icon-eraser',\n      'floatLeft': 'note-icon-float-left',\n      'floatRight': 'note-icon-float-right',\n      'font': 'note-icon-font',\n      'frame': 'note-icon-frame',\n      'italic': 'note-icon-italic',\n      'link': 'note-icon-link',\n      'unlink': 'note-icon-chain-broken',\n      'magic': 'note-icon-magic',\n      'menuCheck': 'note-icon-menu-check',\n      'minus': 'note-icon-minus',\n      'orderedlist': 'note-icon-orderedlist',\n      'pencil': 'note-icon-pencil',\n      'picture': 'note-icon-picture',\n      'question': 'note-icon-question',\n      'redo': 'note-icon-redo',\n      'rollback': 'note-icon-rollback',\n      'square': 'note-icon-square',\n      'strikethrough': 'note-icon-strikethrough',\n      'subscript': 'note-icon-subscript',\n      'superscript': 'note-icon-superscript',\n      'table': 'note-icon-table',\n      'textHeight': 'note-icon-text-height',\n      'trash': 'note-icon-trash',\n      'underline': 'note-icon-underline',\n      'undo': 'note-icon-undo',\n      'unorderedlist': 'note-icon-unorderedlist',\n      'video': 'note-icon-video',\n    },\n  },\n});\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport key from '../core/key';\nimport func from '../core/func';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\nimport range from '../core/range';\nimport { readFileAsDataURL, createImage } from '../core/async';\nimport History from '../editing/History';\nimport Style from '../editing/Style';\nimport Typing from '../editing/Typing';\nimport Table from '../editing/Table';\nimport Bullet from '../editing/Bullet';\n\nconst KEY_BOGUS = 'bogus';\n\n/**\n * @class Editor\n */\nexport default class Editor {\n  constructor(context) {\n    this.context = context;\n\n    this.$note = context.layoutInfo.note;\n    this.$editor = context.layoutInfo.editor;\n    this.$editable = context.layoutInfo.editable;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n\n    this.editable = this.$editable[0];\n    this.lastRange = null;\n    this.snapshot = null;\n\n    this.style = new Style();\n    this.table = new Table();\n    this.typing = new Typing(context);\n    this.bullet = new Bullet();\n    this.history = new History(this.$editable);\n\n    this.context.memo('help.undo', this.lang.help.undo);\n    this.context.memo('help.redo', this.lang.help.redo);\n    this.context.memo('help.tab', this.lang.help.tab);\n    this.context.memo('help.untab', this.lang.help.untab);\n    this.context.memo('help.insertParagraph', this.lang.help.insertParagraph);\n    this.context.memo('help.insertOrderedList', this.lang.help.insertOrderedList);\n    this.context.memo('help.insertUnorderedList', this.lang.help.insertUnorderedList);\n    this.context.memo('help.indent', this.lang.help.indent);\n    this.context.memo('help.outdent', this.lang.help.outdent);\n    this.context.memo('help.formatPara', this.lang.help.formatPara);\n    this.context.memo('help.insertHorizontalRule', this.lang.help.insertHorizontalRule);\n    this.context.memo('help.fontName', this.lang.help.fontName);\n\n    // native commands(with execCommand), generate function for execCommand\n    const commands = [\n      'bold', 'italic', 'underline', 'strikethrough', 'superscript', 'subscript',\n      'justifyLeft', 'justifyCenter', 'justifyRight', 'justifyFull',\n      'formatBlock', 'removeFormat', 'backColor',\n    ];\n\n    for (let idx = 0, len = commands.length; idx < len; idx++) {\n      this[commands[idx]] = ((sCmd) => {\n        return (value) => {\n          this.beforeCommand();\n          document.execCommand(sCmd, false, value);\n          this.afterCommand(true);\n        };\n      })(commands[idx]);\n      this.context.memo('help.' + commands[idx], this.lang.help[commands[idx]]);\n    }\n\n    this.fontName = this.wrapCommand((value) => {\n      return this.fontStyling('font-family', env.validFontName(value));\n    });\n\n    this.fontSize = this.wrapCommand((value) => {\n      const unit = this.currentStyle()['font-size-unit'];\n      return this.fontStyling('font-size', value + unit);\n    });\n\n    this.fontSizeUnit = this.wrapCommand((value) => {\n      const size = this.currentStyle()['font-size'];\n      return this.fontStyling('font-size', size + value);\n    });\n\n    for (let idx = 1; idx <= 6; idx++) {\n      this['formatH' + idx] = ((idx) => {\n        return () => {\n          this.formatBlock('H' + idx);\n        };\n      })(idx);\n      this.context.memo('help.formatH' + idx, this.lang.help['formatH' + idx]);\n    };\n\n    this.insertParagraph = this.wrapCommand(() => {\n      this.typing.insertParagraph(this.editable);\n    });\n\n    this.insertOrderedList = this.wrapCommand(() => {\n      this.bullet.insertOrderedList(this.editable);\n    });\n\n    this.insertUnorderedList = this.wrapCommand(() => {\n      this.bullet.insertUnorderedList(this.editable);\n    });\n\n    this.indent = this.wrapCommand(() => {\n      this.bullet.indent(this.editable);\n    });\n\n    this.outdent = this.wrapCommand(() => {\n      this.bullet.outdent(this.editable);\n    });\n\n    /**\n     * insertNode\n     * insert node\n     * @param {Node} node\n     */\n    this.insertNode = this.wrapCommand((node) => {\n      if (this.isLimited($(node).text().length)) {\n        return;\n      }\n      const rng = this.getLastRange();\n      rng.insertNode(node);\n      this.setLastRange(range.createFromNodeAfter(node).select());\n    });\n\n    /**\n     * insert text\n     * @param {String} text\n     */\n    this.insertText = this.wrapCommand((text) => {\n      if (this.isLimited(text.length)) {\n        return;\n      }\n      const rng = this.getLastRange();\n      const textNode = rng.insertNode(dom.createText(text));\n      this.setLastRange(range.create(textNode, dom.nodeLength(textNode)).select());\n    });\n\n    /**\n     * paste HTML\n     * @param {String} markup\n     */\n    this.pasteHTML = this.wrapCommand((markup) => {\n      if (this.isLimited(markup.length)) {\n        return;\n      }\n      markup = this.context.invoke('codeview.purify', markup);\n      const contents = this.getLastRange().pasteHTML(markup);\n      this.setLastRange(range.createFromNodeAfter(lists.last(contents)).select());\n    });\n\n    /**\n     * formatBlock\n     *\n     * @param {String} tagName\n     */\n    this.formatBlock = this.wrapCommand((tagName, $target) => {\n      const onApplyCustomStyle = this.options.callbacks.onApplyCustomStyle;\n      if (onApplyCustomStyle) {\n        onApplyCustomStyle.call(this, $target, this.context, this.onFormatBlock);\n      } else {\n        this.onFormatBlock(tagName, $target);\n      }\n    });\n\n    /**\n     * insert horizontal rule\n     */\n    this.insertHorizontalRule = this.wrapCommand(() => {\n      const hrNode = this.getLastRange().insertNode(dom.create('HR'));\n      if (hrNode.nextSibling) {\n        this.setLastRange(range.create(hrNode.nextSibling, 0).normalize().select());\n      }\n    });\n\n    /**\n     * lineHeight\n     * @param {String} value\n     */\n    this.lineHeight = this.wrapCommand((value) => {\n      this.style.stylePara(this.getLastRange(), {\n        lineHeight: value,\n      });\n    });\n\n    /**\n     * create link (command)\n     *\n     * @param {Object} linkInfo\n     */\n    this.createLink = this.wrapCommand((linkInfo) => {\n      let linkUrl = linkInfo.url;\n      const linkText = linkInfo.text;\n      const isNewWindow = linkInfo.isNewWindow;\n      const checkProtocol = linkInfo.checkProtocol;\n      let rng = linkInfo.range || this.getLastRange();\n      const additionalTextLength = linkText.length - rng.toString().length;\n      if (additionalTextLength > 0 && this.isLimited(additionalTextLength)) {\n        return;\n      }\n      const isTextChanged = rng.toString() !== linkText;\n\n      // handle spaced urls from input\n      if (typeof linkUrl === 'string') {\n        linkUrl = linkUrl.trim();\n      }\n\n      if (this.options.onCreateLink) {\n        linkUrl = this.options.onCreateLink(linkUrl);\n      } else if (checkProtocol) {\n        // if url doesn't have any protocol and not even a relative or a label, use http:// as default\n        linkUrl = /^([A-Za-z][A-Za-z0-9+-.]*\\:|#|\\/)/.test(linkUrl)\n          ? linkUrl : this.options.defaultProtocol + linkUrl;\n      }\n\n      let anchors = [];\n      if (isTextChanged) {\n        rng = rng.deleteContents();\n        const anchor = rng.insertNode($('<A>' + linkText + '</A>')[0]);\n        anchors.push(anchor);\n      } else {\n        anchors = this.style.styleNodes(rng, {\n          nodeName: 'A',\n          expandClosestSibling: true,\n          onlyPartialContains: true,\n        });\n      }\n\n      $.each(anchors, (idx, anchor) => {\n        $(anchor).attr('href', linkUrl);\n        if (isNewWindow) {\n          $(anchor).attr('target', '_blank');\n        } else {\n          $(anchor).removeAttr('target');\n        }\n      });\n\n      const startRange = range.createFromNodeBefore(lists.head(anchors));\n      const startPoint = startRange.getStartPoint();\n      const endRange = range.createFromNodeAfter(lists.last(anchors));\n      const endPoint = endRange.getEndPoint();\n\n      this.setLastRange(\n        range.create(\n          startPoint.node,\n          startPoint.offset,\n          endPoint.node,\n          endPoint.offset\n        ).select()\n      );\n    });\n\n    /**\n     * setting color\n     *\n     * @param {Object} sObjColor  color code\n     * @param {String} sObjColor.foreColor foreground color\n     * @param {String} sObjColor.backColor background color\n     */\n    this.color = this.wrapCommand((colorInfo) => {\n      const foreColor = colorInfo.foreColor;\n      const backColor = colorInfo.backColor;\n\n      if (foreColor) { document.execCommand('foreColor', false, foreColor); }\n      if (backColor) { document.execCommand('backColor', false, backColor); }\n    });\n\n    /**\n     * Set foreground color\n     *\n     * @param {String} colorCode foreground color code\n     */\n    this.foreColor = this.wrapCommand((colorInfo) => {\n      document.execCommand('styleWithCSS', false, true);\n      document.execCommand('foreColor', false, colorInfo);\n    });\n\n    /**\n     * insert Table\n     *\n     * @param {String} dimension of table (ex : \"5x5\")\n     */\n    this.insertTable = this.wrapCommand((dim) => {\n      const dimension = dim.split('x');\n\n      const rng = this.getLastRange().deleteContents();\n      rng.insertNode(this.table.createTable(dimension[0], dimension[1], this.options));\n    });\n\n    /**\n     * remove media object and Figure Elements if media object is img with Figure.\n     */\n    this.removeMedia = this.wrapCommand(() => {\n      let $target = $(this.restoreTarget()).parent();\n      if ($target.closest('figure').length) {\n        $target.closest('figure').remove();\n      } else {\n        $target = $(this.restoreTarget()).detach();\n      }\n      this.context.triggerEvent('media.delete', $target, this.$editable);\n    });\n\n    /**\n     * float me\n     *\n     * @param {String} value\n     */\n    this.floatMe = this.wrapCommand((value) => {\n      const $target = $(this.restoreTarget());\n      $target.toggleClass('note-float-left', value === 'left');\n      $target.toggleClass('note-float-right', value === 'right');\n      $target.css('float', (value === 'none' ? '' : value));\n    });\n\n    /**\n     * resize overlay element\n     * @param {String} value\n     */\n    this.resize = this.wrapCommand((value) => {\n      const $target = $(this.restoreTarget());\n      value = parseFloat(value);\n      if (value === 0) {\n        $target.css('width', '');\n      } else {\n        $target.css({\n          width: value * 100 + '%',\n          height: '',\n        });\n      }\n    });\n  }\n\n  initialize() {\n    // bind custom events\n    this.$editable.on('keydown', (event) => {\n      if (event.keyCode === key.code.ENTER) {\n        this.context.triggerEvent('enter', event);\n      }\n      this.context.triggerEvent('keydown', event);\n\n      // keep a snapshot to limit text on input event\n      this.snapshot = this.history.makeSnapshot();\n\n      if (!event.isDefaultPrevented()) {\n        if (this.options.shortcuts) {\n          this.handleKeyMap(event);\n        } else {\n          this.preventDefaultEditableShortCuts(event);\n        }\n      }\n      if (this.isLimited(1, event)) {\n        const lastRange = this.getLastRange();\n        if (lastRange.eo - lastRange.so === 0) {\n          return false;\n        }\n      }\n      this.setLastRange();\n    }).on('keyup', (event) => {\n      this.setLastRange();\n      this.context.triggerEvent('keyup', event);\n    }).on('focus', (event) => {\n      this.setLastRange();\n      this.context.triggerEvent('focus', event);\n    }).on('blur', (event) => {\n      this.context.triggerEvent('blur', event);\n    }).on('mousedown', (event) => {\n      this.context.triggerEvent('mousedown', event);\n    }).on('mouseup', (event) => {\n      this.setLastRange();\n      this.history.recordUndo();\n      this.context.triggerEvent('mouseup', event);\n    }).on('scroll', (event) => {\n      this.context.triggerEvent('scroll', event);\n    }).on('paste', (event) => {\n      this.setLastRange();\n      this.context.triggerEvent('paste', event);\n    }).on('input', (event) => {\n      // To limit composition characters (e.g. Korean)\n      if (this.isLimited(0) && this.snapshot) {\n        this.history.applySnapshot(this.snapshot);\n      }\n    });\n\n    this.$editable.attr('spellcheck', this.options.spellCheck);\n\n    this.$editable.attr('autocorrect', this.options.spellCheck);\n\n    if (this.options.disableGrammar) {\n      this.$editable.attr('data-gramm', false);\n    }\n\n    // init content before set event\n    this.$editable.html(dom.html(this.$note) || dom.emptyPara);\n\n    this.$editable.on(env.inputEventName, func.debounce(() => {\n      this.context.triggerEvent('change', this.$editable.html(), this.$editable);\n    }, 10));\n\n    this.$editor.on('focusin', (event) => {\n      this.context.triggerEvent('focusin', event);\n    }).on('focusout', (event) => {\n      this.context.triggerEvent('focusout', event);\n    });\n\n    if (!this.options.airMode) {\n      if (this.options.width) {\n        this.$editor.outerWidth(this.options.width);\n      }\n      if (this.options.height) {\n        this.$editable.outerHeight(this.options.height);\n      }\n      if (this.options.maxHeight) {\n        this.$editable.css('max-height', this.options.maxHeight);\n      }\n      if (this.options.minHeight) {\n        this.$editable.css('min-height', this.options.minHeight);\n      }\n    }\n\n    this.history.recordUndo();\n    this.setLastRange();\n  }\n\n  destroy() {\n    this.$editable.off();\n  }\n\n  handleKeyMap(event) {\n    const keyMap = this.options.keyMap[env.isMac ? 'mac' : 'pc'];\n    const keys = [];\n\n    if (event.metaKey) { keys.push('CMD'); }\n    if (event.ctrlKey && !event.altKey) { keys.push('CTRL'); }\n    if (event.shiftKey) { keys.push('SHIFT'); }\n\n    const keyName = key.nameFromCode[event.keyCode];\n    if (keyName) {\n      keys.push(keyName);\n    }\n\n    const eventName = keyMap[keys.join('+')];\n\n    if (keyName === 'TAB' && !this.options.tabDisable) {\n      this.afterCommand();\n    } else if (eventName) {\n      if (this.context.invoke(eventName) !== false) {\n        event.preventDefault();\n      }\n    } else if (key.isEdit(event.keyCode)) {\n      this.afterCommand();\n    }\n  }\n\n  preventDefaultEditableShortCuts(event) {\n    // B(Bold, 66) / I(Italic, 73) / U(Underline, 85)\n    if ((event.ctrlKey || event.metaKey) &&\n      lists.contains([66, 73, 85], event.keyCode)) {\n      event.preventDefault();\n    }\n  }\n\n  isLimited(pad, event) {\n    pad = pad || 0;\n\n    if (typeof event !== 'undefined') {\n      if (key.isMove(event.keyCode) ||\n          key.isNavigation(event.keyCode) ||\n          (event.ctrlKey || event.metaKey) ||\n          lists.contains([key.code.BACKSPACE, key.code.DELETE], event.keyCode)) {\n        return false;\n      }\n    }\n\n    if (this.options.maxTextLength > 0) {\n      if ((this.$editable.text().length + pad) > this.options.maxTextLength) {\n        return true;\n      }\n    }\n    return false;\n  }\n  /**\n   * create range\n   * @return {WrappedRange}\n   */\n  createRange() {\n    this.focus();\n    this.setLastRange();\n    return this.getLastRange();\n  }\n\n  setLastRange(rng) {\n    if (rng) {\n      this.lastRange = rng;\n    } else {\n      this.lastRange = range.create(this.editable);\n\n      if ($(this.lastRange.sc).closest('.note-editable').length === 0) {\n        this.lastRange = range.createFromBodyElement(this.editable);\n      }\n    }\n  }\n\n  getLastRange() {\n    if (!this.lastRange) {\n      this.setLastRange();\n    }\n    return this.lastRange;\n  }\n\n  /**\n   * saveRange\n   *\n   * save current range\n   *\n   * @param {Boolean} [thenCollapse=false]\n   */\n  saveRange(thenCollapse) {\n    if (thenCollapse) {\n      this.getLastRange().collapse().select();\n    }\n  }\n\n  /**\n   * restoreRange\n   *\n   * restore lately range\n   */\n  restoreRange() {\n    if (this.lastRange) {\n      this.lastRange.select();\n      this.focus();\n    }\n  }\n\n  saveTarget(node) {\n    this.$editable.data('target', node);\n  }\n\n  clearTarget() {\n    this.$editable.removeData('target');\n  }\n\n  restoreTarget() {\n    return this.$editable.data('target');\n  }\n\n  /**\n   * currentStyle\n   *\n   * current style\n   * @return {Object|Boolean} unfocus\n   */\n  currentStyle() {\n    let rng = range.create();\n    if (rng) {\n      rng = rng.normalize();\n    }\n    return rng ? this.style.current(rng) : this.style.fromNode(this.$editable);\n  }\n\n  /**\n   * style from node\n   *\n   * @param {jQuery} $node\n   * @return {Object}\n   */\n  styleFromNode($node) {\n    return this.style.fromNode($node);\n  }\n\n  /**\n   * undo\n   */\n  undo() {\n    this.context.triggerEvent('before.command', this.$editable.html());\n    this.history.undo();\n    this.context.triggerEvent('change', this.$editable.html(), this.$editable);\n  }\n\n  /*\n  * commit\n  */\n  commit() {\n    this.context.triggerEvent('before.command', this.$editable.html());\n    this.history.commit();\n    this.context.triggerEvent('change', this.$editable.html(), this.$editable);\n  }\n\n  /**\n   * redo\n   */\n  redo() {\n    this.context.triggerEvent('before.command', this.$editable.html());\n    this.history.redo();\n    this.context.triggerEvent('change', this.$editable.html(), this.$editable);\n  }\n\n  /**\n   * before command\n   */\n  beforeCommand() {\n    this.context.triggerEvent('before.command', this.$editable.html());\n    // keep focus on editable before command execution\n    this.focus();\n  }\n\n  /**\n   * after command\n   * @param {Boolean} isPreventTrigger\n   */\n  afterCommand(isPreventTrigger) {\n    this.normalizeContent();\n    this.history.recordUndo();\n    if (!isPreventTrigger) {\n      this.context.triggerEvent('change', this.$editable.html(), this.$editable);\n    }\n  }\n\n  /**\n   * handle tab key\n   */\n  tab() {\n    const rng = this.getLastRange();\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.table.tab(rng);\n    } else {\n      if (this.options.tabSize === 0) {\n        return false;\n      }\n\n      if (!this.isLimited(this.options.tabSize)) {\n        this.beforeCommand();\n        this.typing.insertTab(rng, this.options.tabSize);\n        this.afterCommand();\n      }\n    }\n  }\n\n  /**\n   * handle shift+tab key\n   */\n  untab() {\n    const rng = this.getLastRange();\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.table.tab(rng, true);\n    } else {\n      if (this.options.tabSize === 0) {\n        return false;\n      }\n    }\n  }\n\n  /**\n   * run given function between beforeCommand and afterCommand\n   */\n  wrapCommand(fn) {\n    return function() {\n      this.beforeCommand();\n      fn.apply(this, arguments);\n      this.afterCommand();\n    };\n  }\n\n  /**\n   * insert image\n   *\n   * @param {String} src\n   * @param {String|Function} param\n   * @return {Promise}\n   */\n  insertImage(src, param) {\n    return createImage(src, param).then(($image) => {\n      this.beforeCommand();\n\n      if (typeof param === 'function') {\n        param($image);\n      } else {\n        if (typeof param === 'string') {\n          $image.attr('data-filename', param);\n        }\n        $image.css('width', Math.min(this.$editable.width(), $image.width()));\n      }\n\n      $image.show();\n      this.getLastRange().insertNode($image[0]);\n      this.setLastRange(range.createFromNodeAfter($image[0]).select());\n      this.afterCommand();\n    }).fail((e) => {\n      this.context.triggerEvent('image.upload.error', e);\n    });\n  }\n\n  /**\n   * insertImages\n   * @param {File[]} files\n   */\n  insertImagesAsDataURL(files) {\n    $.each(files, (idx, file) => {\n      const filename = file.name;\n      if (this.options.maximumImageFileSize && this.options.maximumImageFileSize < file.size) {\n        this.context.triggerEvent('image.upload.error', this.lang.image.maximumFileSizeError);\n      } else {\n        readFileAsDataURL(file).then((dataURL) => {\n          return this.insertImage(dataURL, filename);\n        }).fail(() => {\n          this.context.triggerEvent('image.upload.error');\n        });\n      }\n    });\n  }\n\n  /**\n   * insertImagesOrCallback\n   * @param {File[]} files\n   */\n  insertImagesOrCallback(files) {\n    const callbacks = this.options.callbacks;\n    // If onImageUpload set,\n    if (callbacks.onImageUpload) {\n      this.context.triggerEvent('image.upload', files);\n      // else insert Image as dataURL\n    } else {\n      this.insertImagesAsDataURL(files);\n    }\n  }\n\n  /**\n   * return selected plain text\n   * @return {String} text\n   */\n  getSelectedText() {\n    let rng = this.getLastRange();\n\n    // if range on anchor, expand range with anchor\n    if (rng.isOnAnchor()) {\n      rng = range.createFromNode(dom.ancestor(rng.sc, dom.isAnchor));\n    }\n\n    return rng.toString();\n  }\n\n  onFormatBlock(tagName, $target) {\n    // [workaround] for MSIE, IE need `<`\n    document.execCommand('FormatBlock', false, env.isMSIE ? '<' + tagName + '>' : tagName);\n\n    // support custom class\n    if ($target && $target.length) {\n      // find the exact element has given tagName\n      if ($target[0].tagName.toUpperCase() !== tagName.toUpperCase()) {\n        $target = $target.find(tagName);\n      }\n\n      if ($target && $target.length) {\n        const className = $target[0].className || '';\n        if (className) {\n          const currentRange = this.createRange();\n\n          const $parent = $([currentRange.sc, currentRange.ec]).closest(tagName);\n          $parent.addClass(className);\n        }\n      }\n    }\n  }\n\n  formatPara() {\n    this.formatBlock('P');\n  }\n\n  fontStyling(target, value) {\n    const rng = this.getLastRange();\n\n    if (rng !== '') {\n      const spans = this.style.styleNodes(rng);\n      this.$editor.find('.note-status-output').html('');\n      $(spans).css(target, value);\n\n      // [workaround] added styled bogus span for style\n      //  - also bogus character needed for cursor position\n      if (rng.isCollapsed()) {\n        const firstSpan = lists.head(spans);\n        if (firstSpan && !dom.nodeLength(firstSpan)) {\n          firstSpan.innerHTML = dom.ZERO_WIDTH_NBSP_CHAR;\n          range.createFromNodeAfter(firstSpan.firstChild).select();\n          this.setLastRange();\n          this.$editable.data(KEY_BOGUS, firstSpan);\n        }\n      }\n    } else {\n      const noteStatusOutput = $.now();\n      this.$editor.find('.note-status-output').html('<div id=\"note-status-output-' + noteStatusOutput + '\" class=\"alert alert-info\">' + this.lang.output.noSelection + '</div>');\n      setTimeout(function() { $('#note-status-output-' + noteStatusOutput).remove(); }, 5000);\n    }\n  }\n\n  /**\n   * unlink\n   *\n   * @type command\n   */\n  unlink() {\n    let rng = this.getLastRange();\n    if (rng.isOnAnchor()) {\n      const anchor = dom.ancestor(rng.sc, dom.isAnchor);\n      rng = range.createFromNode(anchor);\n      rng.select();\n      this.setLastRange();\n\n      this.beforeCommand();\n      document.execCommand('unlink');\n      this.afterCommand();\n    }\n  }\n\n  /**\n   * returns link info\n   *\n   * @return {Object}\n   * @return {WrappedRange} return.range\n   * @return {String} return.text\n   * @return {Boolean} [return.isNewWindow=true]\n   * @return {String} [return.url=\"\"]\n   */\n  getLinkInfo() {\n    const rng = this.getLastRange().expand(dom.isAnchor);\n    // Get the first anchor on range(for edit).\n    const $anchor = $(lists.head(rng.nodes(dom.isAnchor)));\n    const linkInfo = {\n      range: rng,\n      text: rng.toString(),\n      url: $anchor.length ? $anchor.attr('href') : '',\n    };\n\n    // When anchor exists,\n    if ($anchor.length) {\n      // Set isNewWindow by checking its target.\n      linkInfo.isNewWindow = $anchor.attr('target') === '_blank';\n    }\n\n    return linkInfo;\n  }\n\n  addRow(position) {\n    const rng = this.getLastRange(this.$editable);\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.beforeCommand();\n      this.table.addRow(rng, position);\n      this.afterCommand();\n    }\n  }\n\n  addCol(position) {\n    const rng = this.getLastRange(this.$editable);\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.beforeCommand();\n      this.table.addCol(rng, position);\n      this.afterCommand();\n    }\n  }\n\n  deleteRow() {\n    const rng = this.getLastRange(this.$editable);\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.beforeCommand();\n      this.table.deleteRow(rng);\n      this.afterCommand();\n    }\n  }\n\n  deleteCol() {\n    const rng = this.getLastRange(this.$editable);\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.beforeCommand();\n      this.table.deleteCol(rng);\n      this.afterCommand();\n    }\n  }\n\n  deleteTable() {\n    const rng = this.getLastRange(this.$editable);\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.beforeCommand();\n      this.table.deleteTable(rng);\n      this.afterCommand();\n    }\n  }\n\n  /**\n   * @param {Position} pos\n   * @param {jQuery} $target - target element\n   * @param {Boolean} [bKeepRatio] - keep ratio\n   */\n  resizeTo(pos, $target, bKeepRatio) {\n    let imageSize;\n    if (bKeepRatio) {\n      const newRatio = pos.y / pos.x;\n      const ratio = $target.data('ratio');\n      imageSize = {\n        width: ratio > newRatio ? pos.x : pos.y / ratio,\n        height: ratio > newRatio ? pos.x * ratio : pos.y,\n      };\n    } else {\n      imageSize = {\n        width: pos.x,\n        height: pos.y,\n      };\n    }\n\n    $target.css(imageSize);\n  }\n\n  /**\n   * returns whether editable area has focus or not.\n   */\n  hasFocus() {\n    return this.$editable.is(':focus');\n  }\n\n  /**\n   * set focus\n   */\n  focus() {\n    // [workaround] Screen will move when page is scolled in IE.\n    //  - do focus when not focused\n    if (!this.hasFocus()) {\n      this.$editable.focus();\n    }\n  }\n\n  /**\n   * returns whether contents is empty or not.\n   * @return {Boolean}\n   */\n  isEmpty() {\n    return dom.isEmpty(this.$editable[0]) || dom.emptyPara === this.$editable.html();\n  }\n\n  /**\n   * Removes all contents and restores the editable instance to an _emptyPara_.\n   */\n  empty() {\n    this.context.invoke('code', dom.emptyPara);\n  }\n\n  /**\n   * normalize content\n   */\n  normalizeContent() {\n    this.$editable[0].normalize();\n  }\n}\n", "import $ from 'jquery';\n\n/**\n * @method readFileAsDataURL\n *\n * read contents of file as representing URL\n *\n * @param {File} file\n * @return {Promise} - then: dataUrl\n */\nexport function readFileAsDataURL(file) {\n  return $.Deferred((deferred) => {\n    $.extend(new FileReader(), {\n      onload: (e) => {\n        const dataURL = e.target.result;\n        deferred.resolve(dataURL);\n      },\n      onerror: (err) => {\n        deferred.reject(err);\n      },\n    }).readAsDataURL(file);\n  }).promise();\n}\n\n/**\n * @method createImage\n *\n * create `<image>` from url string\n *\n * @param {String} url\n * @return {Promise} - then: $image\n */\nexport function createImage(url) {\n  return $.Deferred((deferred) => {\n    const $img = $('<img>');\n\n    $img.one('load', () => {\n      $img.off('error abort');\n      deferred.resolve($img);\n    }).one('error abort', () => {\n      $img.off('load').detach();\n      deferred.reject($img);\n    }).css({\n      display: 'none',\n    }).appendTo(document.body).attr('src', url);\n  }).promise();\n}\n", "import lists from '../core/lists';\n\nexport default class Clipboard {\n  constructor(context) {\n    this.context = context;\n    this.$editable = context.layoutInfo.editable;\n  }\n\n  initialize() {\n    this.$editable.on('paste', this.pasteByEvent.bind(this));\n  }\n\n  /**\n   * paste by clipboard event\n   *\n   * @param {Event} event\n   */\n  pasteByEvent(event) {\n    const clipboardData = event.originalEvent.clipboardData;\n    if (clipboardData && clipboardData.items && clipboardData.items.length) {\n      const item = clipboardData.items.length > 1 ? clipboardData.items[1] : lists.head(clipboardData.items);\n      if (item.kind === 'file' && item.type.indexOf('image/') !== -1) {\n        // paste img file\n        this.context.invoke('editor.insertImagesOrCallback', [item.getAsFile()]);\n        event.preventDefault();\n\n        this.context.invoke('editor.afterCommand');\n      } else if (item.kind === 'string') {\n        // paste text with maxTextLength check\n        if (this.context.invoke('editor.isLimited', clipboardData.getData('Text').length)) {\n          event.preventDefault();\n        } else {\n          this.context.invoke('editor.afterCommand');\n        }\n      }\n    }\n  }\n}\n", "import $ from 'jquery';\n\nexport default class Dropzone {\n  constructor(context) {\n    this.context = context;\n    this.$eventListener = $(document);\n    this.$editor = context.layoutInfo.editor;\n    this.$editable = context.layoutInfo.editable;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n    this.documentEventHandlers = {};\n\n    this.$dropzone = $([\n      '<div class=\"note-dropzone\">',\n        '<div class=\"note-dropzone-message\"/>',\n      '</div>',\n    ].join('')).prependTo(this.$editor);\n  }\n\n  /**\n   * attach Drag and Drop Events\n   */\n  initialize() {\n    if (this.options.disableDragAndDrop) {\n      // prevent default drop event\n      this.documentEventHandlers.onDrop = (e) => {\n        e.preventDefault();\n      };\n      // do not consider outside of dropzone\n      this.$eventListener = this.$dropzone;\n      this.$eventListener.on('drop', this.documentEventHandlers.onDrop);\n    } else {\n      this.attachDragAndDropEvent();\n    }\n  }\n\n  /**\n   * attach Drag and Drop Events\n   */\n  attachDragAndDropEvent() {\n    let collection = $();\n    const $dropzoneMessage = this.$dropzone.find('.note-dropzone-message');\n\n    this.documentEventHandlers.onDragenter = (e) => {\n      const isCodeview = this.context.invoke('codeview.isActivated');\n      const hasEditorSize = this.$editor.width() > 0 && this.$editor.height() > 0;\n      if (!isCodeview && !collection.length && hasEditorSize) {\n        this.$editor.addClass('dragover');\n        this.$dropzone.width(this.$editor.width());\n        this.$dropzone.height(this.$editor.height());\n        $dropzoneMessage.text(this.lang.image.dragImageHere);\n      }\n      collection = collection.add(e.target);\n    };\n\n    this.documentEventHandlers.onDragleave = (e) => {\n      collection = collection.not(e.target);\n\n      // If nodeName is BODY, then just make it over (fix for IE)\n      if (!collection.length || e.target.nodeName === 'BODY') {\n        collection = $();\n        this.$editor.removeClass('dragover');\n      }\n    };\n\n    this.documentEventHandlers.onDrop = () => {\n      collection = $();\n      this.$editor.removeClass('dragover');\n    };\n\n    // show dropzone on dragenter when dragging a object to document\n    // -but only if the editor is visible, i.e. has a positive width and height\n    this.$eventListener.on('dragenter', this.documentEventHandlers.onDragenter)\n      .on('dragleave', this.documentEventHandlers.onDragleave)\n      .on('drop', this.documentEventHandlers.onDrop);\n\n    // change dropzone's message on hover.\n    this.$dropzone.on('dragenter', () => {\n      this.$dropzone.addClass('hover');\n      $dropzoneMessage.text(this.lang.image.dropImage);\n    }).on('dragleave', () => {\n      this.$dropzone.removeClass('hover');\n      $dropzoneMessage.text(this.lang.image.dragImageHere);\n    });\n\n    // attach dropImage\n    this.$dropzone.on('drop', (event) => {\n      const dataTransfer = event.originalEvent.dataTransfer;\n\n      // stop the browser from opening the dropped content\n      event.preventDefault();\n\n      if (dataTransfer && dataTransfer.files && dataTransfer.files.length) {\n        this.$editable.focus();\n        this.context.invoke('editor.insertImagesOrCallback', dataTransfer.files);\n      } else {\n        $.each(dataTransfer.types, (idx, type) => {\n          // skip moz-specific types\n          if (type.toLowerCase().indexOf('_moz_') > -1) {\n            return;\n          }\n          const content = dataTransfer.getData(type);\n\n          if (type.toLowerCase().indexOf('text') > -1) {\n            this.context.invoke('editor.pasteHTML', content);\n          } else {\n            $(content).each((idx, item) => {\n              this.context.invoke('editor.insertNode', item);\n            });\n          }\n        });\n      }\n    }).on('dragover', false); // prevent default dragover event\n  }\n\n  destroy() {\n    Object.keys(this.documentEventHandlers).forEach((key) => {\n      this.$eventListener.off(key.substr(2).toLowerCase(), this.documentEventHandlers[key]);\n    });\n    this.documentEventHandlers = {};\n  }\n}\n", "import $ from 'jquery';\nconst EDITABLE_PADDING = 24;\n\nexport default class Statusbar {\n  constructor(context) {\n    this.$document = $(document);\n    this.$statusbar = context.layoutInfo.statusbar;\n    this.$editable = context.layoutInfo.editable;\n    this.options = context.options;\n  }\n\n  initialize() {\n    if (this.options.airMode || this.options.disableResizeEditor) {\n      this.destroy();\n      return;\n    }\n\n    this.$statusbar.on('mousedown', (event) => {\n      event.preventDefault();\n      event.stopPropagation();\n\n      const editableTop = this.$editable.offset().top - this.$document.scrollTop();\n      const onMouseMove = (event) => {\n        let height = event.clientY - (editableTop + EDITABLE_PADDING);\n\n        height = (this.options.minheight > 0) ? Math.max(height, this.options.minheight) : height;\n        height = (this.options.maxHeight > 0) ? Math.min(height, this.options.maxHeight) : height;\n\n        this.$editable.height(height);\n      };\n\n      this.$document.on('mousemove', onMouseMove).one('mouseup', () => {\n        this.$document.off('mousemove', onMouseMove);\n      });\n    });\n  }\n\n  destroy() {\n    this.$statusbar.off();\n    this.$statusbar.addClass('locked');\n  }\n}\n", "import $ from 'jquery';\n\nexport default class Fullscreen {\n  constructor(context) {\n    this.context = context;\n\n    this.$editor = context.layoutInfo.editor;\n    this.$toolbar = context.layoutInfo.toolbar;\n    this.$editable = context.layoutInfo.editable;\n    this.$codable = context.layoutInfo.codable;\n\n    this.$window = $(window);\n    this.$scrollbar = $('html, body');\n\n    this.onResize = () => {\n      this.resizeTo({\n        h: this.$window.height() - this.$toolbar.outerHeight(),\n      });\n    };\n  }\n\n  resizeTo(size) {\n    this.$editable.css('height', size.h);\n    this.$codable.css('height', size.h);\n    if (this.$codable.data('cmeditor')) {\n      this.$codable.data('cmeditor').setsize(null, size.h);\n    }\n  }\n\n  /**\n   * toggle fullscreen\n   */\n  toggle() {\n    this.$editor.toggleClass('fullscreen');\n    if (this.isFullscreen()) {\n      this.$editable.data('orgHeight', this.$editable.css('height'));\n      this.$editable.data('orgMaxHeight', this.$editable.css('maxHeight'));\n      this.$editable.css('maxHeight', '');\n      this.$window.on('resize', this.onResize).trigger('resize');\n      this.$scrollbar.css('overflow', 'hidden');\n    } else {\n      this.$window.off('resize', this.onResize);\n      this.resizeTo({ h: this.$editable.data('orgHeight') });\n      this.$editable.css('maxHeight', this.$editable.css('orgMaxHeight'));\n      this.$scrollbar.css('overflow', 'visible');\n    }\n\n    this.context.invoke('toolbar.updateFullscreen', this.isFullscreen());\n  }\n\n  isFullscreen() {\n    return this.$editor.hasClass('fullscreen');\n  }\n}\n", "import $ from 'jquery';\nimport dom from '../core/dom';\n\nexport default class Handle {\n  constructor(context) {\n    this.context = context;\n    this.$document = $(document);\n    this.$editingArea = context.layoutInfo.editingArea;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n\n    this.events = {\n      'summernote.mousedown': (we, e) => {\n        if (this.update(e.target, e)) {\n          e.preventDefault();\n        }\n      },\n      'summernote.keyup summernote.scroll summernote.change summernote.dialog.shown': () => {\n        this.update();\n      },\n      'summernote.disable summernote.blur': () => {\n        this.hide();\n      },\n      'summernote.codeview.toggled': () => {\n        this.update();\n      },\n    };\n  }\n\n  initialize() {\n    this.$handle = $([\n      '<div class=\"note-handle\">',\n        '<div class=\"note-control-selection\">',\n          '<div class=\"note-control-selection-bg\"></div>',\n          '<div class=\"note-control-holder note-control-nw\"></div>',\n          '<div class=\"note-control-holder note-control-ne\"></div>',\n          '<div class=\"note-control-holder note-control-sw\"></div>',\n          '<div class=\"',\n            (this.options.disableResizeImage ? 'note-control-holder' : 'note-control-sizing'),\n          ' note-control-se\"></div>',\n          (this.options.disableResizeImage ? '' : '<div class=\"note-control-selection-info\"></div>'),\n        '</div>',\n      '</div>',\n    ].join('')).prependTo(this.$editingArea);\n\n    this.$handle.on('mousedown', (event) => {\n      if (dom.isControlSizing(event.target)) {\n        event.preventDefault();\n        event.stopPropagation();\n\n        const $target = this.$handle.find('.note-control-selection').data('target');\n        const posStart = $target.offset();\n        const scrollTop = this.$document.scrollTop();\n\n        const onMouseMove = (event) => {\n          this.context.invoke('editor.resizeTo', {\n            x: event.clientX - posStart.left,\n            y: event.clientY - (posStart.top - scrollTop),\n          }, $target, !event.shiftKey);\n\n          this.update($target[0], event);\n        };\n\n        this.$document\n          .on('mousemove', onMouseMove)\n          .one('mouseup', (e) => {\n            e.preventDefault();\n            this.$document.off('mousemove', onMouseMove);\n            this.context.invoke('editor.afterCommand');\n          });\n\n        if (!$target.data('ratio')) { // original ratio.\n          $target.data('ratio', $target.height() / $target.width());\n        }\n      }\n    });\n\n    // Listen for scrolling on the handle overlay.\n    this.$handle.on('wheel', (e) => {\n      e.preventDefault();\n      this.update();\n    });\n  }\n\n  destroy() {\n    this.$handle.remove();\n  }\n\n  update(target, event) {\n    if (this.context.isDisabled()) {\n      return false;\n    }\n\n    const isImage = dom.isImg(target);\n    const $selection = this.$handle.find('.note-control-selection');\n\n    this.context.invoke('imagePopover.update', target, event);\n\n    if (isImage) {\n      const $image = $(target);\n      const position = $image.position();\n      const pos = {\n        left: position.left + parseInt($image.css('marginLeft'), 10),\n        top: position.top + parseInt($image.css('marginTop'), 10),\n      };\n\n      // exclude margin\n      const imageSize = {\n        w: $image.outerWidth(false),\n        h: $image.outerHeight(false),\n      };\n\n      $selection.css({\n        display: 'block',\n        left: pos.left,\n        top: pos.top,\n        width: imageSize.w,\n        height: imageSize.h,\n      }).data('target', $image); // save current image element.\n\n      const origImageObj = new Image();\n      origImageObj.src = $image.attr('src');\n\n      const sizingText = imageSize.w + 'x' + imageSize.h + ' (' + this.lang.image.original + ': ' + origImageObj.width + 'x' + origImageObj.height + ')';\n      $selection.find('.note-control-selection-info').text(sizingText);\n      this.context.invoke('editor.saveTarget', target);\n    } else {\n      this.hide();\n    }\n\n    return isImage;\n  }\n\n  /**\n   * hide\n   *\n   * @param {jQuery} $handle\n   */\n  hide() {\n    this.context.invoke('editor.clearTarget');\n    this.$handle.children().hide();\n  }\n}\n", "import $ from 'jquery';\nimport func from '../core/func';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\nimport range from '../core/range';\nimport key from '../core/key';\n\nconst POPOVER_DIST = 5;\n\nexport default class HintPopover {\n  constructor(context) {\n    this.context = context;\n\n    this.ui = $.summernote.ui;\n    this.$editable = context.layoutInfo.editable;\n    this.options = context.options;\n    this.hint = this.options.hint || [];\n    this.direction = this.options.hintDirection || 'bottom';\n    this.hints = Array.isArray(this.hint) ? this.hint : [this.hint];\n\n    this.events = {\n      'summernote.keyup': (we, e) => {\n        if (!e.isDefaultPrevented()) {\n          this.handleKeyup(e);\n        }\n      },\n      'summernote.keydown': (we, e) => {\n        this.handleKeydown(e);\n      },\n      'summernote.disable summernote.dialog.shown summernote.blur': () => {\n        this.hide();\n      },\n    };\n  }\n\n  shouldInitialize() {\n    return this.hints.length > 0;\n  }\n\n  initialize() {\n    this.lastWordRange = null;\n    this.matchingWord = null;\n    this.$popover = this.ui.popover({\n      className: 'note-hint-popover',\n      hideArrow: true,\n      direction: '',\n    }).render().appendTo(this.options.container);\n\n    this.$popover.hide();\n    this.$content = this.$popover.find('.popover-content,.note-popover-content');\n    this.$content.on('click', '.note-hint-item', (e) => {\n      this.$content.find('.active').removeClass('active');\n      $(e.currentTarget).addClass('active');\n      this.replace();\n    });\n\n    this.$popover.on('mousedown', (e) => { e.preventDefault(); });\n  }\n\n  destroy() {\n    this.$popover.remove();\n  }\n\n  selectItem($item) {\n    this.$content.find('.active').removeClass('active');\n    $item.addClass('active');\n\n    this.$content[0].scrollTop = $item[0].offsetTop - (this.$content.innerHeight() / 2);\n  }\n\n  moveDown() {\n    const $current = this.$content.find('.note-hint-item.active');\n    const $next = $current.next();\n\n    if ($next.length) {\n      this.selectItem($next);\n    } else {\n      let $nextGroup = $current.parent().next();\n\n      if (!$nextGroup.length) {\n        $nextGroup = this.$content.find('.note-hint-group').first();\n      }\n\n      this.selectItem($nextGroup.find('.note-hint-item').first());\n    }\n  }\n\n  moveUp() {\n    const $current = this.$content.find('.note-hint-item.active');\n    const $prev = $current.prev();\n\n    if ($prev.length) {\n      this.selectItem($prev);\n    } else {\n      let $prevGroup = $current.parent().prev();\n\n      if (!$prevGroup.length) {\n        $prevGroup = this.$content.find('.note-hint-group').last();\n      }\n\n      this.selectItem($prevGroup.find('.note-hint-item').last());\n    }\n  }\n\n  replace() {\n    const $item = this.$content.find('.note-hint-item.active');\n\n    if ($item.length) {\n      var node = this.nodeFromItem($item);\n      // If matchingWord length = 0 -> capture OK / open hint / but as mention capture \"\" (\\w*)\n      if (this.matchingWord !== null && this.matchingWord.length === 0) {\n        this.lastWordRange.so = this.lastWordRange.eo;\n      // Else si > 0 and normal case -> adjust range \"before\" for correct position of insertion\n      } else if (this.matchingWord !== null && this.matchingWord.length > 0 && !this.lastWordRange.isCollapsed()) {\n        let rangeCompute = this.lastWordRange.eo - this.lastWordRange.so - this.matchingWord.length;\n        if (rangeCompute > 0) {\n          this.lastWordRange.so += rangeCompute;\n        }\n      }\n      this.lastWordRange.insertNode(node);\n\n      if (this.options.hintSelect === 'next') {\n        var blank = document.createTextNode('');\n        $(node).after(blank);\n        range.createFromNodeBefore(blank).select();\n      } else {\n        range.createFromNodeAfter(node).select();\n      }\n\n      this.lastWordRange = null;\n      this.hide();\n      this.context.invoke('editor.focus');\n    }\n  }\n\n  nodeFromItem($item) {\n    const hint = this.hints[$item.data('index')];\n    const item = $item.data('item');\n    let node = hint.content ? hint.content(item) : item;\n    if (typeof node === 'string') {\n      node = dom.createText(node);\n    }\n    return node;\n  }\n\n  createItemTemplates(hintIdx, items) {\n    const hint = this.hints[hintIdx];\n    return items.map((item, idx) => {\n      const $item = $('<div class=\"note-hint-item\"/>');\n      $item.append(hint.template ? hint.template(item) : item + '');\n      $item.data({\n        'index': hintIdx,\n        'item': item,\n      });\n      return $item;\n    });\n  }\n\n  handleKeydown(e) {\n    if (!this.$popover.is(':visible')) {\n      return;\n    }\n\n    if (e.keyCode === key.code.ENTER) {\n      e.preventDefault();\n      this.replace();\n    } else if (e.keyCode === key.code.UP) {\n      e.preventDefault();\n      this.moveUp();\n    } else if (e.keyCode === key.code.DOWN) {\n      e.preventDefault();\n      this.moveDown();\n    }\n  }\n\n  searchKeyword(index, keyword, callback) {\n    const hint = this.hints[index];\n    if (hint && hint.match.test(keyword) && hint.search) {\n      const matches = hint.match.exec(keyword);\n      this.matchingWord = matches[0];\n      hint.search(matches[1], callback);\n    } else {\n      callback();\n    }\n  }\n\n  createGroup(idx, keyword) {\n    const $group = $('<div class=\"note-hint-group note-hint-group-' + idx + '\"/>');\n    this.searchKeyword(idx, keyword, (items) => {\n      items = items || [];\n      if (items.length) {\n        $group.html(this.createItemTemplates(idx, items));\n        this.show();\n      }\n    });\n\n    return $group;\n  }\n\n  handleKeyup(e) {\n    if (!lists.contains([key.code.ENTER, key.code.UP, key.code.DOWN], e.keyCode)) {\n      let range = this.context.invoke('editor.getLastRange');\n      let wordRange, keyword;\n      if (this.options.hintMode === 'words') {\n        wordRange = range.getWordsRange(range);\n        keyword = wordRange.toString();\n\n        this.hints.forEach((hint) => {\n          if (hint.match.test(keyword)) {\n            wordRange = range.getWordsMatchRange(hint.match);\n            return false;\n          }\n        });\n\n        if (!wordRange) {\n          this.hide();\n          return;\n        }\n\n        keyword = wordRange.toString();\n      } else {\n        wordRange = range.getWordRange();\n        keyword = wordRange.toString();\n      }\n\n      if (this.hints.length && keyword) {\n        this.$content.empty();\n\n        const bnd = func.rect2bnd(lists.last(wordRange.getClientRects()));\n        const containerOffset = $(this.options.container).offset();\n        if (bnd) {\n          bnd.top -= containerOffset.top;\n          bnd.left -= containerOffset.left;\n\n          this.$popover.hide();\n          this.lastWordRange = wordRange;\n          this.hints.forEach((hint, idx) => {\n            if (hint.match.test(keyword)) {\n              this.createGroup(idx, keyword).appendTo(this.$content);\n            }\n          });\n          // select first .note-hint-item\n          this.$content.find('.note-hint-item:first').addClass('active');\n\n          // set position for popover after group is created\n          if (this.direction === 'top') {\n            this.$popover.css({\n              left: bnd.left,\n              top: bnd.top - this.$popover.outerHeight() - POPOVER_DIST,\n            });\n          } else {\n            this.$popover.css({\n              left: bnd.left,\n              top: bnd.top + bnd.height + POPOVER_DIST,\n            });\n          }\n        }\n      } else {\n        this.hide();\n      }\n    }\n  }\n\n  show() {\n    this.$popover.show();\n  }\n\n  hide() {\n    this.$popover.hide();\n  }\n}\n", "import dom from '../core/dom';\n\n/**\n * textarea auto sync.\n */\nexport default class AutoSync {\n  constructor(context) {\n    this.$note = context.layoutInfo.note;\n    this.events = {\n      'summernote.change': () => {\n        this.$note.val(context.invoke('code'));\n      },\n    };\n  }\n\n  shouldInitialize() {\n    return dom.isTextarea(this.$note[0]);\n  }\n}\n", "import lists from '../core/lists';\nimport dom from '../core/dom';\nimport key from '../core/key';\n\nexport default class AutoReplace {\n  constructor(context) {\n    this.context = context;\n    this.options = context.options.replace || {};\n\n    this.keys = [key.code.ENTER, key.code.SPACE, key.code.PERIOD, key.code.COMMA, key.code.SEMICOLON, key.code.SLASH];\n    this.previousKeydownCode = null;\n\n    this.events = {\n      'summernote.keyup': (we, e) => {\n        if (!e.isDefaultPrevented()) {\n          this.handleKeyup(e);\n        }\n      },\n      'summernote.keydown': (we, e) => {\n        this.handleKeydown(e);\n      },\n    };\n  }\n\n  shouldInitialize() {\n    return !!this.options.match;\n  }\n\n  initialize() {\n    this.lastWord = null;\n  }\n\n  destroy() {\n    this.lastWord = null;\n  }\n\n  replace() {\n    if (!this.lastWord) {\n      return;\n    }\n\n    const self = this;\n    const keyword = this.lastWord.toString();\n    this.options.match(keyword, function(match) {\n      if (match) {\n        let node = '';\n\n        if (typeof match === 'string') {\n          node = dom.createText(match);\n        } else if (match instanceof jQuery) {\n          node = match[0];\n        } else if (match instanceof Node) {\n          node = match;\n        }\n\n        if (!node) return;\n        self.lastWord.insertNode(node);\n        self.lastWord = null;\n        self.context.invoke('editor.focus');\n      }\n    });\n  }\n\n  handleKeydown(e) {\n    // this forces it to remember the last whole word, even if multiple termination keys are pressed\n    // before the previous key is let go.\n    if (this.previousKeydownCode && lists.contains(this.keys, this.previousKeydownCode)) {\n      this.previousKeydownCode = e.keyCode;\n      return;\n    }\n\n    if (lists.contains(this.keys, e.keyCode)) {\n      const wordRange = this.context.invoke('editor.createRange').getWordRange();\n      this.lastWord = wordRange;\n    }\n    this.previousKeydownCode = e.keyCode;\n  }\n\n  handleKeyup(e) {\n    if (lists.contains(this.keys, e.keyCode)) {\n      this.replace();\n    }\n  }\n}\n", "import $ from 'jquery';\nexport default class Placeholder {\n  constructor(context) {\n    this.context = context;\n\n    this.$editingArea = context.layoutInfo.editingArea;\n    this.options = context.options;\n\n    if (this.options.inheritPlaceholder === true) {\n      // get placeholder value from the original element\n      this.options.placeholder = this.context.$note.attr('placeholder') || this.options.placeholder;\n    }\n\n    this.events = {\n      'summernote.init summernote.change': () => {\n        this.update();\n      },\n      'summernote.codeview.toggled': () => {\n        this.update();\n      },\n    };\n  }\n\n  shouldInitialize() {\n    return !!this.options.placeholder;\n  }\n\n  initialize() {\n    this.$placeholder = $('<div class=\"note-placeholder\">');\n    this.$placeholder.on('click', () => {\n      this.context.invoke('focus');\n    }).html(this.options.placeholder).prependTo(this.$editingArea);\n\n    this.update();\n  }\n\n  destroy() {\n    this.$placeholder.remove();\n  }\n\n  update() {\n    const isShow = !this.context.invoke('codeview.isActivated') && this.context.invoke('editor.isEmpty');\n    this.$placeholder.toggle(isShow);\n  }\n}\n", "import $ from 'jquery';\nimport func from '../core/func';\nimport lists from '../core/lists';\nimport env from '../core/env';\n\nexport default class Buttons {\n  constructor(context) {\n    this.ui = $.summernote.ui;\n    this.context = context;\n    this.$toolbar = context.layoutInfo.toolbar;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n    this.invertedKeyMap = func.invertObject(\n      this.options.keyMap[env.isMac ? 'mac' : 'pc']\n    );\n  }\n\n  representShortcut(editorMethod) {\n    let shortcut = this.invertedKeyMap[editorMethod];\n    if (!this.options.shortcuts || !shortcut) {\n      return '';\n    }\n\n    if (env.isMac) {\n      shortcut = shortcut.replace('CMD', '⌘').replace('SHIFT', '⇧');\n    }\n\n    shortcut = shortcut.replace('BACKSLASH', '\\\\')\n      .replace('SLASH', '/')\n      .replace('LEFTBRACKET', '[')\n      .replace('RIGHTBRACKET', ']');\n\n    return ' (' + shortcut + ')';\n  }\n\n  button(o) {\n    if (!this.options.tooltip && o.tooltip) {\n      delete o.tooltip;\n    }\n    o.container = this.options.container;\n    return this.ui.button(o);\n  }\n\n  initialize() {\n    this.addToolbarButtons();\n    this.addImagePopoverButtons();\n    this.addLinkPopoverButtons();\n    this.addTablePopoverButtons();\n    this.fontInstalledMap = {};\n  }\n\n  destroy() {\n    delete this.fontInstalledMap;\n  }\n\n  isFontInstalled(name) {\n    if (!this.fontInstalledMap.hasOwnProperty(name)) {\n      this.fontInstalledMap[name] = env.isFontInstalled(name) ||\n        lists.contains(this.options.fontNamesIgnoreCheck, name);\n    }\n    return this.fontInstalledMap[name];\n  }\n\n  isFontDeservedToAdd(name) {\n    name = name.toLowerCase();\n    return (name !== '' && this.isFontInstalled(name) && env.genericFontFamilies.indexOf(name) === -1);\n  }\n\n  colorPalette(className, tooltip, backColor, foreColor) {\n    return this.ui.buttonGroup({\n      className: 'note-color ' + className,\n      children: [\n        this.button({\n          className: 'note-current-color-button',\n          contents: this.ui.icon(this.options.icons.font + ' note-recent-color'),\n          tooltip: tooltip,\n          click: (e) => {\n            const $button = $(e.currentTarget);\n            if (backColor && foreColor) {\n              this.context.invoke('editor.color', {\n                backColor: $button.attr('data-backColor'),\n                foreColor: $button.attr('data-foreColor'),\n              });\n            } else if (backColor) {\n              this.context.invoke('editor.color', {\n                backColor: $button.attr('data-backColor'),\n              });\n            } else if (foreColor) {\n              this.context.invoke('editor.color', {\n                foreColor: $button.attr('data-foreColor'),\n              });\n            }\n          },\n          callback: ($button) => {\n            const $recentColor = $button.find('.note-recent-color');\n            if (backColor) {\n              $recentColor.css('background-color', this.options.colorButton.backColor);\n              $button.attr('data-backColor', this.options.colorButton.backColor);\n            }\n            if (foreColor) {\n              $recentColor.css('color', this.options.colorButton.foreColor);\n              $button.attr('data-foreColor', this.options.colorButton.foreColor);\n            } else {\n              $recentColor.css('color', 'transparent');\n            }\n          },\n        }),\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents('', this.options),\n          tooltip: this.lang.color.more,\n          data: {\n            toggle: 'dropdown',\n          },\n        }),\n        this.ui.dropdown({\n          items: (backColor ? [\n            '<div class=\"note-palette\">',\n              '<div class=\"note-palette-title\">' + this.lang.color.background + '</div>',\n              '<div>',\n                '<button type=\"button\" class=\"note-color-reset btn btn-light\" data-event=\"backColor\" data-value=\"inherit\">',\n                  this.lang.color.transparent,\n                '</button>',\n              '</div>',\n              '<div class=\"note-holder\" data-event=\"backColor\"/>',\n              '<div>',\n                '<button type=\"button\" class=\"note-color-select btn\" data-event=\"openPalette\" data-value=\"backColorPicker\">',\n                  this.lang.color.cpSelect,\n                '</button>',\n                '<input type=\"color\" id=\"backColorPicker\" class=\"note-btn note-color-select-btn\" value=\"' + this.options.colorButton.backColor + '\" data-event=\"backColorPalette\">',\n              '</div>',\n              '<div class=\"note-holder-custom\" id=\"backColorPalette\" data-event=\"backColor\"/>',\n            '</div>',\n          ].join('') : '') +\n          (foreColor ? [\n            '<div class=\"note-palette\">',\n              '<div class=\"note-palette-title\">' + this.lang.color.foreground + '</div>',\n              '<div>',\n                '<button type=\"button\" class=\"note-color-reset btn btn-light\" data-event=\"removeFormat\" data-value=\"foreColor\">',\n                  this.lang.color.resetToDefault,\n                '</button>',\n              '</div>',\n              '<div class=\"note-holder\" data-event=\"foreColor\"/>',\n              '<div>',\n                '<button type=\"button\" class=\"note-color-select btn\" data-event=\"openPalette\" data-value=\"foreColorPicker\">',\n                  this.lang.color.cpSelect,\n                '</button>',\n                '<input type=\"color\" id=\"foreColorPicker\" class=\"note-btn note-color-select-btn\" value=\"' + this.options.colorButton.foreColor + '\" data-event=\"foreColorPalette\">',\n              '</div>', // Fix missing Div, Commented to find easily if it's wrong\n              '<div class=\"note-holder-custom\" id=\"foreColorPalette\" data-event=\"foreColor\"/>',\n            '</div>',\n          ].join('') : ''),\n          callback: ($dropdown) => {\n            $dropdown.find('.note-holder').each((idx, item) => {\n              const $holder = $(item);\n              $holder.append(this.ui.palette({\n                colors: this.options.colors,\n                colorsName: this.options.colorsName,\n                eventName: $holder.data('event'),\n                container: this.options.container,\n                tooltip: this.options.tooltip,\n              }).render());\n            });\n            /* TODO: do we have to record recent custom colors within cookies? */\n            var customColors = [\n              ['#FFFFFF', '#FFFFFF', '#FFFFFF', '#FFFFFF', '#FFFFFF', '#FFFFFF', '#FFFFFF', '#FFFFFF'],\n            ];\n            $dropdown.find('.note-holder-custom').each((idx, item) => {\n              const $holder = $(item);\n              $holder.append(this.ui.palette({\n                colors: customColors,\n                colorsName: customColors,\n                eventName: $holder.data('event'),\n                container: this.options.container,\n                tooltip: this.options.tooltip,\n              }).render());\n            });\n            $dropdown.find('input[type=color]').each((idx, item) => {\n              $(item).change(function() {\n                const $chip = $dropdown.find('#' + $(this).data('event')).find('.note-color-btn').first();\n                const color = this.value.toUpperCase();\n                $chip.css('background-color', color)\n                  .attr('aria-label', color)\n                  .attr('data-value', color)\n                  .attr('data-original-title', color);\n                $chip.click();\n              });\n            });\n          },\n          click: (event) => {\n            event.stopPropagation();\n\n            const $parent = $('.' + className).find('.show');\n            const $button = $(event.target);\n            const eventName = $button.data('event');\n            let value = $button.attr('data-value');\n\n            if (eventName === 'openPalette') {\n              const $picker = $parent.find('#' + value);\n              const $palette = $($parent.find('#' + $picker.data('event')).find('.note-color-row')[0]);\n\n              // Shift palette chips\n              const $chip = $palette.find('.note-color-btn').last().detach();\n\n              // Set chip attributes\n              const color = $picker.val();\n              $chip.css('background-color', color)\n                .attr('aria-label', color)\n                .attr('data-value', color)\n                .attr('data-original-title', color);\n              $palette.prepend($chip);\n              $picker.click();\n            } else if (lists.contains(['backColor', 'foreColor'], eventName)) {\n              const key = eventName === 'backColor' ? 'background-color' : 'color';\n              const $color = $button.closest('.note-color').find('.note-recent-color');\n              const $currentButton = $button.closest('.note-color').find('.note-current-color-button');\n\n              $color.css(key, value);\n              $currentButton.attr('data-' + eventName, value);\n              this.context.invoke('editor.' + eventName, value);\n            }\n          },\n        }),\n      ],\n    }).render();\n  }\n\n  addToolbarButtons() {\n    this.context.memo('button.style', () => {\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents(\n            this.ui.icon(this.options.icons.magic), this.options\n          ),\n          tooltip: this.lang.style.style,\n          data: {\n            toggle: 'dropdown',\n          },\n        }),\n        this.ui.dropdown({\n          className: 'dropdown-style',\n          items: this.options.styleTags,\n          title: this.lang.style.style,\n          template: (item) => {\n            if (typeof item === 'string') {\n              item = { tag: item, title: (this.lang.style.hasOwnProperty(item) ? this.lang.style[item] : item) };\n            }\n\n            const tag = item.tag;\n            const title = item.title;\n            const style = item.style ? ' style=\"' + item.style + '\" ' : '';\n            const className = item.className ? ' class=\"' + item.className + '\"' : '';\n\n            return '<' + tag + style + className + '>' + title + '</' + tag + '>';\n          },\n          click: this.context.createInvokeHandler('editor.formatBlock'),\n        }),\n      ]).render();\n    });\n\n    for (let styleIdx = 0, styleLen = this.options.styleTags.length; styleIdx < styleLen; styleIdx++) {\n      const item = this.options.styleTags[styleIdx];\n\n      this.context.memo('button.style.' + item, () => {\n        return this.button({\n          className: 'note-btn-style-' + item,\n          contents: '<div data-value=\"' + item + '\">' + item.toUpperCase() + '</div>',\n          tooltip: this.lang.style[item],\n          click: this.context.createInvokeHandler('editor.formatBlock'),\n        }).render();\n      });\n    }\n\n    this.context.memo('button.bold', () => {\n      return this.button({\n        className: 'note-btn-bold',\n        contents: this.ui.icon(this.options.icons.bold),\n        tooltip: this.lang.font.bold + this.representShortcut('bold'),\n        click: this.context.createInvokeHandlerAndUpdateState('editor.bold'),\n      }).render();\n    });\n\n    this.context.memo('button.italic', () => {\n      return this.button({\n        className: 'note-btn-italic',\n        contents: this.ui.icon(this.options.icons.italic),\n        tooltip: this.lang.font.italic + this.representShortcut('italic'),\n        click: this.context.createInvokeHandlerAndUpdateState('editor.italic'),\n      }).render();\n    });\n\n    this.context.memo('button.underline', () => {\n      return this.button({\n        className: 'note-btn-underline',\n        contents: this.ui.icon(this.options.icons.underline),\n        tooltip: this.lang.font.underline + this.representShortcut('underline'),\n        click: this.context.createInvokeHandlerAndUpdateState('editor.underline'),\n      }).render();\n    });\n\n    this.context.memo('button.clear', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.eraser),\n        tooltip: this.lang.font.clear + this.representShortcut('removeFormat'),\n        click: this.context.createInvokeHandler('editor.removeFormat'),\n      }).render();\n    });\n\n    this.context.memo('button.strikethrough', () => {\n      return this.button({\n        className: 'note-btn-strikethrough',\n        contents: this.ui.icon(this.options.icons.strikethrough),\n        tooltip: this.lang.font.strikethrough + this.representShortcut('strikethrough'),\n        click: this.context.createInvokeHandlerAndUpdateState('editor.strikethrough'),\n      }).render();\n    });\n\n    this.context.memo('button.superscript', () => {\n      return this.button({\n        className: 'note-btn-superscript',\n        contents: this.ui.icon(this.options.icons.superscript),\n        tooltip: this.lang.font.superscript,\n        click: this.context.createInvokeHandlerAndUpdateState('editor.superscript'),\n      }).render();\n    });\n\n    this.context.memo('button.subscript', () => {\n      return this.button({\n        className: 'note-btn-subscript',\n        contents: this.ui.icon(this.options.icons.subscript),\n        tooltip: this.lang.font.subscript,\n        click: this.context.createInvokeHandlerAndUpdateState('editor.subscript'),\n      }).render();\n    });\n\n    this.context.memo('button.fontname', () => {\n      const styleInfo = this.context.invoke('editor.currentStyle');\n\n      if (this.options.addDefaultFonts) {\n        // Add 'default' fonts into the fontnames array if not exist\n        $.each(styleInfo['font-family'].split(','), (idx, fontname) => {\n          fontname = fontname.trim().replace(/['\"]+/g, '');\n          if (this.isFontDeservedToAdd(fontname)) {\n            if (this.options.fontNames.indexOf(fontname) === -1) {\n              this.options.fontNames.push(fontname);\n            }\n          }\n        });\n      }\n\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents(\n            '<span class=\"note-current-fontname\"/>', this.options\n          ),\n          tooltip: this.lang.font.name,\n          data: {\n            toggle: 'dropdown',\n          },\n        }),\n        this.ui.dropdownCheck({\n          className: 'dropdown-fontname',\n          checkClassName: this.options.icons.menuCheck,\n          items: this.options.fontNames.filter(this.isFontInstalled.bind(this)),\n          title: this.lang.font.name,\n          template: (item) => {\n            return '<span style=\"font-family: ' + env.validFontName(item) + '\">' + item + '</span>';\n          },\n          click: this.context.createInvokeHandlerAndUpdateState('editor.fontName'),\n        }),\n      ]).render();\n    });\n\n    this.context.memo('button.fontsize', () => {\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents('<span class=\"note-current-fontsize\"/>', this.options),\n          tooltip: this.lang.font.size,\n          data: {\n            toggle: 'dropdown',\n          },\n        }),\n        this.ui.dropdownCheck({\n          className: 'dropdown-fontsize',\n          checkClassName: this.options.icons.menuCheck,\n          items: this.options.fontSizes,\n          title: this.lang.font.size,\n          click: this.context.createInvokeHandlerAndUpdateState('editor.fontSize'),\n        }),\n      ]).render();\n    });\n\n    this.context.memo('button.fontsizeunit', () => {\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents('<span class=\"note-current-fontsizeunit\"/>', this.options),\n          tooltip: this.lang.font.sizeunit,\n          data: {\n            toggle: 'dropdown',\n          },\n        }),\n        this.ui.dropdownCheck({\n          className: 'dropdown-fontsizeunit',\n          checkClassName: this.options.icons.menuCheck,\n          items: this.options.fontSizeUnits,\n          title: this.lang.font.sizeunit,\n          click: this.context.createInvokeHandlerAndUpdateState('editor.fontSizeUnit'),\n        }),\n      ]).render();\n    });\n\n    this.context.memo('button.color', () => {\n      return this.colorPalette('note-color-all', this.lang.color.recent, true, true);\n    });\n\n    this.context.memo('button.forecolor', () => {\n      return this.colorPalette('note-color-fore', this.lang.color.foreground, false, true);\n    });\n\n    this.context.memo('button.backcolor', () => {\n      return this.colorPalette('note-color-back', this.lang.color.background, true, false);\n    });\n\n    this.context.memo('button.ul', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.unorderedlist),\n        tooltip: this.lang.lists.unordered + this.representShortcut('insertUnorderedList'),\n        click: this.context.createInvokeHandler('editor.insertUnorderedList'),\n      }).render();\n    });\n\n    this.context.memo('button.ol', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.orderedlist),\n        tooltip: this.lang.lists.ordered + this.representShortcut('insertOrderedList'),\n        click: this.context.createInvokeHandler('editor.insertOrderedList'),\n      }).render();\n    });\n\n    const justifyLeft = this.button({\n      contents: this.ui.icon(this.options.icons.alignLeft),\n      tooltip: this.lang.paragraph.left + this.representShortcut('justifyLeft'),\n      click: this.context.createInvokeHandler('editor.justifyLeft'),\n    });\n\n    const justifyCenter = this.button({\n      contents: this.ui.icon(this.options.icons.alignCenter),\n      tooltip: this.lang.paragraph.center + this.representShortcut('justifyCenter'),\n      click: this.context.createInvokeHandler('editor.justifyCenter'),\n    });\n\n    const justifyRight = this.button({\n      contents: this.ui.icon(this.options.icons.alignRight),\n      tooltip: this.lang.paragraph.right + this.representShortcut('justifyRight'),\n      click: this.context.createInvokeHandler('editor.justifyRight'),\n    });\n\n    const justifyFull = this.button({\n      contents: this.ui.icon(this.options.icons.alignJustify),\n      tooltip: this.lang.paragraph.justify + this.representShortcut('justifyFull'),\n      click: this.context.createInvokeHandler('editor.justifyFull'),\n    });\n\n    const outdent = this.button({\n      contents: this.ui.icon(this.options.icons.outdent),\n      tooltip: this.lang.paragraph.outdent + this.representShortcut('outdent'),\n      click: this.context.createInvokeHandler('editor.outdent'),\n    });\n\n    const indent = this.button({\n      contents: this.ui.icon(this.options.icons.indent),\n      tooltip: this.lang.paragraph.indent + this.representShortcut('indent'),\n      click: this.context.createInvokeHandler('editor.indent'),\n    });\n\n    this.context.memo('button.justifyLeft', func.invoke(justifyLeft, 'render'));\n    this.context.memo('button.justifyCenter', func.invoke(justifyCenter, 'render'));\n    this.context.memo('button.justifyRight', func.invoke(justifyRight, 'render'));\n    this.context.memo('button.justifyFull', func.invoke(justifyFull, 'render'));\n    this.context.memo('button.outdent', func.invoke(outdent, 'render'));\n    this.context.memo('button.indent', func.invoke(indent, 'render'));\n\n    this.context.memo('button.paragraph', () => {\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents(this.ui.icon(this.options.icons.alignLeft), this.options),\n          tooltip: this.lang.paragraph.paragraph,\n          data: {\n            toggle: 'dropdown',\n          },\n        }),\n        this.ui.dropdown([\n          this.ui.buttonGroup({\n            className: 'note-align',\n            children: [justifyLeft, justifyCenter, justifyRight, justifyFull],\n          }),\n          this.ui.buttonGroup({\n            className: 'note-list',\n            children: [outdent, indent],\n          }),\n        ]),\n      ]).render();\n    });\n\n    this.context.memo('button.height', () => {\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents(this.ui.icon(this.options.icons.textHeight), this.options),\n          tooltip: this.lang.font.height,\n          data: {\n            toggle: 'dropdown',\n          },\n        }),\n        this.ui.dropdownCheck({\n          items: this.options.lineHeights,\n          checkClassName: this.options.icons.menuCheck,\n          className: 'dropdown-line-height',\n          title: this.lang.font.height,\n          click: this.context.createInvokeHandler('editor.lineHeight'),\n        }),\n      ]).render();\n    });\n\n    this.context.memo('button.table', () => {\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents(this.ui.icon(this.options.icons.table), this.options),\n          tooltip: this.lang.table.table,\n          data: {\n            toggle: 'dropdown',\n          },\n        }),\n        this.ui.dropdown({\n          title: this.lang.table.table,\n          className: 'note-table',\n          items: [\n            '<div class=\"note-dimension-picker\">',\n              '<div class=\"note-dimension-picker-mousecatcher\" data-event=\"insertTable\" data-value=\"1x1\"/>',\n              '<div class=\"note-dimension-picker-highlighted\"/>',\n              '<div class=\"note-dimension-picker-unhighlighted\"/>',\n            '</div>',\n            '<div class=\"note-dimension-display\">1 x 1</div>',\n          ].join(''),\n        }),\n      ], {\n        callback: ($node) => {\n          const $catcher = $node.find('.note-dimension-picker-mousecatcher');\n          $catcher.css({\n            width: this.options.insertTableMaxSize.col + 'em',\n            height: this.options.insertTableMaxSize.row + 'em',\n          }).mousedown(this.context.createInvokeHandler('editor.insertTable'))\n            .on('mousemove', this.tableMoveHandler.bind(this));\n        },\n      }).render();\n    });\n\n    this.context.memo('button.link', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.link),\n        tooltip: this.lang.link.link + this.representShortcut('linkDialog.show'),\n        click: this.context.createInvokeHandler('linkDialog.show'),\n      }).render();\n    });\n\n    this.context.memo('button.picture', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.picture),\n        tooltip: this.lang.image.image,\n        click: this.context.createInvokeHandler('imageDialog.show'),\n      }).render();\n    });\n\n    this.context.memo('button.video', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.video),\n        tooltip: this.lang.video.video,\n        click: this.context.createInvokeHandler('videoDialog.show'),\n      }).render();\n    });\n\n    this.context.memo('button.hr', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.minus),\n        tooltip: this.lang.hr.insert + this.representShortcut('insertHorizontalRule'),\n        click: this.context.createInvokeHandler('editor.insertHorizontalRule'),\n      }).render();\n    });\n\n    this.context.memo('button.fullscreen', () => {\n      return this.button({\n        className: 'btn-fullscreen',\n        contents: this.ui.icon(this.options.icons.arrowsAlt),\n        tooltip: this.lang.options.fullscreen,\n        click: this.context.createInvokeHandler('fullscreen.toggle'),\n      }).render();\n    });\n\n    this.context.memo('button.codeview', () => {\n      return this.button({\n        className: 'btn-codeview',\n        contents: this.ui.icon(this.options.icons.code),\n        tooltip: this.lang.options.codeview,\n        click: this.context.createInvokeHandler('codeview.toggle'),\n      }).render();\n    });\n\n    this.context.memo('button.redo', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.redo),\n        tooltip: this.lang.history.redo + this.representShortcut('redo'),\n        click: this.context.createInvokeHandler('editor.redo'),\n      }).render();\n    });\n\n    this.context.memo('button.undo', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.undo),\n        tooltip: this.lang.history.undo + this.representShortcut('undo'),\n        click: this.context.createInvokeHandler('editor.undo'),\n      }).render();\n    });\n\n    this.context.memo('button.help', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.question),\n        tooltip: this.lang.options.help,\n        click: this.context.createInvokeHandler('helpDialog.show'),\n      }).render();\n    });\n  }\n\n  /**\n   * image: [\n   *   ['imageResize', ['resizeFull', 'resizeHalf', 'resizeQuarter', 'resizeNone']],\n   *   ['float', ['floatLeft', 'floatRight', 'floatNone']],\n   *   ['remove', ['removeMedia']],\n   * ],\n   */\n  addImagePopoverButtons() {\n    // Image Size Buttons\n    this.context.memo('button.resizeFull', () => {\n      return this.button({\n        contents: '<span class=\"note-fontsize-10\">100%</span>',\n        tooltip: this.lang.image.resizeFull,\n        click: this.context.createInvokeHandler('editor.resize', '1'),\n      }).render();\n    });\n    this.context.memo('button.resizeHalf', () => {\n      return this.button({\n        contents: '<span class=\"note-fontsize-10\">50%</span>',\n        tooltip: this.lang.image.resizeHalf,\n        click: this.context.createInvokeHandler('editor.resize', '0.5'),\n      }).render();\n    });\n    this.context.memo('button.resizeQuarter', () => {\n      return this.button({\n        contents: '<span class=\"note-fontsize-10\">25%</span>',\n        tooltip: this.lang.image.resizeQuarter,\n        click: this.context.createInvokeHandler('editor.resize', '0.25'),\n      }).render();\n    });\n    this.context.memo('button.resizeNone', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.rollback),\n        tooltip: this.lang.image.resizeNone,\n        click: this.context.createInvokeHandler('editor.resize', '0'),\n      }).render();\n    });\n\n    // Float Buttons\n    this.context.memo('button.floatLeft', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.floatLeft),\n        tooltip: this.lang.image.floatLeft,\n        click: this.context.createInvokeHandler('editor.floatMe', 'left'),\n      }).render();\n    });\n\n    this.context.memo('button.floatRight', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.floatRight),\n        tooltip: this.lang.image.floatRight,\n        click: this.context.createInvokeHandler('editor.floatMe', 'right'),\n      }).render();\n    });\n\n    this.context.memo('button.floatNone', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.rollback),\n        tooltip: this.lang.image.floatNone,\n        click: this.context.createInvokeHandler('editor.floatMe', 'none'),\n      }).render();\n    });\n\n    // Remove Buttons\n    this.context.memo('button.removeMedia', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.trash),\n        tooltip: this.lang.image.remove,\n        click: this.context.createInvokeHandler('editor.removeMedia'),\n      }).render();\n    });\n  }\n\n  addLinkPopoverButtons() {\n    this.context.memo('button.linkDialogShow', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.link),\n        tooltip: this.lang.link.edit,\n        click: this.context.createInvokeHandler('linkDialog.show'),\n      }).render();\n    });\n\n    this.context.memo('button.unlink', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.unlink),\n        tooltip: this.lang.link.unlink,\n        click: this.context.createInvokeHandler('editor.unlink'),\n      }).render();\n    });\n  }\n\n  /**\n   * table : [\n   *  ['add', ['addRowDown', 'addRowUp', 'addColLeft', 'addColRight']],\n   *  ['delete', ['deleteRow', 'deleteCol', 'deleteTable']]\n   * ],\n   */\n  addTablePopoverButtons() {\n    this.context.memo('button.addRowUp', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.rowAbove),\n        tooltip: this.lang.table.addRowAbove,\n        click: this.context.createInvokeHandler('editor.addRow', 'top'),\n      }).render();\n    });\n    this.context.memo('button.addRowDown', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.rowBelow),\n        tooltip: this.lang.table.addRowBelow,\n        click: this.context.createInvokeHandler('editor.addRow', 'bottom'),\n      }).render();\n    });\n    this.context.memo('button.addColLeft', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.colBefore),\n        tooltip: this.lang.table.addColLeft,\n        click: this.context.createInvokeHandler('editor.addCol', 'left'),\n      }).render();\n    });\n    this.context.memo('button.addColRight', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.colAfter),\n        tooltip: this.lang.table.addColRight,\n        click: this.context.createInvokeHandler('editor.addCol', 'right'),\n      }).render();\n    });\n    this.context.memo('button.deleteRow', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.rowRemove),\n        tooltip: this.lang.table.delRow,\n        click: this.context.createInvokeHandler('editor.deleteRow'),\n      }).render();\n    });\n    this.context.memo('button.deleteCol', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.colRemove),\n        tooltip: this.lang.table.delCol,\n        click: this.context.createInvokeHandler('editor.deleteCol'),\n      }).render();\n    });\n    this.context.memo('button.deleteTable', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.trash),\n        tooltip: this.lang.table.delTable,\n        click: this.context.createInvokeHandler('editor.deleteTable'),\n      }).render();\n    });\n  }\n\n  build($container, groups) {\n    for (let groupIdx = 0, groupLen = groups.length; groupIdx < groupLen; groupIdx++) {\n      const group = groups[groupIdx];\n      const groupName = Array.isArray(group) ? group[0] : group;\n      const buttons = Array.isArray(group) ? ((group.length === 1) ? [group[0]] : group[1]) : [group];\n\n      const $group = this.ui.buttonGroup({\n        className: 'note-' + groupName,\n      }).render();\n\n      for (let idx = 0, len = buttons.length; idx < len; idx++) {\n        const btn = this.context.memo('button.' + buttons[idx]);\n        if (btn) {\n          $group.append(typeof btn === 'function' ? btn(this.context) : btn);\n        }\n      }\n      $group.appendTo($container);\n    }\n  }\n\n  /**\n   * @param {jQuery} [$container]\n   */\n  updateCurrentStyle($container) {\n    const $cont = $container || this.$toolbar;\n\n    const styleInfo = this.context.invoke('editor.currentStyle');\n    this.updateBtnStates($cont, {\n      '.note-btn-bold': () => {\n        return styleInfo['font-bold'] === 'bold';\n      },\n      '.note-btn-italic': () => {\n        return styleInfo['font-italic'] === 'italic';\n      },\n      '.note-btn-underline': () => {\n        return styleInfo['font-underline'] === 'underline';\n      },\n      '.note-btn-subscript': () => {\n        return styleInfo['font-subscript'] === 'subscript';\n      },\n      '.note-btn-superscript': () => {\n        return styleInfo['font-superscript'] === 'superscript';\n      },\n      '.note-btn-strikethrough': () => {\n        return styleInfo['font-strikethrough'] === 'strikethrough';\n      },\n    });\n\n    if (styleInfo['font-family']) {\n      const fontNames = styleInfo['font-family'].split(',').map((name) => {\n        return name.replace(/[\\'\\\"]/g, '')\n          .replace(/\\s+$/, '')\n          .replace(/^\\s+/, '');\n      });\n      const fontName = lists.find(fontNames, this.isFontInstalled.bind(this));\n\n      $cont.find('.dropdown-fontname a').each((idx, item) => {\n        const $item = $(item);\n        // always compare string to avoid creating another func.\n        const isChecked = ($item.data('value') + '') === (fontName + '');\n        $item.toggleClass('checked', isChecked);\n      });\n      $cont.find('.note-current-fontname').text(fontName).css('font-family', fontName);\n    }\n\n    if (styleInfo['font-size']) {\n      const fontSize = styleInfo['font-size'];\n      $cont.find('.dropdown-fontsize a').each((idx, item) => {\n        const $item = $(item);\n        // always compare with string to avoid creating another func.\n        const isChecked = ($item.data('value') + '') === (fontSize + '');\n        $item.toggleClass('checked', isChecked);\n      });\n      $cont.find('.note-current-fontsize').text(fontSize);\n\n      const fontSizeUnit = styleInfo['font-size-unit'];\n      $cont.find('.dropdown-fontsizeunit a').each((idx, item) => {\n        const $item = $(item);\n        const isChecked = ($item.data('value') + '') === (fontSizeUnit + '');\n        $item.toggleClass('checked', isChecked);\n      });\n      $cont.find('.note-current-fontsizeunit').text(fontSizeUnit);\n    }\n\n    if (styleInfo['line-height']) {\n      const lineHeight = styleInfo['line-height'];\n      $cont.find('.dropdown-line-height li a').each((idx, item) => {\n        // always compare with string to avoid creating another func.\n        const isChecked = ($(item).data('value') + '') === (lineHeight + '');\n        this.className = isChecked ? 'checked' : '';\n      });\n    }\n  }\n\n  updateBtnStates($container, infos) {\n    $.each(infos, (selector, pred) => {\n      this.ui.toggleBtnActive($container.find(selector), pred());\n    });\n  }\n\n  tableMoveHandler(event) {\n    const PX_PER_EM = 18;\n    const $picker = $(event.target.parentNode); // target is mousecatcher\n    const $dimensionDisplay = $picker.next();\n    const $catcher = $picker.find('.note-dimension-picker-mousecatcher');\n    const $highlighted = $picker.find('.note-dimension-picker-highlighted');\n    const $unhighlighted = $picker.find('.note-dimension-picker-unhighlighted');\n\n    let posOffset;\n    // HTML5 with jQuery - e.offsetX is undefined in Firefox\n    if (event.offsetX === undefined) {\n      const posCatcher = $(event.target).offset();\n      posOffset = {\n        x: event.pageX - posCatcher.left,\n        y: event.pageY - posCatcher.top,\n      };\n    } else {\n      posOffset = {\n        x: event.offsetX,\n        y: event.offsetY,\n      };\n    }\n\n    const dim = {\n      c: Math.ceil(posOffset.x / PX_PER_EM) || 1,\n      r: Math.ceil(posOffset.y / PX_PER_EM) || 1,\n    };\n\n    $highlighted.css({ width: dim.c + 'em', height: dim.r + 'em' });\n    $catcher.data('value', dim.c + 'x' + dim.r);\n\n    if (dim.c > 3 && dim.c < this.options.insertTableMaxSize.col) {\n      $unhighlighted.css({ width: dim.c + 1 + 'em' });\n    }\n\n    if (dim.r > 3 && dim.r < this.options.insertTableMaxSize.row) {\n      $unhighlighted.css({ height: dim.r + 1 + 'em' });\n    }\n\n    $dimensionDisplay.html(dim.c + ' x ' + dim.r);\n  }\n}\n", "import $ from 'jquery';\nexport default class Toolbar {\n  constructor(context) {\n    this.context = context;\n\n    this.$window = $(window);\n    this.$document = $(document);\n\n    this.ui = $.summernote.ui;\n    this.$note = context.layoutInfo.note;\n    this.$editor = context.layoutInfo.editor;\n    this.$toolbar = context.layoutInfo.toolbar;\n    this.$editable = context.layoutInfo.editable;\n    this.$statusbar = context.layoutInfo.statusbar;\n    this.options = context.options;\n\n    this.isFollowing = false;\n    this.followScroll = this.followScroll.bind(this);\n  }\n\n  shouldInitialize() {\n    return !this.options.airMode;\n  }\n\n  initialize() {\n    this.options.toolbar = this.options.toolbar || [];\n\n    if (!this.options.toolbar.length) {\n      this.$toolbar.hide();\n    } else {\n      this.context.invoke('buttons.build', this.$toolbar, this.options.toolbar);\n    }\n\n    if (this.options.toolbarContainer) {\n      this.$toolbar.appendTo(this.options.toolbarContainer);\n    }\n\n    this.changeContainer(false);\n\n    this.$note.on('summernote.keyup summernote.mouseup summernote.change', () => {\n      this.context.invoke('buttons.updateCurrentStyle');\n    });\n\n    this.context.invoke('buttons.updateCurrentStyle');\n    if (this.options.followingToolbar) {\n      this.$window.on('scroll resize', this.followScroll);\n    }\n  }\n\n  destroy() {\n    this.$toolbar.children().remove();\n\n    if (this.options.followingToolbar) {\n      this.$window.off('scroll resize', this.followScroll);\n    }\n  }\n\n  followScroll() {\n    if (this.$editor.hasClass('fullscreen')) {\n      return false;\n    }\n\n    const editorHeight = this.$editor.outerHeight();\n    const editorWidth = this.$editor.width();\n    const toolbarHeight = this.$toolbar.height();\n    const statusbarHeight = this.$statusbar.height();\n\n    // check if the web app is currently using another static bar\n    let otherBarHeight = 0;\n    if (this.options.otherStaticBar) {\n      otherBarHeight = $(this.options.otherStaticBar).outerHeight();\n    }\n\n    const currentOffset = this.$document.scrollTop();\n    const editorOffsetTop = this.$editor.offset().top;\n    const editorOffsetBottom = editorOffsetTop + editorHeight;\n    const activateOffset = editorOffsetTop - otherBarHeight;\n    const deactivateOffsetBottom = editorOffsetBottom - otherBarHeight - toolbarHeight - statusbarHeight;\n\n    if (!this.isFollowing &&\n      (currentOffset > activateOffset) && (currentOffset < deactivateOffsetBottom - toolbarHeight)) {\n      this.isFollowing = true;\n      this.$toolbar.css({\n        position: 'fixed',\n        top: otherBarHeight,\n        width: editorWidth,\n        zIndex: 1000,\n      });\n      this.$editable.css({\n        marginTop: this.$toolbar.height() + 5,\n      });\n    } else if (this.isFollowing &&\n      ((currentOffset < activateOffset) || (currentOffset > deactivateOffsetBottom))) {\n      this.isFollowing = false;\n      this.$toolbar.css({\n        position: 'relative',\n        top: 0,\n        width: '100%',\n        zIndex: 'auto',\n      });\n      this.$editable.css({\n        marginTop: '',\n      });\n    }\n  }\n\n  changeContainer(isFullscreen) {\n    if (isFullscreen) {\n      this.$toolbar.prependTo(this.$editor);\n    } else {\n      if (this.options.toolbarContainer) {\n        this.$toolbar.appendTo(this.options.toolbarContainer);\n      }\n    }\n    if (this.options.followingToolbar) {\n      this.followScroll();\n    }\n  }\n\n  updateFullscreen(isFullscreen) {\n    this.ui.toggleBtnActive(this.$toolbar.find('.btn-fullscreen'), isFullscreen);\n\n    this.changeContainer(isFullscreen);\n  }\n\n  updateCodeview(isCodeview) {\n    this.ui.toggleBtnActive(this.$toolbar.find('.btn-codeview'), isCodeview);\n    if (isCodeview) {\n      this.deactivate();\n    } else {\n      this.activate();\n    }\n  }\n\n  activate(isIncludeCodeview) {\n    let $btn = this.$toolbar.find('button');\n    if (!isIncludeCodeview) {\n      $btn = $btn.not('.btn-codeview').not('.btn-fullscreen');\n    }\n    this.ui.toggleBtn($btn, true);\n  }\n\n  deactivate(isIncludeCodeview) {\n    let $btn = this.$toolbar.find('button');\n    if (!isIncludeCodeview) {\n      $btn = $btn.not('.btn-codeview').not('.btn-fullscreen');\n    }\n    this.ui.toggleBtn($btn, false);\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport key from '../core/key';\nimport func from '../core/func';\n\nexport default class LinkDialog {\n  constructor(context) {\n    this.context = context;\n\n    this.ui = $.summernote.ui;\n    this.$body = $(document.body);\n    this.$editor = context.layoutInfo.editor;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n\n    context.memo('help.linkDialog.show', this.options.langInfo.help['linkDialog.show']);\n  }\n\n  initialize() {\n    const $container = this.options.dialogsInBody ? this.$body : this.options.container;\n    const body = [\n      '<div class=\"form-group note-form-group\">',\n        `<label for=\"note-dialog-link-txt-${this.options.id}\" class=\"note-form-label\">${this.lang.link.textToDisplay}</label>`,\n        `<input id=\"note-dialog-link-txt-${this.options.id}\" class=\"note-link-text form-control note-form-control note-input\" type=\"text\"/>`,\n      '</div>',\n      '<div class=\"form-group note-form-group\">',\n        `<label for=\"note-dialog-link-url-${this.options.id}\" class=\"note-form-label\">${this.lang.link.url}</label>`,\n        `<input id=\"note-dialog-link-url-${this.options.id}\" class=\"note-link-url form-control note-form-control note-input\" type=\"text\" value=\"http://\"/>`,\n      '</div>',\n      !this.options.disableLinkTarget\n        ? $('<div/>').append(this.ui.checkbox({\n          className: 'sn-checkbox-open-in-new-window',\n          text: this.lang.link.openInNewWindow,\n          checked: true,\n        }).render()).html()\n        : '',\n      $('<div/>').append(this.ui.checkbox({\n        className: 'sn-checkbox-use-protocol',\n        text: this.lang.link.useProtocol,\n        checked: true,\n      }).render()).html(),\n    ].join('');\n\n    const buttonClass = 'btn btn-primary note-btn note-btn-primary note-link-btn';\n    const footer = `<input type=\"button\" href=\"#\" class=\"${buttonClass}\" value=\"${this.lang.link.insert}\" disabled>`;\n\n    this.$dialog = this.ui.dialog({\n      className: 'link-dialog',\n      title: this.lang.link.insert,\n      fade: this.options.dialogsFade,\n      body: body,\n      footer: footer,\n    }).render().appendTo($container);\n  }\n\n  destroy() {\n    this.ui.hideDialog(this.$dialog);\n    this.$dialog.remove();\n  }\n\n  bindEnterKey($input, $btn) {\n    $input.on('keypress', (event) => {\n      if (event.keyCode === key.code.ENTER) {\n        event.preventDefault();\n        $btn.trigger('click');\n      }\n    });\n  }\n\n  /**\n   * toggle update button\n   */\n  toggleLinkBtn($linkBtn, $linkText, $linkUrl) {\n    this.ui.toggleBtn($linkBtn, $linkText.val() && $linkUrl.val());\n  }\n\n  /**\n   * Show link dialog and set event handlers on dialog controls.\n   *\n   * @param {Object} linkInfo\n   * @return {Promise}\n   */\n  showLinkDialog(linkInfo) {\n    return $.Deferred((deferred) => {\n      const $linkText = this.$dialog.find('.note-link-text');\n      const $linkUrl = this.$dialog.find('.note-link-url');\n      const $linkBtn = this.$dialog.find('.note-link-btn');\n      const $openInNewWindow = this.$dialog\n        .find('.sn-checkbox-open-in-new-window input[type=checkbox]');\n      const $useProtocol = this.$dialog\n        .find('.sn-checkbox-use-protocol input[type=checkbox]');\n\n      this.ui.onDialogShown(this.$dialog, () => {\n        this.context.triggerEvent('dialog.shown');\n\n        // If no url was given and given text is valid URL then copy that into URL Field\n        if (!linkInfo.url && func.isValidUrl(linkInfo.text)) {\n          linkInfo.url = linkInfo.text;\n        }\n\n        $linkText.on('input paste propertychange', () => {\n          // If linktext was modified by input events,\n          // cloning text from linkUrl will be stopped.\n          linkInfo.text = $linkText.val();\n          this.toggleLinkBtn($linkBtn, $linkText, $linkUrl);\n        }).val(linkInfo.text);\n\n        $linkUrl.on('input paste propertychange', () => {\n          // Display same text on `Text to display` as default\n          // when linktext has no text\n          if (!linkInfo.text) {\n            $linkText.val($linkUrl.val());\n          }\n          this.toggleLinkBtn($linkBtn, $linkText, $linkUrl);\n        }).val(linkInfo.url);\n\n        if (!env.isSupportTouch) {\n          $linkUrl.trigger('focus');\n        }\n\n        this.toggleLinkBtn($linkBtn, $linkText, $linkUrl);\n        this.bindEnterKey($linkUrl, $linkBtn);\n        this.bindEnterKey($linkText, $linkBtn);\n\n        const isNewWindowChecked = linkInfo.isNewWindow !== undefined\n          ? linkInfo.isNewWindow : this.context.options.linkTargetBlank;\n\n        $openInNewWindow.prop('checked', isNewWindowChecked);\n\n        const useProtocolChecked = linkInfo.url\n          ? false : this.context.options.useProtocol;\n\n        $useProtocol.prop('checked', useProtocolChecked);\n\n        $linkBtn.one('click', (event) => {\n          event.preventDefault();\n\n          deferred.resolve({\n            range: linkInfo.range,\n            url: $linkUrl.val(),\n            text: $linkText.val(),\n            isNewWindow: $openInNewWindow.is(':checked'),\n            checkProtocol: $useProtocol.is(':checked'),\n          });\n          this.ui.hideDialog(this.$dialog);\n        });\n      });\n\n      this.ui.onDialogHidden(this.$dialog, () => {\n        // detach events\n        $linkText.off();\n        $linkUrl.off();\n        $linkBtn.off();\n\n        if (deferred.state() === 'pending') {\n          deferred.reject();\n        }\n      });\n\n      this.ui.showDialog(this.$dialog);\n    }).promise();\n  }\n\n  /**\n   * @param {Object} layoutInfo\n   */\n  show() {\n    const linkInfo = this.context.invoke('editor.getLinkInfo');\n\n    this.context.invoke('editor.saveRange');\n    this.showLinkDialog(linkInfo).then((linkInfo) => {\n      this.context.invoke('editor.restoreRange');\n      this.context.invoke('editor.createLink', linkInfo);\n    }).fail(() => {\n      this.context.invoke('editor.restoreRange');\n    });\n  }\n}\n", "import $ from 'jquery';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\n\nexport default class LinkPopover {\n  constructor(context) {\n    this.context = context;\n\n    this.ui = $.summernote.ui;\n    this.options = context.options;\n    this.events = {\n      'summernote.keyup summernote.mouseup summernote.change summernote.scroll': () => {\n        this.update();\n      },\n      'summernote.disable summernote.dialog.shown summernote.blur': () => {\n        this.hide();\n      },\n    };\n  }\n\n  shouldInitialize() {\n    return !lists.isEmpty(this.options.popover.link);\n  }\n\n  initialize() {\n    this.$popover = this.ui.popover({\n      className: 'note-link-popover',\n      callback: ($node) => {\n        const $content = $node.find('.popover-content,.note-popover-content');\n        $content.prepend('<span><a target=\"_blank\"></a>&nbsp;</span>');\n      },\n    }).render().appendTo(this.options.container);\n    const $content = this.$popover.find('.popover-content,.note-popover-content');\n\n    this.context.invoke('buttons.build', $content, this.options.popover.link);\n\n    this.$popover.on('mousedown', (e) => { e.preventDefault(); });\n  }\n\n  destroy() {\n    this.$popover.remove();\n  }\n\n  update() {\n    // Prevent focusing on editable when invoke('code') is executed\n    if (!this.context.invoke('editor.hasFocus')) {\n      this.hide();\n      return;\n    }\n\n    const rng = this.context.invoke('editor.getLastRange');\n    if (rng.isCollapsed() && rng.isOnAnchor()) {\n      const anchor = dom.ancestor(rng.sc, dom.isAnchor);\n      const href = $(anchor).attr('href');\n      this.$popover.find('a').attr('href', href).html(href);\n\n      const pos = dom.posFromPlaceholder(anchor);\n      const containerOffset = $(this.options.container).offset();\n      pos.top -= containerOffset.top;\n      pos.left -= containerOffset.left;\n\n      this.$popover.css({\n        display: 'block',\n        left: pos.left,\n        top: pos.top,\n      });\n    } else {\n      this.hide();\n    }\n  }\n\n  hide() {\n    this.$popover.hide();\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport key from '../core/key';\n\nexport default class ImageDialog {\n  constructor(context) {\n    this.context = context;\n    this.ui = $.summernote.ui;\n    this.$body = $(document.body);\n    this.$editor = context.layoutInfo.editor;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n  }\n\n  initialize() {\n    let imageLimitation = '';\n    if (this.options.maximumImageFileSize) {\n      const unit = Math.floor(Math.log(this.options.maximumImageFileSize) / Math.log(1024));\n      const readableSize = (this.options.maximumImageFileSize / Math.pow(1024, unit)).toFixed(2) * 1 +\n                         ' ' + ' KMGTP'[unit] + 'B';\n      imageLimitation = `<small>${this.lang.image.maximumFileSize + ' : ' + readableSize}</small>`;\n    }\n\n    const $container = this.options.dialogsInBody ? this.$body : this.options.container;\n    const body = [\n      '<div class=\"form-group note-form-group note-group-select-from-files\">',\n        '<label for=\"note-dialog-image-file-' + this.options.id + '\" class=\"note-form-label\">' + this.lang.image.selectFromFiles + '</label>',\n        '<input id=\"note-dialog-image-file-' + this.options.id + '\" class=\"note-image-input form-control-file note-form-control note-input\" ',\n        ' type=\"file\" name=\"files\" accept=\"image/*\" multiple=\"multiple\"/>',\n        imageLimitation,\n      '</div>',\n      '<div class=\"form-group note-group-image-url\">',\n        '<label for=\"note-dialog-image-url-' + this.options.id + '\" class=\"note-form-label\">' + this.lang.image.url + '</label>',\n        '<input id=\"note-dialog-image-url-' + this.options.id + '\" class=\"note-image-url form-control note-form-control note-input\" type=\"text\"/>',\n      '</div>',\n    ].join('');\n    const buttonClass = 'btn btn-primary note-btn note-btn-primary note-image-btn';\n    const footer = `<input type=\"button\" href=\"#\" class=\"${buttonClass}\" value=\"${this.lang.image.insert}\" disabled>`;\n\n    this.$dialog = this.ui.dialog({\n      title: this.lang.image.insert,\n      fade: this.options.dialogsFade,\n      body: body,\n      footer: footer,\n    }).render().appendTo($container);\n  }\n\n  destroy() {\n    this.ui.hideDialog(this.$dialog);\n    this.$dialog.remove();\n  }\n\n  bindEnterKey($input, $btn) {\n    $input.on('keypress', (event) => {\n      if (event.keyCode === key.code.ENTER) {\n        event.preventDefault();\n        $btn.trigger('click');\n      }\n    });\n  }\n\n  show() {\n    this.context.invoke('editor.saveRange');\n    this.showImageDialog().then((data) => {\n      // [workaround] hide dialog before restore range for IE range focus\n      this.ui.hideDialog(this.$dialog);\n      this.context.invoke('editor.restoreRange');\n\n      if (typeof data === 'string') { // image url\n        // If onImageLinkInsert set,\n        if (this.options.callbacks.onImageLinkInsert) {\n          this.context.triggerEvent('image.link.insert', data);\n        } else {\n          this.context.invoke('editor.insertImage', data);\n        }\n      } else { // array of files\n        this.context.invoke('editor.insertImagesOrCallback', data);\n      }\n    }).fail(() => {\n      this.context.invoke('editor.restoreRange');\n    });\n  }\n\n  /**\n   * show image dialog\n   *\n   * @param {jQuery} $dialog\n   * @return {Promise}\n   */\n  showImageDialog() {\n    return $.Deferred((deferred) => {\n      const $imageInput = this.$dialog.find('.note-image-input');\n      const $imageUrl = this.$dialog.find('.note-image-url');\n      const $imageBtn = this.$dialog.find('.note-image-btn');\n\n      this.ui.onDialogShown(this.$dialog, () => {\n        this.context.triggerEvent('dialog.shown');\n\n        // Cloning imageInput to clear element.\n        $imageInput.replaceWith($imageInput.clone().on('change', (event) => {\n          deferred.resolve(event.target.files || event.target.value);\n        }).val(''));\n\n        $imageUrl.on('input paste propertychange', () => {\n          this.ui.toggleBtn($imageBtn, $imageUrl.val());\n        }).val('');\n\n        if (!env.isSupportTouch) {\n          $imageUrl.trigger('focus');\n        }\n\n        $imageBtn.click((event) => {\n          event.preventDefault();\n          deferred.resolve($imageUrl.val());\n        });\n\n        this.bindEnterKey($imageUrl, $imageBtn);\n      });\n\n      this.ui.onDialogHidden(this.$dialog, () => {\n        $imageInput.off();\n        $imageUrl.off();\n        $imageBtn.off();\n\n        if (deferred.state() === 'pending') {\n          deferred.reject();\n        }\n      });\n\n      this.ui.showDialog(this.$dialog);\n    });\n  }\n}\n", "import $ from 'jquery';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\n\n/**\n * Image popover module\n *  mouse events that show/hide popover will be handled by Handle.js.\n *  Handle.js will receive the events and invoke 'imagePopover.update'.\n */\nexport default class ImagePopover {\n  constructor(context) {\n    this.context = context;\n    this.ui = $.summernote.ui;\n\n    this.editable = context.layoutInfo.editable[0];\n    this.options = context.options;\n\n    this.events = {\n      'summernote.disable summernote.blur': () => {\n        this.hide();\n      },\n    };\n  }\n\n  shouldInitialize() {\n    return !lists.isEmpty(this.options.popover.image);\n  }\n\n  initialize() {\n    this.$popover = this.ui.popover({\n      className: 'note-image-popover',\n    }).render().appendTo(this.options.container);\n    const $content = this.$popover.find('.popover-content,.note-popover-content');\n    this.context.invoke('buttons.build', $content, this.options.popover.image);\n\n    this.$popover.on('mousedown', (e) => { e.preventDefault(); });\n  }\n\n  destroy() {\n    this.$popover.remove();\n  }\n\n  update(target, event) {\n    if (dom.isImg(target)) {\n      const position = $(target).offset();\n      const containerOffset = $(this.options.container).offset();\n      let pos = {};\n      if (this.options.popatmouse) {\n        pos.left = event.pageX - 20;\n        pos.top = event.pageY;\n      } else {\n        pos = position;\n      }\n      pos.top -= containerOffset.top;\n      pos.left -= containerOffset.left;\n\n      this.$popover.css({\n        display: 'block',\n        left: pos.left,\n        top: pos.top,\n      });\n    } else {\n      this.hide();\n    }\n  }\n\n  hide() {\n    this.$popover.hide();\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\n\nexport default class TablePopover {\n  constructor(context) {\n    this.context = context;\n\n    this.ui = $.summernote.ui;\n    this.options = context.options;\n    this.events = {\n      'summernote.mousedown': (we, e) => {\n        this.update(e.target);\n      },\n      'summernote.keyup summernote.scroll summernote.change': () => {\n        this.update();\n      },\n      'summernote.disable summernote.blur': () => {\n        this.hide();\n      },\n    };\n  }\n\n  shouldInitialize() {\n    return !lists.isEmpty(this.options.popover.table);\n  }\n\n  initialize() {\n    this.$popover = this.ui.popover({\n      className: 'note-table-popover',\n    }).render().appendTo(this.options.container);\n    const $content = this.$popover.find('.popover-content,.note-popover-content');\n\n    this.context.invoke('buttons.build', $content, this.options.popover.table);\n\n    // [workaround] Disable Firefox's default table editor\n    if (env.isFF) {\n      document.execCommand('enableInlineTableEditing', false, false);\n    }\n\n    this.$popover.on('mousedown', (e) => { e.preventDefault(); });\n  }\n\n  destroy() {\n    this.$popover.remove();\n  }\n\n  update(target) {\n    if (this.context.isDisabled()) {\n      return false;\n    }\n\n    const isCell = dom.isCell(target);\n\n    if (isCell) {\n      const pos = dom.posFromPlaceholder(target);\n      const containerOffset = $(this.options.container).offset();\n      pos.top -= containerOffset.top;\n      pos.left -= containerOffset.left;\n\n      this.$popover.css({\n        display: 'block',\n        left: pos.left,\n        top: pos.top,\n      });\n    } else {\n      this.hide();\n    }\n\n    return isCell;\n  }\n\n  hide() {\n    this.$popover.hide();\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport key from '../core/key';\n\nexport default class VideoDialog {\n  constructor(context) {\n    this.context = context;\n\n    this.ui = $.summernote.ui;\n    this.$body = $(document.body);\n    this.$editor = context.layoutInfo.editor;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n  }\n\n  initialize() {\n    const $container = this.options.dialogsInBody ? this.$body : this.options.container;\n    const body = [\n      '<div class=\"form-group note-form-group row-fluid\">',\n        `<label for=\"note-dialog-video-url-${this.options.id}\" class=\"note-form-label\">${this.lang.video.url} <small class=\"text-muted\">${this.lang.video.providers}</small></label>`,\n        `<input id=\"note-dialog-video-url-${this.options.id}\" class=\"note-video-url form-control note-form-control note-input\" type=\"text\"/>`,\n      '</div>',\n    ].join('');\n    const buttonClass = 'btn btn-primary note-btn note-btn-primary note-video-btn';\n    const footer = `<input type=\"button\" href=\"#\" class=\"${buttonClass}\" value=\"${this.lang.video.insert}\" disabled>`;\n\n    this.$dialog = this.ui.dialog({\n      title: this.lang.video.insert,\n      fade: this.options.dialogsFade,\n      body: body,\n      footer: footer,\n    }).render().appendTo($container);\n  }\n\n  destroy() {\n    this.ui.hideDialog(this.$dialog);\n    this.$dialog.remove();\n  }\n\n  bindEnterKey($input, $btn) {\n    $input.on('keypress', (event) => {\n      if (event.keyCode === key.code.ENTER) {\n        event.preventDefault();\n        $btn.trigger('click');\n      }\n    });\n  }\n\n  createVideoNode(url) {\n    // video url patterns(youtube, instagram, vimeo, dailymotion, youku, mp4, ogg, webm)\n    const ytRegExp = /\\/\\/(?:www\\.)?(?:youtu\\.be\\/|youtube\\.com\\/(?:embed\\/|v\\/|watch\\?v=|watch\\?.+&v=))([\\w|-]{11})(?:(?:[\\?&]t=)(\\S+))?$/;\n    const ytRegExpForStart = /^(?:(\\d+)h)?(?:(\\d+)m)?(?:(\\d+)s)?$/;\n    const ytMatch = url.match(ytRegExp);\n\n    const igRegExp = /(?:www\\.|\\/\\/)instagram\\.com\\/p\\/(.[a-zA-Z0-9_-]*)/;\n    const igMatch = url.match(igRegExp);\n\n    const vRegExp = /\\/\\/vine\\.co\\/v\\/([a-zA-Z0-9]+)/;\n    const vMatch = url.match(vRegExp);\n\n    const vimRegExp = /\\/\\/(player\\.)?vimeo\\.com\\/([a-z]*\\/)*(\\d+)[?]?.*/;\n    const vimMatch = url.match(vimRegExp);\n\n    const dmRegExp = /.+dailymotion.com\\/(video|hub)\\/([^_]+)[^#]*(#video=([^_&]+))?/;\n    const dmMatch = url.match(dmRegExp);\n\n    const youkuRegExp = /\\/\\/v\\.youku\\.com\\/v_show\\/id_(\\w+)=*\\.html/;\n    const youkuMatch = url.match(youkuRegExp);\n\n    const qqRegExp = /\\/\\/v\\.qq\\.com.*?vid=(.+)/;\n    const qqMatch = url.match(qqRegExp);\n\n    const qqRegExp2 = /\\/\\/v\\.qq\\.com\\/x?\\/?(page|cover).*?\\/([^\\/]+)\\.html\\??.*/;\n    const qqMatch2 = url.match(qqRegExp2);\n\n    const mp4RegExp = /^.+.(mp4|m4v)$/;\n    const mp4Match = url.match(mp4RegExp);\n\n    const oggRegExp = /^.+.(ogg|ogv)$/;\n    const oggMatch = url.match(oggRegExp);\n\n    const webmRegExp = /^.+.(webm)$/;\n    const webmMatch = url.match(webmRegExp);\n\n    const fbRegExp = /(?:www\\.|\\/\\/)facebook\\.com\\/([^\\/]+)\\/videos\\/([0-9]+)/;\n    const fbMatch = url.match(fbRegExp);\n\n    let $video;\n    if (ytMatch && ytMatch[1].length === 11) {\n      const youtubeId = ytMatch[1];\n      var start = 0;\n      if (typeof ytMatch[2] !== 'undefined') {\n        const ytMatchForStart = ytMatch[2].match(ytRegExpForStart);\n        if (ytMatchForStart) {\n          for (var n = [3600, 60, 1], i = 0, r = n.length; i < r; i++) {\n            start += (typeof ytMatchForStart[i + 1] !== 'undefined' ? n[i] * parseInt(ytMatchForStart[i + 1], 10) : 0);\n          }\n        }\n      }\n      $video = $('<iframe>')\n        .attr('frameborder', 0)\n        .attr('src', '//www.youtube.com/embed/' + youtubeId + (start > 0 ? '?start=' + start : ''))\n        .attr('width', '640').attr('height', '360');\n    } else if (igMatch && igMatch[0].length) {\n      $video = $('<iframe>')\n        .attr('frameborder', 0)\n        .attr('src', 'https://instagram.com/p/' + igMatch[1] + '/embed/')\n        .attr('width', '612').attr('height', '710')\n        .attr('scrolling', 'no')\n        .attr('allowtransparency', 'true');\n    } else if (vMatch && vMatch[0].length) {\n      $video = $('<iframe>')\n        .attr('frameborder', 0)\n        .attr('src', vMatch[0] + '/embed/simple')\n        .attr('width', '600').attr('height', '600')\n        .attr('class', 'vine-embed');\n    } else if (vimMatch && vimMatch[3].length) {\n      $video = $('<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>')\n        .attr('frameborder', 0)\n        .attr('src', '//player.vimeo.com/video/' + vimMatch[3])\n        .attr('width', '640').attr('height', '360');\n    } else if (dmMatch && dmMatch[2].length) {\n      $video = $('<iframe>')\n        .attr('frameborder', 0)\n        .attr('src', '//www.dailymotion.com/embed/video/' + dmMatch[2])\n        .attr('width', '640').attr('height', '360');\n    } else if (youkuMatch && youkuMatch[1].length) {\n      $video = $('<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>')\n        .attr('frameborder', 0)\n        .attr('height', '498')\n        .attr('width', '510')\n        .attr('src', '//player.youku.com/embed/' + youkuMatch[1]);\n    } else if ((qqMatch && qqMatch[1].length) || (qqMatch2 && qqMatch2[2].length)) {\n      const vid = ((qqMatch && qqMatch[1].length) ? qqMatch[1] : qqMatch2[2]);\n      $video = $('<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>')\n        .attr('frameborder', 0)\n        .attr('height', '310')\n        .attr('width', '500')\n        .attr('src', 'https://v.qq.com/iframe/player.html?vid=' + vid + '&amp;auto=0');\n    } else if (mp4Match || oggMatch || webmMatch) {\n      $video = $('<video controls>')\n        .attr('src', url)\n        .attr('width', '640').attr('height', '360');\n    } else if (fbMatch && fbMatch[0].length) {\n      $video = $('<iframe>')\n        .attr('frameborder', 0)\n        .attr('src', 'https://www.facebook.com/plugins/video.php?href=' + encodeURIComponent(fbMatch[0]) + '&show_text=0&width=560')\n        .attr('width', '560').attr('height', '301')\n        .attr('scrolling', 'no')\n        .attr('allowtransparency', 'true');\n    } else {\n      // this is not a known video link. Now what, Cat? Now what?\n      return false;\n    }\n\n    $video.addClass('note-video-clip');\n\n    return $video[0];\n  }\n\n  show() {\n    const text = this.context.invoke('editor.getSelectedText');\n    this.context.invoke('editor.saveRange');\n    this.showVideoDialog(text).then((url) => {\n      // [workaround] hide dialog before restore range for IE range focus\n      this.ui.hideDialog(this.$dialog);\n      this.context.invoke('editor.restoreRange');\n\n      // build node\n      const $node = this.createVideoNode(url);\n\n      if ($node) {\n        // insert video node\n        this.context.invoke('editor.insertNode', $node);\n      }\n    }).fail(() => {\n      this.context.invoke('editor.restoreRange');\n    });\n  }\n\n  /**\n   * show image dialog\n   *\n   * @param {jQuery} $dialog\n   * @return {Promise}\n   */\n  showVideoDialog(text) {\n    return $.Deferred((deferred) => {\n      const $videoUrl = this.$dialog.find('.note-video-url');\n      const $videoBtn = this.$dialog.find('.note-video-btn');\n\n      this.ui.onDialogShown(this.$dialog, () => {\n        this.context.triggerEvent('dialog.shown');\n\n        $videoUrl.on('input paste propertychange', () => {\n          this.ui.toggleBtn($videoBtn, $videoUrl.val());\n        });\n\n        if (!env.isSupportTouch) {\n          $videoUrl.trigger('focus');\n        }\n\n        $videoBtn.click((event) => {\n          event.preventDefault();\n          deferred.resolve($videoUrl.val());\n        });\n\n        this.bindEnterKey($videoUrl, $videoBtn);\n      });\n\n      this.ui.onDialogHidden(this.$dialog, () => {\n        $videoUrl.off();\n        $videoBtn.off();\n\n        if (deferred.state() === 'pending') {\n          deferred.reject();\n        }\n      });\n\n      this.ui.showDialog(this.$dialog);\n    });\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\n\nexport default class HelpDialog {\n  constructor(context) {\n    this.context = context;\n\n    this.ui = $.summernote.ui;\n    this.$body = $(document.body);\n    this.$editor = context.layoutInfo.editor;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n  }\n\n  initialize() {\n    const $container = this.options.dialogsInBody ? this.$body : this.options.container;\n    const body = [\n      '<p class=\"text-center\">',\n        '<a href=\"http://summernote.org/\" target=\"_blank\">Summernote @@VERSION@@</a> · ',\n        '<a href=\"https://github.com/summernote/summernote\" target=\"_blank\">Project</a> · ',\n        '<a href=\"https://github.com/summernote/summernote/issues\" target=\"_blank\">Issues</a>',\n      '</p>',\n    ].join('');\n\n    this.$dialog = this.ui.dialog({\n      title: this.lang.options.help,\n      fade: this.options.dialogsFade,\n      body: this.createShortcutList(),\n      footer: body,\n      callback: ($node) => {\n        $node.find('.modal-body,.note-modal-body').css({\n          'max-height': 300,\n          'overflow': 'scroll',\n        });\n      },\n    }).render().appendTo($container);\n  }\n\n  destroy() {\n    this.ui.hideDialog(this.$dialog);\n    this.$dialog.remove();\n  }\n\n  createShortcutList() {\n    const keyMap = this.options.keyMap[env.isMac ? 'mac' : 'pc'];\n    return Object.keys(keyMap).map((key) => {\n      const command = keyMap[key];\n      const $row = $('<div><div class=\"help-list-item\"/></div>');\n      $row.append($('<label><kbd>' + key + '</kdb></label>').css({\n        'width': 180,\n        'margin-right': 10,\n      })).append($('<span/>').html(this.context.memo('help.' + command) || command));\n      return $row.html();\n    }).join('');\n  }\n\n  /**\n   * show help dialog\n   *\n   * @return {Promise}\n   */\n  showHelpDialog() {\n    return $.Deferred((deferred) => {\n      this.ui.onDialogShown(this.$dialog, () => {\n        this.context.triggerEvent('dialog.shown');\n        deferred.resolve();\n      });\n      this.ui.showDialog(this.$dialog);\n    }).promise();\n  }\n\n  show() {\n    this.context.invoke('editor.saveRange');\n    this.showHelpDialog().then(() => {\n      this.context.invoke('editor.restoreRange');\n    });\n  }\n}\n", "import $ from 'jquery';\nimport func from '../core/func';\nimport lists from '../core/lists';\n\nconst AIR_MODE_POPOVER_X_OFFSET = 20;\n\nexport default class AirPopover {\n  constructor(context) {\n    this.context = context;\n    this.ui = $.summernote.ui;\n    this.options = context.options;\n\n    this.hidable = true;\n\n    this.events = {\n      'summernote.keyup summernote.mouseup summernote.scroll': () => {\n        if (this.options.editing) {\n          this.update();\n        }\n      },\n      'summernote.disable summernote.change summernote.dialog.shown summernote.blur': () => {\n        this.hide();\n      },\n      'summernote.focusout': (we, e) => {\n        if (!this.$popover.is(':active,:focus')) {\n          this.hide();\n        }\n      },\n    };\n  }\n\n  shouldInitialize() {\n    return this.options.airMode && !lists.isEmpty(this.options.popover.air);\n  }\n\n  initialize() {\n    this.$popover = this.ui.popover({\n      className: 'note-air-popover',\n    }).render().appendTo(this.options.container);\n    const $content = this.$popover.find('.popover-content');\n\n    this.context.invoke('buttons.build', $content, this.options.popover.air);\n\n    // disable hiding this popover preemptively by 'summernote.blur' event.\n    this.$popover.on('mousedown', () => { this.hidable = false; });\n    // (re-)enable hiding after 'summernote.blur' has been handled (aka. ignored).\n    this.$popover.on('mouseup', () => { this.hidable = true; });\n  }\n\n  destroy() {\n    this.$popover.remove();\n  }\n\n  update() {\n    const styleInfo = this.context.invoke('editor.currentStyle');\n    if (styleInfo.range && !styleInfo.range.isCollapsed()) {\n      const rect = lists.last(styleInfo.range.getClientRects());\n      if (rect) {\n        const bnd = func.rect2bnd(rect);\n\n        this.$popover.css({\n          display: 'block',\n          left: Math.max(bnd.left + bnd.width / 2, 0) - AIR_MODE_POPOVER_X_OFFSET,\n          top: bnd.top + bnd.height,\n        });\n        this.context.invoke('buttons.updateCurrentStyle', this.$popover);\n      }\n    } else {\n      this.hide();\n    }\n  }\n\n  hide() {\n    if (this.hidable) {\n      this.$popover.hide();\n    }\n  }\n}\n", "import $ from 'jquery';\n\nclass TooltipUI {\n  constructor($node, options) {\n    this.$node = $node;\n    this.options = $.extend({}, {\n      title: '',\n      target: options.container,\n      trigger: 'hover focus',\n      placement: 'bottom',\n    }, options);\n\n    // create tooltip node\n    this.$tooltip = $([\n      '<div class=\"note-tooltip\">',\n        '<div class=\"note-tooltip-arrow\"/>',\n        '<div class=\"note-tooltip-content\"/>',\n      '</div>',\n    ].join(''));\n\n    // define event\n    if (this.options.trigger !== 'manual') {\n      const showCallback = this.show.bind(this);\n      const hideCallback = this.hide.bind(this);\n      const toggleCallback = this.toggle.bind(this);\n\n      this.options.trigger.split(' ').forEach(function(eventName) {\n        if (eventName === 'hover') {\n          $node.off('mouseenter mouseleave');\n          $node.on('mouseenter', showCallback).on('mouseleave', hideCallback);\n        } else if (eventName === 'click') {\n          $node.on('click', toggleCallback);\n        } else if (eventName === 'focus') {\n          $node.on('focus', showCallback).on('blur', hideCallback);\n        }\n      });\n    }\n  }\n\n  show() {\n    const $node = this.$node;\n    const offset = $node.offset();\n    const targetOffset = $(this.options.target).offset();\n    offset.top -= targetOffset.top;\n    offset.left -= targetOffset.left;\n\n    const $tooltip = this.$tooltip;\n    const title = this.options.title || $node.attr('title') || $node.data('title');\n    const placement = this.options.placement || $node.data('placement');\n\n    $tooltip.addClass(placement);\n    $tooltip.find('.note-tooltip-content').text(title);\n    $tooltip.appendTo(this.options.target);\n\n    const nodeWidth = $node.outerWidth();\n    const nodeHeight = $node.outerHeight();\n    const tooltipWidth = $tooltip.outerWidth();\n    const tooltipHeight = $tooltip.outerHeight();\n\n    if (placement === 'bottom') {\n      $tooltip.css({\n        top: offset.top + nodeHeight,\n        left: offset.left + (nodeWidth / 2 - tooltipWidth / 2),\n      });\n    } else if (placement === 'top') {\n      $tooltip.css({\n        top: offset.top - tooltipHeight,\n        left: offset.left + (nodeWidth / 2 - tooltipWidth / 2),\n      });\n    } else if (placement === 'left') {\n      $tooltip.css({\n        top: offset.top + (nodeHeight / 2 - tooltipHeight / 2),\n        left: offset.left - tooltipWidth,\n      });\n    } else if (placement === 'right') {\n      $tooltip.css({\n        top: offset.top + (nodeHeight / 2 - tooltipHeight / 2),\n        left: offset.left + nodeWidth,\n      });\n    }\n\n    $tooltip.addClass('in');\n  }\n\n  hide() {\n    this.$tooltip.removeClass('in');\n    setTimeout(() => {\n      this.$tooltip.remove();\n    }, 200);\n  }\n\n  toggle() {\n    if (this.$tooltip.hasClass('in')) {\n      this.hide();\n    } else {\n      this.show();\n    }\n  }\n}\n\nexport default TooltipUI;\n", "import $ from 'jquery';\n\nclass DropdownUI {\n  constructor($node, options) {\n    this.$button = $node;\n    this.options = $.extend({}, {\n      target: options.container,\n    }, options);\n    this.setEvent();\n  }\n\n  setEvent() {\n    this.$button.on('click', (e) => {\n      this.toggle();\n      e.stopImmediatePropagation();\n    });\n  }\n\n  clear() {\n    var $parent = $('.note-btn-group.open');\n    $parent.find('.note-btn.active').removeClass('active');\n    $parent.removeClass('open');\n  }\n\n  show() {\n    this.$button.addClass('active');\n    this.$button.parent().addClass('open');\n\n    var $dropdown = this.$button.next();\n    var offset = $dropdown.offset();\n    var width = $dropdown.outerWidth();\n    var windowWidth = $(window).width();\n    var targetMarginRight = parseFloat($(this.options.target).css('margin-right'));\n\n    if (offset.left + width > windowWidth - targetMarginRight) {\n      $dropdown.css('margin-left', windowWidth - targetMarginRight - (offset.left + width));\n    } else {\n      $dropdown.css('margin-left', '');\n    }\n  }\n\n  hide() {\n    this.$button.removeClass('active');\n    this.$button.parent().removeClass('open');\n  }\n\n  toggle() {\n    var isOpened = this.$button.parent().hasClass('open');\n\n    this.clear();\n\n    if (isOpened) {\n      this.hide();\n    } else {\n      this.show();\n    }\n  }\n}\n\n$(document).on('click', function(e) {\n  if (!$(e.target).closest('.note-btn-group').length) {\n    $('.note-btn-group.open').removeClass('open');\n    $('.note-btn-group .note-btn.active').removeClass('active');\n  }\n});\n\n$(document).on('click.note-dropdown-menu', function(e) {\n  $(e.target).closest('.note-dropdown-menu').parent().removeClass('open');\n  $(e.target).closest('.note-dropdown-menu').parent().find('.note-btn.active').removeClass('active');\n});\n\nexport default DropdownUI;\n", "import $ from 'jquery';\n\nclass ModalUI {\n  constructor($node, options) {\n    this.$modal = $node;\n    this.$backdrop = $('<div class=\"note-modal-backdrop\"/>');\n  }\n\n  show() {\n    this.$backdrop.appendTo(document.body).show();\n    this.$modal.addClass('open').show();\n    this.$modal.trigger('note.modal.show');\n    this.$modal.off('click', '.close').on('click', '.close', this.hide.bind(this));\n    this.$modal.on('keydown', (event) => {\n      if (event.which === 27) {\n        event.preventDefault();\n        this.hide();\n      }\n    });\n  }\n\n  hide() {\n    this.$modal.removeClass('open').hide();\n    this.$backdrop.hide();\n    this.$modal.trigger('note.modal.hide');\n    this.$modal.off('keydown');\n  }\n}\n\nexport default ModalUI;\n", "import $ from 'jquery';\nimport renderer from '../base/renderer';\nimport TooltipUI from './ui/TooltipUI';\nimport DropdownUI from './ui/DropdownUI';\nimport ModalUI from './ui/ModalUI';\n\nconst editor = renderer.create('<div class=\"note-editor note-frame\"/>');\nconst toolbar = renderer.create('<div class=\"note-toolbar\" role=\"toolbar\"/>');\nconst editingArea = renderer.create('<div class=\"note-editing-area\"/>');\nconst codable = renderer.create('<textarea class=\"note-codable\" aria-multiline=\"true\"/>');\nconst editable = renderer.create('<div class=\"note-editable\" contentEditable=\"true\" role=\"textbox\" aria-multiline=\"true\"/>');\nconst statusbar = renderer.create([\n  '<output class=\"note-status-output\" role=\"status\" aria-live=\"polite\"/>',\n  '<div class=\"note-statusbar\" role=\"status\">',\n    '<div class=\"note-resizebar\" aria-label=\"resize\">',\n      '<div class=\"note-icon-bar\"/>',\n      '<div class=\"note-icon-bar\"/>',\n      '<div class=\"note-icon-bar\"/>',\n    '</div>',\n  '</div>',\n].join(''));\n\nconst airEditor = renderer.create('<div class=\"note-editor note-airframe\"/>');\nconst airEditable = renderer.create([\n  '<div class=\"note-editable\" contentEditable=\"true\" role=\"textbox\" aria-multiline=\"true\"/>',\n  '<output class=\"note-status-output\" role=\"status\" aria-live=\"polite\"/>',\n].join(''));\n\nconst buttonGroup = renderer.create('<div class=\"note-btn-group\">');\nconst button = renderer.create('<button type=\"button\" class=\"note-btn\" tabindex=\"-1\">', function($node, options) {\n  // set button type\n  if (options && options.tooltip) {\n    $node.attr({\n      'aria-label': options.tooltip,\n    });\n    $node.data('_lite_tooltip', new TooltipUI($node, {\n      title: options.tooltip,\n      container: options.container,\n    })).on('click', (e) => {\n      $(e.currentTarget).data('_lite_tooltip').hide();\n    });\n  }\n  if (options.contents) {\n    $node.html(options.contents);\n  }\n\n  if (options && options.data && options.data.toggle === 'dropdown') {\n    $node.data('_lite_dropdown', new DropdownUI($node, {\n      container: options.container,\n    }));\n  }\n});\n\nconst dropdown = renderer.create('<div class=\"note-dropdown-menu\" role=\"list\">', function($node, options) {\n  const markup = Array.isArray(options.items) ? options.items.map(function(item) {\n    const value = (typeof item === 'string') ? item : (item.value || '');\n    const content = options.template ? options.template(item) : item;\n    const $temp = $('<a class=\"note-dropdown-item\" href=\"#\" data-value=\"' + value + '\" role=\"listitem\" aria-label=\"' + value + '\"></a>');\n\n    $temp.html(content).data('item', item);\n\n    return $temp;\n  }) : options.items;\n\n  $node.html(markup).attr({ 'aria-label': options.title });\n\n  $node.on('click', '> .note-dropdown-item', function(e) {\n    const $a = $(this);\n\n    const item = $a.data('item');\n    const value = $a.data('value');\n\n    if (item.click) {\n      item.click($a);\n    } else if (options.itemClick) {\n      options.itemClick(e, item, value);\n    }\n  });\n});\n\nconst dropdownCheck = renderer.create('<div class=\"note-dropdown-menu note-check\" role=\"list\">', function($node, options) {\n  const markup = Array.isArray(options.items) ? options.items.map(function(item) {\n    const value = (typeof item === 'string') ? item : (item.value || '');\n    const content = options.template ? options.template(item) : item;\n\n    const $temp = $('<a class=\"note-dropdown-item\" href=\"#\" data-value=\"' + value + '\" role=\"listitem\" aria-label=\"' + item + '\"></a>');\n    $temp.html([icon(options.checkClassName), ' ', content]).data('item', item);\n    return $temp;\n  }) : options.items;\n\n  $node.html(markup).attr({ 'aria-label': options.title });\n\n  $node.on('click', '> .note-dropdown-item', function(e) {\n    const $a = $(this);\n\n    const item = $a.data('item');\n    const value = $a.data('value');\n\n    if (item.click) {\n      item.click($a);\n    } else if (options.itemClick) {\n      options.itemClick(e, item, value);\n    }\n  });\n});\n\nconst dropdownButtonContents = function(contents, options) {\n  return contents + ' ' + icon(options.icons.caret, 'span');\n};\n\nconst dropdownButton = function(opt, callback) {\n  return buttonGroup([\n    button({\n      className: 'dropdown-toggle',\n      contents: opt.title + ' ' + icon('note-icon-caret'),\n      tooltip: opt.tooltip,\n      data: {\n        toggle: 'dropdown',\n      },\n    }),\n    dropdown({\n      className: opt.className,\n      items: opt.items,\n      template: opt.template,\n      itemClick: opt.itemClick,\n    }),\n  ], { callback: callback }).render();\n};\n\nconst dropdownCheckButton = function(opt, callback) {\n  return buttonGroup([\n    button({\n      className: 'dropdown-toggle',\n      contents: opt.title + ' ' + icon('note-icon-caret'),\n      tooltip: opt.tooltip,\n      data: {\n        toggle: 'dropdown',\n      },\n    }),\n    dropdownCheck({\n      className: opt.className,\n      checkClassName: opt.checkClassName,\n      items: opt.items,\n      template: opt.template,\n      itemClick: opt.itemClick,\n    }),\n  ], { callback: callback }).render();\n};\n\nconst paragraphDropdownButton = function(opt) {\n  return buttonGroup([\n    button({\n      className: 'dropdown-toggle',\n      contents: opt.title + ' ' + icon('note-icon-caret'),\n      tooltip: opt.tooltip,\n      data: {\n        toggle: 'dropdown',\n      },\n    }),\n    dropdown([\n      buttonGroup({\n        className: 'note-align',\n        children: opt.items[0],\n      }),\n      buttonGroup({\n        className: 'note-list',\n        children: opt.items[1],\n      }),\n    ]),\n  ]).render();\n};\n\nconst tableMoveHandler = function(event, col, row) {\n  const PX_PER_EM = 18;\n  const $picker = $(event.target.parentNode); // target is mousecatcher\n  const $dimensionDisplay = $picker.next();\n  const $catcher = $picker.find('.note-dimension-picker-mousecatcher');\n  const $highlighted = $picker.find('.note-dimension-picker-highlighted');\n  const $unhighlighted = $picker.find('.note-dimension-picker-unhighlighted');\n\n  let posOffset;\n  // HTML5 with jQuery - e.offsetX is undefined in Firefox\n  if (event.offsetX === undefined) {\n    const posCatcher = $(event.target).offset();\n    posOffset = {\n      x: event.pageX - posCatcher.left,\n      y: event.pageY - posCatcher.top,\n    };\n  } else {\n    posOffset = {\n      x: event.offsetX,\n      y: event.offsetY,\n    };\n  }\n\n  const dim = {\n    c: Math.ceil(posOffset.x / PX_PER_EM) || 1,\n    r: Math.ceil(posOffset.y / PX_PER_EM) || 1,\n  };\n\n  $highlighted.css({ width: dim.c + 'em', height: dim.r + 'em' });\n  $catcher.data('value', dim.c + 'x' + dim.r);\n\n  if (dim.c > 3 && dim.c < col) {\n    $unhighlighted.css({ width: dim.c + 1 + 'em' });\n  }\n\n  if (dim.r > 3 && dim.r < row) {\n    $unhighlighted.css({ height: dim.r + 1 + 'em' });\n  }\n\n  $dimensionDisplay.html(dim.c + ' x ' + dim.r);\n};\n\nconst tableDropdownButton = function(opt) {\n  return buttonGroup([\n    button({\n      className: 'dropdown-toggle',\n      contents: opt.title + ' ' + icon('note-icon-caret'),\n      tooltip: opt.tooltip,\n      data: {\n        toggle: 'dropdown',\n      },\n    }),\n    dropdown({\n      className: 'note-table',\n      items: [\n        '<div class=\"note-dimension-picker\">',\n          '<div class=\"note-dimension-picker-mousecatcher\" data-event=\"insertTable\" data-value=\"1x1\"/>',\n          '<div class=\"note-dimension-picker-highlighted\"/>',\n          '<div class=\"note-dimension-picker-unhighlighted\"/>',\n        '</div>',\n        '<div class=\"note-dimension-display\">1 x 1</div>',\n      ].join(''),\n    }),\n  ], {\n    callback: function($node) {\n      const $catcher = $node.find('.note-dimension-picker-mousecatcher');\n      $catcher.css({\n        width: opt.col + 'em',\n        height: opt.row + 'em',\n      })\n        .mousedown(opt.itemClick)\n        .mousemove(function(e) {\n          tableMoveHandler(e, opt.col, opt.row);\n        });\n    },\n  }).render();\n};\n\nconst palette = renderer.create('<div class=\"note-color-palette\"/>', function($node, options) {\n  const contents = [];\n  for (let row = 0, rowSize = options.colors.length; row < rowSize; row++) {\n    const eventName = options.eventName;\n    const colors = options.colors[row];\n    const colorsName = options.colorsName[row];\n    const buttons = [];\n    for (let col = 0, colSize = colors.length; col < colSize; col++) {\n      const color = colors[col];\n      const colorName = colorsName[col];\n      buttons.push([\n        '<button type=\"button\" class=\"note-btn note-color-btn\"',\n        'style=\"background-color:', color, '\" ',\n        'data-event=\"', eventName, '\" ',\n        'data-value=\"', color, '\" ',\n        'data-title=\"', colorName, '\" ',\n        'aria-label=\"', colorName, '\" ',\n        'data-toggle=\"button\" tabindex=\"-1\"></button>',\n      ].join(''));\n    }\n    contents.push('<div class=\"note-color-row\">' + buttons.join('') + '</div>');\n  }\n  $node.html(contents.join(''));\n\n  $node.find('.note-color-btn').each(function() {\n    $(this).data('_lite_tooltip', new TooltipUI($(this), {\n      container: options.container,\n    }));\n  });\n});\n\nconst colorDropdownButton = function(opt, type) {\n  return buttonGroup({\n    className: 'note-color',\n    children: [\n      button({\n        className: 'note-current-color-button',\n        contents: opt.title,\n        tooltip: opt.lang.color.recent,\n        click: opt.currentClick,\n        callback: function($button) {\n          const $recentColor = $button.find('.note-recent-color');\n\n          if (type !== 'foreColor') {\n            $recentColor.css('background-color', '#FFFF00');\n            $button.attr('data-backColor', '#FFFF00');\n          }\n        },\n      }),\n      button({\n        className: 'dropdown-toggle',\n        contents: icon('note-icon-caret'),\n        tooltip: opt.lang.color.more,\n        data: {\n          toggle: 'dropdown',\n        },\n      }),\n      dropdown({\n        items: [\n          '<div>',\n            '<div class=\"note-btn-group btn-background-color\">',\n              '<div class=\"note-palette-title\">' + opt.lang.color.background + '</div>',\n            '<div>',\n            '<button type=\"button\" class=\"note-color-reset note-btn note-btn-block\" data-event=\"backColor\" data-value=\"inherit\">',\n              opt.lang.color.transparent,\n            '</button>',\n          '</div>',\n          '<div class=\"note-holder\" data-event=\"backColor\"/>',\n            '<div class=\"btn-sm\">',\n              '<input type=\"color\" id=\"html5bcp\" class=\"note-btn btn-secondary\" value=\"#21104A\" style=\"width:100%;\" data-value=\"cp\">',\n              '<button type=\"button\" class=\"note-color-reset btn\" data-event=\"backColor\" data-value=\"cpbackColor\">',\n                opt.lang.color.cpSelect,\n              '</button>',\n            '</div>',\n          '</div>',\n          '<div class=\"note-btn-group btn-foreground-color\">',\n            '<div class=\"note-palette-title\">' + opt.lang.color.foreground + '</div>',\n            '<div>',\n              '<button type=\"button\" class=\"note-color-reset note-btn note-btn-block\" data-event=\"removeFormat\" data-value=\"foreColor\">',\n                opt.lang.color.resetToDefault,\n              '</button>',\n            '</div>',\n            '<div class=\"note-holder\" data-event=\"foreColor\"/>',\n              '<div class=\"btn-sm\">',\n                '<input type=\"color\" id=\"html5fcp\" class=\"note-btn btn-secondary\" value=\"#21104A\" style=\"width:100%;\" data-value=\"cp\">',\n                '<button type=\"button\" class=\"note-color-reset btn\" data-event=\"foreColor\" data-value=\"cpforeColor\">',\n                  opt.lang.color.cpSelect,\n                '</button>',\n              '</div>',\n            '</div>',\n          '</div>',\n        ].join(''),\n        callback: function($dropdown) {\n          $dropdown.find('.note-holder').each(function() {\n            const $holder = $(this);\n            $holder.append(palette({\n              colors: opt.colors,\n              eventName: $holder.data('event'),\n            }).render());\n          });\n\n          if (type === 'fore') {\n            $dropdown.find('.btn-background-color').hide();\n            $dropdown.css({ 'min-width': '210px' });\n          } else if (type === 'back') {\n            $dropdown.find('.btn-foreground-color').hide();\n            $dropdown.css({ 'min-width': '210px' });\n          }\n        },\n        click: function(event) {\n          const $button = $(event.target);\n          const eventName = $button.data('event');\n          let value = $button.data('value');\n          const foreinput = document.getElementById('html5fcp').value;\n          const backinput = document.getElementById('html5bcp').value;\n          if (value === 'cp') {\n            event.stopPropagation();\n          } else if (value === 'cpbackColor') {\n            value = backinput;\n          } else if (value === 'cpforeColor') {\n            value = foreinput;\n          }\n\n          if (eventName && value) {\n            const key = eventName === 'backColor' ? 'background-color' : 'color';\n            const $color = $button.closest('.note-color').find('.note-recent-color');\n            const $currentButton = $button.closest('.note-color').find('.note-current-color-button');\n\n            $color.css(key, value);\n            $currentButton.attr('data-' + eventName, value);\n\n            if (type === 'fore') {\n              opt.itemClick('foreColor', value);\n            } else if (type === 'back') {\n              opt.itemClick('backColor', value);\n            } else {\n              opt.itemClick(eventName, value);\n            }\n          }\n        },\n      }),\n    ],\n  }).render();\n};\n\nconst dialog = renderer.create('<div class=\"note-modal\" aria-hidden=\"false\" tabindex=\"-1\" role=\"dialog\"/>', function($node, options) {\n  if (options.fade) {\n    $node.addClass('fade');\n  }\n  $node.attr({\n    'aria-label': options.title,\n  });\n  $node.html([\n    '<div class=\"note-modal-content\">',\n      (options.title ? '<div class=\"note-modal-header\"><button type=\"button\" class=\"close\" aria-label=\"Close\" aria-hidden=\"true\"><i class=\"note-icon-close\"></i></button><h4 class=\"note-modal-title\">' + options.title + '</h4></div>' : ''),\n      '<div class=\"note-modal-body\">' + options.body + '</div>',\n      (options.footer ? '<div class=\"note-modal-footer\">' + options.footer + '</div>' : ''),\n    '</div>',\n  ].join(''));\n\n  $node.data('modal', new ModalUI($node, options));\n});\n\nconst videoDialog = function(opt) {\n  const body = '<div class=\"note-form-group\">' +\n    '<label for=\"note-dialog-video-url-' + opt.id + '\" class=\"note-form-label\">' + opt.lang.video.url + ' <small class=\"text-muted\">' + opt.lang.video.providers + '</small></label>' +\n    '<input id=\"note-dialog-video-url-' + opt.id + '\" class=\"note-video-url note-input\" type=\"text\"/>' +\n  '</div>';\n  const footer = [\n    '<button type=\"button\" href=\"#\" class=\"note-btn note-btn-primary note-video-btn disabled\" disabled>',\n      opt.lang.video.insert,\n    '</button>',\n  ].join('');\n\n  return dialog({\n    title: opt.lang.video.insert,\n    fade: opt.fade,\n    body: body,\n    footer: footer,\n  }).render();\n};\n\nconst imageDialog = function(opt) {\n  const body = '<div class=\"note-form-group note-group-select-from-files\">' +\n    '<label for=\"note-dialog-image-file-' + opt.id + '\" class=\"note-form-label\">' + opt.lang.image.selectFromFiles + '</label>' +\n    '<input id=\"note-dialog-image-file-' + opt.id + '\" class=\"note-note-image-input note-input\" type=\"file\" name=\"files\" accept=\"image/*\" multiple=\"multiple\"/>' +\n    opt.imageLimitation +\n  '</div>' +\n  '<div class=\"note-form-group\">' +\n    '<label for=\"note-dialog-image-url-' + opt.id + '\" class=\"note-form-label\">' + opt.lang.image.url + '</label>' +\n    '<input id=\"note-dialog-image-url-' + opt.id + '\" class=\"note-image-url note-input\" type=\"text\"/>' +\n  '</div>';\n  const footer = [\n    '<button href=\"#\" type=\"button\" class=\"note-btn note-btn-primary note-btn-large note-image-btn disabled\" disabled>',\n      opt.lang.image.insert,\n    '</button>',\n  ].join('');\n\n  return dialog({\n    title: opt.lang.image.insert,\n    fade: opt.fade,\n    body: body,\n    footer: footer,\n  }).render();\n};\n\nconst linkDialog = function(opt) {\n  const body = '<div class=\"note-form-group\">' +\n    '<label for=\"note-dialog-link-txt-' + opt.id + '\" class=\"note-form-label\">' + opt.lang.link.textToDisplay + '</label>' +\n    '<input id=\"note-dialog-link-txt-' + opt.id + '\" class=\"note-link-text note-input\" type=\"text\"/>' +\n  '</div>' +\n  '<div class=\"note-form-group\">' +\n    '<label for=\"note-dialog-link-url-' + opt.id + '\" class=\"note-form-label\">' + opt.lang.link.url + '</label>' +\n    '<input id=\"note-dialog-link-url-' + opt.id + '\" class=\"note-link-url note-input\" type=\"text\" value=\"http://\"/>' +\n  '</div>' +\n  (!opt.disableLinkTarget ? '<div class=\"checkbox\"><label for=\"note-dialog-link-nw-' + opt.id + '\"><input id=\"note-dialog-link-nw-' + opt.id + '\" type=\"checkbox\" checked> ' + opt.lang.link.openInNewWindow + '</label></div>' : '') +\n  '<div class=\"checkbox\"><label for=\"note-dialog-link-up-' + opt.id + '\"><input id=\"note-dialog-link-up-' + opt.id + '\" type=\"checkbox\" checked> ' + opt.lang.link.useProtocol + '</label></div>';\n  const footer = [\n    '<button href=\"#\" type=\"button\" class=\"note-btn note-btn-primary note-link-btn disabled\" disabled>',\n      opt.lang.link.insert,\n    '</button>',\n  ].join('');\n\n  return dialog({\n    className: 'link-dialog',\n    title: opt.lang.link.insert,\n    fade: opt.fade,\n    body: body,\n    footer: footer,\n  }).render();\n};\n\nconst popover = renderer.create([\n  '<div class=\"note-popover bottom\">',\n    '<div class=\"note-popover-arrow\"/>',\n    '<div class=\"popover-content note-children-container\"/>',\n  '</div>',\n].join(''), function($node, options) {\n  const direction = typeof options.direction !== 'undefined' ? options.direction : 'bottom';\n\n  $node.addClass(direction).hide();\n\n  if (options.hideArrow) {\n    $node.find('.note-popover-arrow').hide();\n  }\n});\n\nconst checkbox = renderer.create('<div class=\"checkbox\"></div>', function($node, options) {\n  $node.html([\n    '<label' + (options.id ? ' for=\"note-' + options.id + '\"' : '') + '>',\n      '<input role=\"checkbox\" type=\"checkbox\"' + (options.id ? ' id=\"note-' + options.id + '\"' : ''),\n      (options.checked ? ' checked' : ''),\n      ' aria-checked=\"' + (options.checked ? 'true' : 'false') + '\"/>',\n      (options.text ? options.text : ''),\n    '</label>',\n  ].join(''));\n});\n\nconst icon = function(iconClassName, tagName) {\n  tagName = tagName || 'i';\n  return '<' + tagName + ' class=\"' + iconClassName + '\"/>';\n};\n\nconst ui = function(editorOptions) {\n  return {\n    editor: editor,\n    toolbar: toolbar,\n    editingArea: editingArea,\n    codable: codable,\n    editable: editable,\n    statusbar: statusbar,\n    airEditor: airEditor,\n    airEditable: airEditable,\n    buttonGroup: buttonGroup,\n    button: button,\n    dropdown: dropdown,\n    dropdownCheck: dropdownCheck,\n    dropdownButton: dropdownButton,\n    dropdownButtonContents: dropdownButtonContents,\n    dropdownCheckButton: dropdownCheckButton,\n    paragraphDropdownButton: paragraphDropdownButton,\n    tableDropdownButton: tableDropdownButton,\n    colorDropdownButton: colorDropdownButton,\n    palette: palette,\n    dialog: dialog,\n    videoDialog: videoDialog,\n    imageDialog: imageDialog,\n    linkDialog: linkDialog,\n    popover: popover,\n    checkbox: checkbox,\n    icon: icon,\n    options: editorOptions,\n\n    toggleBtn: function($btn, isEnable) {\n      $btn.toggleClass('disabled', !isEnable);\n      $btn.attr('disabled', !isEnable);\n    },\n\n    toggleBtnActive: function($btn, isActive) {\n      $btn.toggleClass('active', isActive);\n    },\n\n    check: function($dom, value) {\n      $dom.find('.checked').removeClass('checked');\n      $dom.find('[data-value=\"' + value + '\"]').addClass('checked');\n    },\n\n    onDialogShown: function($dialog, handler) {\n      $dialog.one('note.modal.show', handler);\n    },\n\n    onDialogHidden: function($dialog, handler) {\n      $dialog.one('note.modal.hide', handler);\n    },\n\n    showDialog: function($dialog) {\n      $dialog.data('modal').show();\n    },\n\n    hideDialog: function($dialog) {\n      $dialog.data('modal').hide();\n    },\n\n    /**\n     * get popover content area\n     *\n     * @param $popover\n     * @returns {*}\n     */\n    getPopoverContent: function($popover) {\n      return $popover.find('.note-popover-content');\n    },\n\n    /**\n     * get dialog's body area\n     *\n     * @param $dialog\n     * @returns {*}\n     */\n    getDialogBody: function($dialog) {\n      return $dialog.find('.note-modal-body');\n    },\n\n    createLayout: function($note) {\n      const $editor = (editorOptions.airMode ? airEditor([\n        editingArea([\n          codable(),\n          airEditable(),\n        ]),\n      ]) : (editorOptions.toolbarPosition === 'bottom'\n        ? editor([\n          editingArea([\n            codable(),\n            editable(),\n          ]),\n          toolbar(),\n          statusbar(),\n        ])\n        : editor([\n          toolbar(),\n          editingArea([\n            codable(),\n            editable(),\n          ]),\n          statusbar(),\n        ])\n      )).render();\n\n      $editor.insertAfter($note);\n\n      return {\n        note: $note,\n        editor: $editor,\n        toolbar: $editor.find('.note-toolbar'),\n        editingArea: $editor.find('.note-editing-area'),\n        editable: $editor.find('.note-editable'),\n        codable: $editor.find('.note-codable'),\n        statusbar: $editor.find('.note-statusbar'),\n      };\n    },\n\n    removeLayout: function($note, layoutInfo) {\n      $note.html(layoutInfo.editable.html());\n      layoutInfo.editor.remove();\n      $note.off('summernote'); // remove summernote custom event\n      $note.show();\n    },\n  };\n};\n\nexport default ui;\n", "import $ from 'j<PERSON>y';\nimport ui from './ui';\nimport '../base/settings.js';\n\nimport '../../styles/summernote-lite.scss';\n\n$.summernote = $.extend($.summernote, {\n  ui_template: ui,\n  interface: 'lite',\n});\n"], "sourceRoot": ""}