(function(n){var s={},c={},l={},a={treeId:"",treeObj:null,view:{addDiyDom:null,autoCancelSelected:!0,dblClickExpand:!0,expandSpeed:"fast",fontCss:{},nameIsHTML:!1,selectedMulti:!0,showIcon:!0,showLine:!0,showTitle:!0,txtSelectedEnable:!1},data:{key:{children:"children",name:"name",title:"",url:"url"},simpleData:{enable:!1,idKey:"id",pIdKey:"pId",rootPId:null},keep:{parent:!1,leaf:!1}},"async":{enable:!1,contentType:"application/x-www-form-urlencoded",type:"post",dataType:"text",url:"",autoParam:[],otherParam:[],dataFilter:null},callback:{beforeAsync:null,beforeClick:null,beforeDblClick:null,beforeRightClick:null,beforeMouseDown:null,beforeMouseUp:null,beforeExpand:null,beforeCollapse:null,beforeRemove:null,onAsyncError:null,onAsyncSuccess:null,onNodeCreated:null,onClick:null,onDblClick:null,onRightClick:null,onMouseDown:null,onMouseUp:null,onExpand:null,onCollapse:null,onRemove:null}},y=function(n){var i=t.getRoot(n);i||(i={},t.setRoot(n,i));i[n.data.key.children]=[];i.expandTriggerFlag=!1;i.curSelectedList=[];i.noSelection=!0;i.createdNodes=[];i.zId=0;i._ver=(new Date).getTime()},p=function(n){var i=t.getCache(n);i||(i={},t.setCache(n,i));i.nodes=[];i.doms=[]},w=function(n){var t=n.treeObj,r=i.event;t.bind(r.NODECREATED,function(t,i,r){u.apply(n.callback.onNodeCreated,[t,i,r])});t.bind(r.CLICK,function(t,i,r,f,e){u.apply(n.callback.onClick,[i,r,f,e])});t.bind(r.EXPAND,function(t,i,r){u.apply(n.callback.onExpand,[t,i,r])});t.bind(r.COLLAPSE,function(t,i,r){u.apply(n.callback.onCollapse,[t,i,r])});t.bind(r.ASYNC_SUCCESS,function(t,i,r,f){u.apply(n.callback.onAsyncSuccess,[t,i,r,f])});t.bind(r.ASYNC_ERROR,function(t,i,r,f,e,o){u.apply(n.callback.onAsyncError,[t,i,r,f,e,o])});t.bind(r.REMOVE,function(t,i,r){u.apply(n.callback.onRemove,[t,i,r])});t.bind(r.SELECTED,function(t,i,r,f){u.apply(n.callback.onSelected,[i,r,f])});t.bind(r.UNSELECTED,function(t,i,r,f){u.apply(n.callback.onUnSelected,[i,r,f])})},b=function(n){var r=n.treeObj,t=i.event;r.unbind(t.NODECREATED).unbind(t.CLICK).unbind(t.EXPAND).unbind(t.COLLAPSE).unbind(t.ASYNC_SUCCESS).unbind(t.ASYNC_ERROR).unbind(t.REMOVE).unbind(t.SELECTED).unbind(t.UNSELECTED)},k=function(n){var c=n.target,f=t.getSetting(n.data.treeId),e="",a=null,o="",s="",v=null,l=null,r=null;if(u.eqs(n.type,"mousedown")?s="mousedown":u.eqs(n.type,"mouseup")?s="mouseup":u.eqs(n.type,"contextmenu")?s="contextmenu":u.eqs(n.type,"click")?u.eqs(c.tagName,"span")&&c.getAttribute("treeNode"+i.id.SWITCH)!==null?(e=u.getNodeMainDom(c).id,o="switchNode"):(r=u.getMDom(f,c,[{tagName:"a",attrName:"treeNode"+i.id.A}]),r&&(e=u.getNodeMainDom(r).id,o="clickNode")):u.eqs(n.type,"dblclick")&&(s="dblclick",r=u.getMDom(f,c,[{tagName:"a",attrName:"treeNode"+i.id.A}]),r&&(e=u.getNodeMainDom(r).id,o="switchNode")),s.length>0&&e.length==0&&(r=u.getMDom(f,c,[{tagName:"a",attrName:"treeNode"+i.id.A}]),r&&(e=u.getNodeMainDom(r).id)),e.length>0){a=t.getNodeCache(f,e);switch(o){case"switchNode":a.isParent?u.eqs(n.type,"click")||u.eqs(n.type,"dblclick")&&u.apply(f.view.dblClickExpand,[f.treeId,a],f.view.dblClickExpand)?v=h.onSwitchNode:o="":o="";break;case"clickNode":v=h.onClickNode}}switch(s){case"mousedown":l=h.onZTreeMousedown;break;case"mouseup":l=h.onZTreeMouseup;break;case"dblclick":l=h.onZTreeDblclick;break;case"contextmenu":l=h.onZTreeContextmenu}return{stop:!1,node:a,nodeEventType:o,nodeEventCallback:v,treeEventType:s,treeEventCallback:l}},d=function(n,i,r,f,e,o){if(r){var h=t.getRoot(n),s=n.data.key.children;r.level=i;r.tId=n.treeId+"_"+ ++h.zId;r.parentTId=f?f.tId:null;r.open=typeof r.open=="string"?u.eqs(r.open,"true"):!!r.open;r[s]&&r[s].length>0?(r.isParent=!0,r.zAsync=!0):(r.isParent=typeof r.isParent=="string"?u.eqs(r.isParent,"true"):!!r.isParent,r.open=r.isParent&&!n.async.enable?r.open:!1,r.zAsync=!r.isParent);r.isFirstNode=e;r.isLastNode=o;r.getParentNode=function(){return t.getNodeCache(n,r.parentTId)};r.getPreNode=function(){return t.getPreNode(n,r)};r.getNextNode=function(){return t.getNextNode(n,r)};r.isAjaxing=!1;t.fixPIdKeyValue(n,r)}},e={bind:[w],unbind:[b],caches:[p],nodes:[d],proxys:[k],roots:[y],beforeA:[],afterA:[],innerBeforeA:[],innerAfterA:[],zTreeTools:[]},t={addNodeCache:function(n,i){t.getCache(n).nodes[t.getNodeCacheId(i.tId)]=i},getNodeCacheId:function(n){return n.substring(n.lastIndexOf("_")+1)},addAfterA:function(n){e.afterA.push(n)},addBeforeA:function(n){e.beforeA.push(n)},addInnerAfterA:function(n){e.innerAfterA.push(n)},addInnerBeforeA:function(n){e.innerBeforeA.push(n)},addInitBind:function(n){e.bind.push(n)},addInitUnBind:function(n){e.unbind.push(n)},addInitCache:function(n){e.caches.push(n)},addInitNode:function(n){e.nodes.push(n)},addInitProxy:function(n,t){t?e.proxys.splice(0,0,n):e.proxys.push(n)},addInitRoot:function(n){e.roots.push(n)},addNodesData:function(n,t,i){var u=n.data.key.children;t[u]||(t[u]=[]);t[u].length>0&&(t[u][t[u].length-1].isLastNode=!1,r.setNodeLineIcos(n,t[u][t[u].length-1]));t.isParent=!0;t[u]=t[u].concat(i)},addSelectedNode:function(n,i){var r=t.getRoot(n);t.isSelectedNode(n,i)||r.curSelectedList.push(i)},addCreatedNode:function(n,i){if(!!n.callback.onNodeCreated||!!n.view.addDiyDom){var r=t.getRoot(n);r.createdNodes.push(i)}},addZTreeTools:function(n){e.zTreeTools.push(n)},exSetting:function(t){n.extend(!0,a,t)},fixPIdKeyValue:function(n,t){n.data.simpleData.enable&&(t[n.data.simpleData.pIdKey]=t.parentTId?t.getParentNode()[n.data.simpleData.idKey]:n.data.simpleData.rootPId)},getAfterA:function(){for(var n=0,t=e.afterA.length;n<t;n++)e.afterA[n].apply(this,arguments)},getBeforeA:function(){for(var n=0,t=e.beforeA.length;n<t;n++)e.beforeA[n].apply(this,arguments)},getInnerAfterA:function(){for(var n=0,t=e.innerAfterA.length;n<t;n++)e.innerAfterA[n].apply(this,arguments)},getInnerBeforeA:function(){for(var n=0,t=e.innerBeforeA.length;n<t;n++)e.innerBeforeA[n].apply(this,arguments)},getCache:function(n){return l[n.treeId]},getNextNode:function(n,i){var u,f,r,e;if(!i)return null;for(u=n.data.key.children,f=i.parentTId?i.getParentNode():t.getRoot(n),r=0,e=f[u].length-1;r<=e;r++)if(f[u][r]===i)return r==e?null:f[u][r+1];return null},getNodeByParam:function(n,i,r,u){var o,f,s,e;if(!i||!r)return null;for(o=n.data.key.children,f=0,s=i.length;f<s;f++){if(i[f][r]==u)return i[f];if(e=t.getNodeByParam(n,i[f][o],r,u),e)return e}return null},getNodeCache:function(n,i){if(!i)return null;var r=l[n.treeId].nodes[t.getNodeCacheId(i)];return r?r:null},getNodeName:function(n,t){var i=n.data.key.name;return""+t[i]},getNodeTitle:function(n,t){var i=n.data.key.title===""?n.data.key.name:n.data.key.title;return""+t[i]},getNodes:function(n){return t.getRoot(n)[n.data.key.children]},getNodesByParam:function(n,i,r,u){var o,e,f,s;if(!i||!r)return[];for(o=n.data.key.children,e=[],f=0,s=i.length;f<s;f++)i[f][r]==u&&e.push(i[f]),e=e.concat(t.getNodesByParam(n,i[f][o],r,u));return e},getNodesByParamFuzzy:function(n,i,r,u){var o,e,f,s;if(!i||!r)return[];for(o=n.data.key.children,e=[],u=u.toLowerCase(),f=0,s=i.length;f<s;f++)typeof i[f][r]=="string"&&i[f][r].toLowerCase().indexOf(u)>-1&&e.push(i[f]),e=e.concat(t.getNodesByParamFuzzy(n,i[f][o],r,u));return e},getNodesByFilter:function(n,i,r,f,e){var c,s,o,l,h;if(!i)return f?null:[];for(c=n.data.key.children,s=f?null:[],o=0,l=i.length;o<l;o++){if(u.apply(r,[i[o],e],!1)){if(f)return i[o];s.push(i[o])}if(h=t.getNodesByFilter(n,i[o][c],r,f,e),f&&!!h)return h;s=f?h:s.concat(h)}return s},getPreNode:function(n,i){var u,f,r,e;if(!i)return null;for(u=n.data.key.children,f=i.parentTId?i.getParentNode():t.getRoot(n),r=0,e=f[u].length;r<e;r++)if(f[u][r]===i)return r==0?null:f[u][r-1];return null},getRoot:function(n){return n?c[n.treeId]:null},getRoots:function(){return c},getSetting:function(n){return s[n]},getSettings:function(){return s},getZTreeTools:function(n){var t=this.getRoot(this.getSetting(n));return t?t.treeTools:null},initCache:function(){for(var n=0,t=e.caches.length;n<t;n++)e.caches[n].apply(this,arguments)},initNode:function(){for(var n=0,t=e.nodes.length;n<t;n++)e.nodes[n].apply(this,arguments)},initRoot:function(){for(var n=0,t=e.roots.length;n<t;n++)e.roots[n].apply(this,arguments)},isSelectedNode:function(n,i){for(var u=t.getRoot(n),r=0,f=u.curSelectedList.length;r<f;r++)if(i===u.curSelectedList[r])return!0;return!1},removeNodeCache:function(n,i){var u=n.data.key.children,r,f;if(i[u])for(r=0,f=i[u].length;r<f;r++)arguments.callee(n,i[u][r]);t.getCache(n).nodes[t.getNodeCacheId(i.tId)]=null},removeSelectedNode:function(n,i){for(var u=t.getRoot(n),r=0,f=u.curSelectedList.length;r<f;r++)i!==u.curSelectedList[r]&&t.getNodeCache(n,u.curSelectedList[r].tId)||(u.curSelectedList.splice(r,1),r--,f--)},setCache:function(n,t){l[n.treeId]=t},setRoot:function(n,t){c[n.treeId]=t},setZTreeTools:function(){for(var n=0,t=e.zTreeTools.length;n<t;n++)e.zTreeTools[n].apply(this,arguments)},transformToArrayFormat:function(n,i){var e,r,f,o;if(!i)return[];if(e=n.data.key.children,r=[],u.isArray(i))for(f=0,o=i.length;f<o;f++)r.push(i[f]),i[f][e]&&(r=r.concat(t.transformToArrayFormat(n,i[f][e])));else r.push(i),i[e]&&(r=r.concat(t.transformToArrayFormat(n,i[e])));return r},transformTozTreeFormat:function(n,t){var i,e,o=n.data.simpleData.idKey,f=n.data.simpleData.pIdKey,s=n.data.key.children,h,r;if(!o||o==""||!t)return[];if(u.isArray(t)){for(h=[],r=[],i=0,e=t.length;i<e;i++)r[t[i][o]]=t[i];for(i=0,e=t.length;i<e;i++)r[t[i][f]]&&t[i][o]!=t[i][f]?(r[t[i][f]][s]||(r[t[i][f]][s]=[]),r[t[i][f]][s].push(t[i])):h.push(t[i]);return h}return[t]}},o={bindEvent:function(){for(var n=0,t=e.bind.length;n<t;n++)e.bind[n].apply(this,arguments)},unbindEvent:function(){for(var n=0,t=e.unbind.length;n<t;n++)e.unbind[n].apply(this,arguments)},bindTree:function(n){var i={treeId:n.treeId},t=n.treeObj;n.view.txtSelectedEnable||t.bind("selectstart",function(n){var t=n.originalEvent.srcElement.nodeName.toLowerCase();return t==="input"||t==="textarea"}).css({"-moz-user-select":"-moz-none"});t.bind("click",i,o.proxy);t.bind("dblclick",i,o.proxy);t.bind("mouseover",i,o.proxy);t.bind("mouseout",i,o.proxy);t.bind("mousedown",i,o.proxy);t.bind("mouseup",i,o.proxy);t.bind("contextmenu",i,o.proxy)},unbindTree:function(n){var t=n.treeObj;t.unbind("click",o.proxy).unbind("dblclick",o.proxy).unbind("mouseover",o.proxy).unbind("mouseout",o.proxy).unbind("mousedown",o.proxy).unbind("mouseup",o.proxy).unbind("contextmenu",o.proxy)},doProxy:function(){for(var t,i=[],n=0,r=e.proxys.length;n<r;n++)if(t=e.proxys[n].apply(this,arguments),i.push(t),t.stop)break;return i},proxy:function(n){var c=t.getSetting(n.data.treeId),f,h,i;if(!u.uCanDo(c,n))return!0;var e=o.doProxy(n),r=!0,s=!1;for(f=0,h=e.length;f<h;f++)i=e[f],i.nodeEventCallback&&(s=!0,r=i.nodeEventCallback.apply(i,[n,i.node])&&r),i.treeEventCallback&&(s=!0,r=i.treeEventCallback.apply(i,[n,i.node])&&r);return r}},h={onSwitchNode:function(n,i){var f=t.getSetting(n.data.treeId);if(i.open){if(u.apply(f.callback.beforeCollapse,[f.treeId,i],!0)==!1)return!0;t.getRoot(f).expandTriggerFlag=!0;r.switchNode(f,i)}else{if(u.apply(f.callback.beforeExpand,[f.treeId,i],!0)==!1)return!0;t.getRoot(f).expandTriggerFlag=!0;r.switchNode(f,i)}return!0},onClickNode:function(n,f){var e=t.getSetting(n.data.treeId),o=e.view.autoCancelSelected&&(n.ctrlKey||n.metaKey)&&t.isSelectedNode(e,f)?0:e.view.autoCancelSelected&&(n.ctrlKey||n.metaKey)&&e.view.selectedMulti?2:1;return u.apply(e.callback.beforeClick,[e.treeId,f,o],!0)==!1?!0:(o===0?r.cancelPreSelectedNode(e,f):r.selectNode(e,f,o===2),e.treeObj.trigger(i.event.CLICK,[n,e.treeId,f,o]),!0)},onZTreeMousedown:function(n,i){var r=t.getSetting(n.data.treeId);return u.apply(r.callback.beforeMouseDown,[r.treeId,i],!0)&&u.apply(r.callback.onMouseDown,[n,r.treeId,i]),!0},onZTreeMouseup:function(n,i){var r=t.getSetting(n.data.treeId);return u.apply(r.callback.beforeMouseUp,[r.treeId,i],!0)&&u.apply(r.callback.onMouseUp,[n,r.treeId,i]),!0},onZTreeDblclick:function(n,i){var r=t.getSetting(n.data.treeId);return u.apply(r.callback.beforeDblClick,[r.treeId,i],!0)&&u.apply(r.callback.onDblClick,[n,r.treeId,i]),!0},onZTreeContextmenu:function(n,i){var r=t.getSetting(n.data.treeId);return u.apply(r.callback.beforeRightClick,[r.treeId,i],!0)&&u.apply(r.callback.onRightClick,[n,r.treeId,i]),typeof r.callback.onRightClick!="function"}},u={apply:function(n,t,i){return typeof n=="function"?n.apply(v,t?t:[]):i},canAsync:function(n,t){var i=n.data.key.children;return n.async.enable&&t&&t.isParent&&!(t.zAsync||t[i]&&t[i].length>0)},clone:function(n){var i,t;if(n===null)return null;i=u.isArray(n)?[]:{};for(t in n)i[t]=n[t]instanceof Date?new Date(n[t].getTime()):typeof n[t]=="object"?arguments.callee(n[t]):n[t];return i},eqs:function(n,t){return n.toLowerCase()===t.toLowerCase()},isArray:function(n){return Object.prototype.toString.apply(n)==="[object Array]"},$:function(t,i,r){return!i||typeof i=="string"||(r=i,i=""),typeof t=="string"?n(t,r?r.treeObj.get(0).ownerDocument:null):n("#"+t.tId+i,r?r.treeObj:null)},getMDom:function(n,t,i){if(!t)return null;while(t&&t.id!==n.treeId){for(var r=0,f=i.length;t.tagName&&r<f;r++)if(u.eqs(t.tagName,i[r].tagName)&&t.getAttribute(i[r].attrName)!==null)return t;t=t.parentNode}return null},getNodeMainDom:function(t){return n(t).parent("li").get(0)||n(t).parentsUntil("li").parent().get(0)},isChildOrSelf:function(t,i){return n(t).closest("#"+i).length>0},uCanDo:function(){return!0}},r={addNodes:function(n,e,o,s){if(!n.data.keep.leaf||!e||e.isParent)if(u.isArray(o)||(o=[o]),n.data.simpleData.enable&&(o=t.transformTozTreeFormat(n,o)),e){var h=f(e,i.id.SWITCH,n),c=f(e,i.id.ICON,n),l=f(e,i.id.UL,n);e.open||(r.replaceSwitchClass(e,h,i.folder.CLOSE),r.replaceIcoClass(e,c,i.folder.CLOSE),e.open=!1,l.css({display:"none"}));t.addNodesData(n,e,o);r.createNodes(n,e.level+1,o,e);s||r.expandCollapseParentNode(n,e,!0)}else t.addNodesData(n,t.getRoot(n),o),r.createNodes(n,0,o,null)},appendNodes:function(n,i,u,f,e,o){var h,l,c,v,s,a;if(!u)return[];for(h=[],l=n.data.key.children,c=0,v=u.length;c<v;c++){if(s=u[c],e){var y=f?f:t.getRoot(n),p=y[l],w=p.length==u.length&&c==0,b=c==u.length-1;t.initNode(n,i,s,f,w,b,o);t.addNodeCache(n,s)}a=[];s[l]&&s[l].length>0&&(a=r.appendNodes(n,i+1,s[l],s,e,o&&s.open));o&&(r.makeDOMNodeMainBefore(h,n,s),r.makeDOMNodeLine(h,n,s),t.getBeforeA(n,s,h),r.makeDOMNodeNameBefore(h,n,s),t.getInnerBeforeA(n,s,h),r.makeDOMNodeIcon(h,n,s),t.getInnerAfterA(n,s,h),r.makeDOMNodeNameAfter(h,n,s),t.getAfterA(n,s,h),s.isParent&&s.open&&r.makeUlHtml(n,s,h,a.join("")),r.makeDOMNodeMainAfter(h,n,s),t.addCreatedNode(n,s))}return h},appendParentULDom:function(n,t){var o=[],u=f(t,n),e,s,h;u.get(0)||!t.parentTId||(r.appendParentULDom(n,t.getParentNode()),u=f(t,n));e=f(t,i.id.UL,n);e.get(0)&&e.remove();s=n.data.key.children;h=r.appendNodes(n,t.level+1,t[s],t,!1,!0);r.makeUlHtml(n,t,o,h.join(""));u.append(o.join(""))},asyncNode:function(e,o,s,h){var c,v,b,a,l,y,p,w;if(o&&!o.isParent)return u.apply(h),!1;if(o&&o.isAjaxing)return!1;if(u.apply(e.callback.beforeAsync,[e.treeId,o],!0)==!1)return u.apply(h),!1;for(o&&(o.isAjaxing=!0,b=f(o,i.id.ICON,e),b.attr({style:"","class":i.className.BUTTON+" "+i.className.ICO_LOADING})),a={},c=0,v=e.async.autoParam.length;o&&c<v;c++)l=e.async.autoParam[c].split("="),y=l,l.length>1&&(y=l[1],l=l[0]),a[y]=o[l];if(u.isArray(e.async.otherParam))for(c=0,v=e.async.otherParam.length;c<v;c+=2)a[e.async.otherParam[c]]=e.async.otherParam[c+1];else for(p in e.async.otherParam)a[p]=e.async.otherParam[p];return w=t.getRoot(e)._ver,n.ajax({contentType:e.async.contentType,cache:!1,type:e.async.type,url:u.apply(e.async.url,[e.treeId,o],e.async.url),data:a,dataType:e.async.dataType,success:function(n){if(w==t.getRoot(e)._ver){var f=[];try{f=n&&n.length!=0?typeof n=="string"?eval("("+n+")"):n:[]}catch(c){f=n}o&&(o.isAjaxing=null,o.zAsync=!0);r.setNodeLineIcos(e,o);f&&f!==""?(f=u.apply(e.async.dataFilter,[e.treeId,o,f],f),r.addNodes(e,o,!f?[]:u.clone(f),!!s)):r.addNodes(e,o,[],!!s);e.treeObj.trigger(i.event.ASYNC_SUCCESS,[e.treeId,o,n]);u.apply(h)}},error:function(n,u,f){w==t.getRoot(e)._ver&&(o&&(o.isAjaxing=null),r.setNodeLineIcos(e,o),e.treeObj.trigger(i.event.ASYNC_ERROR,[e.treeId,o,n,u,f]))}}),!0},cancelPreSelectedNode:function(n,r,u){for(var h=t.getRoot(n).curSelectedList,e,s=h.length-1;s>=0;s--)if(e=h[s],r===e||!r&&(!u||u!==e))if(f(e,i.id.A,n).removeClass(i.node.CURSELECTED),r){t.removeSelectedNode(n,r);n.treeObj.trigger(i.event.UNSELECTED,[o,n.treeId,e]);break}else h.splice(s,1),n.treeObj.trigger(i.event.UNSELECTED,[o,n.treeId,e])},createNodeCallback:function(n){var r,f;if(!!n.callback.onNodeCreated||!!n.view.addDiyDom)for(r=t.getRoot(n);r.createdNodes.length>0;)f=r.createdNodes.shift(),u.apply(n.view.addDiyDom,[n.treeId,f]),!n.callback.onNodeCreated||n.treeObj.trigger(i.event.NODECREATED,[n.treeId,f])},createNodes:function(n,u,e,o){var s,h;if(e&&e.length!=0){var c=t.getRoot(n),l=n.data.key.children,a=!o||o.open||!!f(o[l][0],n).get(0);c.createdNodes=[];s=r.appendNodes(n,u,e,o,!0,a);o?(h=f(o,i.id.UL,n),h.get(0)&&h.append(s.join(""))):n.treeObj.append(s.join(""));r.createNodeCallback(n)}},destroy:function(n){n&&(t.initCache(n),t.initRoot(n),o.unbindTree(n),o.unbindEvent(n),n.treeObj.empty(),delete s[n.treeId])},expandCollapseNode:function(n,e,o,s,h){var y=t.getRoot(n),c=n.data.key.children,a;if(!e){u.apply(h,[]);return}if(y.expandTriggerFlag&&(a=h,h=function(){a&&a();e.open?n.treeObj.trigger(i.event.EXPAND,[n.treeId,e]):n.treeObj.trigger(i.event.COLLAPSE,[n.treeId,e])},y.expandTriggerFlag=!1),!e.open&&e.isParent&&(!f(e,i.id.UL,n).get(0)||e[c]&&e[c].length>0&&!f(e[c][0],n).get(0))&&(r.appendParentULDom(n,e),r.createNodeCallback(n)),e.open==o){u.apply(h,[]);return}var l=f(e,i.id.UL,n),p=f(e,i.id.SWITCH,n),v=f(e,i.id.ICON,n);e.isParent?(e.open=!e.open,e.iconOpen&&e.iconClose&&v.attr("style",r.makeNodeIcoStyle(n,e)),e.open?(r.replaceSwitchClass(e,p,i.folder.OPEN),r.replaceIcoClass(e,v,i.folder.OPEN),s==!1||n.view.expandSpeed==""?(l.show(),u.apply(h,[])):e[c]&&e[c].length>0?l.slideDown(n.view.expandSpeed,h):(l.show(),u.apply(h,[]))):(r.replaceSwitchClass(e,p,i.folder.CLOSE),r.replaceIcoClass(e,v,i.folder.CLOSE),s!=!1&&n.view.expandSpeed!=""&&e[c]&&e[c].length>0?l.slideUp(n.view.expandSpeed,h):(l.hide(),u.apply(h,[])))):u.apply(h,[])},expandCollapseParentNode:function(n,t,i,u,f){if(t){if(t.parentTId)r.expandCollapseNode(n,t,i,u);else{r.expandCollapseNode(n,t,i,u,f);return}t.parentTId&&r.expandCollapseParentNode(n,t.getParentNode(),i,u,f)}},expandCollapseSonNode:function(n,i,u,f,e){var l=t.getRoot(n),h=n.data.key.children,s=i?i[h]:l[h],a=i?!1:f,v=t.getRoot(n).expandTriggerFlag,o,c;if(t.getRoot(n).expandTriggerFlag=!1,s)for(o=0,c=s.length;o<c;o++)s[o]&&r.expandCollapseSonNode(n,s[o],u,a);t.getRoot(n).expandTriggerFlag=v;r.expandCollapseNode(n,i,u,f,e)},isSelectedNode:function(n,i){if(!i)return!1;for(var u=t.getRoot(n).curSelectedList,r=u.length-1;r>=0;r--)if(i===u[r])return!0;return!1},makeDOMNodeIcon:function(n,u,f){var e=t.getNodeName(u,f),o=u.view.nameIsHTML?e:e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;");n.push("<span id='",f.tId,i.id.ICON,"' title='' treeNode",i.id.ICON," class='",r.makeNodeIcoClass(u,f),"' style='",r.makeNodeIcoStyle(u,f),"'><\/span><span id='",f.tId,i.id.SPAN,"'>",o,"<\/span>")},makeDOMNodeLine:function(n,t,u){n.push("<span id='",u.tId,i.id.SWITCH,"' title='' class='",r.makeNodeLineClass(t,u),"' treeNode",i.id.SWITCH,"><\/span>")},makeDOMNodeMainAfter:function(n){n.push("<\/li>")},makeDOMNodeMainBefore:function(n,t,r){n.push("<li id='",r.tId,"' class='",i.className.LEVEL,r.level,"' tabindex='0' hidefocus='true' treenode>")},makeDOMNodeNameAfter:function(n){n.push("<\/a>")},makeDOMNodeNameBefore:function(n,f,e){var s=t.getNodeTitle(f,e),o=r.makeNodeUrl(f,e),h=r.makeNodeFontCss(f,e),c=[];for(var l in h)c.push(l,":",h[l],";");n.push("<a id='",e.tId,i.id.A,"' class='",i.className.LEVEL,e.level,"' treeNode",i.id.A,' onclick="',e.click||"",'" ',o!=null&&o.length>0?"href='"+o+"'":""," target='",r.makeNodeTarget(e),"' style='",c.join(""),"'");u.apply(f.view.showTitle,[f.treeId,e],f.view.showTitle)&&s&&n.push("title='",s.replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),"'");n.push(">")},makeNodeFontCss:function(n,t){var i=u.apply(n.view.fontCss,[n.treeId,t],n.view.fontCss);return i&&typeof i!="function"?i:{}},makeNodeIcoClass:function(n,t){var r=["ico"];return t.isAjaxing||(r[0]=(t.iconSkin?t.iconSkin+"_":"")+r[0],t.isParent?r.push(t.open?i.folder.OPEN:i.folder.CLOSE):r.push(i.folder.DOCU)),i.className.BUTTON+" "+r.join("_")},makeNodeIcoStyle:function(n,t){var i=[],r;return t.isAjaxing||(r=t.isParent&&t.iconOpen&&t.iconClose?t.open?t.iconOpen:t.iconClose:t.icon,r&&i.push("background:url(",r,") 0 0 no-repeat;"),n.view.showIcon!=!1&&u.apply(n.view.showIcon,[n.treeId,t],!0)||i.push("width:0px;height:0px;")),i.join("")},makeNodeLineClass:function(n,t){var u=[];return n.view.showLine?t.level==0&&t.isFirstNode&&t.isLastNode?u.push(i.line.ROOT):t.level==0&&t.isFirstNode?u.push(i.line.ROOTS):t.isLastNode?u.push(i.line.BOTTOM):u.push(i.line.CENTER):u.push(i.line.NOLINE),t.isParent?u.push(t.open?i.folder.OPEN:i.folder.CLOSE):u.push(i.folder.DOCU),r.makeNodeLineClassEx(t)+u.join("_")},makeNodeLineClassEx:function(n){return i.className.BUTTON+" "+i.className.LEVEL+n.level+" "+i.className.SWITCH+" "},makeNodeTarget:function(n){return n.target||"_blank"},makeNodeUrl:function(n,t){var i=n.data.key.url;return t[i]?t[i]:null},makeUlHtml:function(n,t,u,f){u.push("<ul id='",t.tId,i.id.UL,"' class='",i.className.LEVEL,t.level," ",r.makeUlLineClass(n,t),"' style='display:",t.open?"block":"none","'>");u.push(f);u.push("<\/ul>")},makeUlLineClass:function(n,t){return n.view.showLine&&!t.isLastNode?i.line.LINE:""},removeChildNodes:function(n,u){var s,e,o,h,c,l;if(u&&(s=n.data.key.children,e=u[s],e)){for(o=0,h=e.length;o<h;o++)t.removeNodeCache(n,e[o]);t.removeSelectedNode(n);delete u[s];n.data.keep.parent?f(u,i.id.UL,n).empty():(u.isParent=!1,u.open=!1,c=f(u,i.id.SWITCH,n),l=f(u,i.id.ICON,n),r.replaceSwitchClass(u,c,i.folder.DOCU),r.replaceIcoClass(u,l,i.folder.DOCU),f(u,i.id.UL,n).remove())}},setFirstNode:function(n,t){var i=n.data.key.children,r=t[i].length;r>0&&(t[i][0].isFirstNode=!0)},setLastNode:function(n,t){var i=n.data.key.children,r=t[i].length;r>0&&(t[i][r-1].isLastNode=!0)},removeNode:function(n,u){var y=t.getRoot(n),o=n.data.key.children,e=u.parentTId?u.getParentNode():y,c,p,l,h,v,a,s,w;if(u.isFirstNode=!1,u.isLastNode=!1,u.getPreNode=function(){return null},u.getNextNode=function(){return null},t.getNodeCache(n,u.tId)){for(f(u,n).remove(),t.removeNodeCache(n,u),t.removeSelectedNode(n,u),c=0,p=e[o].length;c<p;c++)if(e[o][c].tId==u.tId){e[o].splice(c,1);break}r.setFirstNode(n,e);r.setLastNode(n,e);a=e[o].length;n.data.keep.parent||a!=0?n.view.showLine&&a>0&&(s=e[o][a-1],l=f(s,i.id.UL,n),h=f(s,i.id.SWITCH,n),v=f(s,i.id.ICON,n),e==y?e[o].length==1?r.replaceSwitchClass(s,h,i.line.ROOT):(w=f(e[o][0],i.id.SWITCH,n),r.replaceSwitchClass(e[o][0],w,i.line.ROOTS),r.replaceSwitchClass(s,h,i.line.BOTTOM)):r.replaceSwitchClass(s,h,i.line.BOTTOM),l.removeClass(i.line.LINE)):(e.isParent=!1,e.open=!1,l=f(e,i.id.UL,n),h=f(e,i.id.SWITCH,n),v=f(e,i.id.ICON,n),r.replaceSwitchClass(e,h,i.folder.DOCU),r.replaceIcoClass(e,v,i.folder.DOCU),l.css("display","none"))}},replaceIcoClass:function(n,t,r){var f,u;if(t&&!n.isAjaxing&&(f=t.attr("class"),f!=undefined)){u=f.split("_");switch(r){case i.folder.OPEN:case i.folder.CLOSE:case i.folder.DOCU:u[u.length-1]=r}t.attr("class",u.join("_"))}},replaceSwitchClass:function(n,t,u){var e,f;if(t&&(e=t.attr("class"),e!=undefined)){f=e.split("_");switch(u){case i.line.ROOT:case i.line.ROOTS:case i.line.CENTER:case i.line.BOTTOM:case i.line.NOLINE:f[0]=r.makeNodeLineClassEx(n)+u;break;case i.folder.OPEN:case i.folder.CLOSE:case i.folder.DOCU:f[1]=u}t.attr("class",f.join("_"));u!==i.folder.DOCU?t.removeAttr("disabled"):t.attr("disabled","disabled")}},selectNode:function(n,u,e){e||r.cancelPreSelectedNode(n,null,u);f(u,i.id.A,n).addClass(i.node.CURSELECTED);t.addSelectedNode(n,u);n.treeObj.trigger(i.event.SELECTED,[o,n.treeId,u])},setNodeFontCss:function(n,t){var e=f(t,i.id.A,n),u=r.makeNodeFontCss(n,t);u&&e.css(u)},setNodeLineIcos:function(n,t){if(t){var u=f(t,i.id.SWITCH,n),o=f(t,i.id.UL,n),e=f(t,i.id.ICON,n),s=r.makeUlLineClass(n,t);s.length==0?o.removeClass(i.line.LINE):o.addClass(s);u.attr("class",r.makeNodeLineClass(n,t));t.isParent?u.removeAttr("disabled"):u.attr("disabled","disabled");e.removeAttr("style");e.attr("style",r.makeNodeIcoStyle(n,t));e.attr("class",r.makeNodeIcoClass(n,t))}},setNodeName:function(n,r){var o=t.getNodeTitle(n,r),e=f(r,i.id.SPAN,n),s;e.empty();n.view.nameIsHTML?e.html(t.getNodeName(n,r)):e.text(t.getNodeName(n,r));u.apply(n.view.showTitle,[n.treeId,r],n.view.showTitle)&&(s=f(r,i.id.A,n),s.attr("title",o?o:""))},setNodeTarget:function(n,t){var u=f(t,i.id.A,n);u.attr("target",r.makeNodeTarget(t))},setNodeUrl:function(n,t){var e=f(t,i.id.A,n),u=r.makeNodeUrl(n,t);u==null||u.length==0?e.removeAttr("href"):e.attr("href",u)},switchNode:function(n,t){if(t.open||!u.canAsync(n,t))r.expandCollapseNode(n,t,!t.open);else if(n.async.enable){if(!r.asyncNode(n,t)){r.expandCollapseNode(n,t,!t.open);return}}else t&&r.expandCollapseNode(n,t,!t.open)}};n.fn.zTree={consts:{className:{BUTTON:"button",LEVEL:"level",ICO_LOADING:"ico_loading",SWITCH:"switch"},event:{NODECREATED:"ztree_nodeCreated",CLICK:"ztree_click",EXPAND:"ztree_expand",COLLAPSE:"ztree_collapse",ASYNC_SUCCESS:"ztree_async_success",ASYNC_ERROR:"ztree_async_error",REMOVE:"ztree_remove",SELECTED:"ztree_selected",UNSELECTED:"ztree_unselected"},id:{A:"_a",ICON:"_ico",SPAN:"_span",SWITCH:"_switch",UL:"_ul"},line:{ROOT:"root",ROOTS:"roots",CENTER:"center",BOTTOM:"bottom",NOLINE:"noline",LINE:"line"},folder:{OPEN:"open",CLOSE:"close",DOCU:"docu"},node:{CURSELECTED:"curSelectedNode"}},_z:{tools:u,view:r,event:o,data:t},getZTreeObj:function(n){var i=t.getZTreeTools(n);return i?i:null},destroy:function(n){if(!!n&&n.length>0)r.destroy(t.getSetting(n));else for(var i in s)r.destroy(s[i])},init:function(e,h,c){var l=u.clone(a),v,y,p;return n.extend(!0,l,h),l.treeId=e.attr("id"),l.treeObj=e,l.treeObj.empty(),s[l.treeId]=l,typeof document.body.style.maxHeight=="undefined"&&(l.view.expandSpeed=""),t.initRoot(l),v=t.getRoot(l),y=l.data.key.children,c=c?u.clone(u.isArray(c)?c:[c]):[],v[y]=l.data.simpleData.enable?t.transformTozTreeFormat(l,c):c,t.initCache(l),o.unbindTree(l),o.bindTree(l),o.unbindEvent(l),o.bindEvent(l),p={setting:l,addNodes:function(n,t,i){function e(){r.addNodes(l,n,f,i==!0)}if(!t||(n||(n=null),n&&!n.isParent&&l.data.keep.leaf))return null;var f=u.clone(u.isArray(t)?t:[t]);return u.canAsync(l,n)?r.asyncNode(l,n,i,e):e(),f},cancelSelectedNode:function(n){r.cancelPreSelectedNode(l,n)},destroy:function(){r.destroy(l)},expandAll:function(n){return n=!!n,r.expandCollapseSonNode(l,null,n,!0),n},expandNode:function(n,i,e,o,s){if(!n||!n.isParent||(i!==!0&&i!==!1&&(i=!n.open),s=!!s,s&&i&&u.apply(l.callback.beforeExpand,[l.treeId,n],!0)==!1)||s&&!i&&u.apply(l.callback.beforeCollapse,[l.treeId,n],!0)==!1||(i&&n.parentTId&&r.expandCollapseParentNode(l,n.getParentNode(),i,!1),i===n.open&&!e))return null;if(t.getRoot(l).expandTriggerFlag=s,!u.canAsync(l,n)&&e)r.expandCollapseSonNode(l,n,i,!0,function(){if(o!==!1)try{f(n,l).focus().blur()}catch(t){}});else if(n.open=!i,r.switchNode(this.setting,n),o!==!1)try{f(n,l).focus().blur()}catch(h){}return i},getNodes:function(){return t.getNodes(l)},getNodeByParam:function(n,i,r){return n?t.getNodeByParam(l,r?r[l.data.key.children]:t.getNodes(l),n,i):null},getNodeByTId:function(n){return t.getNodeCache(l,n)},getNodesByParam:function(n,i,r){return n?t.getNodesByParam(l,r?r[l.data.key.children]:t.getNodes(l),n,i):null},getNodesByParamFuzzy:function(n,i,r){return n?t.getNodesByParamFuzzy(l,r?r[l.data.key.children]:t.getNodes(l),n,i):null},getNodesByFilter:function(n,i,r,u){return(i=!!i,!n||typeof n!="function")?i?null:[]:t.getNodesByFilter(l,r?r[l.data.key.children]:t.getNodes(l),n,i,u)},getNodeIndex:function(n){var r,u,i,f;if(!n)return null;for(r=l.data.key.children,u=n.parentTId?n.getParentNode():t.getRoot(l),i=0,f=u[r].length;i<f;i++)if(u[r][i]==n)return i;return-1},getSelectedNodes:function(){for(var i=[],r=t.getRoot(l).curSelectedList,n=0,u=r.length;n<u;n++)i.push(r[n]);return i},isSelectedNode:function(n){return t.isSelectedNode(l,n)},reAsyncChildNodes:function(n,u,e){var s,o,h,c,a;if(this.setting.async.enable){if(s=!n,s&&(n=t.getRoot(l)),u=="refresh"){for(o=this.setting.data.key.children,h=0,c=n[o]?n[o].length:0;h<c;h++)t.removeNodeCache(l,n[o][h]);t.removeSelectedNode(l);n[o]=[];s?this.setting.treeObj.empty():(a=f(n,i.id.UL,l),a.empty())}r.asyncNode(this.setting,s?null:n,!!e)}},refresh:function(){this.setting.treeObj.empty();var n=t.getRoot(l),i=n[l.data.key.children];t.initRoot(l);n[l.data.key.children]=i;t.initCache(l);r.createNodes(l,0,n[l.data.key.children])},removeChildNodes:function(n){if(!n)return null;var i=l.data.key.children,t=n[i];return r.removeChildNodes(l,n),t?t:null},removeNode:function(n,t){n&&((t=!!t,t&&u.apply(l.callback.beforeRemove,[l.treeId,n],!0)==!1)||(r.removeNode(l,n),t&&this.setting.treeObj.trigger(i.event.REMOVE,[l.treeId,n])))},selectNode:function(n,t){if(n&&u.uCanDo(l)){if(t=l.view.selectedMulti&&t,n.parentTId)r.expandCollapseParentNode(l,n.getParentNode(),!0,!1,function(){try{f(n,l).focus().blur()}catch(t){}});else try{f(n,l).focus().blur()}catch(i){}r.selectNode(l,n,t)}},transformTozTreeNodes:function(n){return t.transformTozTreeFormat(l,n)},transformToArray:function(n){return t.transformToArrayFormat(l,n)},updateNode:function(n){if(n){var t=f(n,l);t.get(0)&&u.uCanDo(l)&&(r.setNodeName(l,n),r.setNodeTarget(l,n),r.setNodeUrl(l,n),r.setNodeLineIcos(l,n),r.setNodeFontCss(l,n))}}},v.treeTools=p,t.setZTreeTools(l,p),v[y]&&v[y].length>0?r.createNodes(l,0,v[y]):l.async.enable&&l.async.url&&l.async.url!==""&&r.asyncNode(l),p}};var v=n.fn.zTree,f=u.$,i=v.consts})(jQuery),function(n){var o={event:{CHECK:"ztree_check"},id:{CHECK:"_check"},checkbox:{STYLE:"checkbox",DEFAULT:"chk",DISABLED:"disable",FALSE:"false",TRUE:"true",FULL:"full",PART:"part",FOCUS:"focus"},radio:{STYLE:"radio",TYPE_ALL:"all",TYPE_LEVEL:"level"}},a={check:{enable:!1,autoCheckTrigger:!1,chkStyle:o.checkbox.STYLE,nocheckInherit:!1,chkDisabledInherit:!1,radioType:o.radio.TYPE_LEVEL,chkboxType:{Y:"ps",N:"ps"}},data:{key:{checked:"checked"}},callback:{beforeCheck:null,onCheck:null}},v=function(n){var t=r.getRoot(n);t.radioCheckedList=[]},y=function(){},p=function(n){var t=n.treeObj,r=i.event;t.bind(r.CHECK,function(t,i,r,f){t.srcEvent=i;u.apply(n.callback.onCheck,[t,r,f])})},w=function(n){var t=n.treeObj,r=i.event;t.unbind(r.CHECK)},b=function(n){var t=n.target,o=r.getSetting(n.data.treeId),e="",c=null,f="",h=null;if(u.eqs(n.type,"mouseover")?o.check.enable&&u.eqs(t.tagName,"span")&&t.getAttribute("treeNode"+i.id.CHECK)!==null&&(e=u.getNodeMainDom(t).id,f="mouseoverCheck"):u.eqs(n.type,"mouseout")?o.check.enable&&u.eqs(t.tagName,"span")&&t.getAttribute("treeNode"+i.id.CHECK)!==null&&(e=u.getNodeMainDom(t).id,f="mouseoutCheck"):u.eqs(n.type,"click")&&o.check.enable&&u.eqs(t.tagName,"span")&&t.getAttribute("treeNode"+i.id.CHECK)!==null&&(e=u.getNodeMainDom(t).id,f="checkNode"),e.length>0){c=r.getNodeCache(o,e);switch(f){case"checkNode":h=s.onCheckNode;break;case"mouseoverCheck":h=s.onMouseoverCheck;break;case"mouseoutCheck":h=s.onMouseoutCheck}}return{stop:f==="checkNode",node:c,nodeEventType:f,nodeEventCallback:h,treeEventType:"",treeEventCallback:null}},k=function(n,t,f,e){var o,s;f&&(o=n.data.key.checked,typeof f[o]=="string"&&(f[o]=u.eqs(f[o],"true")),f[o]=!!f[o],f.checkedOld=f[o],typeof f.nocheck=="string"&&(f.nocheck=u.eqs(f.nocheck,"true")),f.nocheck=!!f.nocheck||n.check.nocheckInherit&&e&&!!e.nocheck,typeof f.chkDisabled=="string"&&(f.chkDisabled=u.eqs(f.chkDisabled,"true")),f.chkDisabled=!!f.chkDisabled||n.check.chkDisabledInherit&&e&&!!e.chkDisabled,typeof f.halfCheck=="string"&&(f.halfCheck=u.eqs(f.halfCheck,"true")),f.halfCheck=!!f.halfCheck,f.check_Child_State=-1,f.check_Focus=!1,f.getCheckStatus=function(){return r.getCheckStatus(n,f)},n.check.chkStyle==i.radio.STYLE&&n.check.radioType==i.radio.TYPE_ALL&&f[o]&&(s=r.getRoot(n),s.radioCheckedList.push(f)))},d=function(n,u,f){var e=n.data.key.checked;n.check.enable&&(r.makeChkFlag(n,u),f.push("<span ID='",u.tId,i.id.CHECK,"' class='",t.makeChkClass(n,u),"' treeNode",i.id.CHECK,u.nocheck===!0?" style='display:none;'":"","><\/span>"))},g=function(n,e){e.checkNode=function(n,r,e,o){var s=this.setting.data.key.checked,h;if(n.chkDisabled!==!0){if(r!==!0&&r!==!1&&(r=!n[s]),o=!!o,n[s]!==r||e){if(o&&u.apply(this.setting.callback.beforeCheck,[this.setting.treeId,n],!0)==!1)return}else return;u.uCanDo(this.setting)&&this.setting.check.enable&&n.nocheck!==!0&&(n[s]=r,h=f(n,i.id.CHECK,this.setting),(e||this.setting.check.chkStyle===i.radio.STYLE)&&t.checkNodeRelation(this.setting,n),t.setChkClass(this.setting,h,n),t.repairParentChkClassWithSelf(this.setting,n),o&&this.setting.treeObj.trigger(i.event.CHECK,[null,this.setting.treeId,n]))}};e.checkAllNodes=function(n){t.repairAllChk(this.setting,!!n)};e.getCheckedNodes=function(n){var t=this.setting.data.key.children;return n=n!==!1,r.getTreeCheckedNodes(this.setting,r.getRoot(this.setting)[t],n)};e.getChangeCheckedNodes=function(){var n=this.setting.data.key.children;return r.getTreeChangeCheckedNodes(this.setting,r.getRoot(this.setting)[n])};e.setChkDisabled=function(n,i,r,u){i=!!i;r=!!r;u=!!u;t.repairSonChkDisabled(this.setting,n,i,u);t.repairParentChkDisabled(this.setting,n.getParentNode(),i,r)};var o=e.updateNode;e.updateNode=function(n,r){var s,h;(o&&o.apply(e,arguments),n&&this.setting.check.enable)&&(s=f(n,this.setting),s.get(0)&&u.uCanDo(this.setting)&&(h=f(n,i.id.CHECK,this.setting),(r==!0||this.setting.check.chkStyle===i.radio.STYLE)&&t.checkNodeRelation(this.setting,n),t.setChkClass(this.setting,h,n),t.repairParentChkClassWithSelf(this.setting,n)))}},nt={getRadioCheckedList:function(n){for(var i=r.getRoot(n).radioCheckedList,t=0,u=i.length;t<u;t++)r.getNodeCache(n,i[t].tId)||(i.splice(t,1),t--,u--);return i},getCheckStatus:function(n,t){if(!n.check.enable||t.nocheck||t.chkDisabled)return null;var r=n.data.key.checked;return{checked:t[r],half:t.halfCheck?t.halfCheck:n.check.chkStyle==i.radio.STYLE?t.check_Child_State===2:t[r]?t.check_Child_State>-1&&t.check_Child_State<2:t.check_Child_State>0}},getTreeCheckedNodes:function(n,t,u,f){var e,s;if(!t)return[];var h=n.data.key.children,c=n.data.key.checked,o=u&&n.check.chkStyle==i.radio.STYLE&&n.check.radioType==i.radio.TYPE_ALL;for(f=f?f:[],e=0,s=t.length;e<s;e++){if(t[e].nocheck!==!0&&t[e].chkDisabled!==!0&&t[e][c]==u&&(f.push(t[e]),o))break;if(r.getTreeCheckedNodes(n,t[e][h],u,f),o&&f.length>0)break}return f},getTreeChangeCheckedNodes:function(n,t,i){var f,e,u,o;if(!t)return[];for(f=n.data.key.children,e=n.data.key.checked,i=i?i:[],u=0,o=t.length;u<o;u++)t[u].nocheck!==!0&&t[u].chkDisabled!==!0&&t[u][e]!=t[u].checkedOld&&i.push(t[u]),r.getTreeChangeCheckedNodes(n,t[u][f],i);return i},makeChkFlag:function(n,t){var e,h,r,u;if(t){var o=n.data.key.children,s=n.data.key.checked,f=-1;if(t[o])for(e=0,h=t[o].length;e<h;e++)if(r=t[o][e],u=-1,n.check.chkStyle==i.radio.STYLE)if(u=r.nocheck===!0||r.chkDisabled===!0?r.check_Child_State:r.halfCheck===!0?2:r[s]?2:r.check_Child_State>0?2:0,u==2){f=2;break}else u==0&&(f=0);else if(n.check.chkStyle==i.checkbox.STYLE)if(u=r.nocheck===!0||r.chkDisabled===!0?r.check_Child_State:r.halfCheck===!0?1:r[s]?r.check_Child_State===-1||r.check_Child_State===2?2:1:r.check_Child_State>0?1:0,u===1){f=1;break}else if(u===2&&f>-1&&e>0&&u!==f){f=1;break}else if(f===2&&u>-1&&u<2){f=1;break}else u>-1&&(f=u);t.check_Child_State=f}}},s={onCheckNode:function(n,e){var o,s,h;return e.chkDisabled===!0?!1:(o=r.getSetting(n.data.treeId),s=o.data.key.checked,u.apply(o.callback.beforeCheck,[o.treeId,e],!0)==!1)?!0:(e[s]=!e[s],t.checkNodeRelation(o,e),h=f(e,i.id.CHECK,o),t.setChkClass(o,h,e),t.repairParentChkClassWithSelf(o,e),o.treeObj.trigger(i.event.CHECK,[n,o.treeId,e]),!0)},onMouseoverCheck:function(n,u){if(u.chkDisabled===!0)return!1;var e=r.getSetting(n.data.treeId),o=f(u,i.id.CHECK,e);return u.check_Focus=!0,t.setChkClass(e,o,u),!0},onMouseoutCheck:function(n,u){if(u.chkDisabled===!0)return!1;var e=r.getSetting(n.data.treeId),o=f(u,i.id.CHECK,e);return u.check_Focus=!1,t.setChkClass(e,o,u),!0}},tt={checkNodeRelation:function(n,u){var e,o,l,c=n.data.key.children,s=n.data.key.checked,a=i.radio,h,v;if(n.check.chkStyle==a.STYLE){if(h=r.getRadioCheckedList(n),u[s])if(n.check.radioType==a.TYPE_ALL){for(o=h.length-1;o>=0;o--)e=h[o],e[s]&&e!=u&&(e[s]=!1,h.splice(o,1),t.setChkClass(n,f(e,i.id.CHECK,n),e),e.parentTId!=u.parentTId&&t.repairParentChkClassWithSelf(n,e));h.push(u)}else for(v=u.parentTId?u.getParentNode():r.getRoot(n),o=0,l=v[c].length;o<l;o++)e=v[c][o],e[s]&&e!=u&&(e[s]=!1,t.setChkClass(n,f(e,i.id.CHECK,n),e));else if(n.check.radioType==a.TYPE_ALL)for(o=0,l=h.length;o<l;o++)if(u==h[o]){h.splice(o,1);break}}else u[s]&&(!u[c]||u[c].length==0||n.check.chkboxType.Y.indexOf("s")>-1)&&t.setSonNodeCheckBox(n,u,!0),!u[s]&&(!u[c]||u[c].length==0||n.check.chkboxType.N.indexOf("s")>-1)&&t.setSonNodeCheckBox(n,u,!1),u[s]&&n.check.chkboxType.Y.indexOf("p")>-1&&t.setParentNodeCheckBox(n,u,!0),!u[s]&&n.check.chkboxType.N.indexOf("p")>-1&&t.setParentNodeCheckBox(n,u,!1)},makeChkClass:function(n,t){var f=n.data.key.checked,r=i.checkbox,o=i.radio,e="",u;return e=t.chkDisabled===!0?r.DISABLED:t.halfCheck?r.PART:n.check.chkStyle==o.STYLE?t.check_Child_State<1?r.FULL:r.PART:t[f]?t.check_Child_State===2||t.check_Child_State===-1?r.FULL:r.PART:t.check_Child_State<1?r.FULL:r.PART,u=n.check.chkStyle+"_"+(t[f]?r.TRUE:r.FALSE)+"_"+e,u=t.check_Focus&&t.chkDisabled!==!0?u+"_"+r.FOCUS:u,i.className.BUTTON+" "+r.DEFAULT+" "+u},repairAllChk:function(n,u){var e,h,f;if(n.check.enable&&n.check.chkStyle===i.checkbox.STYLE){var c=n.data.key.checked,o=n.data.key.children,s=r.getRoot(n);for(e=0,h=s[o].length;e<h;e++)f=s[o][e],f.nocheck!==!0&&f.chkDisabled!==!0&&(f[c]=u),t.setSonNodeCheckBox(n,f,u)}},repairChkClass:function(n,u){if(u&&(r.makeChkFlag(n,u),u.nocheck!==!0)){var e=f(u,i.id.CHECK,n);t.setChkClass(n,e,u)}},repairParentChkClass:function(n,i){if(i&&i.parentTId){var r=i.getParentNode();t.repairChkClass(n,r);t.repairParentChkClass(n,r)}},repairParentChkClassWithSelf:function(n,i){if(i){var r=n.data.key.children;i[r]&&i[r].length>0?t.repairParentChkClass(n,i[r][0]):t.repairParentChkClass(n,i)}},repairSonChkDisabled:function(n,i,r,u){var f,e,o,s;if(i&&(f=n.data.key.children,i.chkDisabled!=r&&(i.chkDisabled=r),t.repairChkClass(n,i),i[f]&&u))for(e=0,o=i[f].length;e<o;e++)s=i[f][e],t.repairSonChkDisabled(n,s,r,u)},repairParentChkDisabled:function(n,i,r,u){i&&(i.chkDisabled!=r&&u&&(i.chkDisabled=r),t.repairChkClass(n,i),t.repairParentChkDisabled(n,i.getParentNode(),r,u))},setChkClass:function(n,i,r){i&&(r.nocheck===!0?i.hide():i.show(),i.attr("class",t.makeChkClass(n,r)))},setParentNodeCheckBox:function(n,u,e,o){var v=n.data.key.children,l=n.data.key.checked,y=f(u,i.id.CHECK,n),c,h,s,a;if(o||(o=u),r.makeChkFlag(n,u),u.nocheck!==!0&&u.chkDisabled!==!0&&(u[l]=e,t.setChkClass(n,y,u),n.check.autoCheckTrigger&&u!=o&&n.treeObj.trigger(i.event.CHECK,[null,n.treeId,u])),u.parentTId){if(c=!0,!e)for(h=u.getParentNode()[v],s=0,a=h.length;s<a;s++)if(h[s].nocheck!==!0&&h[s].chkDisabled!==!0&&h[s][l]||(h[s].nocheck===!0||h[s].chkDisabled===!0)&&h[s].check_Child_State>0){c=!1;break}c&&t.setParentNodeCheckBox(n,u.getParentNode(),e,o)}},setSonNodeCheckBox:function(n,u,e,o){var h,c,a,l;if(u){var s=n.data.key.children,v=n.data.key.checked,y=f(u,i.id.CHECK,n);if(o||(o=u),h=!1,u[s])for(c=0,a=u[s].length;c<a&&u.chkDisabled!==!0;c++)l=u[s][c],t.setSonNodeCheckBox(n,l,e,o),l.chkDisabled===!0&&(h=!0);u!=r.getRoot(n)&&u.chkDisabled!==!0&&(h&&u.nocheck!==!0&&r.makeChkFlag(n,u),u.nocheck!==!0&&u.chkDisabled!==!0?(u[v]=e,h||(u.check_Child_State=u[s]&&u[s].length>0?e?2:0:-1)):u.check_Child_State=-1,t.setChkClass(n,y,u),n.check.autoCheckTrigger&&u!=o&&u.nocheck!==!0&&u.chkDisabled!==!0&&n.treeObj.trigger(i.event.CHECK,[null,n.treeId,u]))}}},it={tools:{},view:tt,event:{},data:nt},h,c,l;n.extend(!0,n.fn.zTree.consts,o);n.extend(!0,n.fn.zTree._z,it);var e=n.fn.zTree,u=e._z.tools,i=e.consts,t=e._z.view,r=e._z.data,rt=e._z.event,f=u.$;r.exSetting(a);r.addInitBind(p);r.addInitUnBind(w);r.addInitCache(y);r.addInitNode(k);r.addInitProxy(b,!0);r.addInitRoot(v);r.addBeforeA(d);r.addZTreeTools(g);h=t.createNodes;t.createNodes=function(n,i,r,u){(h&&h.apply(t,arguments),r)&&t.repairParentChkClassWithSelf(n,u)};c=t.removeNode;t.removeNode=function(n,i){var r=i.getParentNode();(c&&c.apply(t,arguments),i&&r)&&(t.repairChkClass(n,r),t.repairParentChkClass(n,r))};l=t.appendNodes;t.appendNodes=function(n,i,u,f){var e="";return l&&(e=l.apply(t,arguments)),f&&r.makeChkFlag(n,f),e}}(jQuery),function(n){var e={event:{DRAG:"ztree_drag",DROP:"ztree_drop",RENAME:"ztree_rename",DRAGMOVE:"ztree_dragmove"},id:{EDIT:"_edit",INPUT:"_input",REMOVE:"_remove"},move:{TYPE_INNER:"inner",TYPE_PREV:"prev",TYPE_NEXT:"next"},node:{CURSELECTED_EDIT:"curSelectedNode_Edit",TMPTARGET_TREE:"tmpTargetzTree",TMPTARGET_NODE:"tmpTargetNode"}},p=function(n){var t=u.getRoot(n),i=u.getRoots();t.curEditNode=null;t.curEditInput=null;t.curHoverNode=null;t.dragFlag=0;t.dragNodeShowBefore=[];t.dragMaskList=[];i.showHoverDom=!0},w=function(){},b=function(n){var i=n.treeObj,u=t.event;i.bind(u.RENAME,function(t,i,u,f){r.apply(n.callback.onRename,[t,i,u,f])});i.bind(u.DRAG,function(t,i,u,f){r.apply(n.callback.onDrag,[i,u,f])});i.bind(u.DRAGMOVE,function(t,i,u,f){r.apply(n.callback.onDragMove,[i,u,f])});i.bind(u.DROP,function(t,i,u,f,e,o,s){r.apply(n.callback.onDrop,[i,u,f,e,o,s])})},k=function(n){var i=n.treeObj,r=t.event;i.unbind(r.RENAME);i.unbind(r.DRAG);i.unbind(r.DRAGMOVE);i.unbind(r.DROP)},d=function(n){var c=n.target,o=u.getSetting(n.data.treeId),a=n.relatedTarget,f="",l=null,e="",h=null,i=null;if(r.eqs(n.type,"mouseover")?(i=r.getMDom(o,c,[{tagName:"a",attrName:"treeNode"+t.id.A}]),i&&(f=r.getNodeMainDom(i).id,e="hoverOverNode")):r.eqs(n.type,"mouseout")?(i=r.getMDom(o,a,[{tagName:"a",attrName:"treeNode"+t.id.A}]),i||(f="remove",e="hoverOutNode")):r.eqs(n.type,"mousedown")&&(i=r.getMDom(o,c,[{tagName:"a",attrName:"treeNode"+t.id.A}]),i&&(f=r.getNodeMainDom(i).id,e="mousedownNode")),f.length>0){l=u.getNodeCache(o,f);switch(e){case"mousedownNode":h=s.onMousedownNode;break;case"hoverOverNode":h=s.onHoverOverNode;break;case"hoverOutNode":h=s.onHoverOutNode}}return{stop:!1,node:l,nodeEventType:e,nodeEventCallback:h,treeEventType:"",treeEventCallback:null}},g=function(n,t,i){i&&(i.isHover=!1,i.editNameFlag=!1)},nt=function(n,e){e.cancelEditName=function(n){var t=u.getRoot(this.setting);t.curEditNode&&i.cancelCurEditNode(this.setting,n?n:null,!0)};e.copyNode=function(n,u,f,e){if(!u||n&&!n.isParent&&this.setting.data.keep.leaf&&f===t.move.TYPE_INNER)return null;var h=this,o=r.clone(u);if(n||(n=null,f=t.move.TYPE_INNER),f==t.move.TYPE_INNER){function s(){i.addNodes(h.setting,n,[o],e)}r.canAsync(this.setting,n)?i.asyncNode(this.setting,n,e,s):s()}else i.addNodes(this.setting,n.parentNode,[o],e),i.moveNode(this.setting,n,o,f,!1,e);return o};e.editName=function(n){n&&n.tId&&n===u.getNodeCache(this.setting,n.tId)&&(n.parentTId&&i.expandCollapseParentNode(this.setting,n.getParentNode(),!0),i.editNode(this.setting,n))};e.moveNode=function(n,u,e,o){function s(){i.moveNode(h.setting,n,u,e,!1,o)}if(!u)return u;if(n&&!n.isParent&&this.setting.data.keep.leaf&&e===t.move.TYPE_INNER||n&&(u.parentTId==n.tId&&e==t.move.TYPE_INNER||f(u,this.setting).find("#"+n.tId).length>0))return null;n||(n=null);var h=this;return r.canAsync(this.setting,n)&&e===t.move.TYPE_INNER?i.asyncNode(this.setting,n,o,s):s(),u};e.setEditable=function(n){return this.setting.edit.enable=n,this.refresh()}},tt={setSonNodeLevel:function(n,t,i){var r,f,e;if(i&&(r=n.data.key.children,i.level=t?t.level+1:0,i[r]))for(f=0,e=i[r].length;f<e;f++)i[r][f]&&u.setSonNodeLevel(n,i,i[r][f])}},s={onHoverOverNode:function(n,t){var r=u.getSetting(n.data.treeId),f=u.getRoot(r);if(f.curHoverNode!=t)s.onHoverOutNode(n);f.curHoverNode=t;i.addHoverDom(r,t)},onHoverOutNode:function(n){var r=u.getSetting(n.data.treeId),t=u.getRoot(r);t.curHoverNode&&!u.isSelectedNode(r,t.curHoverNode)&&(i.removeTreeDom(r,t.curHoverNode),t.curHoverNode=null)},onMousedownNode:function(o,s){function at(o){var s,ht,d,at,dt,ki,gt,ni,ti,hi,ct,vt,ot,ui,wt,bt,bi;if(w.dragFlag==0&&Math.abs(yt-o.clientX)<c.edit.drag.minMoveSize&&Math.abs(pt-o.clientY)<c.edit.drag.minMoveSize)return!0;if(ki=c.data.key.children,tt.css("cursor","pointer"),w.dragFlag==0){if(r.apply(c.callback.beforeDrag,[c.treeId,l],!0)==!1)return et(o),!0;for(s=0,ht=l.length;s<ht;s++)s==0&&(w.dragNodeShowBefore=[]),d=l[s],d.isParent&&d.open?(i.expandCollapseNode(c,d,!d.open),w.dragNodeShowBefore[d.tId]=!0):w.dragNodeShowBefore[d.tId]=!1;if(w.dragFlag=1,st.showHoverDom=!1,r.showIfameMask(c,!0),gt=!0,ni=-1,l.length>1)for(ti=l[0].parentTId?l[0].getParentNode()[ki]:u.getNodes(c),dt=[],s=0,ht=ti.length;s<ht;s++)if(w.dragNodeShowBefore[ti[s].tId]!==undefined&&(gt&&ni>-1&&ni+1!==s&&(gt=!1),dt.push(ti[s]),ni=s),l.length===dt.length){l=dt;break}for(gt&&(it=l[0].getPreNode(),rt=l[l.length-1].getNextNode()),k=f("<ul class='zTreeDragUL'><\/ul>",c),s=0,ht=l.length;s<ht;s++)(d=l[s],d.editNameFlag=!1,i.selectNode(c,d,s>0),i.removeTreeDom(c,d),s>c.edit.drag.maxShowNodeNum-1)||(at=f("<li id='"+d.tId+"_tmp'><\/li>",c),at.append(f(d,t.id.A,c).clone()),at.css("padding","0"),at.children("#"+d.tId+t.id.A).removeClass(t.node.CURSELECTED),k.append(at),s==c.edit.drag.maxShowNodeNum-1&&(at=f("<li id='"+d.tId+"_moretmp'><a>  ...  <\/a><\/li>",c),k.append(at)));k.attr("id",l[0].tId+t.id.UL+"_tmp");k.addClass(c.treeObj.attr("class"));k.appendTo(tt);p=f("<span class='tmpzTreeMove_arrow'><\/span>",c);p.attr("id","zTreeMove_arrow_tmp");p.appendTo(tt);c.treeObj.trigger(t.event.DRAG,[o,c.treeId,l])}if(w.dragFlag==1){a&&p.attr("id")==o.target.id&&v&&o.clientX+b.scrollLeft()+2>n("#"+v+t.id.A,a).offset().left?(hi=n("#"+v+t.id.A,a),o.target=hi.length>0?hi.get(0):o.target):a&&(a.removeClass(t.node.TMPTARGET_TREE),v&&n("#"+v+t.id.A,a).removeClass(t.node.TMPTARGET_NODE+"_"+t.move.TYPE_PREV).removeClass(t.node.TMPTARGET_NODE+"_"+e.move.TYPE_NEXT).removeClass(t.node.TMPTARGET_NODE+"_"+e.move.TYPE_INNER));a=null;v=null;g=!1;h=c;ct=u.getSettings();for(vt in ct)ct[vt].treeId&&ct[vt].edit.enable&&ct[vt].treeId!=c.treeId&&(o.target.id==ct[vt].treeId||n(o.target).parents("#"+ct[vt].treeId).length>0)&&(g=!0,h=ct[vt]);var ii=b.scrollTop(),ci=b.scrollLeft(),ri=h.treeObj.offset(),fr=h.treeObj.get(0).scrollHeight,er=h.treeObj.get(0).scrollWidth,li=o.clientY+ii-ri.top,ai=h.treeObj.height()+ri.top-o.clientY-ii,vi=o.clientX+ci-ri.left,yi=h.treeObj.width()+ri.left-o.clientX-ci,di=li<c.edit.drag.borderMax&&li>c.edit.drag.borderMin,gi=ai<c.edit.drag.borderMax&&ai>c.edit.drag.borderMin,nr=vi<c.edit.drag.borderMax&&vi>c.edit.drag.borderMin,tr=yi<c.edit.drag.borderMax&&yi>c.edit.drag.borderMin,or=li>c.edit.drag.borderMin&&ai>c.edit.drag.borderMin&&vi>c.edit.drag.borderMin&&yi>c.edit.drag.borderMin,sr=di&&h.treeObj.scrollTop()<=0,hr=gi&&h.treeObj.scrollTop()+h.treeObj.height()+10>=fr,cr=nr&&h.treeObj.scrollLeft()<=0,lr=tr&&h.treeObj.scrollLeft()+h.treeObj.width()+10>=er;if(o.target&&r.isChildOrSelf(o.target,h.treeId)){for(ot=o.target;ot&&ot.tagName&&!r.eqs(ot.tagName,"li")&&ot.id!=h.treeId;)ot=ot.parentNode;for(ui=!0,s=0,ht=l.length;s<ht;s++)if(d=l[s],ot.id===d.tId){ui=!1;break}else if(f(d,c).find("#"+ot.id).length>0){ui=!1;break}ui&&o.target&&r.isChildOrSelf(o.target,ot.id+t.id.A)&&(a=n(ot),v=ot.id)}if(d=l[0],or&&r.isChildOrSelf(o.target,h.treeId)&&(!a&&(o.target.id==h.treeId||sr||hr||cr||lr)&&(g||!g&&d.parentTId)&&(a=h.treeObj),di?h.treeObj.scrollTop(h.treeObj.scrollTop()-10):gi&&h.treeObj.scrollTop(h.treeObj.scrollTop()+10),nr?h.treeObj.scrollLeft(h.treeObj.scrollLeft()-10):tr&&h.treeObj.scrollLeft(h.treeObj.scrollLeft()+10),a&&a!=h.treeObj&&a.offset().left<h.treeObj.offset().left&&h.treeObj.scrollLeft(h.treeObj.scrollLeft()+a.offset().left-h.treeObj.offset().left)),k.css({top:o.clientY+ii+3+"px",left:o.clientX+ci+3+"px"}),wt=0,bt=0,a&&a.attr("id")!=h.treeId){var nt=v==null?null:u.getNodeCache(h,v),pi=(o.ctrlKey||o.metaKey)&&c.edit.drag.isMove&&c.edit.drag.isCopy||!c.edit.drag.isMove&&c.edit.drag.isCopy,ar=!!(it&&v===it.tId),vr=!!(rt&&v===rt.tId),yr=d.parentTId&&d.parentTId==v,fi=(pi||!vr)&&r.apply(h.edit.drag.prev,[h.treeId,l,nt],!!h.edit.drag.prev),ei=(pi||!ar)&&r.apply(h.edit.drag.next,[h.treeId,l,nt],!!h.edit.drag.next),wi=(pi||!yr)&&!(h.data.keep.leaf&&!nt.isParent)&&r.apply(h.edit.drag.inner,[h.treeId,l,nt],!!h.edit.drag.inner);if(fi||ei||wi){var kt=n("#"+v+t.id.A,a),ir=nt.isLastNode?null:n("#"+nt.getNextNode().tId+t.id.A,a.next()),oi=kt.offset().top,pr=kt.offset().left,rr=fi?wi?.25:ei?.5:1:-1,ur=ei?wi?.75:fi?.5:0:-1,si=(o.clientY+ii-oi)/kt.height();(rr==1||si<=rr&&si>=-.2)&&fi?(wt=1-p.width(),bt=oi-p.height()/2,y=t.move.TYPE_PREV):(ur==0||si>=ur&&si<=1.2)&&ei?(wt=1-p.width(),bt=ir==null||nt.isParent&&nt.open?oi+kt.height()-p.height()/2:ir.offset().top-p.height()/2,y=t.move.TYPE_NEXT):(wt=5-p.width(),bt=oi,y=t.move.TYPE_INNER);p.css({display:"block",top:bt+"px",left:pr+wt+"px"});kt.addClass(t.node.TMPTARGET_NODE+"_"+y);(ut!=v||ft!=y)&&(lt=(new Date).getTime());nt&&nt.isParent&&y==t.move.TYPE_INNER&&(bi=!0,window.zTreeMoveTimer&&window.zTreeMoveTargetNodeTId!==nt.tId?(clearTimeout(window.zTreeMoveTimer),window.zTreeMoveTargetNodeTId=null):window.zTreeMoveTimer&&window.zTreeMoveTargetNodeTId===nt.tId&&(bi=!1),bi&&(window.zTreeMoveTimer=setTimeout(function(){y==t.move.TYPE_INNER&&nt&&nt.isParent&&!nt.open&&(new Date).getTime()-lt>h.edit.drag.autoOpenTime&&r.apply(h.callback.beforeDragOpen,[h.treeId,nt],!0)&&(i.switchNode(h,nt),h.edit.drag.autoExpandTrigger&&h.treeObj.trigger(t.event.EXPAND,[h.treeId,nt]))},h.edit.drag.autoOpenTime+50),window.zTreeMoveTargetNodeTId=nt.tId))}else a=null,v="",y=t.move.TYPE_INNER,p.css({display:"none"}),window.zTreeMoveTimer&&(clearTimeout(window.zTreeMoveTimer),window.zTreeMoveTargetNodeTId=null)}else y=t.move.TYPE_INNER,a&&r.apply(h.edit.drag.inner,[h.treeId,l,null],!!h.edit.drag.inner)?a.addClass(t.node.TMPTARGET_TREE):a=null,p.css({display:"none"}),window.zTreeMoveTimer&&(clearTimeout(window.zTreeMoveTimer),window.zTreeMoveTargetNodeTId=null);ut=v;ft=y;c.treeObj.trigger(t.event.DRAGMOVE,[o,c.treeId,l])}return!1}function et(o){var rt,ot,it,nt,d,s;if(window.zTreeMoveTimer&&(clearTimeout(window.zTreeMoveTimer),window.zTreeMoveTargetNodeTId=null),ut=null,ft=null,b.unbind("mousemove",at),b.unbind("mouseup",et),b.unbind("selectstart",vt),tt.css("cursor","auto"),a&&(a.removeClass(t.node.TMPTARGET_TREE),v&&n("#"+v+t.id.A,a).removeClass(t.node.TMPTARGET_NODE+"_"+t.move.TYPE_PREV).removeClass(t.node.TMPTARGET_NODE+"_"+e.move.TYPE_NEXT).removeClass(t.node.TMPTARGET_NODE+"_"+e.move.TYPE_INNER)),r.showIfameMask(c,!1),st.showHoverDom=!0,w.dragFlag!=0){for(w.dragFlag=0,rt=0,ot=l.length;rt<ot;rt++)it=l[rt],it.isParent&&w.dragNodeShowBefore[it.tId]&&!it.open&&(i.expandCollapseNode(c,it,!it.open),delete w.dragNodeShowBefore[it.tId]);if(k&&k.remove(),p&&p.remove(),nt=(o.ctrlKey||o.metaKey)&&c.edit.drag.isMove&&c.edit.drag.isCopy||!c.edit.drag.isMove&&c.edit.drag.isCopy,!nt&&a&&v&&l[0].parentTId&&v==l[0].parentTId&&y==t.move.TYPE_INNER&&(a=null),a){if(d=v==null?null:u.getNodeCache(h,v),r.apply(c.callback.beforeDrop,[h.treeId,l,d,y,nt],!0)==!1){i.selectNodes(ct,l);return}s=nt?r.clone(l):l;function ht(){if(g){if(!nt)for(var n=0,r=l.length;n<r;n++)i.removeNode(c,l[n]);if(y==t.move.TYPE_INNER)i.addNodes(h,d,s);else if(i.addNodes(h,d.getParentNode(),s),y==t.move.TYPE_PREV)for(n=0,r=s.length;n<r;n++)i.moveNode(h,d,s[n],y,!1);else for(n=-1,r=s.length-1;n<r;r--)i.moveNode(h,d,s[r],y,!1)}else if(nt&&y==t.move.TYPE_INNER)i.addNodes(h,d,s);else if(nt&&i.addNodes(h,d.getParentNode(),s),y!=t.move.TYPE_NEXT)for(n=0,r=s.length;n<r;n++)i.moveNode(h,d,s[n],y,!1);else for(n=-1,r=s.length-1;n<r;r--)i.moveNode(h,d,s[r],y,!1);i.selectNodes(h,s);f(s[0],c).focus().blur();c.treeObj.trigger(t.event.DROP,[o,h.treeId,s,d,y,nt])}y==t.move.TYPE_INNER&&r.canAsync(h,d)?i.asyncNode(h,d,!1,ht):ht()}else i.selectNodes(ct,l),c.treeObj.trigger(t.event.DROP,[o,c.treeId,l,null,null,null])}}function vt(){return!1}var d,ot,c=u.getSetting(o.data.treeId),w=u.getRoot(c),st=u.getRoots();if(o.button==2||!c.edit.enable||!c.edit.drag.isCopy&&!c.edit.drag.isMove)return!0;var ht=o.target,nt=u.getRoot(c).curSelectedList,l=[];if(u.isSelectedNode(c,s))for(d=0,ot=nt.length;d<ot;d++){if(nt[d].editNameFlag&&r.eqs(ht.tagName,"input")&&ht.getAttribute("treeNode"+t.id.INPUT)!==null)return!0;if(l.push(nt[d]),l[0].parentTId!==nt[d].parentTId){l=[s];break}}else l=[s];i.editNodeBlur=!0;i.cancelCurEditNode(c);var b=n(c.treeObj.get(0).ownerDocument),tt=n(c.treeObj.get(0).ownerDocument.body),k,p,a,g=!1,h=c,ct=c,it,rt,ut=null,ft=null,v=null,y=t.move.TYPE_INNER,yt=o.clientX,pt=o.clientY,lt=(new Date).getTime();return r.uCanDo(c)&&b.bind("mousemove",at),b.bind("mouseup",et),b.bind("selectstart",vt),o.preventDefault&&o.preventDefault(),!0}},it={getAbs:function(n){var t=n.getBoundingClientRect(),i=document.body.scrollTop+document.documentElement.scrollTop,r=document.body.scrollLeft+document.documentElement.scrollLeft;return[t.left+r,t.top+i]},inputFocus:function(n){n.get(0)&&(n.focus(),r.setCursorPosition(n.get(0),n.val().length))},inputSelect:function(n){n.get(0)&&(n.focus(),n.select())},setCursorPosition:function(n,t){if(n.setSelectionRange)n.focus(),n.setSelectionRange(t,t);else if(n.createTextRange){var i=n.createTextRange();i.collapse(!0);i.moveEnd("character",t);i.moveStart("character",t);i.select()}},showIfameMask:function(n,t){for(var e=u.getRoot(n),o,i,h;e.dragMaskList.length>0;)e.dragMaskList[0].remove(),e.dragMaskList.shift();if(t)for(o=f("iframe",n),i=0,h=o.length;i<h;i++){var s=o.get(i),c=r.getAbs(s),l=f("<div id='zTreeMask_"+i+"' class='zTreeMask' style='top:"+c[1]+"px; left:"+c[0]+"px; width:"+s.offsetWidth+"px; height:"+s.offsetHeight+"px;'><\/div>",n);l.appendTo(f("body",n));e.dragMaskList.push(l)}}},rt={addEditBtn:function(n,u){if(!u.editNameFlag&&!(f(u,t.id.EDIT,n).length>0)&&r.apply(n.edit.showRenameBtn,[n.treeId,u],n.edit.showRenameBtn)){var e=f(u,t.id.A,n),o="<span class='"+t.className.BUTTON+" edit' id='"+u.tId+t.id.EDIT+"' title='"+r.apply(n.edit.renameTitle,[n.treeId,u],n.edit.renameTitle)+"' treeNode"+t.id.EDIT+" style='display:none;'><\/span>";e.append(o);f(u,t.id.EDIT,n).bind("click",function(){return!r.uCanDo(n)||r.apply(n.callback.beforeEditName,[n.treeId,u],!0)==!1?!1:(i.editNode(n,u),!1)}).show()}},addRemoveBtn:function(n,u){if(!u.editNameFlag&&!(f(u,t.id.REMOVE,n).length>0)&&r.apply(n.edit.showRemoveBtn,[n.treeId,u],n.edit.showRemoveBtn)){var e=f(u,t.id.A,n),o="<span class='"+t.className.BUTTON+" remove' id='"+u.tId+t.id.REMOVE+"' title='"+r.apply(n.edit.removeTitle,[n.treeId,u],n.edit.removeTitle)+"' treeNode"+t.id.REMOVE+" style='display:none;'><\/span>";e.append(o);f(u,t.id.REMOVE,n).bind("click",function(){return!r.uCanDo(n)||r.apply(n.callback.beforeRemove,[n.treeId,u],!0)==!1?!1:(i.removeNode(n,u),n.treeObj.trigger(t.event.REMOVE,[n.treeId,u]),!1)}).bind("mousedown",function(){return!0}).show()}},addHoverDom:function(n,t){u.getRoots().showHoverDom&&(t.isHover=!0,n.edit.enable&&(i.addEditBtn(n,t),i.addRemoveBtn(n,t)),r.apply(n.view.addHoverDom,[n.treeId,t]))},cancelCurEditNode:function(n,e,o){var h=u.getRoot(n),a=n.data.key.name,s=h.curEditNode,c,l,v;if(s){if(c=h.curEditInput,l=e?e:o?s[a]:c.val(),r.apply(n.callback.beforeRename,[n.treeId,s,l,o],!0)===!1)return!1;s[a]=l;v=f(s,t.id.A,n);v.removeClass(t.node.CURSELECTED_EDIT);c.unbind();i.setNodeName(n,s);s.editNameFlag=!1;h.curEditNode=null;h.curEditInput=null;i.selectNode(n,s,!1);n.treeObj.trigger(t.event.RENAME,[n.treeId,s,o])}return h.noSelection=!0,!0},editNode:function(n,e){var s=u.getRoot(n),h,o;if(i.editNodeBlur=!1,u.isSelectedNode(n,e)&&s.curEditNode==e&&e.editNameFlag){setTimeout(function(){r.inputFocus(s.curEditInput)},0);return}h=n.data.key.name;e.editNameFlag=!0;i.removeTreeDom(n,e);i.cancelCurEditNode(n);i.selectNode(n,e,!1);f(e,t.id.SPAN,n).html("<input type=text class='rename' id='"+e.tId+t.id.INPUT+"' treeNode"+t.id.INPUT+" >");o=f(e,t.id.INPUT,n);o.attr("value",e[h]);n.edit.editNameSelectAll?r.inputSelect(o):r.inputFocus(o);o.bind("blur",function(){i.editNodeBlur||i.cancelCurEditNode(n)}).bind("keydown",function(t){t.keyCode=="13"?(i.editNodeBlur=!0,i.cancelCurEditNode(n)):t.keyCode=="27"&&i.cancelCurEditNode(n,null,!0)}).bind("click",function(){return!1}).bind("dblclick",function(){return!1});f(e,t.id.A,n).addClass(t.node.CURSELECTED_EDIT);s.curEditInput=o;s.noSelection=!1;s.curEditNode=e},moveNode:function(n,r,e,o,s,h){var tt=u.getRoot(n),c=n.data.key.children,l,g,p,a,b,rt,w;if(r!=e&&(!n.data.keep.leaf||!r||r.isParent||o!=t.move.TYPE_INNER)){l=e.parentTId?e.getParentNode():tt;g=r===null||r==tt;g&&r===null&&(r=tt);g&&(o=t.move.TYPE_INNER);p=r.parentTId?r.getParentNode():tt;o!=t.move.TYPE_PREV&&o!=t.move.TYPE_NEXT&&(o=t.move.TYPE_INNER);o==t.move.TYPE_INNER&&(g?e.parentTId=null:(r.isParent||(r.isParent=!0,r.open=!!r.open,i.setNodeLineIcos(n,r)),e.parentTId=r.tId));g?(a=n.treeObj,b=a):(h||o!=t.move.TYPE_INNER?h||i.expandCollapseNode(n,r.getParentNode(),!0,!1):i.expandCollapseNode(n,r,!0,!1),a=f(r,n),b=f(r,t.id.UL,n),!a.get(0)||b.get(0)||(rt=[],i.makeUlHtml(n,r,rt,""),a.append(rt.join(""))),b=f(r,t.id.UL,n));w=f(e,n);w.get(0)?a.get(0)||w.remove():w=i.appendNodes(n,e.level,[e],null,!1,!0).join("");b.get(0)&&o==t.move.TYPE_INNER?b.append(w):a.get(0)&&o==t.move.TYPE_PREV?a.before(w):a.get(0)&&o==t.move.TYPE_NEXT&&a.after(w);var v,it,k=-1,nt=0,d=null,y=null,ut=e.level;if(e.isFirstNode)k=0,l[c].length>1&&(d=l[c][1],d.isFirstNode=!0);else if(e.isLastNode)k=l[c].length-1,d=l[c][k-1],d.isLastNode=!0;else for(v=0,it=l[c].length;v<it;v++)if(l[c][v].tId==e.tId){k=v;break}if(k>=0&&l[c].splice(k,1),o!=t.move.TYPE_INNER)for(v=0,it=p[c].length;v<it;v++)p[c][v].tId==r.tId&&(nt=v);if(o==t.move.TYPE_INNER?(r[c]||(r[c]=[]),r[c].length>0&&(y=r[c][r[c].length-1],y.isLastNode=!1),r[c].splice(r[c].length,0,e),e.isLastNode=!0,e.isFirstNode=r[c].length==1):r.isFirstNode&&o==t.move.TYPE_PREV?(p[c].splice(nt,0,e),y=r,y.isFirstNode=!1,e.parentTId=r.parentTId,e.isFirstNode=!0,e.isLastNode=!1):r.isLastNode&&o==t.move.TYPE_NEXT?(p[c].splice(nt+1,0,e),y=r,y.isLastNode=!1,e.parentTId=r.parentTId,e.isFirstNode=!1,e.isLastNode=!0):(o==t.move.TYPE_PREV?p[c].splice(nt,0,e):p[c].splice(nt+1,0,e),e.parentTId=r.parentTId,e.isFirstNode=!1,e.isLastNode=!1),u.fixPIdKeyValue(n,e),u.setSonNodeLevel(n,e.getParentNode(),e),i.setNodeLineIcos(n,e),i.repairNodeLevelClass(n,e,ut),!n.data.keep.parent&&l[c].length<1){l.isParent=!1;l.open=!1;var ft=f(l,t.id.UL,n),et=f(l,t.id.SWITCH,n),ot=f(l,t.id.ICON,n);i.replaceSwitchClass(l,et,t.folder.DOCU);i.replaceIcoClass(l,ot,t.folder.DOCU);ft.css("display","none")}else d&&i.setNodeLineIcos(n,d);y&&i.setNodeLineIcos(n,y);!!n.check&&n.check.enable&&i.repairChkClass&&(i.repairChkClass(n,l),i.repairParentChkClassWithSelf(n,l),l!=e.parent&&i.repairParentChkClassWithSelf(n,e));h||i.expandCollapseParentNode(n,e.getParentNode(),!0,s)}},removeEditBtn:function(n,i){f(i,t.id.EDIT,n).unbind().remove()},removeRemoveBtn:function(n,i){f(i,t.id.REMOVE,n).unbind().remove()},removeTreeDom:function(n,t){t.isHover=!1;i.removeEditBtn(n,t);i.removeRemoveBtn(n,t);r.apply(n.view.removeHoverDom,[n.treeId,t])},repairNodeLevelClass:function(n,i,r){if(r!==i.level){var o=f(i,n),s=f(i,t.id.A,n),h=f(i,t.id.UL,n),u=t.className.LEVEL+r,e=t.className.LEVEL+i.level;o.removeClass(u);o.addClass(e);s.removeClass(u);s.addClass(e);h.removeClass(u);h.addClass(e)}},selectNodes:function(n,t){for(var r=0,u=t.length;r<u;r++)i.selectNode(n,t[r],r>0)}},ut={tools:it,view:rt,event:{},data:tt},h,c,y,l,a,v;n.extend(!0,n.fn.zTree.consts,e);n.extend(!0,n.fn.zTree._z,ut);var o=n.fn.zTree,r=o._z.tools,t=o.consts,i=o._z.view,u=o._z.data,ft=o._z.event,f=r.$;u.exSetting({edit:{enable:!1,editNameSelectAll:!1,showRemoveBtn:!0,showRenameBtn:!0,removeTitle:"remove",renameTitle:"rename",drag:{autoExpandTrigger:!1,isCopy:!0,isMove:!0,prev:!0,next:!0,inner:!0,minMoveSize:5,borderMax:10,borderMin:-5,maxShowNodeNum:5,autoOpenTime:500}},view:{addHoverDom:null,removeHoverDom:null},callback:{beforeDrag:null,beforeDragOpen:null,beforeDrop:null,beforeEditName:null,beforeRename:null,onDrag:null,onDragMove:null,onDrop:null,onRename:null}});u.addInitBind(b);u.addInitUnBind(k);u.addInitCache(w);u.addInitNode(g);u.addInitProxy(d);u.addInitRoot(p);u.addZTreeTools(nt);h=i.cancelPreSelectedNode;i.cancelPreSelectedNode=function(n,t){for(var f=u.getRoot(n).curSelectedList,r=0,e=f.length;r<e;r++)if((!t||t===f[r])&&(i.removeTreeDom(n,f[r]),t))break;h&&h.apply(i,arguments)};c=i.createNodes;i.createNodes=function(n,t,r,u){(c&&c.apply(i,arguments),r)&&i.repairParentChkClassWithSelf&&i.repairParentChkClassWithSelf(n,u)};y=i.makeNodeUrl;i.makeNodeUrl=function(n){return n.edit.enable?null:y.apply(i,arguments)};l=i.removeNode;i.removeNode=function(n,t){var r=u.getRoot(n);r.curEditNode===t&&(r.curEditNode=null);l&&l.apply(i,arguments)};a=i.selectNode;i.selectNode=function(n,t){var r=u.getRoot(n);return u.isSelectedNode(n,t)&&r.curEditNode==t&&t.editNameFlag?!1:(a&&a.apply(i,arguments),i.addHoverDom(n,t),!0)};v=r.uCanDo;r.uCanDo=function(n,t){var f=u.getRoot(n);return t&&(r.eqs(t.type,"mouseover")||r.eqs(t.type,"mouseout")||r.eqs(t.type,"mousedown")||r.eqs(t.type,"mouseup"))?!0:(f.curEditNode&&(i.editNodeBlur=!1,f.curEditInput.focus()),!f.curEditNode&&(v?v.apply(i,arguments):!0))}}(jQuery),function(n){"use strict";n.fn.ysTree=function(t,i){if(typeof t=="string")return n.fn.ysTree.methods[t](this,i);var r=n.extend({},n.fn.ysTree.defaults,t||{}),u=n(this),f=u.attr("id");return u.css("overflow-y","auto").css("max-height",r.maxHeight),ys.ajax({url:r.url,"async":r.async,success:function(t){for(var e,i,o=n.fn.zTree.init(n("#"+f),r,t.Data),u=0;u<=r.expandLevel;u++)for(e=o.getNodesByParam("level",u),i=0;i<e.length;i++)o.expandNode(e[i],!0,!1,!1)}}),u};n.fn.ysTree.methods={getCheckedNodes:function(t,i){var r=n.fn.zTree.getZTreeObj(n(t).attr("id")),u=ys.isNullOrEmpty(i)?"id":i,f=r.getCheckedNodes(!0);return n.map(f,function(n){return n[u]}).join()},setCheckedNodes:function(t,i){if(!ys.isNullOrEmpty(i)){var u=i.split(","),r=n.fn.zTree.getZTreeObj(n(t).attr("id"));r.cancelSelectedNode();n.each(u,function(n,t){var i=r.getNodeByParam("id",t);r.checkNode(i,!0,!1,!0)})}},setCheckedNodesByName:function(t,i){if(!ys.isNullOrEmpty(i)){var u=i.split(","),r=n.fn.zTree.getZTreeObj(n(t).attr("id"));r.cancelSelectedNode();n.each(u,function(n,t){var i=r.getNodeByParam("name",t);r.checkNode(i,!0,!1,!0)})}}};n.fn.ysTree.defaults={url:"","async":!1,maxHeight:"300px",expandLevel:0,check:{enable:!1},view:{selectedMulti:!1,nameIsHTML:!0},data:{simpleData:{enable:!0}},callback:{}};n.fn.ysComboBoxTree=function(t,i){var s;if(typeof t=="string")return n.fn.ysComboBoxTree.methods[t](this,i);var r=n.extend({},n.fn.ysComboBoxTree.defaults,t||{}),u=n(this),o=u.attr("id"),e=o+"_input",f=o+"_tree";return u.css("position","relative"),s="<input id='"+e+"' name='"+e+"' readonly='readonly' type='text' class='form-control' />",s+="<div id='"+f+"' class='ztree treeSelect-panel' style='overflow-y: auto;max-height:"+r.maxHeight+";border:1px solid #e5e6e7;margin-top:1px;display:none'><\/div>",n(s).appendTo(u),n("#"+e).click(function(){var t=n("#"+f);t.is(":hidden")?t.show():t.hide()}),ys.ajax({url:r.url,"async":r.async,success:function(t){var v=n("#"+f),y=n("#"+e),a=r.callback.customOnClick,s,c,h,l;if(r.callback.onClick=function(n,t,i){for(var r="",f="",e=i;e!=null;)r=e.name+">"+r,f=e.id+","+f,e=e.getParentNode();r=ys.trimEnd(r,">");f=ys.trimEnd(f,",");u.attr("data-key",f);u.attr("data-value",r);y.val(r);v.hide();a&&a(n,t,i)},r.defaultName!=undefined&&r.defaultName.length>0&&t.Data!=undefined&&t.Data.unshift({id:-1,name:r.defaultName,pid:0}),u.ztree=n.fn.zTree.init(n("#"+f),r,t.Data),r.expandLevel>=0)for(s=0;s<=r.expandLevel;s++)for(c=u.ztree.getNodesByParam("level",s),h=0;h<c.length;h++)u.ztree.expandNode(c[h],!0,!1,!1);l=r.callback.customOnSuccess;l&&l(i,o,t)}}),n(document).click(function(t){var t=t?t:window.event,r=t.srcElement||t.target,i,u;n(r).hasClass("form-control")||(i=n(r).attr("id"),(ys.isNullOrEmpty(i)||i.indexOf("_tree")==-1)&&(u=n("#"+f),u.hide(),t.stopPropagation()))}),u};n.fn.ysComboBoxTree.methods={getValue:function(t){return n(t).attr("data-key")},setValue:function(t,i){var e="0",f;i&&(f=i.toString().split(","),e=f[f.length-1]);var o=t.attr("id"),s=o+"_tree",r=n.fn.zTree.getZTreeObj(s),u=r.getNodeByParam("id",e);if(u!=null){r.cancelSelectedNode();r.selectNode(u,!0);r.expandNode(u,!0,!1);r.setting.callback.onClick("setValue",r.setting.treeId,u)}return n(t)}};n.fn.ysComboBoxTree.defaults={url:"","async":!1,maxHeight:"200px",expandLevel:0,check:{enable:!1},view:{selectedMulti:!1,nameIsHTML:!0},data:{simpleData:{enable:!0}},callback:{},defaultName:undefined}}(jQuery);