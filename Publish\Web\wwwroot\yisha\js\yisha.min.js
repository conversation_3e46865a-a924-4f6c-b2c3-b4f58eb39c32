window.ys={},function(n,t){"use strict";n.extend(t,{openDialog:function(i){t.isMobile()?(i.width="auto",i.height="auto"):i.height||(i.height=n(window).height()-50+"px");var r=n.extend({type:2,title:"",width:"768px",content:"",maxmin:!0,shade:.4,btn:["确认","关闭"],callback:null,shadeClose:!1,fix:!1,closeBtn:1},i);layer.open({type:r.type,area:[r.width,r.height],maxmin:r.maxmin,shade:r.shade,title:r.title,content:r.content,btn:r.btn,shadeClose:r.shadeClose,fix:r.fix,closeBtn:r.closeBtn,yes:r.callback,cancel:function(){return!0},restore:function(){n(".layui-layer").css({top:"0",left:"0"})}})},openDialogContent:function(i){t.isMobile()?(i.width="auto",i.height==undefined&&(i.height="auto")):i.height||(i.height=n(window).height()-50+"px");var r=n.extend({type:1,title:!1,width:"768px",content:"",maxmin:!1,shade:.4,btn:null,callback:null,shadeClose:!0,fix:!0,closeBtn:0},i);layer.open({type:r.type,area:[r.width,r.height],maxmin:r.maxmin,shade:r.shade,title:r.title,content:r.content,btn:r.btn,shadeClose:r.shadeClose,fix:r.fix,closeBtn:r.closeBtn,yes:r.callback,cancel:function(){return!0}})},closeDialog:function(){var n=parent.layer.getFrameIndex(window.name);parent.layer.close(n)},msgWarning:function(n){layer.msg(n,{icon:0,time:1e3,shift:5})},msgSuccess:function(n){t.isNullOrEmpty(n)&&(n="操作成功");top.layer.msg(n,{icon:1,time:1e3,shift:5})},msgError:function(n){t.isNullOrEmpty(n)&&(n="操作失败");layer.msg(n,{icon:2,time:3e3,shift:5})},alertWarning:function(n){layer.alert(n,{icon:0,title:"系统提示",btn:["确认"],btnclass:["btn btn-primary"]})},alertSuccess:function(n){layer.alert(n,{icon:1,title:"系统提示",btn:["确认"],btnclass:["btn btn-primary"]})},alertError:function(n){layer.alert(n,{icon:2,title:"系统提示",btn:["确认"],btnclass:["btn btn-primary"]})},confirm:function(n,t,i){layer.confirm(n,{icon:3,title:"系统提示",btn:["确认","取消"],btnclass:["btn btn-primary","btn btn-danger"]},function(n){layer.close(n);t(!0)},function(){i!=undefined&&i(!0)})},showLoading:function(t){n.blockUI({message:'<div class="loaderbox"><div class="loading-activity"><\/div> '+t+"<\/div>",css:{border:"none",backgroundColor:"transparent"}})},closeLoading:function(){setTimeout(function(){n.unblockUI()},50)},getIds:function(t){var i="";return n.each(t,function(n,t){t.Id!=null&&(n==0?i=t.Id:i+=","+t.Id)}),i},checkRowEdit:function(n){if(n.length==0)t.msgError("您没有选择任何行！");else if(n.length>1)t.msgError("您的选择大于1行！");else if(n.length==1)return!0;return!1},checkRowDelete:function(n){if(n.length==0)t.msgError("您没有选择任何行！");else if(n.length>0)return!0;return!1},ajax:function(i){var r=n.extend({url:i.url,"async":!0,type:"get",data:i.data||{},dataType:i.dataType||"json",error:function(){t.alertError("系统出错了")},success:function(){t.msgSuccess()},beforeSend:function(){t.showLoading("正在处理中...")},complete:function(){t.closeLoading()}},i);if(t.isNullOrEmpty(r.url)){t.alertError("url 参数不能为空");return}n.ajax({url:r.url,"async":r.async,type:r.type,data:r.data,dataType:r.dataType,error:r.error,success:r.success,beforeSend:r.beforeSend,complete:r.complete})},ajaxUploadFile:function(i){var r=n.extend({url:i.url,data:i.data||{},error:function(){t.alertError("系统出错了")},success:function(){t.msgSuccess()},beforeSend:function(){t.showLoading("正在处理中...")},complete:function(){t.closeLoading()}},i);if(t.isNullOrEmpty(r.url)){t.alertError("url 参数不能为空");return}if(t.isNullOrEmpty(r.data)){t.alertError("data 参数不能为空");return}n.ajax({url:r.url,data:r.data,type:"post",processData:!1,contentType:!1,error:r.error,success:r.success,beforeSend:r.beforeSend,complete:r.complete})},exportExcel:function(n,i){t.ajax({url:n,type:"post",data:i,success:function(n){n.Tag==1?window.location.href=ctx+"File/DownloadFile?filePath="+n.Data+"&delete=1":t.msgError(n.Message)},beforeSend:function(){t.showLoading("正在导出数据，请稍后...")}})},request:function(n){var i=decodeURI(window.location.search),r=new RegExp("(^|&)"+n+"=([^&]*)(&|$)"),t=i.substr(1).match(r);return t!=null?unescape(t[2]):null},getHttpFileName:function(n){if(n==null||n=="")return n;var t=n.lastIndexOf("/");return t>0?n.substring(t+1):n},getFileNameWithoutExtension:function(n){if(n==null||n=="")return n;var t=n.indexOf(".");return t>0?n.substring(0,t):n},changeURLParam:function(n,t,i){var e=t+"=([^&]*)",r=t+"="+i,f,u;return n.match(e)?(f="/("+t+"=)([^&]*)/gi",n.replace(eval(f),r)):n.match("[?]")?(u=n.split("#"),u.length>1?u[0]+"&"+r+"#"+u[1]:n+"&"+r):n+"?"+r},isNullOrEmpty:function(n){return typeof n=="string"&&n==""||n==null||n==undefined?!0:!1},getJson:function(n){return n},getGuid:function(){for(var i,t="",n=1;n<=32;n++)i=Math.floor(Math.random()*16).toString(16),t+=i,(n==8||n==12||n==16||n==20)&&(t+="-");return t},getValueByKey:function(t,i){var r="";return n.each(t,function(n,t){t.Key==i&&(r=t.Value)}),r},getLastValue:function(n){if(!t.isNullOrEmpty(n)){var i=n.toString().split(",");return i[i.length-1]}return""},formatDate:function(n,t){var i,r,u;if(!n)return"";i=n;typeof n=="string"&&(i=n.indexOf("/Date(")>-1?new Date(parseInt(n.replace("/Date(","").replace(")/",""),10)):new Date(Date.parse(n.replace(/-/g,"/").replace("T"," ").split(".")[0])));r={"M+":i.getMonth()+1,"d+":i.getDate(),"H+":i.getHours(),"m+":i.getMinutes(),"s+":i.getSeconds(),"q+":Math.floor((i.getMonth()+3)/3),S:i.getMilliseconds()};/(y+)/.test(t)&&(t=t.replace(RegExp.$1,(i.getFullYear()+"").substr(4-RegExp.$1.length)));for(u in r)new RegExp("("+u+")").test(t)&&(t=t.replace(RegExp.$1,RegExp.$1.length==1?r[u]:("00"+r[u]).substr((""+r[u]).length)));return t},trimStart:function(n,t){var r,i;return t==null||t==""?n.replace(/^s*/,""):(r=new RegExp("^"+t+"*"),i=n.replace(r,""),i)},trimEnd:function(n,t){var r,i;if(t==null||t==""){for(r=/s/,i=n.length;r.test(n.charAt(--i)););return n.slice(0,i+1)}for(r=new RegExp(t),i=n.length;r.test(n.charAt(--i)););return n.slice(0,i+1)},toString:function(n){return n==null?"":n.toString()},openLink:function(n,t){var i=document.createElement("a");i.target=t?t:"_blank";i.href=n;i.click()},recursion:function(n,i,r,u,f){u||(u="id");f||(f="parentId");for(var e in n)if(n[e][u]==i)return r.push(n[e]),t.recursion(n,n[e][f],r,u,f)},isMobile:function(){return navigator.userAgent.match(/(Android|iPhone|SymbianOS|Windows Phone|iPad|iPod)/i)}})}(window.jQuery,window.ys),function(n){"use strict";n.fn.ysRadioBox=function(t,i){var u,f,r,e;return typeof t=="string"?n.fn.ysRadioBox.methods[t](this,i):(u=n(this),f=u.attr("id"),!f)?!1:(r=n.extend({url:null,key:"Key",value:"Value",data:null,dataName:"Data","default":undefined},t),e={loadData:function(){r.url&&n.ajax({url:r.url,type:"get",dataType:"json","async":!1,cache:!1,success:function(n){r.data=n;r.dataName&&r.data!=null&&(r.data=r.data[r.dataName])},error:function(){throw exception;}})},render:function(t){if(t.data&&t.data.length>=0){var r=u.attr("ref"),e=f+"_radiobox",i="";n.each(t.data,function(n){var u=t.data[n];i+="<label class='radio-box'>";i+="<input type='radio' name='"+e+"' value='"+u[t.key]+"' ref='"+r+"' /> "+u[t.value];i+="<\/label>";u.IsDefault==1&&(t.default=u[t.key])});u.append(i)}t.default!=undefined&&u.ysRadioBox("setValue",t.default)}},e.loadData(),e.render(r),u)};n.fn.ysRadioBox.methods={getValue:function(t){var i="";return n(t).find("div.checked").each(function(t,r){i+=n(r).find("input[type=radio]").val();i+=","}),i.indexOf(",")>=0&&(i=i.substring(0,i.length-1)),i},setValue:function(t,i){if(!ys.isNullOrEmpty(i)){typeof i!="string"&&(i=i.toString());n(t).find("div").each(function(t,i){n(i).removeClass("checked")});var r=i.split(",");n.each(r,function(i,r){var u=n(t).find("input[type=radio][value="+r+"]");u.attr("checked",!0);u.parent().addClass("checked")})}}};n.fn.ysCheckBox=function(t,i){var u,f,r,e;return typeof t=="string"?n.fn.ysCheckBox.methods[t](this,i):(u=n(this),f=u.attr("id"),!f)?!1:(r=n.extend({url:null,key:"Key",value:"Value",data:null,dataName:"Data","default":undefined},t),e={loadData:function(){r.url&&(n.ajax({url:r.url,type:"get",dataType:"json","async":!1,cache:!1,success:function(n){r.data=n},error:function(){throw exception;}}),r.dataName&&r.data!=null&&(r.data=r.data[r.dataName]))},render:function(t){if(t.data&&t.data.length>=0){var r=f+"_checkbox",i="";n.each(t.data,function(n){var u=t.data[n];i+="<label class='check-box'>";i+="<input name='"+r+"' type='checkbox' value='"+u[t.key]+"'>"+u[t.value]+"<\/input>";i+="<\/label>";u.IsDefault==1&&(t.default=u[t.key])});u.append(i);n(".check-box").iCheck({checkboxClass:"icheckbox-blue",radioClass:"iradio-blue"})}t.default!=undefined&&u.ysCheckBox("setValue",t.default)}},e.loadData(),e.render(r),u)};n.fn.ysCheckBox.methods={getValue:function(t){var i="";return n(t).find("div.checked").each(function(t,r){i+=n(r).find("input[type=checkbox]").val();i+=","}),i.indexOf(",")>=0&&(i=i.substring(0,i.length-1)),i},setValue:function(t,i){if(!ys.isNullOrEmpty(i)){typeof i!="string"&&(i=i.toString());var r=i.split(",");n.each(r,function(i,r){var u=n(t).find("input[type=checkbox][value="+r+"]");u.attr("checked",!0);u.parent().addClass("checked")})}}};n.fn.ysComboBox=function(t,i){var u,f,r,e;return typeof t=="string"?n.fn.ysComboBox.methods[t](this,i):(u=n(this),f=u.attr("id"),!f)?!1:(r=n.extend({url:null,key:"Key",value:"Value",maxHeight:"160px","class":null,multiple:!1,data:null,dataName:"Data",onChange:null,defaultName:"","default":undefined,minimumResultsForSearch:-1,placeholder:"请选择",onSelect:null},t),e={loadData:function(){r.url&&(n.ajax({url:r.url,type:"get",dataType:"json","async":!1,cache:!1,success:function(n){r.data=n},error:function(){throw exception;}}),r.dataName&&r.data!=null&&(r.data=r.data[r.dataName]))},render:function(t){var i,s,o,h,e,c,l;if(t.data||(t.data=[]),t.data.length>15&&(t.minimumResultsForSearch=0),i=f+"_select",s="",t.multiple&&(s='multiple=""'),o="<select id='"+i+"' name='"+i+"' class='"+ys.toString(t.class)+"' "+s+" maxheight="+t.maxHeight+">",h=n("#"+i).length>0,h&&n("#"+i).empty(),e="",c=!1,t.data.length>0&&(c=t.data[0][t.value]instanceof Array),c||(t.class?t.multiple||(e+="<option value='' selected='selected' ><\/option>"):e+=t.defaultName!=undefined&&t.defaultName!=""?"<option value='-1'>"+t.defaultName+"<\/option>":"<option value='-1'>全部<\/option>"),l=t.data.length,n.each(t.data,function(i){var r=t.data[i];typeof r=="string"?(i==0&&l==1&&(t.default=r),e+="<option value='"+r+"'>"+r+"<\/option>"):r[t.value]instanceof Array?(e+="<optgroup label='--"+r[t.key]+"--'>",n.each(r[t.value],function(n){var i=r[t.value][n];e+="<option value='"+i[t.key]+"'>"+i[t.value]+"<\/option>";r.IsDefault==1&&(t.default=r[t.key])})):i==0&&l==1?(e+="<option value='"+r[t.key]+"' >"+r[t.value]+"<\/option>",t.default=r[t.key]):(e+="<option value='"+r[t.key]+"'>"+r[t.value]+"<\/option>",r.IsDefault==1&&(t.default=r[t.key]))}),h)n("#"+i).append(e);else if(o+=e,o+="<\/select>",u.append(o),t.onChange&&n("#"+i).change(t.onChange),t.onSelect)n("#"+i).on("select2:select",t.onSelect);if(t.class?n("#"+i).select2({minimumResultsForSearch:t.minimumResultsForSearch,placeholder:r.placeholder,language:"zh-CN"}):n("#"+i).select2({minimumResultsForSearch:t.minimumResultsForSearch,placeholder:{id:"-1",text:r.defaultName},language:"zh-CN"}),!t.multiple)n("#"+i).on("select2:open",()=>{n(".select2-search__field").attr("placeholder","关键词检索")});t.class||n("#"+f).find(".select2-container").width(280);t.default!=undefined&&n("#"+i).val(t.default).trigger("change")}},e.loadData(),e.render(r),u)};n.fn.ysComboBox.methods={getValue:function(t){var i=n("#"+n(t).attr("id")+"_select").select2("val");return i==null?"":i.toString()},setValue:function(t,i){ys.isNullOrEmpty(i)||(typeof i!="string"&&(i=i.toString()),n("#"+n(t).attr("id")+"_select").val(i.split(",")).trigger("change"))}};n.fn.getWebControls=function(t){var i={};return t&&typeof t=="object"&&(i=t),n(this).find("[col]").each(function(t,r){var f=n(r).attr("id"),u=n(r).attr("col");r.tagName=="INPUT"?r.type=="checkbox"?n(r).prop("checked")&&(i[u]=i[u]?i[u]+","+n(r).val():n(r).val()):r.type=="radio"?n(r).prop("checked")&&(i[u]=n(r).val()):i[u]=n(r).val():r.tagName=="SELECT"?i[u]=n(r).val():r.tagName=="DIV"?i[u]=n(r).find("#"+f+"_tree").length>0?n(r).ysComboBoxTree("getValue"):n(r).find("#"+f+"_select").length>0?n(r).ysComboBox("getValue"):n(r).find("input[type=checkbox]").length>0?n(r).ysCheckBox("getValue"):n(r).find("input[type=radio]").length>0?n(r).ysRadioBox("getValue"):n(r).html():r.tagName=="IMG"?i[u]=n(r).prop("src"):r.tagName=="SPAN"?i[u]=n(r).find("#"+f+"_select").length>0?n(r).ysComboBox("getValue"):n(r).html():r.tagName=="TEXTAREA"&&(i[u]=n(r).val())}),i};n.fn.setWebControls=function(t){return n(this).find("[col]").each(function(i,r){var f=n(r).attr("id"),u=n(r).attr("col");r.tagName=="INPUT"?r.type=="checkbox"?n(r).val()==t[u]&&n(r).prop("checked","checked"):r.type=="radio"?n(r).val()==t[u]&&(n(r).iCheck?n(r).iCheck("check"):n(r).prop("checked",!0)):n(r).val(t[u]):r.tagName=="SELECT"?n(r).val(t[u]):r.tagName=="DIV"?n(r).find("#"+f+"_tree").length>0?n(r).ysComboBoxTree("setValue",t[u]):n(r).find("#"+f+"_select").length>0?n(r).ysComboBox("setValue",t[u]):n(r).find("input[type=checkbox]").length>0?n(r).ysCheckBox("setValue",t[u]):n(r).find("input[type=radio]").length>0?n(r).ysRadioBox("setValue",t[u]):n(r).html(t[u]):r.tagName=="SPAN"?n(r).html(t[u]):r.tagName=="TEXTAREA"&&n(r).val(t[u])}),t}}(window.jQuery);var imgages={showext:function(){for(var t=$("img[modal='zoomImg']").length,i=[],n=0;n<t;n++)i[n]=$("img[modal='zoomImg']").eq(n).prop("src");$("img[modal='zoomImg']").each(function(){$(this).on("click",function(){function r(){var o,s,u,t,r;$(".mask-layer-imgbox").append('<p><img src="" alt=""><\/p>');$(".mask-layer-imgbox img").prop("src",i[n]);var f=$(".auto-img-center").width(),e=$(".auto-img-center").height(),h=$(".auto-img-center img").width(),c=$(".auto-img-center img").height();h>c?($(".auto-img-center img").css("width",f),o=$(".auto-img-center img").height(),$(".auto-img-center img").css("margin-top",-(o-e)/2)):($(".auto-img-center img").css("height",e),s=$(".auto-img-center img").width(),$(".auto-img-center img").css("margin-left",-(s-f)/2));u=$(".mask-layer-imgbox p");u.bind("mousedown",function(n){n.preventDefault&&n.preventDefault();var t=$(this)[0].offsetLeft,i=$(this)[0].offsetTop,r=n.pageX,f=n.pageY;$(".mask-layer-imgbox").bind("mousemove",function(n){var e=n.pageX-r,o=n.pageY-f,s=t+e+"px",h=i+o+"px";u.css({top:h,left:s})})});$(".mask-layer-imgbox").bind("mouseup",function(){$(this).unbind("mousemove")});t=1;$(".mask-out").click(function(){t+=.1;$(".mask-layer-imgbox img").css({transform:"scale("+t+")","-moz-transform":"scale("+t+")","-ms-transform":"scale("+t+")","-o-transform":"scale("+t+")","-webkit-":"scale("+t+")"})});$(".mask-in").click(function(){t-=.1;t<=.1?(t=.1,$(".mask-layer-imgbox img").css({transform:"scale(.1)","-moz-transform":"scale(.1)","-ms-transform":"scale(.1)","-o-transform":"scale(.1)","-webkit-transform":"scale(.1)"})):$(".mask-layer-imgbox img").css({transform:"scale("+t+")","-moz-transform":"scale("+t+")","-ms-transform":"scale("+t+")","-o-transform":"scale("+t+")","-webkit-transform":"scale("+t+")"})});r=0;$(".mask-clockwise").click(function(){r+=90;$(".mask-layer-imgbox img").parent("p").css({transform:"rotate("+r+"deg)","-moz-transform":"rotate("+r+"deg)","-ms-transform":"rotate("+r+"deg)","-o-transform":"rotate("+r+"deg)","-webkit-transform":"rotate("+r+"deg)"})});$(".mask-counterclockwise").click(function(){r-=90;$(".mask-layer-imgbox img").parent("p").css({transform:"rotate("+r+"deg)","-moz-transform":"rotate("+r+"deg)","-ms-transform":"rotate("+r+"deg)","-o-transform":"rotate("+r+"deg)","-webkit-transform":"rotate("+r+"deg)"})});$(".mask-close").click(function(){$(".mask-layer").remove()});$(".mask-layer-black").click(function(){$(".mask-layer").remove()})}$("body").append('<div class="mask-layer">   <div class="mask-layer-black"><\/div>   <div class="mask-layer-container">       <div class="mask-layer-container-operate">           <button class="mask-prev btn-default-styles" style="float: left">上一张<\/button>           <button class="mask-out btn-default-styles">放大<\/button>           <button class="mask-in btn-default-styles">缩小<\/button>           <button class="mask-clockwise btn-default-styles">顺旋转<\/button>           <button class="mask-counterclockwise btn-default-styles">逆旋转<\/button>           <button class="mask-close btn-default-styles">关闭<\/button>           <button class="mask-next btn-default-styles" style="float: right">下一张<\/button>       <\/div>       <div class="mask-layer-imgbox auto-img-center"><\/div>   <\/div><\/div>');var e=$(this),u=$(this).prop("src"),f=i.indexOf(u),n=f;r();$(".mask-next").on("click",function(){$(".mask-layer-imgbox p img").remove();n++;n==t&&(n=0);r()});$(".mask-prev").on("click",function(){$(".mask-layer-imgbox p img").remove();n--;n==-1&&(n=t-1);r()})})})},showextalink:function(){for(var t=$("a[modal='zoomImg']").length,i=[],n=0;n<t;n++)i[n]=$("a[modal='zoomImg']").eq(n).attr("src");$("a[modal='zoomImg']").each(function(){$(this).on("click",function(){function r(){var e,c,u,t,r;$(".mask-layer-imgbox").append('<p style="text-align:center;"><img src="" alt=""><\/p>');$(".mask-layer-imgbox img").attr("src",i[n]);var o=$(".auto-img-center").width(),f=$(".auto-img-center").height(),s=$(".auto-img-center img").width(),h=$(".auto-img-center img").height();s>h?($(".auto-img-center img").css("width",o),e=$(".auto-img-center img").height(),$(".auto-img-center img").css("margin-top",-(e-f)/2)):($(".auto-img-center img").css("height",f),c=$(".auto-img-center img").width());u=$(".mask-layer-imgbox p");u.bind("mousedown",function(n){n.preventDefault&&n.preventDefault();var t=$(this)[0].offsetLeft,i=$(this)[0].offsetTop,r=n.pageX,f=n.pageY;$(".mask-layer-imgbox").bind("mousemove",function(n){var e=n.pageX-r,o=n.pageY-f,s=t+e+"px",h=i+o+"px";u.css({top:h,left:s})})});$(".mask-layer-imgbox").bind("mouseup",function(){$(this).unbind("mousemove")});t=1;$(".mask-out").click(function(){t+=.1;$(".mask-layer-imgbox img").css({transform:"scale("+t+")","-moz-transform":"scale("+t+")","-ms-transform":"scale("+t+")","-o-transform":"scale("+t+")","-webkit-":"scale("+t+")"})});$(".mask-in").click(function(){t-=.1;t<=.1?(t=.1,$(".mask-layer-imgbox img").css({transform:"scale(.1)","-moz-transform":"scale(.1)","-ms-transform":"scale(.1)","-o-transform":"scale(.1)","-webkit-transform":"scale(.1)"})):$(".mask-layer-imgbox img").css({transform:"scale("+t+")","-moz-transform":"scale("+t+")","-ms-transform":"scale("+t+")","-o-transform":"scale("+t+")","-webkit-transform":"scale("+t+")"})});r=0;$(".mask-clockwise").click(function(){r+=90;$(".mask-layer-imgbox img").parent("p").css({transform:"rotate("+r+"deg)","-moz-transform":"rotate("+r+"deg)","-ms-transform":"rotate("+r+"deg)","-o-transform":"rotate("+r+"deg)","-webkit-transform":"rotate("+r+"deg)"})});$(".mask-counterclockwise").click(function(){r-=90;$(".mask-layer-imgbox img").parent("p").css({transform:"rotate("+r+"deg)","-moz-transform":"rotate("+r+"deg)","-ms-transform":"rotate("+r+"deg)","-o-transform":"rotate("+r+"deg)","-webkit-transform":"rotate("+r+"deg)"})});$(".mask-close").click(function(){$(".mask-layer").remove()});$(".mask-layer-black").click(function(){$(".mask-layer").remove()})}$("body").append('<div class="mask-layer">   <div class="mask-layer-black"><\/div>   <div class="mask-layer-container">       <div class="mask-layer-container-operate">           <button class="mask-prev btn-default-styles" style="float: left">上一张<\/button>           <button class="mask-out btn-default-styles">放大<\/button>           <button class="mask-in btn-default-styles">缩小<\/button>           <button class="mask-clockwise btn-default-styles">顺旋转<\/button>           <button class="mask-counterclockwise btn-default-styles">逆旋转<\/button>           <button class="mask-close btn-default-styles">关闭<\/button>           <button class="mask-next btn-default-styles" style="float: right">下一张<\/button>       <\/div>       <div class="mask-layer-imgbox auto-img-center"><\/div>   <\/div><\/div>');var e=$(this),u=$(this).attr("src"),f=i.indexOf(u),n=f;r();$(".mask-next").on("click",function(){$(".mask-layer-imgbox p img").remove();n++;n==t&&(n=0);r()});$(".mask-prev").on("click",function(){$(".mask-layer-imgbox p img").remove();n--;n==-1&&(n=t-1);r()})})})}};