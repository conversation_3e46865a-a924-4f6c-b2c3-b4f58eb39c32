(function(n){"use strict";function u(t){var o=n("#"+t),s=o.attr("callback"),u=o.attr("limit"),l,a;if(!s||s==""){alert("请提供上传方法");return}var v=document.getElementById(t),h=o.parents(".z_photo"),r=v.files,c=h.find(".up-section").length,y=c+r.length;r.length>u||y>u?alert("上传图片数目不可以超过"+u+"个，请重新选择"):c<u&&(f(r),l=function(r){var u=n("<section class='up-section fl loading'>"),e,f,s;h.prepend(u);e=n("<span class='up-span'>");e.appendTo(u);var c=n("#"+t).attr("context"),o=ys.getGuid(),l=n("<img id='"+o+"' class='close-upimg'> style='display:"+i+"'").on("click",function(){n("#"+t).imageUpload("deleteImage",o)});l.attr("src",c+"lib/imageupload/1.0/img/delete.png").appendTo(u);f=n("<img class='up-img up-opcity'>");f.attr("src",r);f.appendTo(u);s=n("<p class='img-name-p'>");s.html(r.substring(r.lastIndexOf("/")+1)).appendTo(u);setTimeout(function(){n(".up-section").removeClass("loading");n(".up-img").removeClass("up-opcity")},450);n("#"+t).imageUpload("checkImageLimit")},a=r[r.length-1],e(eval(s),[a,l]))}function f(n){for(var i,u,f=[],e=0,t;t=n[e];e++)i=t.name.split("").reverse().join(""),i.split(".")[0]!=null?(u=i.split(".")[0].split("").reverse().join(""),console.log(u+"===type==="),jQuery.inArray(u,r.fileType)>-1?t.size>=r.fileSize?(alert(t.size),alert('您这个"'+t.name+'"文件大小过大')):f.push(t):alert('您这个"'+t.name+'"上传类型不符合')):alert('您这个"'+t.name+'"没有类型, 无法识别');return f}function e(n,t){n.apply(this,t)}var t,i="none",r={fileType:["jpg","png","bmp","jpeg"],fileSize:10485760};n.fn.imageUpload=function(r,f){if(typeof r=="string")return n.fn.imageUpload.methods[r](this,f);var o=n.extend({},n.fn.imageUpload.defaults,r||{}),s=n(this),c=s.attr("id"),h=c+"_file",e="";e+='<section class="img-section">';e+='    <div class="z_photo upimg-div clear">';e+='       <section class="z_file fl">';o.canAdd==1&&(e+='           <img src="'+o.context+'lib/imageupload/1.0/img/add.png" class="add-img">',i="block");e+='           <input type="file" name="'+h+'" id="'+h+'" class="file-image" callback="'+o.uploadImage+'" context="'+o.context+'" limit="'+o.limit+'"  value="" accept="image/jpg,image/jpeg,image/png,image/bmp" />';e+="       <\/section>";e+="   <\/div>";e+="<\/section>";e+='<aside class="mask works-mask">';e+='   <div class="mask-content">';e+='       <div class="del-p">您确定要删除图片吗？<\/div>';e+='       <div class="check-p"><span class="del-com wsdel-ok">确定<\/span><span class="wsdel-no">取消<\/span><\/div>';e+="   <\/div>";e+="<\/aside>";s.append(e);s.find(".wsdel-ok").click(function(){n(".works-mask").hide();var i=t.siblings().length;i<6&&t.parent().find(".z_file").show();t.remove()});s.find(".wsdel-no").click(function(){n(".works-mask").hide()});n("#"+h).change(function(){u(h)})};n.fn.imageUpload.defaults={uploadImage:"",limit:10,context:"",canPreview:1,canAdd:1};n.fn.imageUpload.methods={getImageUrl:function(t){for(var r="",u=n(t).find(".up-section").find(".up-img"),i=0;i<u.length;i++)i==0?r+=n(u[i]).attr("src"):(r+=";",r+=n(u[i]).attr("src"));return r},setImageUrl:function(t,r){var u;if(r){var c=n(t).attr("id"),o=c+"_file",h=n("#"+o).attr("context"),f=r.split(";");for(u=0;u<f.length;u++)if(f[u]!=""){var s=ys.getGuid(),l=f[u].substring(f[u].lastIndexOf("/")+1),e="";e+='<section class="up-section fl">';e+='   <span class="up-span"><\/span>';e+='   <img id="'+s+'" class="close-upimg" style="display:'+i+'" src="'+h+'lib/imageupload/1.0/img/delete.png" />';e+=f[u].indexOf("http")>-1?'   <img class="up-img" src="'+f[u]+'" />':'   <img class="up-img" src="'+h+ys.trimStart(f[u],"/")+'" />';e+='   <p class="img-name-p">"'+l+'"<\/p>';e+="<\/section>";n(e).insertBefore(n(t).find(".z_file"));n("#"+s).on("click",function(){n("#"+o).imageUpload("deleteImage",s)})}n(".up-span").Huipreview();n("#"+o).imageUpload("checkImageLimit")}},deleteImage:function(i,r){var u=n(i),f=u.attr("id");!event||(event.preventDefault(),event.stopPropagation());n(".works-mask").show();t=n("#"+r).parent();n("#"+f).imageUpload("checkImageLimit")},checkImageLimit:function(t){var i=n(t),r=i.parents(".z_photo").find(".up-section").length,u=i.attr("limit");r>=u?i.parent().hide():i.parent().show()}}})(jQuery);!function(n){n.fn.Huipreview=function(t){var t=n.extend({type:"image",className:"active",bigImgWidth:300,top:0},t);this.each(function(){var i=n(this),r;i.hover(function(){clearTimeout(r);r=setTimeout(function(){var r,f,o;n("#preview-wraper").remove();r=i.find("img");r.length==0&&(r=i.parent().find(".up-img"));var a=r.attr("src"),u=r.attr("src"),w=r.attr("width"),b=r.attr("height"),s=n(window).width(),v=s/2,y=i.parent().offset().top-t.top,e=i.parent().offset().left,h=i.parent().width(),k=i.parent().height(),p=e+h/2,c="auto",l="auto";if(p<v?c=h+e+"px":l=s-e+"px",i.addClass(t.className),u=="")return!1;f='<div id="preview-wraper" style="position: absolute;z-index:999;width:'+t.bigImgWidth+"px;height:auto;top:"+y+"px;right:"+l+";left:"+c+'">';f+=t.type=="video"?'<video id="banner-video" width="100%" autoplay loop><source type="video/mp4" src="'+u+'" /><object width="100%" type="http://lib.h-ui.net/flashmediaelement.swf"><param name="movie" value="http://lib.h-ui.net/flashmediaelement.swf" /><param name="flashvars" value="'+midimg+'" /><\/object><\/video>':'<img src="'+a+'" width="'+t.bigImgWidth+'">';f+="<\/div>";n("body").append(f);t.type=="image"&&(o=new Image,o.src=u,o.onload=function(){n("#preview-wraper").find("img").attr("src",u).css("width",t.bigImgWidth)})},10)},function(){clearTimeout(r);i.removeClass(t.className);n("#preview-wraper").remove()})})}}(window.jQuery);