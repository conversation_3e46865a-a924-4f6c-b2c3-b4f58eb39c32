@charset "utf-8";
* {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box
}

*, body {
	padding: 0px;
	margin: 0px;
	font-family: "微软雅黑";
}
ol, ul, p, h1, h2, h3, h4, h5, h6 {
	padding: 0;
	margin: 0
}

body {
	background: #000d4a url(map_bg.jpg) center center;
	background-size: cover;
	color: #fff;
	font-size: .1rem;
	overflow: hidden;
}

li {
	list-style-type: none;
}
i {
	margin: 0px;
	padding: 0px;
	text-indent: 0px;
}

img {
	border: none;
	max-width: 100%;
}

a {
	text-decoration: none;
	color: #399bff;
}

	a.active, a:focus {
		outline: none !important;
		text-decoration: none;
	}



a:hover {
	color: #06c;
	text-decoration: none !important
}

html, body {
	height: 100%;
}

.clearfix:after, .clearfix:before {
	display: table;
	content: " "
}

.clearfix:after {
	clear: both
}

.loading {
	position: fixed;
	left: 0;
	top: 0;
	font-size: 16px;
	z-index: 100000000;
	width: 100%;
	height: 100%;
	background: #1a1a1c;
	text-align: center;
}

.loadbox {
	position: absolute;
	width: 160px;
	height: 150px;
	color: #324e93;
	left: 50%;
	top: 50%;
	margin-top: -100px;
	margin-left: -75px;
}

	.loadbox img {
		margin: 10px auto;
		display: block;
		width: 40px;
	}

.head {
	height: 1.05rem;
	background: url(head_bg.png) no-repeat center center;
	background-size: 100% 100%;
	position: relative
}

	.head h1 {
		color: #fff;
		text-align: center;
		font-size: .4rem;
		line-height: .8rem;
		letter-spacing: -1px;
	}

		.head h1 img {
			width: 1.5rem;
			display: inline-block;
			vertical-align: middle;
		}

.time {
	position: absolute;
	right: .15rem;
	top: 0;
	line-height: .75rem;
	color: rgba(255,255,255,.7);
	font-size: .3rem;
	padding-right: .1rem;
	font-family: electronicFont;
}

.mainbox {
	padding: 0 .2rem 0rem .2rem;
	height: calc(100% - 1.05rem)
}

	.mainbox > ul {
		margin-left: -.1rem;
		margin-right: -.1rem;
		height: 100%
	}

		.mainbox > ul > li {
			float: left;
			padding: 0 .1rem;
			height: 100%;
			width: 33.333%
		}

.boxall {
	padding: 0 .2rem .2rem .2rem;
	background: rgba(6,48,109,.5);
	position: relative;
	margin-bottom: .15rem;
	z-index: auto;
	border: 1px solid rgba(25, 186, 139, .17);
}

	.boxall:before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: linear-gradient(to left, #02a6b5, #02a6b5) left top no-repeat, linear-gradient(to bottom, #02a6b5, #02a6b5) left top no-repeat, linear-gradient(to left, #02a6b5, #02a6b5) right top no-repeat, linear-gradient(to bottom, #02a6b5, #02a6b5) right top no-repeat, linear-gradient(to left, #02a6b5, #02a6b5) left bottom no-repeat, linear-gradient(to bottom, #02a6b5, #02a6b5) left bottom no-repeat, linear-gradient(to left, #02a6b5, #02a6b5) right bottom no-repeat, linear-gradient(to left, #02a6b5, #02a6b5) right bottom no-repeat;
		background-size: 2px 10px, 10px 2px, 2px 10px, 10px 2px;
	}

.alltitle {
	font-size: .15rem;
	color: #fff;
	line-height: .5rem;
	position: relative;
	padding-left: .15rem
}

	.alltitle:before {
		position: absolute;
		height: .15rem;
		width: 3px;
		background: #49bcf7;
		border-radius: 5px;
		content: "";
		left: 0;
		top: 55%;
		margin-top: -.1rem;
	}

.boxnav {
	height: calc(100% - .5rem);
}

.gradient-text-one {
	background-image: -webkit-linear-gradient(bottom,#daf9ff,#399bff);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.alltitleHeader {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 0.15rem;
}

.laboratory {
	height: 20%;
}

	.laboratory .laboratorySum,
	.laboratory .laboratoryMean {
		font-size: .15rem;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0.05rem;
	}

		.laboratory .laboratorySum ul,
		.laboratory .laboratoryMean ul {
			width: 80%;
			text-align: center;
			display: flex;
			justify-content: space-around;
			align-items: center;
		}

	.laboratory ul li div:first-child {
		color: #FEF000;
	}

.registerNum, .alredyNum {
	display: flex;
	justify-content: space-around;
	align-items: center;
	padding-top: .15rem;
}

	.registerNum li, .alredyNum li {
		text-align: center;
		color: #fff;
		font-size: .25rem;
	}

	.alredyNum li {
		font-size: .18rem;
	}

		.registerNum li > div div:first-child, .alredyNum li > div div:first-child {
			color: #FEF000;
		}
.colorTooltip {
	display: inline-block;
	margin-right: 5px;
	border-radius: 50%;
	width: 10px;
	height: 10px;
	left: 5px
}
#marquee {
	width: 100%;
	overflow: hidden;
}
.experimentDynamic #dynamicAll {
	font-family: "宋体" !important;
	color: #e3e3e3 !important;
}

	.experimentDynamic #dynamicAll li {
		padding: 0.05rem .1rem;
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: .13rem;
	}
		.experimentDynamic #dynamicAll li div:nth-child(1) {
			width: 30%;
			word-break: break-all;
			display: -webkit-box;
			-webkit-line-clamp: 1;
			-webkit-box-orient: vertical;
			overflow: hidden;
		}
		.experimentDynamic #dynamicAll li div:nth-child(2) {
			width: 45%;
			word-break: break-all;
			display: -webkit-box;
			-webkit-line-clamp: 1;
			-webkit-box-orient: vertical;
			overflow: hidden;
		}
#gradeRatio, #gradePlan, #stagePrivate, #experimentRank, #stageRoom {
	position: relative;
}

.ratioGradeContent, .planGradeContent, .privateStageContent, .rankContent, .roomStageContent {
	display: none;
}
.ratioItem_hover:hover .ratioGradeContent,
.planItem_hover:hover .planGradeContent,
.privateItem_hover:hover .privateStageContent,
.roomItem_hover:hover .roomStageContent,
.rankItem_hover:hover .rankContent {
	display: block;
	min-width: 80px;
	position: absolute;
	left: -25px;
	border: 1px solid #062b83;
	border-radius: 4px;
	background-color: #062b83;
	box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 12px 0px;
	box-sizing: border-box;
	z-index: 9999;
}

.itemHover {
	cursor: pointer;
}

.content ul {
	padding: 6px 0;
}

	.content ul li {
		text-align: center;
		padding: 2px 0;
	}

		.content ul li:hover {
			background-color: #5D9FFF;
			color: #FEF000;
			font-weight: 700;
		}

.selectLi {
	background-color: #5D9FFF;
	color: #FEF000;
	font-weight: 700;
}

/*谷哥滚动条样式*/
::-webkit-scrollbar {
	width: 5px;
	height: 5px;
	position: absolute
}

::-webkit-scrollbar-thumb {
	background-color: #5bc0de
}

::-webkit-scrollbar-track {
	background-color: #ddd
}
