﻿html {
    overflow: hidden;
    height: 100%;
    border: none;
    padding: 0px;
    margin: 0px;
}

.box {
    position: relative;
    border-radius: 3px;
    background: #ffffff;
    border-top: 3px solid #d2d6de;
    margin-bottom: 20px;
    width: 100%;
    box-shadow: 0 1px 1px rgba(0,0,0,0.1)
}

.box-header:before, .box-body:before, .box-footer:before, .box-header:after, .box-body:after, .box-footer:after {
    content: " ";
    display: table
}

.box-header:after, .box-body:after, .box-footer:after {
    clear: both
}

.btn-box-tool {
    padding: 5px;
    font-size: 12px;
    background: transparent;
    color: #97a0b3;
}

    .open .btn-box-tool,
    .btn-box-tool:hover {
        color: #606c84;
    }

.box-main {
    margin: 0;
    border: 0;
    padding-top: 2px;
    border-radius: 0;
    box-shadow: none
}

    .box-main > .box-header {
        border-bottom: 1px solid #eee;
        padding: 12px 10px 2px 15px
    }

.box-header .box-title {
    display: inline-block;
    font-size: 18px;
    margin: 0;
    line-height: 1;
}

.box-main > .box-header .box-title {
    font-size: 16px;
    margin-top: 2px;
    float: left
}

    .box-main > .box-header .box-title .fa {
        font-size: 14px;
        padding-right: 3px;
        margin-top: -2px
    }

.box-main > .box-header .box-tools {
    position: relative;
    top: -5px;
    right: 0
}

    .box-main > .box-header .box-tools .btn {
        padding: 3px 10px 5px 10px;
        font-size: 14px;
        margin-bottom: 2px
    }

    .box-main > .box-header .box-tools .btn-box-tool {
        padding: 4px 2px
    }

.box-main form > .box-footer, .nav-main form > .box-footer {
    background: #fafafa
}

    .box-main form > .box-footer .row, .nav-main form > .box-footer .row {
        margin: 5px 0 5px -25px
    }

/** select2 样式修改 */
.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #1AB394;
    border-color: #1AB394;
    padding: 1px 10px;
    color: #fff
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    margin-right: 5px;
    color: rgba(255,255,255,0.7)
}

    .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
        color: #fff
    }

.select2-container .select2-selection--single .select2-selection__rendered {
    padding-right: 10px
}

/* Select2 宽度修复 */
.select2-container {
    width: 100% !important;
    min-width: 100px !important;
}

.select2-container--default .select2-selection--single {
    border: 1px solid #e1e5e9;
    border-radius: 6px;
/*    height: 38px;*/
/*    line-height: 36px;*/
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
/*    line-height: 36px;*/
    padding-left: 12px;
    color: #333;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
/*    height: 36px;*/
}

.select2-dropdown {
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    padding: 6px 8px;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #2196f3;
}

/* 确保下拉框在动态绑定后保持宽度 */
span.select2-container--default {
    width: 100% !important;
}

/** 表单验证 样式布局 */
label.error {
    position: absolute;
    right: 38px;
    top: 7px;
    color: #ef392b;
    font-size: 12px;
    z-index: 1;
}

.Validform_error, input.error, select.error {
    background-color: #fbe2e2;
    border-color: #c66161;
    color: #c00
}

.Validform_wrong, .Validform_right, .Validform_warning {
    display: inline-block;
    height: 20px;
    font-size: 12px;
    vertical-align: middle;
    padding-left: 25px
}

.i-checks label.error, .check-box label.error, .radio-box label.error {
    right: auto;
    width: 150px;
    left: 210px;
    top: 1px;
    max-width: none;
}

/** 遮罩层 */
.loaderbox {
    display: inline-block;
    min-width: 125px;
    padding: 10px;
    margin: 0 auto;
    color: #000 !important;
    font-size: 13px;
    font-weight: 400;
    text-align: center;
    vertical-align: middle;
    border: 1px solid #ddd;
    background-color: #eee;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    border-radius: 2px;
    -webkit-box-shadow: 0 1px 8px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0 1px 8px rgba(0, 0, 0, 0.1);
    box-shadow: 0 1px 8px rgba(0, 0, 0, 0.1);
}

    .loaderbox .loading-activity {
        float: left;
        width: 18px;
        height: 18px;
        border: solid 2px transparent;
        border-top-color: #000;
        border-left-color: #000;
        border-radius: 10px;
        -webkit-animation: pace-spinner 400ms linear infinite;
        -moz-animation: pace-spinner 400ms linear infinite;
        -ms-animation: pace-spinner 400ms linear infinite;
        -o-animation: pace-spinner 400ms linear infinite;
        animation: pace-spinner 400ms linear infinite;
    }

@media (max-width: 767px) {
    .loading-activity {
        width: 18px;
        height: 18px;
    }
}

@-ms-keyframes pace-spinner {
    0% {
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    100% {
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes pace-spinner {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/** 表单查询条件 */
ul {
    margin: 0;
    padding: 0;
    -webkit-tap-highlight-color: rgba(0,0,0,0);
}

li {
    list-style: none;
}

.time-input {
    display: block;
    width: 100%;
    padding-left: 10px;
}

label {
    font-weight: normal;
}

.container-div {
    padding: 10px 35px;
    height: 100%;
}

    .container-div .row {
        height: 100%;
    }

.search-collapse, .select-table {
    width: 100%;
    background: #fff;
    border-radius: 8px;
    margin-top: 10px;
    padding-top: 10px;
    padding-bottom: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,.08);
    border: 1px solid #f0f0f0;
}

/* 默认限制搜索区域高度 */
/*.search-collapse,
.select-table {
    max-height: 60px;
    overflow: hidden;
}*/

/* 当搜索区域内容超过一行时，允许适当扩展 */
.search-collapse .select-list > ul {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    min-height: 40px;
}

.select-table .select-list > ul {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    min-height: 40px;
}

/* 确保 flex 布局下的列表项不使用 float */
.search-collapse .select-list > ul > li,
.select-table .select-list > ul > li {
    float: none !important;
}

/* InstrumentInput 页面的特殊样式 */
#searchDiv.search-collapse {
    max-height: none !important;
    overflow: visible !important;
    min-height: auto !important;
}

/* InstrumentInput 页面的按钮行样式优化 */
#searchDiv .select-list > ul > li {
    height: auto !important;
    min-height: 28px;
    margin: 6px 15px 6px 0px;
}

#searchDiv .select-list li .btn {
    height: auto !important;
    min-height: 32px;
    padding: 5px 16px !important;
    line-height: 1.4 !important;
    margin-right:5px;
}

.search-collapse {
    position: relative;
}

    .search-collapse .col-sm-6 .control-label {
        color: #333;
    }

@media ( max-width : 768px) {
    .search-collapse {
        display: none;
    }
}

@media ( min-width : 768px) {
    .select-list > ul > li {
        float: left;
    }
}

.select-list > ul > li {
    color: #333;
    margin: 5px 15px 5px 0px;
    height: 32px;
    display: flex;
    align-items: center;
}

.select-list li input {
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    background: #fafbfc;
    outline: none;
    height: 28px;
    width: 280px;
   /* padding: 0 10px;
    font-size: 13px;*/
    transition: all 0.3s ease;
}

.select-list li input:focus {
    border-color: #4285f4;
    background: #fff;
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.1);
}

.select-list li .submit-btn {
    border: 0px;
    border-radius: 4px;
    background: transparent;
    outline: none;
    width: 40px;
    height: 23px;
}

.select-list li select {
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    background: #fafbfc;
    outline: none;
    height: 32px;
    width: 280px;
    padding: 0 10px;
    font-size: 13px;
    transition: all 0.3s ease;
}

.select-list li select:focus {
    border-color: #4285f4;
    background: #fff;
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.1);
}

.select-list .select-time input {
    width: 133px;
}

.select-time label, .select-time span, .select-time input {
    float: left;
}

.select-time label {
    margin-top: 5px;
}

.select-time span {
    display: block;
    margin: 5px 5px;
}

.search-btn {
    background-color: #1ab394;
    border-color: #1ab394;
    color: #FFF;
    margin-bottom: 3px;
    display: inline-block;
    padding: 4px 12px;
    margin-bottom: 0;
    font-size: 13px;
    font-weight: 400;
    line-height: 1.3;
    text-align: center;
    white-space: nowrap;
    border-radius: 4px;
    vertical-align: middle;
    cursor: pointer;
    height: 32px;
}

/** 表格查询数据 */
.table-striped {
    min-height: 75%;
}

    .table-striped .bootstrap-table {
        border: 0px !important;
      /*  border-radius: 8px;*/
/*        overflow: hidden;*/
       /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);*/
    }

    /* 表格第一行标题的圆角 */
    .table-striped .table > thead:first-child > tr:first-child > th:first-child {
        border-top-left-radius: 8px;
    }

    .table-striped .table > thead:first-child > tr:first-child > th:last-child {
        border-top-right-radius: 8px;
    }

    .table-striped .table, .fixed-table-container, table, .table-striped .table, .table > thead > tr > th, .table > tbody > tr > th, .table > tfoot > tr > th, .table > thead > tr > td, .table > tbody > tr > td, .table > tfoot > tr > td {
        border-bottom: 1px solid #f0f0f0 !important;
        background-color: transparent;
    }

     
        .table-striped .table > tbody > tr > td {
            padding: 12px 10px;
            font-size: 14px;
            color: #333333;
            vertical-align: middle;
            border-bottom: 1px solid #e3f2fd;
        }

        .table-striped .table > tbody > tr:hover {
            background-color: #e3f2fd;
            transition: background-color 0.2s ease;
        }

        .table-striped .table > tbody > tr:nth-child(even) {
            background-color: #f8fbff;
        }

        .table-striped .table > tbody > tr:nth-child(even):hover {
            background-color: #e3f2fd;
        }

.table-bordered td, .table-bordered th {
    border: 1px solid #ddd !important
}

.control-label > .red {
    position: relative;
    top: 3px;
    color: red;
}

.treeSelect-panel {
    position: absolute;
    overflow: auto;
    border: 1px solid #DCDCDC;
    width: 100%;
    display: none;
    border-top-width: 0;
    z-index: 101;
    background: white;
}

.treeSelect-input {
    width: 100%;
    padding: 5px;
}

.treeSelect-mask {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    background-color: #FFF;
    opacity: .1;
    z-index: 99;
}

/* jQuery autocomplete */
.ui-state-active,
.ui-widget-content .ui-state-active,
.ui-widget-header .ui-state-active,
a.ui-button:active,
.ui-button:active,
.ui-button.ui-state-active:hover {
    border: 1px solid #c5c5c5;
    background: #f3f3f4;
    font-weight: normal;
    color: #676a6c;
}

/** 首页样式 **/
.ax_close_max {
    position: fixed;
    top: 5px;
    left: 5px;
    z-index: 9999;
    display: none;
    color: #ccc;
}

.navbar-right > .user-menu > .dropdown-menu {
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    padding: 1px 0 0 0;
    border-top-width: 0;
    width: 138px;
}

.navbar-right > .user-menu .user-image {
    float: left;
    width: 27px;
    height: 27px;
    border-radius: 50%;
    margin-right: 8px;
    margin-top: -3px;
}

@media (max-width:767px) {
    .navbar-right > .user-menu .user-image {
        float: none;
        margin-right: 0;
        margin-top: -8px;
        line-height: 10px;
    }
}

.dropdown-menu > li > a > .glyphicon, .dropdown-menu > li > a > .fa, .dropdown-menu > li > a > .ion {
    margin-right: 10px;
}

.dropdown-menu > li > a:hover {
    background-color: #e1e3e9;
    color: #333;
}

.dropdown-menu > .divider {
    background-color: #eee;
}

.img-xs {
    width: 32px;
    height: 32px;
}

.img-sm {
    width: 64px;
    height: 64px;
}

.img-md {
    width: 96px;
    height: 96px;
}

.img-lg {
    width: 120px;
    height: 120px;
}
#helpBtn {
    color: gray;
    font-size: 16px;
    cursor: pointer;
    margin-top: 5px;
}

/* 现代化按钮样式 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    padding: 8px 16px;
    font-size: 14px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.btn-primary {
    background: linear-gradient(135deg, #4285f4 0%, #3367d6 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(66, 133, 244, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #3367d6 0%, #2851a3 100%);
    box-shadow: 0 4px 8px rgba(66, 133, 244, 0.4);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, #34a853 0%, #2d8f47 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(52, 168, 83, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, #2d8f47 0%, #1e5f31 100%);
    box-shadow: 0 4px 8px rgba(52, 168, 83, 0.4);
    transform: translateY(-1px);
}

.btn-warning {
    background: linear-gradient(135deg, #fbbc04 0%, #ea8600 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(251, 188, 4, 0.3);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #ea8600 0%, #d17400 100%);
    box-shadow: 0 4px 8px rgba(251, 188, 4, 0.4);
    transform: translateY(-1px);
}

.btn-danger {
    background: linear-gradient(135deg, #ea4335 0%, #d33b2c 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(234, 67, 53, 0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #d33b2c 0%, #b52d20 100%);
    box-shadow: 0 4px 8px rgba(234, 67, 53, 0.4);
    transform: translateY(-1px);
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);
}

.btn-info:hover {
    background: linear-gradient(135deg, #138496 0%, #0f6674 100%);
    box-shadow: 0 4px 8px rgba(23, 162, 184, 0.4);
    transform: translateY(-1px);
}

.btn-secondary {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
    background: #e9ecef;
    border-color: #adb5bd;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

.btn.disabled, .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

.btn-xs {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 13px;
    border-radius: 5px;
}

/* 工具栏样式优化 */
.btn-group-sm.hidden-xs {
    margin: 8px 0;
    padding: 0;
    background: transparent;
    border-radius: 0;
    box-shadow: none;
    border: none;
    min-height: auto;
    display: block;
}

/* 当工具栏为空时隐藏 */
.btn-group-sm.hidden-xs:empty {
    display: none !important;
    margin: 0;
    padding: 0;
    height: 0;
}

/* 当按钮组只包含空白内容时也隐藏 */
.btn-group-sm.hidden-xs.empty-group {
    display: none !important;
    margin: 0;
    padding: 0;
    height: 0;
}

.btn-group-sm.hidden-xs .btn {
    margin-right: 8px;
    margin-bottom: 6px;
}

/* 通用按钮组样式 */
.btn-group-sm {
    background: transparent;
    border: none;
    box-shadow: none;
}

/* 搜索区域标签样式 */
.select-list li label {
    font-weight: 500;
    color: #5f6368;
    margin-bottom: 4px;
    font-size: 13px;
}

/* 分页样式优化 */
.pagination > li > a,
.pagination > li > span {
    border-radius: 6px;
    margin: 0 2px;
    border: 1px solid #e1e5e9;
    color: #5f6368;
    padding: 8px 12px;
    transition: all 0.3s ease;
}

.pagination > li > a:hover,
.pagination > li > span:hover {
    background-color: #f8f9fa;
    border-color: #4285f4;
    color: #4285f4;
}

.pagination > .active > a,
.pagination > .active > span {
    background-color: #4285f4;
    border-color: #4285f4;
    color: white;
}

/* 复选框样式优化 */
input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #4285f4;
}

/* 表格操作按钮样式 */
.table td .btn {
    margin-right: 4px;
}

/* 表格中的按钮组样式 */
.table td .btn-group {
    display: flex;
    gap: 4px;
}

/* 表格中的图标样式 */
.table td .fa,
.table th .fa {
    margin-right: 4px;
}

/* 表格中的操作列样式 */
.table td .btn-success.btn-xs {
    background: linear-gradient(135deg, #34a853 0%, #2d8f47 100%);
    padding: 4px 8px;
}

.table td .btn-danger.btn-xs {
    background: linear-gradient(135deg, #ea4335 0%, #d33b2c 100%);
    padding: 4px 8px;
}

.table td .btn-primary.btn-xs {
    background: linear-gradient(135deg, #4285f4 0%, #3367d6 100%);
    padding: 4px 8px;
}

.table td .btn-info.btn-xs {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    padding: 4px 8px;
}

.table td .btn-warning.btn-xs {
    background: linear-gradient(135deg, #fbbc04 0%, #ea8600 100%);
    padding: 4px 8px;
}

/* Bootstrap Table 样式覆盖 */
.fixed-table-container {
    border: 1px solid #f0f0f0 !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(0,0,0,.08) !important;
}

.bootstrap-table .table {
    border-bottom: none !important;
    border-radius: 8px !important;
}

.fixed-table-header {
    border-bottom: 1px solid #e8eaed !important;
}

.fixed-table-body {
    border-radius: 0 0 8px 8px;
}

/* 表格工具栏样式 */
.fixed-table-toolbar {
    padding: 6px 12px !important;
    background: transparent !important;
    border-bottom: none !important;
    border-radius: 0 !important;
    min-height: auto !important;
    max-height: 55px !important;
/*    overflow: hidden !important;*/
    line-height: 1.2 !important;
}

/* 当工具栏内容为空时隐藏 - 使用兼容性更好的选择器 */
.fixed-table-toolbar .btn-group-sm.hidden-xs:empty {
    display: none !important;
}

.fixed-table-toolbar .bs-bars:empty {
    display: none !important;
}

/* 当工具栏没有内容时的样式 */
.fixed-table-toolbar.toolbar-empty {
    display: none !important;
    padding: 0 !important;
    margin: 0 !important;
    height: 0 !important;
}

/* 自动检测空工具栏的脚本会添加这个类 */
.auto-hide-empty-toolbar {
    display: none !important;
    padding: 0 !important;
    margin: 0 !important;
    height: 0 !important;
}

/*
 * 空工具栏自动检测脚本
 * 在页面加载完成后执行，检测并隐藏空的工具栏
 */

/* 整体页面优化 */
body {
    background-color: #f5f7fa !important;
    font-family: "Microsoft YaHei", "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
}

/* 页面内容区域优化 */
#page-wrapper {
    background-color: #f5f7fa;
    min-height: calc(100vh - 60px);
}

.wrapper-content {
    padding: 15px 20px;
}

/* 面包屑导航优化 */
.breadcrumb {
    background: transparent;
    border: none;
    margin-bottom: 15px;
    padding: 8px 0;
    font-size: 13px;
}

.breadcrumb > li + li:before {
    color: #90a4ae;
    content: ">";
    padding: 0 8px;
}

/* 页面标题优化 */
.page-heading {
    border-bottom: 1px solid #e3f2fd;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.page-heading h2 {
    color: #1976d2;
    font-weight: 500;
    margin: 0;
    font-size: 24px;
}

/* 卡片容器优化 */
.ibox {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8eaed;
    margin-bottom: 20px;
    overflow: hidden;
}

/*.ibox-title {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #e3f2fd;
    padding: 12px 20px;
    min-height: auto;
}*/

.ibox-title h5 {
    color: #1976d2;
    font-weight: 600;
    margin: 0;
    font-size: 16px;
}

.ibox-content {
    padding: 20px;
    background: #ffffff;
}

/* 白色背景区域优化 */
.white-bg {
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}

/* 表单优化 */
.form-group {
    margin-bottom: 15px;
}

.form-control {
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #2196f3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

/* 下拉菜单修复 */
.dropdown-menu {
    background-color: #ffffff !important;
    border: 1px solid #e1e5e9 !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    padding: 8px 0 !important;
    min-width: 160px !important;
}

.dropdown-menu > li > a {
    color: #333333 !important;
    padding: 8px 16px !important;
    display: block !important;
    clear: both !important;
    font-weight: normal !important;
    line-height: 1.4 !important;
    white-space: nowrap !important;
    text-decoration: none !important;
}

.dropdown-menu > li > a:hover,
.dropdown-menu > li > a:focus {
    background-color: #f8f9fa !important;
    color: #1976d2 !important;
}

.dropdown-menu > .divider {
    height: 1px !important;
    margin: 8px 0 !important;
    overflow: hidden !important;
    background-color: #e9ecef !important;
}

/* 用户菜单特殊样式 */
.navbar-right > .user-menu > .dropdown-menu {
    background-color: #ffffff !important;
    border: 1px solid #e1e5e9 !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    right: 0 !important;
    left: auto !important;
    margin-top: 2px !important;
}

/* 按钮优化 */
.btn {
    border-radius: 5px;
    font-weight: 500;
    padding: 5px 10px;
    font-size: 12px;
    transition: all 0.3s ease;
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    color: #ffffff;
    box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(33, 150, 243, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    color: #ffffff;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, #388e3c 0%, #2e7d32 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.4);
}

.btn-warning {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    color: #ffffff;
    box-shadow: 0 2px 4px rgba(255, 152, 0, 0.3);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #f57c00 0%, #ef6c00 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(255, 152, 0, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    color: #ffffff;
    box-shadow: 0 2px 4px rgba(244, 67, 54, 0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #d32f2f 0%, #c62828 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(244, 67, 54, 0.4);
}



.fixed-table-toolbar .btn-group .btn {
    margin-right: 6px;
    padding: 4px 12px !important;
    font-size: 13px !important;
    line-height: 1.3 !important;
    height: 32px !important;
    border-radius: 4px !important;
}

.fixed-table-toolbar .bs-bars {
    margin: 0;
    padding: 0;
    line-height: 1.3;
}

/* 当 bs-bars 为空时隐藏 */
.fixed-table-toolbar .bs-bars:empty {
    display: none !important;
}

/* 分页工具栏 */
.fixed-table-pagination {
    padding: 15px 20px !important;
    background: #fafbfc !important;
    border-top: 1px solid #e8eaed !important;
    border-radius: 0 0 8px 8px !important;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .container-div {
        padding: 10px 15px;
    }

    .btn-group-sm.hidden-xs {
        padding: 0;
        margin: 5px 0;
    }

    .btn-group-sm.hidden-xs .btn {
        margin-bottom: 8px;
        width: 100%;
        margin-right: 0;
    }

    .select-list li input,
    .select-list li select {
        width: 100%;
    }

    .fixed-table-toolbar,
    .fixed-table-pagination {
        padding: 10px 15px !important;
    }
}
@media (min-width: 768px){
    .form-horizontal .control-label {
        padding-top: 7px;
        margin-bottom: 0;
        text-align: right !important;
    }
}
.btn-sm {
    padding: 5px 10px !important;
    font-size: 12px !important;
    border-radius: 5px !important;
}
.keep-open.btn-group .btn-secondary {
    margin-top: -6px;
}
.btn-group.d-flex {
 display: block !important;
}
.fileinput-cancel.fileinput-cancel-button{
    display:none !important;
}