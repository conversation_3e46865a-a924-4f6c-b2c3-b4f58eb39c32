{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"ZackEFCore.Batch.Dm_NET6/6.6.3": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "6.0.19", "Zack.EFCore.Batch_NET6": "6.1.3", "dmdbms.Microsoft.EntityFrameworkCore.Dm": "6.0.16.16649"}, "runtime": {"ZackEFCore.Batch.Dm_NET6.dll": {}}}, "dmdbms.DmProvider/1.1.0.16649": {"runtime": {"lib/net6.0/DmProvider.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.0.16649"}}, "resources": {"lib/net6.0/en/DmProvider.resources.dll": {"locale": "en"}, "lib/net6.0/zh-CN/DmProvider.resources.dll": {"locale": "zh-CN"}}}, "dmdbms.Microsoft.EntityFrameworkCore.Dm/6.0.16.16649": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "6.0.19", "dmdbms.DmProvider": "1.1.0.16649"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Dm.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.0.0"}}}, "Microsoft.EntityFrameworkCore/6.0.19": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "6.0.19", "Microsoft.EntityFrameworkCore.Analyzers": "6.0.19", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.Logging": "6.0.0", "System.Collections.Immutable": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "6.0.19.0", "fileVersion": "6.0.1923.31801"}}}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.19": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "6.0.19.0", "fileVersion": "6.0.1923.31801"}}}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.19": {}, "Microsoft.EntityFrameworkCore.Relational/6.0.19": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.19", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "6.0.19.0", "fileVersion": "6.0.1923.31801"}}}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.222.6406"}}}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.DependencyInjection/6.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.1022.47605"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Logging/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Options/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Options.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Primitives/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Collections.Immutable/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.1523.11507"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "Zack.EFCore.Batch_NET6/6.1.3": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "6.0.19"}, "runtime": {"Zack.EFCore.Batch_NET6.dll": {}}}}}, "libraries": {"ZackEFCore.Batch.Dm_NET6/6.6.3": {"type": "project", "serviceable": false, "sha512": ""}, "dmdbms.DmProvider/1.1.0.16649": {"type": "package", "serviceable": true, "sha512": "sha512-E6ZUN352V7gZGvGUtQNro6pJ1gcdYm26dEWWKyPST5FLa9Bk94NPClxxDgEfujh1LmFHj5qZuhZcsAbFOIFl1w==", "path": "dmdbms.dmprovider/1.1.0.16649", "hashPath": "dmdbms.dmprovider.1.1.0.16649.nupkg.sha512"}, "dmdbms.Microsoft.EntityFrameworkCore.Dm/6.0.16.16649": {"type": "package", "serviceable": true, "sha512": "sha512-bxvfjKhd9UzVIWEqCLeHqc3DDjMI2Rgr9T4VLaid0Mi5EhZjhUtFKuXo6cf4YaviO3yHdauNFihNh1of1ri9Aw==", "path": "dmdbms.microsoft.entityframeworkcore.dm/6.0.16.16649", "hashPath": "dmdbms.microsoft.entityframeworkcore.dm.6.0.16.16649.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/6.0.19": {"type": "package", "serviceable": true, "sha512": "sha512-rJyVCPookNEBScNenXtU2CRHD6YzdyI0V4NNGQrGMpREuWlrEyB3WhOzBuIvuEAYWKx8B7R82p9loC+OZ4ZvNA==", "path": "microsoft.entityframeworkcore/6.0.19", "hashPath": "microsoft.entityframeworkcore.6.0.19.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.19": {"type": "package", "serviceable": true, "sha512": "sha512-8lbiDRXU5pAELDvgLZj2NMeeIsnLAIk2n/q3wVc6ctU0rPYbeng3541hbeDj9mjd7kPtt0Vm5FVdcpjJFAy1dw==", "path": "microsoft.entityframeworkcore.abstractions/6.0.19", "hashPath": "microsoft.entityframeworkcore.abstractions.6.0.19.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.19": {"type": "package", "serviceable": true, "sha512": "sha512-0XxgSQ2iymaIu8esrh8uGjK3BToPkRYdQXufr+7/mFiCHZ+ChfywxiRZJvPTpSUiHbT6lyFAnNT171RMVqMjcw==", "path": "microsoft.entityframeworkcore.analyzers/6.0.19", "hashPath": "microsoft.entityframeworkcore.analyzers.6.0.19.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/6.0.19": {"type": "package", "serviceable": true, "sha512": "sha512-LG0uv5ydT5d8Vxe9iNendVxe3YQCIxpl2l6OlRCRM5h0R8Er3pHD3JKhmZTAX/1rUe1v2mxs15knd0rfJC5SCQ==", "path": "microsoft.entityframeworkcore.relational/6.0.19", "hashPath": "microsoft.entityframeworkcore.relational.6.0.19.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bcz5sSFJbganH0+YrfvIjJDIcKNW7TL07C4d1eTmXy/wOt52iz4LVogJb6pazs7W0+74j0YpXFErvp++Aq5Bsw==", "path": "microsoft.extensions.caching.abstractions/6.0.0", "hashPath": "microsoft.extensions.caching.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-B4y+Cev05eMcjf1na0v9gza6GUtahXbtY1JCypIgx3B4Ea/KAgsWyXEmW4q6zMbmTMtKzmPVk09rvFJirvMwTg==", "path": "microsoft.extensions.caching.memory/6.0.1", "hashPath": "microsoft.extensions.caching.memory.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qWzV9o+ZRWq+pGm+1dF+R7qTgTYoXvbyowRoBxQJGfqTpqDun2eteerjRQhq5PQ/14S+lqto3Ft4gYaRyl4rdQ==", "path": "microsoft.extensions.configuration.abstractions/6.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-vWXPg3HJQIpZkENn1KWq8SfbqVujVD7S7vIAyFXXqK5xkf1Vho+vG0bLBCHxU36lD1cLLtmGpfYf0B3MYFi9tQ==", "path": "microsoft.extensions.dependencyinjection/6.0.1", "hashPath": "microsoft.extensions.dependencyinjection.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlzi2IYREJH3/m6+lUrQlujzX8wDitm4QGnUu6kUXTQAWPuZY8i+ticFJbzfqaetLA6KR/rO6Ew/HuYD+bxifg==", "path": "microsoft.extensions.dependencyinjection.abstractions/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eIbyj40QDg1NDz0HBW0S5f3wrLVnKWnDJ/JtZ+yJDFnDj90VoPuoPmFkeaXrtu+0cKm5GRAwoDf+dBWXK0TUdg==", "path": "microsoft.extensions.logging/6.0.0", "hashPath": "microsoft.extensions.logging.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/HggWBbTwy8TgebGSX5DBZ24ndhzi93sHUBDvP1IxbZD7FDokYzdAr6+vbWGjw2XAfR2EJ1sfKUotpjHnFWPxA==", "path": "microsoft.extensions.logging.abstractions/6.0.0", "hashPath": "microsoft.extensions.logging.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dzXN0+V1AyjOe2xcJ86Qbo233KHuLEY0njf/P2Kw8SfJU+d45HNS2ctJdnEnrWbM9Ye2eFgaC5Mj9otRMU6IsQ==", "path": "microsoft.extensions.options/6.0.0", "hashPath": "microsoft.extensions.options.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9+PnzmQFfEFNR9J2aDTfJGGupShHjOuGw4VUv+JB044biSHrnmCIMD+mJHmb2H7YryrfBEXDurxQ47gJZdCKNQ==", "path": "microsoft.extensions.primitives/6.0.0", "hashPath": "microsoft.extensions.primitives.6.0.0.nupkg.sha512"}, "System.Collections.Immutable/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "path": "system.collections.immutable/6.0.0", "hashPath": "system.collections.immutable.6.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "Zack.EFCore.Batch_NET6/6.1.3": {"type": "project", "serviceable": false, "sha512": ""}}}