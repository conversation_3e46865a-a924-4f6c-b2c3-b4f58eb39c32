{"version": 3, "file": "metisMenu.esm.js", "sources": ["../src/util.js", "../src/index.js"], "sourcesContent": ["import $ from 'jquery';\n\nconst Util = (($) => { // eslint-disable-line no-shadow\n  const TRANSITION_END = 'transitionend';\n\n  const Util = { // eslint-disable-line no-shadow\n    TRANSITION_END: 'mmTransitionEnd',\n\n    triggerTransitionEnd(element) {\n      $(element).trigger(TRANSITION_END);\n    },\n\n    supportsTransitionEnd() {\n      return Boolean(TRANSITION_END);\n    },\n  };\n\n  function getSpecialTransitionEndEvent() {\n    return {\n      bindType: TRANSITION_END,\n      delegateType: TRANSITION_END,\n      handle(event) {\n        if ($(event.target).is(this)) {\n          return event\n            .handleObj\n            .handler\n            .apply(this, arguments); // eslint-disable-line prefer-rest-params\n        }\n        return undefined;\n      },\n    };\n  }\n\n  function transitionEndEmulator(duration) {\n    let called = false;\n\n    $(this).one(Util.TRANSITION_END, () => {\n      called = true;\n    });\n\n    setTimeout(() => {\n      if (!called) {\n        Util.triggerTransitionEnd(this);\n      }\n    }, duration);\n\n    return this;\n  }\n\n  function setTransitionEndSupport() {\n    $.fn.mmEmulateTransitionEnd = transitionEndEmulator; // eslint-disable-line no-param-reassign\n    // eslint-disable-next-line no-param-reassign\n    $.event.special[Util.TRANSITION_END] = getSpecialTransitionEndEvent();\n  }\n\n  setTransitionEndSupport();\n\n  return Util;\n})($);\n\nexport default Util;\n", "import $ from 'jquery';\nimport Util from './util';\n\nconst NAME = 'metisMenu';\nconst DATA_KEY = 'metisMenu';\nconst EVENT_KEY = `.${DATA_KEY}`;\nconst DATA_API_KEY = '.data-api';\nconst JQUERY_NO_CONFLICT = $.fn[NAME];\nconst TRANSITION_DURATION = 350;\n\nconst Default = {\n  toggle: true,\n  preventDefault: true,\n  triggerElement: 'a',\n  parentTrigger: 'li',\n  subMenu: 'ul',\n};\n\nconst Event = {\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  CLICK_DATA_API: `click${EVENT_KEY}${DATA_API_KEY}`,\n};\n\nconst ClassName = {\n  METIS: 'metismenu',\n  ACTIVE: 'mm-active',\n  SHOW: 'mm-show',\n  COLLAPSE: 'mm-collapse',\n  COLLAPSING: 'mm-collapsing',\n  COLLAPSED: 'mm-collapsed',\n};\n\nclass MetisMenu {\n  // eslint-disable-line no-shadow\n  constructor(element, config) {\n    this.element = element;\n    this.config = {\n      ...Default,\n      ...config,\n    };\n    this.transitioning = null;\n\n    this.init();\n  }\n\n  init() {\n    const self = this;\n    const conf = this.config;\n    const el = $(this.element);\n\n    el.addClass(ClassName.METIS); // add metismenu class to element\n\n    el.find(`${conf.parentTrigger}.${ClassName.ACTIVE}`)\n      .children(conf.triggerElement)\n      .attr('aria-expanded', 'true'); // add attribute aria-expanded=true the trigger element\n\n    el.find(`${conf.parentTrigger}.${ClassName.ACTIVE}`)\n      .parents(conf.parentTrigger)\n      .addClass(ClassName.ACTIVE);\n\n    el.find(`${conf.parentTrigger}.${ClassName.ACTIVE}`)\n      .parents(conf.parentTrigger)\n      .children(conf.triggerElement)\n      .attr('aria-expanded', 'true'); // add attribute aria-expanded=true the triggers of all parents\n\n    el.find(`${conf.parentTrigger}.${ClassName.ACTIVE}`)\n      .has(conf.subMenu)\n      .children(conf.subMenu)\n      .addClass(`${ClassName.COLLAPSE} ${ClassName.SHOW}`);\n\n    el\n      .find(conf.parentTrigger)\n      .not(`.${ClassName.ACTIVE}`)\n      .has(conf.subMenu)\n      .children(conf.subMenu)\n      .addClass(ClassName.COLLAPSE);\n\n    el\n      .find(conf.parentTrigger)\n      // .has(conf.subMenu)\n      .children(conf.triggerElement)\n      .on(Event.CLICK_DATA_API, function (e) { // eslint-disable-line func-names\n        const eTar = $(this);\n\n        if (eTar.attr('aria-disabled') === 'true') {\n          return;\n        }\n\n        if (conf.preventDefault && eTar.attr('href') === '#') {\n          e.preventDefault();\n        }\n\n        const paRent = eTar.parent(conf.parentTrigger);\n        const sibLi = paRent.siblings(conf.parentTrigger);\n        const sibTrigger = sibLi.children(conf.triggerElement);\n\n        if (paRent.hasClass(ClassName.ACTIVE)) {\n          eTar.attr('aria-expanded', 'false');\n          self.removeActive(paRent);\n        } else {\n          eTar.attr('aria-expanded', 'true');\n          self.setActive(paRent);\n          if (conf.toggle) {\n            self.removeActive(sibLi);\n            sibTrigger.attr('aria-expanded', 'false');\n          }\n        }\n\n        if (conf.onTransitionStart) {\n          conf.onTransitionStart(e);\n        }\n      });\n  }\n\n  setActive(li) {\n    $(li).addClass(ClassName.ACTIVE);\n    const ul = $(li).children(this.config.subMenu);\n    if (ul.length > 0 && !ul.hasClass(ClassName.SHOW)) {\n      this.show(ul);\n    }\n  }\n\n  removeActive(li) {\n    $(li).removeClass(ClassName.ACTIVE);\n    const ul = $(li).children(`${this.config.subMenu}.${ClassName.SHOW}`);\n    if (ul.length > 0) {\n      this.hide(ul);\n    }\n  }\n\n  show(element) {\n    if (this.transitioning || $(element).hasClass(ClassName.COLLAPSING)) {\n      return;\n    }\n    const elem = $(element);\n\n    const startEvent = $.Event(Event.SHOW);\n    elem.trigger(startEvent);\n\n    if (startEvent.isDefaultPrevented()) {\n      return;\n    }\n\n    elem.parent(this.config.parentTrigger).addClass(ClassName.ACTIVE);\n\n    if (this.config.toggle) {\n      const toggleElem = elem.parent(this.config.parentTrigger).siblings().children(`${this.config.subMenu}.${ClassName.SHOW}`);\n      this.hide(toggleElem);\n    }\n\n    elem\n      .removeClass(ClassName.COLLAPSE)\n      .addClass(ClassName.COLLAPSING)\n      .height(0);\n\n    this.setTransitioning(true);\n\n    const complete = () => {\n      // check if disposed\n      if (!this.config || !this.element) {\n        return;\n      }\n      elem\n        .removeClass(ClassName.COLLAPSING)\n        .addClass(`${ClassName.COLLAPSE} ${ClassName.SHOW}`)\n        .height('');\n\n      this.setTransitioning(false);\n\n      elem.trigger(Event.SHOWN);\n    };\n\n    elem\n      .height(element[0].scrollHeight)\n      .one(Util.TRANSITION_END, complete)\n      .mmEmulateTransitionEnd(TRANSITION_DURATION);\n  }\n\n  hide(element) {\n    if (\n      this.transitioning || !$(element).hasClass(ClassName.SHOW)\n    ) {\n      return;\n    }\n\n    const elem = $(element);\n\n    const startEvent = $.Event(Event.HIDE);\n    elem.trigger(startEvent);\n\n    if (startEvent.isDefaultPrevented()) {\n      return;\n    }\n\n    elem.parent(this.config.parentTrigger).removeClass(ClassName.ACTIVE);\n    // eslint-disable-next-line no-unused-expressions\n    elem.height(elem.height())[0].offsetHeight;\n\n    elem\n      .addClass(ClassName.COLLAPSING)\n      .removeClass(ClassName.COLLAPSE)\n      .removeClass(ClassName.SHOW);\n\n    this.setTransitioning(true);\n\n    const complete = () => {\n      // check if disposed\n      if (!this.config || !this.element) {\n        return;\n      }\n      if (this.transitioning && this.config.onTransitionEnd) {\n        this.config.onTransitionEnd();\n      }\n\n      this.setTransitioning(false);\n      elem.trigger(Event.HIDDEN);\n\n      elem\n        .removeClass(ClassName.COLLAPSING)\n        .addClass(ClassName.COLLAPSE);\n    };\n\n    if (elem.height() === 0 || elem.css('display') === 'none') {\n      complete();\n    } else {\n      elem\n        .height(0)\n        .one(Util.TRANSITION_END, complete)\n        .mmEmulateTransitionEnd(TRANSITION_DURATION);\n    }\n  }\n\n  setTransitioning(isTransitioning) {\n    this.transitioning = isTransitioning;\n  }\n\n  dispose() {\n    $.removeData(this.element, DATA_KEY);\n\n    $(this.element)\n      .find(this.config.parentTrigger)\n      // .has(this.config.subMenu)\n      .children(this.config.triggerElement)\n      .off(Event.CLICK_DATA_API);\n\n    this.transitioning = null;\n    this.config = null;\n    this.element = null;\n  }\n\n  static jQueryInterface(config) {\n    // eslint-disable-next-line func-names\n    return this.each(function () {\n      const $this = $(this);\n      let data = $this.data(DATA_KEY);\n      const conf = {\n        ...Default,\n        ...$this.data(),\n        ...(typeof config === 'object' && config ? config : {}),\n      };\n\n      if (!data) {\n        data = new MetisMenu(this, conf);\n        $this.data(DATA_KEY, data);\n      }\n\n      if (typeof config === 'string') {\n        if (data[config] === undefined) {\n          throw new Error(`No method named \"${config}\"`);\n        }\n        data[config]();\n      }\n    });\n  }\n}\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = MetisMenu.jQueryInterface; // eslint-disable-line no-param-reassign\n$.fn[NAME].Constructor = MetisMenu; // eslint-disable-line no-param-reassign\n$.fn[NAME].noConflict = () => {\n  // eslint-disable-line no-param-reassign\n  $.fn[NAME] = JQUERY_NO_CONFLICT; // eslint-disable-line no-param-reassign\n  return MetisMenu.jQueryInterface;\n};\n\nexport default MetisMenu;\n"], "names": [], "mappings": ";;;;;;;;;AAEA,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK;AACrB,EAAE,MAAM,cAAc,GAAG,eAAe,CAAC;AACzC;AACA,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,cAAc,EAAE,iBAAiB;AACrC;AACA,IAAI,oBAAoB,CAAC,OAAO,EAAE;AAClC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;AACzC,KAAK;AACL;AACA,IAAI,qBAAqB,GAAG;AAC5B,MAAM,OAAO,OAAO,CAAC,cAAc,CAAC,CAAC;AACrC,KAAK;AACL,GAAG,CAAC;AACJ;AACA,EAAE,SAAS,4BAA4B,GAAG;AAC1C,IAAI,OAAO;AACX,MAAM,QAAQ,EAAE,cAAc;AAC9B,MAAM,YAAY,EAAE,cAAc;AAClC,MAAM,MAAM,CAAC,KAAK,EAAE;AACpB,QAAQ,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;AACtC,UAAU,OAAO,KAAK;AACtB,aAAa,SAAS;AACtB,aAAa,OAAO;AACpB,aAAa,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACpC,SAAS;AACT,QAAQ,OAAO,SAAS,CAAC;AACzB,OAAO;AACP,KAAK,CAAC;AACN,GAAG;AACH;AACA,EAAE,SAAS,qBAAqB,CAAC,QAAQ,EAAE;AAC3C,IAAI,IAAI,MAAM,GAAG,KAAK,CAAC;AACvB;AACA,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM;AAC3C,MAAM,MAAM,GAAG,IAAI,CAAC;AACpB,KAAK,CAAC,CAAC;AACP;AACA,IAAI,UAAU,CAAC,MAAM;AACrB,MAAM,IAAI,CAAC,MAAM,EAAE;AACnB,QAAQ,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;AACxC,OAAO;AACP,KAAK,EAAE,QAAQ,CAAC,CAAC;AACjB;AACA,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA,EAAE,SAAS,uBAAuB,GAAG;AACrC,IAAI,CAAC,CAAC,EAAE,CAAC,sBAAsB,GAAG,qBAAqB,CAAC;AACxD;AACA,IAAI,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,4BAA4B,EAAE,CAAC;AAC1E,GAAG;AACH;AACA,EAAE,uBAAuB,EAAE,CAAC;AAC5B;AACA,EAAE,OAAO,IAAI,CAAC;AACd,CAAC,EAAE,CAAC,CAAC;;ACvDL,MAAM,IAAI,GAAG,WAAW,CAAC;AACzB,MAAM,QAAQ,GAAG,WAAW,CAAC;AAC7B,MAAM,SAAS,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;AACjC,MAAM,YAAY,GAAG,WAAW,CAAC;AACjC,MAAM,kBAAkB,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AACtC,MAAM,mBAAmB,GAAG,GAAG,CAAC;AAChC;AACA,MAAM,OAAO,GAAG;AAChB,EAAE,MAAM,EAAE,IAAI;AACd,EAAE,cAAc,EAAE,IAAI;AACtB,EAAE,cAAc,EAAE,GAAG;AACrB,EAAE,aAAa,EAAE,IAAI;AACrB,EAAE,OAAO,EAAE,IAAI;AACf,CAAC,CAAC;AACF;AACA,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AAC1B,EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AAC5B,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AAC1B,EAAE,MAAM,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AAC9B,EAAE,cAAc,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,YAAY,CAAC,CAAC;AACpD,CAAC,CAAC;AACF;AACA,MAAM,SAAS,GAAG;AAClB,EAAE,KAAK,EAAE,WAAW;AACpB,EAAE,MAAM,EAAE,WAAW;AACrB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,QAAQ,EAAE,aAAa;AACzB,EAAE,UAAU,EAAE,eAAe;AAC7B,EAAE,SAAS,EAAE,cAAc;AAC3B,CAAC,CAAC;AACF;AACA,MAAM,SAAS,CAAC;AAChB;AACA,EAAE,WAAW,CAAC,OAAO,EAAE,MAAM,EAAE;AAC/B,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,IAAI,IAAI,CAAC,MAAM,GAAG;AAClB,MAAM,GAAG,OAAO;AAChB,MAAM,GAAG,MAAM;AACf,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC9B;AACA,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;AAChB,GAAG;AACH;AACA,EAAE,IAAI,GAAG;AACT,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC;AACtB,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;AAC7B,IAAI,MAAM,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC/B;AACA,IAAI,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACjC;AACA,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;AACxD,OAAO,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC;AACpC,OAAO,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;AACrC;AACA,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;AACxD,OAAO,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC;AAClC,OAAO,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAClC;AACA,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;AACxD,OAAO,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC;AAClC,OAAO,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC;AACpC,OAAO,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;AACrC;AACA,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;AACxD,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC;AACxB,OAAO,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;AAC7B,OAAO,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3D;AACA,IAAI,EAAE;AACN,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;AAC/B,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;AAClC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC;AACxB,OAAO,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;AAC7B,OAAO,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;AACpC;AACA,IAAI,EAAE;AACN,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;AAC/B;AACA,OAAO,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC;AACpC,OAAO,EAAE,CAAC,KAAK,CAAC,cAAc,EAAE,UAAU,CAAC,EAAE;AAC7C,QAAQ,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B;AACA,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,MAAM,EAAE;AACnD,UAAU,OAAO;AACjB,SAAS;AACT;AACA,QAAQ,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE;AAC9D,UAAU,CAAC,CAAC,cAAc,EAAE,CAAC;AAC7B,SAAS;AACT;AACA,QAAQ,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACvD,QAAQ,MAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC1D,QAAQ,MAAM,UAAU,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AAC/D;AACA,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;AAC/C,UAAU,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;AAC9C,UAAU,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AACpC,SAAS,MAAM;AACf,UAAU,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;AAC7C,UAAU,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AACjC,UAAU,IAAI,IAAI,CAAC,MAAM,EAAE;AAC3B,YAAY,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AACrC,YAAY,UAAU,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;AACtD,WAAW;AACX,SAAS;AACT;AACA,QAAQ,IAAI,IAAI,CAAC,iBAAiB,EAAE;AACpC,UAAU,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;AACpC,SAAS;AACT,OAAO,CAAC,CAAC;AACT,GAAG;AACH;AACA,EAAE,SAAS,CAAC,EAAE,EAAE;AAChB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AACrC,IAAI,MAAM,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACnD,IAAI,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;AACvD,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACpB,KAAK;AACL,GAAG;AACH;AACA,EAAE,YAAY,CAAC,EAAE,EAAE;AACnB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AACxC,IAAI,MAAM,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1E,IAAI,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;AACvB,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACpB,KAAK;AACL,GAAG;AACH;AACA,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE;AACzE,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;AAC5B;AACA,IAAI,MAAM,UAAU,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC3C,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAC7B;AACA,IAAI,IAAI,UAAU,CAAC,kBAAkB,EAAE,EAAE;AACzC,MAAM,OAAO;AACb,KAAK;AACL;AACA,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AACtE;AACA,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;AAC5B,MAAM,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAChI,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC5B,KAAK;AACL;AACA,IAAI,IAAI;AACR,OAAO,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC;AACtC,OAAO,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC;AACrC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;AACjB;AACA,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAChC;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM;AAC3B;AACA,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACzC,QAAQ,OAAO;AACf,OAAO;AACP,MAAM,IAAI;AACV,SAAS,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC;AAC1C,SAAS,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;AAC5D,SAAS,MAAM,CAAC,EAAE,CAAC,CAAC;AACpB;AACA,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AACnC;AACA,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAChC,KAAK,CAAC;AACN;AACA,IAAI,IAAI;AACR,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;AACtC,OAAO,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC;AACzC,OAAO,sBAAsB,CAAC,mBAAmB,CAAC,CAAC;AACnD,GAAG;AACH;AACA,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI;AACJ,MAAM,IAAI,CAAC,aAAa,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC;AAChE,MAAM;AACN,MAAM,OAAO;AACb,KAAK;AACL;AACA,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;AAC5B;AACA,IAAI,MAAM,UAAU,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC3C,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAC7B;AACA,IAAI,IAAI,UAAU,CAAC,kBAAkB,EAAE,EAAE;AACzC,MAAM,OAAO;AACb,KAAK;AACL;AACA,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AACzE;AACA,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;AAC/C;AACA,IAAI,IAAI;AACR,OAAO,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC;AACrC,OAAO,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC;AACtC,OAAO,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACnC;AACA,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAChC;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM;AAC3B;AACA,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACzC,QAAQ,OAAO;AACf,OAAO;AACP,MAAM,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;AAC7D,QAAQ,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;AACtC,OAAO;AACP;AACA,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AACnC,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACjC;AACA,MAAM,IAAI;AACV,SAAS,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC;AAC1C,SAAS,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;AACtC,KAAK,CAAC;AACN;AACA,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,MAAM,EAAE;AAC/D,MAAM,QAAQ,EAAE,CAAC;AACjB,KAAK,MAAM;AACX,MAAM,IAAI;AACV,SAAS,MAAM,CAAC,CAAC,CAAC;AAClB,SAAS,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC;AAC3C,SAAS,sBAAsB,CAAC,mBAAmB,CAAC,CAAC;AACrD,KAAK;AACL,GAAG;AACH;AACA,EAAE,gBAAgB,CAAC,eAAe,EAAE;AACpC,IAAI,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC;AACzC,GAAG;AACH;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AACzC;AACA,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;AACnB,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;AACtC;AACA,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;AAC3C,OAAO,GAAG,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AACjC;AACA,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC9B,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACvB,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,GAAG;AACH;AACA,EAAE,OAAO,eAAe,CAAC,MAAM,EAAE;AACjC;AACA,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;AACjC,MAAM,MAAM,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;AAC5B,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACtC,MAAM,MAAM,IAAI,GAAG;AACnB,QAAQ,GAAG,OAAO;AAClB,QAAQ,GAAG,KAAK,CAAC,IAAI,EAAE;AACvB,QAAQ,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,GAAG,MAAM,GAAG,EAAE,CAAC;AAC/D,OAAO,CAAC;AACR;AACA,MAAM,IAAI,CAAC,IAAI,EAAE;AACjB,QAAQ,IAAI,GAAG,IAAI,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACzC,QAAQ,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACnC,OAAO;AACP;AACA,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AACtC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,SAAS,EAAE;AACxC,UAAU,MAAM,IAAI,KAAK,CAAC,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD,SAAS;AACT,QAAQ,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;AACvB,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,eAAe,CAAC;AACvC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,WAAW,GAAG,SAAS,CAAC;AACnC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,UAAU,GAAG,MAAM;AAC9B;AACA,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC;AAClC,EAAE,OAAO,SAAS,CAAC,eAAe,CAAC;AACnC,CAAC;;;;"}