using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Quartz;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Service.SystemManage;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Business.SystemManage;
using Dqy.Syjx.Model.Param.SystemManage;

namespace Dqy.Syjx.Business.AutoJob
{
    public class JobCenter
    {
        public void Start()
        {
            Task.Run(async () =>
            {
                TData<List<AutoJobEntity>> obj = await new AutoJobBLL().GetList(new AutoJobListParam() { JobStatus = 1 }); //查询启用的定时任务
                if (obj.Tag == 1)
                {
                    AddScheduleJob(obj.Data);
                }
            });
        }

        #region 添加任务计划
        private void AddScheduleJob(List<AutoJobEntity> entityList)
        {
            try
            {
                foreach (AutoJobEntity entity in entityList)
                {
                    if (entity.StartTime == null)
                    {
                        entity.StartTime = DateTime.Now;
                    }
                    DateTimeOffset starRunTime = DateBuilder.NextGivenSecondDate(entity.StartTime, 1);
                    if (entity.EndTime == null)
                    {
                        entity.EndTime = DateTime.MaxValue.AddDays(-1);
                    }
                    DateTimeOffset endRunTime = DateBuilder.NextGivenSecondDate(entity.EndTime, 1);

                    var scheduler = JobScheduler.GetScheduler();
                    IJobDetail job = JobBuilder.Create<JobExecute>().WithIdentity(entity.JobName, entity.JobGroupName).Build();
                    job.JobDataMap.Add("Id", entity.Id);

                    ICronTrigger trigger = (ICronTrigger)TriggerBuilder.Create()
                                                 .StartAt(starRunTime)
                                                 .EndAt(endRunTime)
                                                 .WithIdentity(entity.JobName, entity.JobGroupName)
                                                 .WithCronSchedule(entity.CronExpression, x => x.InTimeZone(TimeZoneInfo.Local))
                                                 .Build();

                    scheduler.ScheduleJob(job, trigger);
                    scheduler.Start();
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error(ex);
            }
        }
        #endregion

        #region 清除任务计划
        public void ClearScheduleJob()
        {
            try
            {
                JobScheduler.GetScheduler().Clear();
            }
            catch (Exception ex)
            {
                LogHelper.Error(ex);
            }
        }
        #endregion
    }
}
