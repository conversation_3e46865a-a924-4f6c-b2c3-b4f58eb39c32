/**
 * 工具栏辅助脚本
 * 用于检测和隐藏空的工具栏
 */

(function() {
    'use strict';
    
    /**
     * 检测并隐藏空的工具栏
     */
    function hideEmptyToolbars() {
        // 检查所有的 fixed-table-toolbar
        document.querySelectorAll('.fixed-table-toolbar').forEach(function(toolbar) {
            var btnGroup = toolbar.querySelector('.btn-group-sm.hidden-xs');
            var bsBars = toolbar.querySelector('.bs-bars');
            var isEmpty = false;
            
            // 检查按钮组是否为空
            if (!btnGroup || btnGroup.children.length === 0) {
                isEmpty = true;
            } else {
                // 检查是否有实际的按钮内容
                var hasButtons = btnGroup.querySelectorAll('a, button, .btn').length > 0;
                var hasNonEmptyText = btnGroup.textContent.trim() !== '';
                
                if (!hasButtons && !hasNonEmptyText) {
                    isEmpty = true;
                }
            }
            
            // 检查 bs-bars 是否为空
            if (bsBars && bsBars.children.length === 0 && bsBars.textContent.trim() === '') {
                isEmpty = true;
            }
            
            // 应用或移除隐藏类
            if (isEmpty) {
                toolbar.classList.add('auto-hide-empty-toolbar');
            } else {
                toolbar.classList.remove('auto-hide-empty-toolbar');
            }
        });
        
        // 检查空的按钮组
        document.querySelectorAll('.btn-group-sm.hidden-xs').forEach(function(btnGroup) {
            var hasContent = btnGroup.querySelectorAll('a, button, .btn').length > 0 || 
                           btnGroup.textContent.trim() !== '';
            
            if (!hasContent) {
                btnGroup.classList.add('empty-group');
            } else {
                btnGroup.classList.remove('empty-group');
            }
        });
    }
    
    /**
     * 初始化函数
     */
    function init() {
        // 页面加载完成后执行
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', hideEmptyToolbars);
        } else {
            hideEmptyToolbars();
        }
        
        // 监听动态内容变化
        if (window.MutationObserver) {
            var observer = new MutationObserver(function(mutations) {
                var shouldCheck = false;
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && 
                        (mutation.target.classList.contains('fixed-table-toolbar') ||
                         mutation.target.classList.contains('btn-group-sm') ||
                         mutation.target.classList.contains('bs-bars'))) {
                        shouldCheck = true;
                    }
                });
                
                if (shouldCheck) {
                    setTimeout(hideEmptyToolbars, 100);
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
        
        // 为 Bootstrap Table 事件添加监听
        if (window.jQuery) {
            jQuery(document).on('post-body.bs.table', function() {
                setTimeout(hideEmptyToolbars, 100);
            });
            
            jQuery(document).on('load-success.bs.table', function() {
                setTimeout(hideEmptyToolbars, 100);
            });
        }
    }
    
    // 导出到全局作用域
    window.ToolbarHelper = {
        hideEmptyToolbars: hideEmptyToolbars,
        init: init
    };
    
    // 自动初始化
    init();
})();
