function resetToolbarStatus(){$("#btnDelete")&&$("#btnDelete").addClass("disabled");$("#btnSingleDelete")&&$("#btnSingleDelete").addClass("disabled");$("#btnEdit")&&$("#btnEdit").addClass("disabled")}function createMenuItem(n,t){var f=ys.getGuid(),i,r,u;return n==undefined||$.trim(n).length==0?!1:(i=$(window.parent.document),$(".menuTab",i).each(function(){if($(this).data("id").split("?")[0]==n.split("?")[0])return $(".mainContent .YiSha_iframe",i).each(function(){if($(this).data("id").split("?")[0]==n.split("?")[0])return $(this).remove(),!1}),$(this).remove(),!1}),r='<a href="javascript:;" class="active menuTab" data-id="'+n+'">'+t+' <i class="fa fa-times-circle"><\/i><\/a>',$(".menuTab",i).removeClass("active"),u='<iframe class="YiSha_iframe" name="iframe'+f+'" width="100%" height="100%" src="'+n+'" frameborder="0" data-id="'+n+'" seamless><\/iframe>',$(".mainContent",i).find("iframe.YiSha_iframe").hide().parents(".mainContent").append(u),$(".menuTabs .page-tabs-content",i).append(r),!1)}function createMenuAndCloseCurrent(n,t){var f=ys.getGuid(),i,r,u;return n==undefined||$.trim(n).length==0?!1:(i=$(window.parent.document),$(".menuTab",i).each(function(){if($(this).hasClass("active")){var n=$(this).data("id").split("?")[0];return $(".mainContent .YiSha_iframe",i).each(function(){if($(this).data("id").split("?")[0]==n)return $(this).remove(),!1}),$(this).remove(),!1}}),$(".menuTab",i).each(function(){if($(this).data("id").split("?")[0]==n.split("?")[0])return $(".mainContent .YiSha_iframe",i).each(function(){if($(this).data("id").split("?")[0]==n.split("?")[0])return $(this).remove(),!1}),$(this).remove(),!1}),r='<a href="javascript:;" class="active menuTab" data-id="'+n+'">'+t+' <i class="fa fa-times-circle"><\/i><\/a>',$(".menuTab",i).removeClass("active"),u='<iframe class="YiSha_iframe" name="iframe'+f+'" width="100%" height="100%" src="'+n+'" frameborder="0" data-id="'+n+'" seamless><\/iframe>',$(".mainContent",i).find("iframe.YiSha_iframe").hide().parents(".mainContent").append(u),$(".menuTabs .page-tabs-content",i).append(r),!1)}$(function(){var n,t,i;$(".check-box").length>0&&$(".check-box").iCheck({checkboxClass:"icheckbox-blue",radioClass:"iradio-blue"});$(".radio-box").length>0&&$(".radio-box").iCheck({checkboxClass:"icheckbox-blue",radioClass:"iradio-blue"});$(".select-time").length>10&&layui.use("laydate",function(){var i=layui.laydate,n=i.render({elem:"#startTime",max:$("#endTime").val(),theme:"molv",trigger:"click",done:function(n,i){n!==""?(t.config.min.year=i.year,t.config.min.month=i.month-1,t.config.min.date=i.date):(t.config.min.year="",t.config.min.month="",t.config.min.date="")}}),t=i.render({elem:"#endTime",min:$("#startTime").val(),theme:"molv",trigger:"click",done:function(t,i){t!==""?(n.config.max.year=i.year,n.config.max.month=i.month-1,n.config.max.date=i.date):(n.config.max.year="",n.config.max.month="",n.config.max.date="")}})});$("#keyword").length>0&&$("#keyword").bind("focus",function(){$("#keyword").hasClass("empty")&&$("#keyword").removeClass("empty")}).bind("blur",function(n){$("#keyword").val()===""&&$("#keyword").addClass("empty");$.tree.searchNode(n)}).bind("input propertychange",$.tree.searchNode);n=!1;$("#btnExpandAll").click(function(){n?$("#gridTable").bootstrapTreeTable("expandAll"):$("#gridTable").bootstrapTreeTable("collapseAll");n=n?!1:!0});$("#gridTable").on("check.bs.table uncheck.bs.table check-all.bs.table uncheck-all.bs.table",function(){var n=$("#gridTable").bootstrapTable("getSelections");$("#btnDelete")&&$("#btnDelete").toggleClass("disabled",!n.length);$("#btnSingleDelete")&&$("#btnSingleDelete").toggleClass("disabled",n.length!=1);$("#btnEdit")&&$("#btnEdit").toggleClass("disabled",n.length!=1)});$.fn.select2!==undefined&&$("select.form-control.select2").each(function(){$(this).select2().on("change",function(){$(this).valid()})});$("#searchDiv").keyup(function(n){n.which===13&&$("#btnSearch").click()});top.getButtonAuthority&&(t=[],$("#toolbar").find("a").each(function(n,i){t.push(i.id)}),$(".toolbar").find("a").each(function(n,i){t.push(i.id)}),i=top.getButtonAuthority(window.location.href,t),i&&$.each(i,function(n,t){$("#"+t).remove()}));$("input:text, input:password, input:radio, select").each(function(n,t){t.id&&$(t).attr("name",t.id)})});