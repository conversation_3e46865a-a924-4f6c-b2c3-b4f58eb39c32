/*!
 * jQuery JavaScript Library v3.7.1
 * https://jquery.com/
 *
 * Copyright OpenJS Foundation and other contributors
 * Released under the MIT license
 * -\-/license
 *
 * Date: 2023-08-28T13:37Z
 */
(function(n,t){"use strict";typeof module=="object"&&typeof module.exports=="object"?module.exports=n.document?t(n,!0):function(n){if(!n.document)throw new Error("jQuery requires a window with a document");return t(n)}:t(n)})(typeof window!="undefined"?window:this,function(n,t){"use strict";function kr(n,t,i){i=i||u;var r,e,f=i.createElement("script");if(f.text=n,t)for(r in oe)e=t[r]||t.getAttribute&&t.getAttribute(r),e&&f.setAttribute(r,e);i.head.appendChild(f).parentNode.removeChild(f)}function ft(n){return n==null?n+"":typeof n=="object"||typeof n=="function"?fi[wr.call(n)]||"object":typeof n}function pi(n){var t=!!n&&"length"in n&&n.length,i=ft(n);return e(n)||ut(n)?!1:i==="array"||t===0||typeof t=="number"&&t>0&&t-1 in n}function s(n,t){return n.nodeName&&n.nodeName.toLowerCase()===t.toLowerCase()}function ae(n,t){return t?n==="\0"?"�":n.slice(0,-1)+"\\"+n.charCodeAt(n.length-1).toString(16)+" ":"\\"+n}function wi(n,t,r){return e(t)?i.grep(n,function(n,i){return!!t.call(n,i,n)!==r}):t.nodeType?i.grep(n,function(n){return n===t!==r}):typeof t!="string"?i.grep(n,function(n){return d.call(t,n)>-1!==r}):i.filter(t,n,r)}function su(n,t){while((n=n[t])&&n.nodeType!==1);return n}function ve(n){var t={};return i.each(n.match(p)||[],function(n,i){t[i]=!0}),t}function ot(n){return n}function oi(n){throw n;}function hu(n,t,i,r){var u;try{n&&e(u=n.promise)?u.call(n).done(t).fail(i):n&&e(u=n.then)?u.call(n,t,i):t.apply(undefined,[n].slice(r))}catch(n){i.apply(undefined,[n])}}function hi(){u.removeEventListener("DOMContentLoaded",hi);n.removeEventListener("load",hi);i.ready()}function we(n,t){return t.toUpperCase()}function k(n){return n.replace(ye,"ms-").replace(pe,we)}function dt(){this.expando=i.expando+dt.uid++}function de(n){return n==="true"?!0:n==="false"?!1:n==="null"?null:n===+n+""?+n:be.test(n)?JSON.parse(n):n}function lu(n,t,i){var r;if(i===undefined&&n.nodeType===1)if(r="data-"+t.replace(ke,"-$&").toLowerCase(),i=n.getAttribute(r),typeof i=="string"){try{i=de(i)}catch(u){}c.set(n,t,i)}else i=undefined;return i}function vu(n,t,r,u){var s,h,c=20,l=u?function(){return u.cur()}:function(){return i.css(n,t,"")},o=l(),e=r&&r[3]||(i.cssNumber[t]?"":"px"),f=n.nodeType&&(i.cssNumber[t]||e!=="px"&&+o)&&gt.exec(i.css(n,t));if(f&&f[3]!==e){for(o=o/2,e=e||f[3],f=+o||1;c--;)i.style(n,t,f+e),(1-h)*(1-(h=l()/o||.5))<=0&&(c=0),f=f/h;f=f*2;i.style(n,t,f+e);r=r||[]}return r&&(f=+f||+o||0,s=r[1]?f+(r[1]+1)*r[2]:+r[2],u&&(u.unit=e,u.start=f,u.end=s)),s}function no(n){var r,f=n.ownerDocument,u=n.nodeName,t=bi[u];return t?t:(r=f.body.appendChild(f.createElement(u)),t=i.css(r,"display"),r.parentNode.removeChild(r),t==="none"&&(t="block"),bi[u]=t,t)}function ct(n,t){for(var e,u,f=[],i=0,o=n.length;i<o;i++)(u=n[i],u.style)&&(e=u.style.display,t?(e==="none"&&(f[i]=r.get(u,"display")||null,f[i]||(u.style.display="")),u.style.display===""&&ni(u)&&(f[i]=no(u))):e!=="none"&&(f[i]="none",r.set(u,"display",e)));for(i=0;i<o;i++)f[i]!=null&&(n[i].style.display=f[i]);return n}function l(n,t){var r;return(r=typeof n.getElementsByTagName!="undefined"?n.getElementsByTagName(t||"*"):typeof n.querySelectorAll!="undefined"?n.querySelectorAll(t||"*"):[],t===undefined||t&&s(n,t))?i.merge([n],r):r}function ki(n,t){for(var i=0,u=n.length;i<u;i++)r.set(n[i],"globalEval",!t||r.get(t[i],"globalEval"))}function bu(n,t,r,u,f){for(var e,o,p,c,w,a,s=t.createDocumentFragment(),v=[],h=0,b=n.length;h<b;h++)if(e=n[h],e||e===0)if(ft(e)==="object")i.merge(v,e.nodeType?[e]:e);else if(wu.test(e)){for(o=o||s.appendChild(t.createElement("div")),p=(yu.exec(e)||["",""])[1].toLowerCase(),c=y[p]||y._default,o.innerHTML=c[1]+i.htmlPrefilter(e)+c[2],a=c[0];a--;)o=o.lastChild;i.merge(v,o.childNodes);o=s.firstChild;o.textContent=""}else v.push(t.createTextNode(e));for(s.textContent="",h=0;e=v[h++];){if(u&&i.inArray(e,u)>-1){f&&f.push(e);continue}if(w=ht(e),o=l(s.appendChild(e),"script"),w&&ki(o),r)for(a=0;e=o[a++];)pu.test(e.type||"")&&r.push(e)}return s}function lt(){return!0}function at(){return!1}function gi(n,t,r,u,f,e){var o,s;if(typeof t=="object"){typeof r!="string"&&(u=u||r,r=undefined);for(s in t)gi(n,s,r,u,t[s],e);return n}if(u==null&&f==null?(f=r,u=r=undefined):f==null&&(typeof r=="string"?(f=u,u=undefined):(f=u,u=r,r=undefined)),f===!1)f=at;else if(!f)return n;return e===1&&(o=f,f=function(n){return i().off(n),o.apply(this,arguments)},f.guid=o.guid||(o.guid=i.guid++)),n.each(function(){i.event.add(this,t,f,u,r)})}function ci(n,t,u){if(!u){r.get(n,t)===undefined&&i.event.add(n,t,lt);return}r.set(n,t,!1);i.event.add(n,t,{namespace:!1,handler:function(n){var f,u=r.get(this,t);if(n.isTrigger&1&&this[t]){if(u)(i.event.special[t]||{}).delegateType&&n.stopPropagation();else if(u=v.call(arguments),r.set(this,t,u),this[t](),f=r.get(this,t),r.set(this,t,!1),u!==f)return n.stopImmediatePropagation(),n.preventDefault(),f}else u&&(r.set(this,t,i.event.trigger(u[0],u.slice(1),this)),n.stopPropagation(),n.isImmediatePropagationStopped=lt)}})}function ku(n,t){return s(n,"table")&&s(t.nodeType!==11?t:t.firstChild,"tr")?i(n).children("tbody")[0]||n:n}function uo(n){return n.type=(n.getAttribute("type")!==null)+"/"+n.type,n}function fo(n){return(n.type||"").slice(0,5)==="true/"?n.type=n.type.slice(5):n.removeAttribute("type"),n}function du(n,t){var f,o,e,s,h,l,u;if(t.nodeType===1){if(r.hasData(n)&&(s=r.get(n),u=s.events,u)){r.remove(t,"handle events");for(e in u)for(f=0,o=u[e].length;f<o;f++)i.event.add(t,e,u[e][f])}c.hasData(n)&&(h=c.access(n),l=i.extend({},h),c.set(t,l))}}function eo(n,t){var i=t.nodeName.toLowerCase();i==="input"&&ti.test(n.type)?t.checked=n.checked:(i==="input"||i==="textarea")&&(t.defaultValue=n.defaultValue)}function vt(n,t,u,o){t=pr(t);var a,b,c,v,s,y,h=0,p=n.length,d=p-1,w=t[0],k=e(w);if(k||p>1&&typeof w=="string"&&!f.checkClone&&io.test(w))return n.each(function(i){var r=n.eq(i);k&&(t[0]=w.call(this,i,r.html()));vt(r,t,u,o)});if(p&&(a=bu(t,n[0].ownerDocument,!1,n,o),b=a.firstChild,a.childNodes.length===1&&(a=b),b||o)){for(c=i.map(l(a,"script"),uo),v=c.length;h<p;h++)s=a,h!==d&&(s=i.clone(s,!0,!0),v&&i.merge(c,l(s,"script"))),u.call(n[h],s,h);if(v)for(y=c[c.length-1].ownerDocument,i.map(c,fo),h=0;h<v;h++)s=c[h],pu.test(s.type||"")&&!r.access(s,"globalEval")&&i.contains(y,s)&&(s.src&&(s.type||"").toLowerCase()!=="module"?i._evalUrl&&!s.noModule&&i._evalUrl(s.src,{nonce:s.nonce||s.getAttribute("nonce")},y):kr(s.textContent.replace(ro,""),s,y))}return n}function gu(n,t,r){for(var u,e=t?i.filter(t,n):n,f=0;(u=e[f])!=null;f++)r||u.nodeType!==1||i.cleanData(l(u)),u.parentNode&&(r&&ht(u)&&ki(l(u,"script")),u.parentNode.removeChild(u));return n}function ii(n,t,r){var o,s,h,u,c=tr.test(t),e=n.style;return r=r||li(n),r&&(u=r.getPropertyValue(t)||r[t],c&&u&&(u=u.replace(kt,"$1")||undefined),u!==""||ht(n)||(u=i.style(n,t)),!f.pixelBoxStyles()&&nr.test(u)&&oo.test(t)&&(o=e.width,s=e.minWidth,h=e.maxWidth,e.minWidth=e.maxWidth=e.width=u,u=r.width,e.width=o,e.minWidth=s,e.maxWidth=h)),u!==undefined?u+"":u}function tf(n,t){return{get:function(){if(n()){delete this.get;return}return(this.get=t).apply(this,arguments)}}}function so(n){for(var i=n[0].toUpperCase()+n.slice(1),t=rf.length;t--;)if(n=rf[t]+i,n in uf)return n}function ir(n){var t=i.cssProps[n]||ff[n];return t?t:n in uf?n:ff[n]=so(n)||n}function of(n,t,i){var r=gt.exec(t);return r?Math.max(0,r[2]-(i||0))+(r[3]||"px"):t}function rr(n,t,r,u,f,e){var o=t==="width"?1:0,h=0,s=0,c=0;if(r===(u?"border":"content"))return 0;for(;o<4;o+=2)r==="margin"&&(c+=i.css(n,r+nt[o],!0,f)),u?(r==="content"&&(s-=i.css(n,"padding"+nt[o],!0,f)),r!=="margin"&&(s-=i.css(n,"border"+nt[o]+"Width",!0,f))):(s+=i.css(n,"padding"+nt[o],!0,f),r!=="padding"?s+=i.css(n,"border"+nt[o]+"Width",!0,f):h+=i.css(n,"border"+nt[o]+"Width",!0,f));return!u&&e>=0&&(s+=Math.max(0,Math.ceil(n["offset"+t[0].toUpperCase()+t.slice(1)]-e-s-h-.5))||0),s+c}function sf(n,t,r){var e=li(n),l=!f.boxSizingReliable()||r,o=l&&i.css(n,"boxSizing",!1,e)==="border-box",h=o,u=ii(n,t,e),c="offset"+t[0].toUpperCase()+t.slice(1);if(nr.test(u)){if(!r)return u;u="auto"}return(!f.boxSizingReliable()&&o||!f.reliableTrDimensions()&&s(n,"tr")||u==="auto"||!parseFloat(u)&&i.css(n,"display",!1,e)==="inline")&&n.getClientRects().length&&(o=i.css(n,"boxSizing",!1,e)==="border-box",h=c in n,h&&(u=n[c])),u=parseFloat(u)||0,u+rr(n,t,r||(o?"border":"content"),h,e,u)+"px"}function a(n,t,i,r,u){return new a.prototype.init(n,t,i,r,u)}function ur(){ai&&(u.hidden===!1&&n.requestAnimationFrame?n.requestAnimationFrame(ur):n.setTimeout(ur,i.fx.interval),i.fx.tick())}function lf(){return n.setTimeout(function(){yt=undefined}),yt=Date.now()}function vi(n,t){var r,u=0,i={height:n};for(t=t?1:0;u<4;u+=2-t)r=nt[u],i["margin"+r]=i["padding"+r]=n;return t&&(i.opacity=i.width=n),i}function af(n,t,i){for(var u,f=(w.tweeners[t]||[]).concat(w.tweeners["*"]),r=0,e=f.length;r<e;r++)if(u=f[r].call(i,t,n))return u}function lo(n,t,u){var f,y,w,c,b,s,o,l,k="width"in t||"height"in t,v=this,p={},h=n.style,a=n.nodeType&&ni(n),e=r.get(n,"fxshow");u.queue||(c=i._queueHooks(n,"fx"),c.unqueued==null&&(c.unqueued=0,b=c.empty.fire,c.empty.fire=function(){c.unqueued||b()}),c.unqueued++,v.always(function(){v.always(function(){c.unqueued--;i.queue(n,"fx").length||c.empty.fire()})}));for(f in t)if(y=t[f],hf.test(y)){if(delete t[f],w=w||y==="toggle",y===(a?"hide":"show"))if(y==="show"&&e&&e[f]!==undefined)a=!0;else continue;p[f]=e&&e[f]||i.style(n,f)}if(s=!i.isEmptyObject(t),s||!i.isEmptyObject(p)){k&&n.nodeType===1&&(u.overflow=[h.overflow,h.overflowX,h.overflowY],o=e&&e.display,o==null&&(o=r.get(n,"display")),l=i.css(n,"display"),l==="none"&&(o?l=o:(ct([n],!0),o=n.style.display||o,l=i.css(n,"display"),ct([n]))),(l==="inline"||l==="inline-block"&&o!=null)&&i.css(n,"float")==="none"&&(s||(v.done(function(){h.display=o}),o==null&&(l=h.display,o=l==="none"?"":l)),h.display="inline-block"));u.overflow&&(h.overflow="hidden",v.always(function(){h.overflow=u.overflow[0];h.overflowX=u.overflow[1];h.overflowY=u.overflow[2]}));s=!1;for(f in p)s||(e?"hidden"in e&&(a=e.hidden):e=r.access(n,"fxshow",{display:o}),w&&(e.hidden=!a),a&&ct([n],!0),v.done(function(){a||ct([n]);r.remove(n,"fxshow");for(f in p)i.style(n,f,p[f])})),s=af(a?e[f]:0,f,v),f in e||(e[f]=s.start,a&&(s.end=s.start,s.start=0))}}function ao(n,t){var r,f,e,u,o;for(r in n)if(f=k(r),e=t[f],u=n[r],Array.isArray(u)&&(e=u[1],u=n[r]=u[0]),r!==f&&(n[f]=u,delete n[r]),o=i.cssHooks[f],o&&"expand"in o){u=o.expand(u);delete n[f];for(r in u)r in n||(n[r]=u[r],t[r]=e)}else t[f]=e}function w(n,t,r){var o,s,h=0,a=w.prefilters.length,f=i.Deferred().always(function(){delete l.elem}),l=function(){if(s)return!1;for(var o=yt||lf(),t=Math.max(0,u.startTime+u.duration-o),h=t/u.duration||0,i=1-h,r=0,e=u.tweens.length;r<e;r++)u.tweens[r].run(i);return(f.notifyWith(n,[u,i,t]),i<1&&e)?t:(e||f.notifyWith(n,[u,1,0]),f.resolveWith(n,[u]),!1)},u=f.promise({elem:n,props:i.extend({},t),opts:i.extend(!0,{specialEasing:{},easing:i.easing._default},r),originalProperties:t,originalOptions:r,startTime:yt||lf(),duration:r.duration,tweens:[],createTween:function(t,r){var f=i.Tween(n,u.opts,t,r,u.opts.specialEasing[t]||u.opts.easing);return u.tweens.push(f),f},stop:function(t){var i=0,r=t?u.tweens.length:0;if(s)return this;for(s=!0;i<r;i++)u.tweens[i].run(1);return t?(f.notifyWith(n,[u,1,0]),f.resolveWith(n,[u,t])):f.rejectWith(n,[u,t]),this}}),c=u.props;for(ao(c,u.opts.specialEasing);h<a;h++)if(o=w.prefilters[h].call(u,n,c,u.opts),o)return e(o.stop)&&(i._queueHooks(u.elem,u.opts.queue).stop=o.stop.bind(o)),o;return i.map(c,af,u),e(u.opts.start)&&u.opts.start.call(n,u),u.progress(u.opts.progress).done(u.opts.done,u.opts.complete).fail(u.opts.fail).always(u.opts.always),i.fx.timer(i.extend(l,{elem:n,anim:u,queue:u.opts.queue})),u}function it(n){var t=n.match(p)||[];return t.join(" ")}function rt(n){return n.getAttribute&&n.getAttribute("class")||""}function fr(n){return Array.isArray(n)?n:typeof n=="string"?n.match(p)||[]:[]}function hr(n,t,r,u){var f;if(Array.isArray(t))i.each(t,function(t,i){r||vo.test(n)?u(n,i):hr(n+"["+(typeof i=="object"&&i!=null?t:"")+"]",i,r,u)});else if(r||ft(t)!=="object")u(n,t);else for(f in t)hr(n+"["+f+"]",t[f],r,u)}function ne(n){return function(t,i){typeof t!="string"&&(i=t,t="*");var r,u=0,f=t.toLowerCase().match(p)||[];if(e(i))while(r=f[u++])r[0]==="+"?(r=r.slice(1)||"*",(n[r]=n[r]||[]).unshift(i)):(n[r]=n[r]||[]).push(i)}}function te(n,t,r,u){function e(s){var h;return f[s]=!0,i.each(n[s]||[],function(n,i){var s=i(t,r,u);if(typeof s!="string"||o||f[s]){if(o)return!(h=s)}else return t.dataTypes.unshift(s),e(s),!1}),h}var f={},o=n===cr;return e(t.dataTypes[0])||!f["*"]&&e("*")}function ar(n,t){var r,u,f=i.ajaxSettings.flatOptions||{};for(r in t)t[r]!==undefined&&((f[r]?n:u||(u={}))[r]=t[r]);return u&&i.extend(!0,n,u),n}function is(n,t,i){for(var e,u,f,o,s=n.contents,r=n.dataTypes;r[0]==="*";)r.shift(),e===undefined&&(e=n.mimeType||t.getResponseHeader("Content-Type"));if(e)for(u in s)if(s[u]&&s[u].test(e)){r.unshift(u);break}if(r[0]in i)f=r[0];else{for(u in i){if(!r[0]||n.converters[u+" "+r[0]]){f=u;break}o||(o=u)}f=f||o}if(f)return f!==r[0]&&r.unshift(f),i[f]}function rs(n,t,i,r){var h,u,f,s,e,o={},c=n.dataTypes.slice();if(c[1])for(f in n.converters)o[f.toLowerCase()]=n.converters[f];for(u=c.shift();u;)if(n.responseFields[u]&&(i[n.responseFields[u]]=t),!e&&r&&n.dataFilter&&(t=n.dataFilter(t,n.dataType)),e=u,u=c.shift(),u)if(u==="*")u=e;else if(e!=="*"&&e!==u){if(f=o[e+" "+u]||o["* "+u],!f)for(h in o)if(s=h.split(" "),s[1]===u&&(f=o[e+" "+s[0]]||o["* "+s[0]],f)){f===!0?f=o[h]:o[h]!==!0&&(u=s[0],c.unshift(s[1]));break}if(f!==!0)if(f&&n.throws)t=f(t);else try{t=f(t)}catch(l){return{state:"parsererror",error:f?l:"No conversion from "+e+" to "+u}}}return{state:"success",data:t}}var h=[],yr=Object.getPrototypeOf,v=h.slice,pr=h.flat?function(n){return h.flat.call(n)}:function(n){return h.concat.apply([],n)},ui=h.push,d=h.indexOf,fi={},wr=fi.toString,bt=fi.hasOwnProperty,br=bt.toString,ee=br.call(Object),f={},e=function(n){return typeof n=="function"&&typeof n.nodeType!="number"&&typeof n.item!="function"},ut=function(n){return n!=null&&n===n.window},u=n.document,oe={type:!0,src:!0,nonce:!0,noModule:!0},dr="3.7.1",se=/HTML$/i,i=function(n,t){return new i.fn.init(n,t)},gr,b,ei,ru,uu,fu,eu,ou,p,cu,si,st,ni,bi,y,wu,di,yt,ai,hf,cf,vf,pt,yf,pf,wf,or,sr,ie,wt,vr,yi,re,ue,fe;i.fn=i.prototype={jquery:dr,constructor:i,length:0,toArray:function(){return v.call(this)},get:function(n){return n==null?v.call(this):n<0?this[n+this.length]:this[n]},pushStack:function(n){var t=i.merge(this.constructor(),n);return t.prevObject=this,t},each:function(n){return i.each(this,n)},map:function(n){return this.pushStack(i.map(this,function(t,i){return n.call(t,i,t)}))},slice:function(){return this.pushStack(v.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(i.grep(this,function(n,t){return(t+1)%2}))},odd:function(){return this.pushStack(i.grep(this,function(n,t){return t%2}))},eq:function(n){var i=this.length,t=+n+(n<0?i:0);return this.pushStack(t>=0&&t<i?[this[t]]:[])},end:function(){return this.prevObject||this.constructor()},push:ui,sort:h.sort,splice:h.splice};i.extend=i.fn.extend=function(){var s,u,f,t,o,c,n=arguments[0]||{},r=1,l=arguments.length,h=!1;for(typeof n=="boolean"&&(h=n,n=arguments[r]||{},r++),typeof n=="object"||e(n)||(n={}),r===l&&(n=this,r--);r<l;r++)if((s=arguments[r])!=null)for(u in s)(t=s[u],u!=="__proto__"&&n!==t)&&(h&&t&&(i.isPlainObject(t)||(o=Array.isArray(t)))?(f=n[u],c=o&&!Array.isArray(f)?[]:o||i.isPlainObject(f)?f:{},o=!1,n[u]=i.extend(h,c,t)):t!==undefined&&(n[u]=t));return n};i.extend({expando:"jQuery"+(dr+Math.random()).replace(/\D/g,""),isReady:!0,error:function(n){throw new Error(n);},noop:function(){},isPlainObject:function(n){var t,i;return!n||wr.call(n)!=="[object Object]"?!1:(t=yr(n),!t)?!0:(i=bt.call(t,"constructor")&&t.constructor,typeof i=="function"&&br.call(i)===ee)},isEmptyObject:function(n){for(var t in n)return!1;return!0},globalEval:function(n,t,i){kr(n,{nonce:t&&t.nonce},i)},each:function(n,t){var r,i=0;if(pi(n)){for(r=n.length;i<r;i++)if(t.call(n[i],i,n[i])===!1)break}else for(i in n)if(t.call(n[i],i,n[i])===!1)break;return n},text:function(n){var r,u="",f=0,t=n.nodeType;if(!t)while(r=n[f++])u+=i.text(r);return t===1||t===11?n.textContent:t===9?n.documentElement.textContent:t===3||t===4?n.nodeValue:u},makeArray:function(n,t){var r=t||[];return n!=null&&(pi(Object(n))?i.merge(r,typeof n=="string"?[n]:n):ui.call(r,n)),r},inArray:function(n,t,i){return t==null?-1:d.call(t,n,i)},isXMLDoc:function(n){var i=n&&n.namespaceURI,t=n&&(n.ownerDocument||n).documentElement;return!se.test(i||t&&t.nodeName||"HTML")},merge:function(n,t){for(var u=+t.length,i=0,r=n.length;i<u;i++)n[r++]=t[i];return n.length=r,n},grep:function(n,t,i){for(var u,f=[],r=0,e=n.length,o=!i;r<e;r++)u=!t(n[r],r),u!==o&&f.push(n[r]);return f},map:function(n,t,i){var e,u,r=0,f=[];if(pi(n))for(e=n.length;r<e;r++)u=t(n[r],r,i),u!=null&&f.push(u);else for(r in n)u=t(n[r],r,i),u!=null&&f.push(u);return pr(f)},guid:1,support:f});typeof Symbol=="function"&&(i.fn[Symbol.iterator]=h[Symbol.iterator]);i.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(n,t){fi["[object "+t+"]"]=t.toLowerCase()});var he=h.pop,ce=h.sort,le=h.splice,o="[\\x20\\t\\r\\n\\f]",kt=new RegExp("^"+o+"+|((?:^|[^\\\\])(?:\\\\.)*)"+o+"+$","g");i.contains=function(n,t){var i=t&&t.parentNode;return n===i||!!(i&&i.nodeType===1&&(n.contains?n.contains(i):n.compareDocumentPosition&&n.compareDocumentPosition(i)&16))};gr=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;i.escapeSelector=function(n){return(n+"").replace(gr,ae)};b=u;ei=ui,function(){function fr(){try{return u.activeElement}catch(n){}}function r(n,t,o,s){var h,k,v,y,d,b,g,p=t&&t.ownerDocument,w=t?t.nodeType:9;if(o=o||[],typeof n!="string"||!n||w!==1&&w!==9&&w!==11)return o;if(!s&&(nt(t),t=t||u,a)){if(w!==11&&(d=ir.exec(n)))if(h=d[1]){if(w===9)if(v=t.getElementById(h)){if(v.id===h)return l.call(o,v),o}else return o;else if(p&&(v=p.getElementById(h))&&r.contains(t,v)&&v.id===h)return l.call(o,v),o}else{if(d[2])return l.apply(o,t.getElementsByTagName(n)),o;if((h=d[3])&&t.getElementsByClassName)return l.apply(o,t.getElementsByClassName(h)),o}if(!ht[n+" "]&&(!c||!c.test(n))){if(g=n,p=t,w===1&&(ki.test(n)||li.test(n))){for(p=dt.test(n)&&gt(t.parentNode)||t,p==t&&f.scope||((y=t.getAttribute("id"))?y=i.escapeSelector(y):t.setAttribute("id",y=e)),b=et(n),k=b.length;k--;)b[k]=(y?"#"+y:":scope")+" "+at(b[k]);g=b.join(",")}try{return l.apply(o,p.querySelectorAll(g)),o}catch(tt){ht(n,!0)}finally{y===e&&t.removeAttribute("id")}}}return yi(n.replace(kt,"$1"),t,o,s)}function lt(){function n(r,u){return i.push(r+" ")>t.cacheLength&&delete n[i.shift()],n[r+" "]=u}var i=[];return n}function y(n){return n[e]=!0,n}function ut(n){var t=u.createElement("fieldset");try{return!!n(t)}catch(i){return!1}finally{t.parentNode&&t.parentNode.removeChild(t);t=null}}function er(n){return function(t){return s(t,"input")&&t.type===n}}function or(n){return function(t){return(s(t,"input")||s(t,"button"))&&t.type===n}}function ai(n){return function(t){return"form"in t?t.parentNode&&t.disabled===!1?"label"in t?"label"in t.parentNode?t.parentNode.disabled===n:t.disabled===n:t.isDisabled===n||t.isDisabled!==!n&&ur(t)===n:t.disabled===n:"label"in t?t.disabled===n:!1}}function it(n){return y(function(t){return t=+t,y(function(i,r){for(var u,f=n([],i.length,t),e=f.length;e--;)i[u=f[e]]&&(i[u]=!(r[u]=i[u]))})})}function gt(n){return n&&typeof n.getElementsByTagName!="undefined"&&n}function nt(n){var s,h=n?n.ownerDocument||n:b;return h==u||h.nodeType!==9||!h.documentElement?u:(u=h,p=u.documentElement,a=!i.isXMLDoc(u),pt=p.matches||p.webkitMatchesSelector||p.msMatchesSelector,p.msMatchesSelector&&b!=u&&(s=u.defaultView)&&s.top!==s&&s.addEventListener("unload",rr),f.getById=ut(function(n){return p.appendChild(n).id=i.expando,!u.getElementsByName||!u.getElementsByName(i.expando).length}),f.disconnectedMatch=ut(function(n){return pt.call(n,"*")}),f.scope=ut(function(){return u.querySelectorAll(":scope")}),f.cssHas=ut(function(){try{return u.querySelector(":has(*,:jqfake)"),!1}catch(n){return!0}}),f.getById?(t.filter.ID=function(n){var t=n.replace(k,g);return function(n){return n.getAttribute("id")===t}},t.find.ID=function(n,t){if(typeof t.getElementById!="undefined"&&a){var i=t.getElementById(n);return i?[i]:[]}}):(t.filter.ID=function(n){var t=n.replace(k,g);return function(n){var i=typeof n.getAttributeNode!="undefined"&&n.getAttributeNode("id");return i&&i.value===t}},t.find.ID=function(n,t){if(typeof t.getElementById!="undefined"&&a){var i,u,f,r=t.getElementById(n);if(r){if(i=r.getAttributeNode("id"),i&&i.value===n)return[r];for(f=t.getElementsByName(n),u=0;r=f[u++];)if(i=r.getAttributeNode("id"),i&&i.value===n)return[r]}return[]}}),t.find.TAG=function(n,t){return typeof t.getElementsByTagName!="undefined"?t.getElementsByTagName(n):t.querySelectorAll(n)},t.find.CLASS=function(n,t){if(typeof t.getElementsByClassName!="undefined"&&a)return t.getElementsByClassName(n)},c=[],ut(function(n){var t;p.appendChild(n).innerHTML="<a id='"+e+"' href='' disabled='disabled'><\/a><select id='"+e+"-\r\\' disabled='disabled'><option selected=''><\/option><\/select>";n.querySelectorAll("[selected]").length||c.push("\\["+o+"*(?:value|"+si+")");n.querySelectorAll("[id~="+e+"-]").length||c.push("~=");n.querySelectorAll("a#"+e+"+*").length||c.push(".#.+[+~]");n.querySelectorAll(":checked").length||c.push(":checked");t=u.createElement("input");t.setAttribute("type","hidden");n.appendChild(t).setAttribute("name","D");p.appendChild(n).disabled=!0;n.querySelectorAll(":disabled").length!==2&&c.push(":enabled",":disabled");t=u.createElement("input");t.setAttribute("name","");n.appendChild(t);n.querySelectorAll("[name='']").length||c.push("\\["+o+"*name"+o+"*="+o+"*(?:''|\"\")")}),f.cssHas||c.push(":has"),c=c.length&&new RegExp(c.join("|")),wt=function(n,t){if(n===t)return st=!0,0;var i=!n.compareDocumentPosition-!t.compareDocumentPosition;return i?i:(i=(n.ownerDocument||n)==(t.ownerDocument||t)?n.compareDocumentPosition(t):1,i&1||!f.sortDetached&&t.compareDocumentPosition(n)===i)?n===u||n.ownerDocument==b&&r.contains(b,n)?-1:t===u||t.ownerDocument==b&&r.contains(b,t)?1:ft?d.call(ft,n)-d.call(ft,t):0:i&4?-1:1},u)}function vi(){}function et(n,i){var e,f,s,o,u,h,c,l=fi[n+" "];if(l)return i?0:l.slice(0);for(u=n,h=[],c=t.preFilter;u;){(!e||(f=bi.exec(u)))&&(f&&(u=u.slice(f[0].length)||u),h.push(s=[]));e=!1;(f=li.exec(u))&&(e=f.shift(),s.push({value:e,type:f[0].replace(kt," ")}),u=u.slice(e.length));for(o in t.filter)(f=ct[o].exec(u))&&(!c[o]||(f=c[o](f)))&&(e=f.shift(),s.push({value:e,type:o,matches:f}),u=u.slice(e.length));if(!e)break}return i?u.length:u?r.error(n):fi(n,h).slice(0)}function at(n){for(var t=0,r=n.length,i="";t<r;t++)i+=n[t].value;return i}function vt(n,t,i){var r=t.dir,u=t.next,f=u||r,o=i&&f==="parentNode",h=pi++;return t.first?function(t,i,u){while(t=t[r])if(t.nodeType===1||o)return n(t,i,u);return!1}:function(t,i,c){var l,a,v=[w,h];if(c){while(t=t[r])if((t.nodeType===1||o)&&n(t,i,c))return!0}else while(t=t[r])if(t.nodeType===1||o)if(a=t[e]||(t[e]={}),u&&s(t,u))t=t[r]||t;else{if((l=a[f])&&l[0]===w&&l[1]===h)return v[2]=l[2];if(a[f]=v,v[2]=n(t,i,c))return!0}return!1}}function ni(n){return n.length>1?function(t,i,r){for(var u=n.length;u--;)if(!n[u](t,i,r))return!1;return!0}:n[0]}function sr(n,t,i){for(var u=0,f=t.length;u<f;u++)r(n,t[u],i);return i}function yt(n,t,i,r,u){for(var e,o=[],f=0,s=n.length,h=t!=null;f<s;f++)(e=n[f])&&(!i||i(e,r,u))&&(o.push(e),h&&t.push(f));return o}function ti(n,t,i,r,u,f){return r&&!r[e]&&(r=ti(r)),u&&!u[e]&&(u=ti(u,f)),y(function(f,e,o,s){var a,c,v,h,w=[],p=[],b=e.length,k=f||sr(t||"*",o.nodeType?[o]:o,[]),y=n&&(f||!t)?yt(k,w,n,o,s):k;if(i?(h=u||(f?n:b||r)?[]:e,i(y,h,o,s)):h=y,r)for(a=yt(h,p),r(a,[],o,s),c=a.length;c--;)(v=a[c])&&(h[p[c]]=!(y[p[c]]=v));if(f){if(u||n){if(u){for(a=[],c=h.length;c--;)(v=h[c])&&a.push(y[c]=v);u(null,h=[],a,s)}for(c=h.length;c--;)(v=h[c])&&(a=u?d.call(f,v):w[c])>-1&&(f[a]=!(e[a]=v))}}else h=yt(h===e?h.splice(b,h.length):h),u?u(null,e,h,s):l.apply(e,h)})}function ii(n){for(var o,u,r,s=n.length,h=t.relative[n[0].type],c=h||t.relative[" "],i=h?1:0,l=vt(function(n){return n===o},c,!0),a=vt(function(n){return d.call(o,n)>-1},c,!0),f=[function(n,t,i){var r=!h&&(i||t!=ot)||((o=t).nodeType?l(n,t,i):a(n,t,i));return o=null,r}];i<s;i++)if(u=t.relative[n[i].type])f=[vt(ni(f),u)];else{if(u=t.filter[n[i].type].apply(null,n[i].matches),u[e]){for(r=++i;r<s;r++)if(t.relative[n[r].type])break;return ti(i>1&&ni(f),i>1&&at(n.slice(0,i-1).concat({value:n[i-2].type===" "?"*":""})).replace(kt,"$1"),u,i<r&&ii(n.slice(i,r)),r<s&&ii(n=n.slice(r)),r<s&&at(n))}f.push(u)}return ni(f)}function hr(n,r){var f=r.length>0,e=n.length>0,o=function(o,s,h,c,v){var y,g,k,d=0,p="0",tt=o&&[],b=[],it=ot,rt=o||e&&t.find.TAG("*",v),ut=w+=it==null?1:Math.random()||.1,ft=rt.length;for(v&&(ot=s==u||s||v);p!==ft&&(y=rt[p])!=null;p++){if(e&&y){for(g=0,s||y.ownerDocument==u||(nt(y),h=!a);k=n[g++];)if(k(y,s||u,h)){l.call(c,y);break}v&&(w=ut)}f&&((y=!k&&y)&&d--,o&&tt.push(y))}if(d+=p,f&&p!==d){for(g=0;k=r[g++];)k(tt,b,s,h);if(o){if(d>0)while(p--)tt[p]||b[p]||(b[p]=he.call(c));b=yt(b)}l.apply(c,b);v&&!o&&b.length>0&&d+r.length>1&&i.uniqueSort(c)}return v&&(w=ut,ot=it),tt};return f?y(o):o}function ri(n,t){var r,u=[],f=[],i=oi[n+" "];if(!i){for(t||(t=et(n)),r=t.length;r--;)i=ii(t[r]),i[e]?u.push(i):f.push(i);i=oi(n,hr(f,u));i.selector=n}return i}function yi(n,i,r,u){var o,f,e,c,v,h=typeof n=="function"&&n,s=!u&&et(n=h.selector||n);if(r=r||[],s.length===1){if(f=s[0]=s[0].slice(0),f.length>2&&(e=f[0]).type==="ID"&&i.nodeType===9&&a&&t.relative[f[1].type]){if(i=(t.find.ID(e.matches[0].replace(k,g),i)||[])[0],i)h&&(i=i.parentNode);else return r;n=n.slice(f.shift().value.length)}for(o=ct.needsContext.test(n)?0:f.length;o--;){if(e=f[o],t.relative[c=e.type])break;if((v=t.find[c])&&(u=v(e.matches[0].replace(k,g),dt.test(f[0].type)&&gt(i.parentNode)||i))){if(f.splice(o,1),n=u.length&&at(f),!n)return l.apply(r,u),r;break}}}return(h||ri(n,s))(u,i,!a,r,!i||dt.test(n)&&gt(i.parentNode)||i),r}var rt,t,ot,ft,st,l=ei,u,p,a,c,pt,e=i.expando,w=0,pi=0,ui=lt(),fi=lt(),oi=lt(),ht=lt(),wt=function(n,t){return n===t&&(st=!0),0},si="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",tt="(?:\\\\[\\da-fA-F]{1,6}"+o+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",hi="\\["+o+"*("+tt+")(?:"+o+"*([*^$|!~]?=)"+o+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+tt+"))|)"+o+"*\\]",ci=":("+tt+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+hi+")*)|.*)\\)|)",wi=new RegExp(o+"+","g"),bi=new RegExp("^"+o+"*,"+o+"*"),li=new RegExp("^"+o+"*([>+~]|"+o+")"+o+"*"),ki=new RegExp(o+"|>"),di=new RegExp(ci),gi=new RegExp("^"+tt+"$"),ct={ID:new RegExp("^#("+tt+")"),CLASS:new RegExp("^\\.("+tt+")"),TAG:new RegExp("^("+tt+"|[*])"),ATTR:new RegExp("^"+hi),PSEUDO:new RegExp("^"+ci),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+o+"*(even|odd|(([+-]|)(\\d*)n|)"+o+"*(?:([+-]|)"+o+"*(\\d+)|))"+o+"*\\)|)","i"),bool:new RegExp("^(?:"+si+")$","i"),needsContext:new RegExp("^"+o+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+o+"*((?:-\\d)?\\d*)"+o+"*\\)|)(?=[^-]|$)","i")},nr=/^(?:input|select|textarea|button)$/i,tr=/^h\d$/i,ir=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,dt=/[+~]/,k=new RegExp("\\\\[\\da-fA-F]{1,6}"+o+"?|\\\\([^\\r\\n\\f])","g"),g=function(n,t){var i="0x"+n.slice(1)-65536;return t?t:i<0?String.fromCharCode(i+65536):String.fromCharCode(i>>10|55296,i&1023|56320)},rr=function(){nt()},ur=vt(function(n){return n.disabled===!0&&s(n,"fieldset")},{dir:"parentNode",next:"legend"});try{l.apply(h=v.call(b.childNodes),b.childNodes);h[b.childNodes.length].nodeType}catch(cr){l={apply:function(n,t){ei.apply(n,v.call(t))},call:function(n){ei.apply(n,v.call(arguments,1))}}}r.matches=function(n,t){return r(n,null,null,t)};r.matchesSelector=function(n,t){if(nt(n),a&&!ht[t+" "]&&(!c||!c.test(t)))try{var i=pt.call(n,t);if(i||f.disconnectedMatch||n.document&&n.document.nodeType!==11)return i}catch(e){ht(t,!0)}return r(t,u,null,[n]).length>0};r.contains=function(n,t){return(n.ownerDocument||n)!=u&&nt(n),i.contains(n,t)};r.attr=function(n,i){(n.ownerDocument||n)!=u&&nt(n);var r=t.attrHandle[i.toLowerCase()],f=r&&bt.call(t.attrHandle,i.toLowerCase())?r(n,i,!a):undefined;return f!==undefined?f:n.getAttribute(i)};r.error=function(n){throw new Error("Syntax error, unrecognized expression: "+n);};i.uniqueSort=function(n){var r,u=[],t=0,i=0;if(st=!f.sortStable,ft=!f.sortStable&&v.call(n,0),ce.call(n,wt),st){while(r=n[i++])r===n[i]&&(t=u.push(i));while(t--)le.call(n,u[t],1)}return ft=null,n};i.fn.uniqueSort=function(){return this.pushStack(i.uniqueSort(v.apply(this)))};t=i.expr={cacheLength:50,createPseudo:y,match:ct,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(n){return n[1]=n[1].replace(k,g),n[3]=(n[3]||n[4]||n[5]||"").replace(k,g),n[2]==="~="&&(n[3]=" "+n[3]+" "),n.slice(0,4)},CHILD:function(n){return n[1]=n[1].toLowerCase(),n[1].slice(0,3)==="nth"?(n[3]||r.error(n[0]),n[4]=+(n[4]?n[5]+(n[6]||1):2*(n[3]==="even"||n[3]==="odd")),n[5]=+(n[7]+n[8]||n[3]==="odd")):n[3]&&r.error(n[0]),n},PSEUDO:function(n){var i,t=!n[6]&&n[2];return ct.CHILD.test(n[0])?null:(n[3]?n[2]=n[4]||n[5]||"":t&&di.test(t)&&(i=et(t,!0))&&(i=t.indexOf(")",t.length-i)-t.length)&&(n[0]=n[0].slice(0,i),n[2]=t.slice(0,i)),n.slice(0,3))}},filter:{TAG:function(n){var t=n.replace(k,g).toLowerCase();return n==="*"?function(){return!0}:function(n){return s(n,t)}},CLASS:function(n){var t=ui[n+" "];return t||(t=new RegExp("(^|"+o+")"+n+"("+o+"|$)"))&&ui(n,function(n){return t.test(typeof n.className=="string"&&n.className||typeof n.getAttribute!="undefined"&&n.getAttribute("class")||"")})},ATTR:function(n,t,i){return function(u){var f=r.attr(u,n);return f==null?t==="!=":t?(f+="",t==="=")?f===i:t==="!="?f!==i:t==="^="?i&&f.indexOf(i)===0:t==="*="?i&&f.indexOf(i)>-1:t==="$="?i&&f.slice(-i.length)===i:t==="~="?(" "+f.replace(wi," ")+" ").indexOf(i)>-1:t==="|="?f===i||f.slice(0,i.length+1)===i+"-":!1:!0}},CHILD:function(n,t,i,r,u){var h=n.slice(0,3)!=="nth",o=n.slice(-4)!=="last",f=t==="of-type";return r===1&&u===0?function(n){return!!n.parentNode}:function(t,i,c){var y,p,l,v,k,d=h!==o?"nextSibling":"previousSibling",b=t.parentNode,nt=f&&t.nodeName.toLowerCase(),g=!c&&!f,a=!1;if(b){if(h){while(d){for(l=t;l=l[d];)if(f?s(l,nt):l.nodeType===1)return!1;k=d=n==="only"&&!k&&"nextSibling"}return!0}if(k=[o?b.firstChild:b.lastChild],o&&g){for(p=b[e]||(b[e]={}),y=p[n]||[],v=y[0]===w&&y[1],a=v&&y[2],l=v&&b.childNodes[v];l=++v&&l&&l[d]||(a=v=0)||k.pop();)if(l.nodeType===1&&++a&&l===t){p[n]=[w,v,a];break}}else if(g&&(p=t[e]||(t[e]={}),y=p[n]||[],v=y[0]===w&&y[1],a=v),a===!1)while(l=++v&&l&&l[d]||(a=v=0)||k.pop())if((f?s(l,nt):l.nodeType===1)&&++a&&(g&&(p=l[e]||(l[e]={}),p[n]=[w,a]),l===t))break;return a-=u,a===r||a%r==0&&a/r>=0}}},PSEUDO:function(n,i){var f,u=t.pseudos[n]||t.setFilters[n.toLowerCase()]||r.error("unsupported pseudo: "+n);return u[e]?u(i):u.length>1?(f=[n,n,"",i],t.setFilters.hasOwnProperty(n.toLowerCase())?y(function(n,t){for(var r,f=u(n,i),e=f.length;e--;)r=d.call(n,f[e]),n[r]=!(t[r]=f[e])}):function(n){return u(n,0,f)}):u}},pseudos:{not:y(function(n){var t=[],r=[],i=ri(n.replace(kt,"$1"));return i[e]?y(function(n,t,r,u){for(var e,o=i(n,null,u,[]),f=n.length;f--;)(e=o[f])&&(n[f]=!(t[f]=e))}):function(n,u,f){return t[0]=n,i(t,null,f,r),t[0]=null,!r.pop()}}),has:y(function(n){return function(t){return r(n,t).length>0}}),contains:y(function(n){return n=n.replace(k,g),function(t){return(t.textContent||i.text(t)).indexOf(n)>-1}}),lang:y(function(n){return gi.test(n||"")||r.error("unsupported lang: "+n),n=n.replace(k,g).toLowerCase(),function(t){var i;do if(i=a?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return i=i.toLowerCase(),i===n||i.indexOf(n+"-")===0;while((t=t.parentNode)&&t.nodeType===1);return!1}}),target:function(t){var i=n.location&&n.location.hash;return i&&i.slice(1)===t.id},root:function(n){return n===p},focus:function(n){return n===fr()&&u.hasFocus()&&!!(n.type||n.href||~n.tabIndex)},enabled:ai(!1),disabled:ai(!0),checked:function(n){return s(n,"input")&&!!n.checked||s(n,"option")&&!!n.selected},selected:function(n){return n.parentNode&&n.parentNode.selectedIndex,n.selected===!0},empty:function(n){for(n=n.firstChild;n;n=n.nextSibling)if(n.nodeType<6)return!1;return!0},parent:function(n){return!t.pseudos.empty(n)},header:function(n){return tr.test(n.nodeName)},input:function(n){return nr.test(n.nodeName)},button:function(n){return s(n,"input")&&n.type==="button"||s(n,"button")},text:function(n){var t;return s(n,"input")&&n.type==="text"&&((t=n.getAttribute("type"))==null||t.toLowerCase()==="text")},first:it(function(){return[0]}),last:it(function(n,t){return[t-1]}),eq:it(function(n,t,i){return[i<0?i+t:i]}),even:it(function(n,t){for(var i=0;i<t;i+=2)n.push(i);return n}),odd:it(function(n,t){for(var i=1;i<t;i+=2)n.push(i);return n}),lt:it(function(n,t,i){for(var r=i<0?i+t:i>t?t:i;--r>=0;)n.push(r);return n}),gt:it(function(n,t,i){for(var r=i<0?i+t:i;++r<t;)n.push(r);return n})}};t.pseudos.nth=t.pseudos.eq;for(rt in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})t.pseudos[rt]=er(rt);for(rt in{submit:!0,reset:!0})t.pseudos[rt]=or(rt);vi.prototype=t.filters=t.pseudos;t.setFilters=new vi;f.sortStable=e.split("").sort(wt).join("")===e;nt();f.sortDetached=ut(function(n){return n.compareDocumentPosition(u.createElement("fieldset"))&1});i.find=r;i.expr[":"]=i.expr.pseudos;i.unique=i.uniqueSort;r.compile=ri;r.select=yi;r.setDocument=nt;r.tokenize=et;r.escape=i.escapeSelector;r.getText=i.text;r.isXML=i.isXMLDoc;r.selectors=i.expr;r.support=i.support;r.uniqueSort=i.uniqueSort}();var et=function(n,t,r){for(var u=[],f=r!==undefined;(n=n[t])&&n.nodeType!==9;)if(n.nodeType===1){if(f&&i(n).is(r))break;u.push(n)}return u},nu=function(n,t){for(var i=[];n;n=n.nextSibling)n.nodeType===1&&n!==t&&i.push(n);return i},tu=i.expr.match.needsContext,iu=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;i.filter=function(n,t,r){var u=t[0];return(r&&(n=":not("+n+")"),t.length===1&&u.nodeType===1)?i.find.matchesSelector(u,n)?[u]:[]:i.find.matches(n,i.grep(t,function(n){return n.nodeType===1}))};i.fn.extend({find:function(n){var t,r,u=this.length,f=this;if(typeof n!="string")return this.pushStack(i(n).filter(function(){for(t=0;t<u;t++)if(i.contains(f[t],this))return!0}));for(r=this.pushStack([]),t=0;t<u;t++)i.find(n,f[t],r);return u>1?i.uniqueSort(r):r},filter:function(n){return this.pushStack(wi(this,n||[],!1))},not:function(n){return this.pushStack(wi(this,n||[],!0))},is:function(n){return!!wi(this,typeof n=="string"&&tu.test(n)?i(n):n||[],!1).length}});uu=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;fu=i.fn.init=function(n,t,r){var f,o;if(!n)return this;if(r=r||ru,typeof n=="string"){if(f=n[0]==="<"&&n[n.length-1]===">"&&n.length>=3?[null,n,null]:uu.exec(n),f&&(f[1]||!t)){if(f[1]){if(t=t instanceof i?t[0]:t,i.merge(this,i.parseHTML(f[1],t&&t.nodeType?t.ownerDocument||t:u,!0)),iu.test(f[1])&&i.isPlainObject(t))for(f in t)e(this[f])?this[f](t[f]):this.attr(f,t[f]);return this}return o=u.getElementById(f[2]),o&&(this[0]=o,this.length=1),this}return!t||t.jquery?(t||r).find(n):this.constructor(t).find(n)}return n.nodeType?(this[0]=n,this.length=1,this):e(n)?r.ready!==undefined?r.ready(n):n(i):i.makeArray(n,this)};fu.prototype=i.fn;ru=i(u);eu=/^(?:parents|prev(?:Until|All))/;ou={children:!0,contents:!0,next:!0,prev:!0};i.fn.extend({has:function(n){var t=i(n,this),r=t.length;return this.filter(function(){for(var n=0;n<r;n++)if(i.contains(this,t[n]))return!0})},closest:function(n,t){var r,f=0,o=this.length,u=[],e=typeof n!="string"&&i(n);if(!tu.test(n))for(;f<o;f++)for(r=this[f];r&&r!==t;r=r.parentNode)if(r.nodeType<11&&(e?e.index(r)>-1:r.nodeType===1&&i.find.matchesSelector(r,n))){u.push(r);break}return this.pushStack(u.length>1?i.uniqueSort(u):u)},index:function(n){return n?typeof n=="string"?d.call(i(n),this[0]):d.call(this,n.jquery?n[0]:n):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(n,t){return this.pushStack(i.uniqueSort(i.merge(this.get(),i(n,t))))},addBack:function(n){return this.add(n==null?this.prevObject:this.prevObject.filter(n))}});i.each({parent:function(n){var t=n.parentNode;return t&&t.nodeType!==11?t:null},parents:function(n){return et(n,"parentNode")},parentsUntil:function(n,t,i){return et(n,"parentNode",i)},next:function(n){return su(n,"nextSibling")},prev:function(n){return su(n,"previousSibling")},nextAll:function(n){return et(n,"nextSibling")},prevAll:function(n){return et(n,"previousSibling")},nextUntil:function(n,t,i){return et(n,"nextSibling",i)},prevUntil:function(n,t,i){return et(n,"previousSibling",i)},siblings:function(n){return nu((n.parentNode||{}).firstChild,n)},children:function(n){return nu(n.firstChild)},contents:function(n){return n.contentDocument!=null&&yr(n.contentDocument)?n.contentDocument:(s(n,"template")&&(n=n.content||n),i.merge([],n.childNodes))}},function(n,t){i.fn[n]=function(r,u){var f=i.map(this,t,r);return n.slice(-5)!=="Until"&&(u=r),u&&typeof u=="string"&&(f=i.filter(u,f)),this.length>1&&(ou[n]||i.uniqueSort(f),eu.test(n)&&f.reverse()),this.pushStack(f)}});p=/[^\x20\t\r\n\f]+/g;i.Callbacks=function(n){n=typeof n=="string"?ve(n):i.extend({},n);var o,r,c,u,t=[],s=[],f=-1,l=function(){for(u=u||n.once,c=o=!0;s.length;f=-1)for(r=s.shift();++f<t.length;)t[f].apply(r[0],r[1])===!1&&n.stopOnFalse&&(f=t.length,r=!1);n.memory||(r=!1);o=!1;u&&(t=r?[]:"")},h={add:function(){return t&&(r&&!o&&(f=t.length-1,s.push(r)),function u(r){i.each(r,function(i,r){e(r)?n.unique&&h.has(r)||t.push(r):r&&r.length&&ft(r)!=="string"&&u(r)})}(arguments),r&&!o&&l()),this},remove:function(){return i.each(arguments,function(n,r){for(var u;(u=i.inArray(r,t,u))>-1;)t.splice(u,1),u<=f&&f--}),this},has:function(n){return n?i.inArray(n,t)>-1:t.length>0},empty:function(){return t&&(t=[]),this},disable:function(){return u=s=[],t=r="",this},disabled:function(){return!t},lock:function(){return u=s=[],r||o||(t=r=""),this},locked:function(){return!!u},fireWith:function(n,t){return u||(t=t||[],t=[n,t.slice?t.slice():t],s.push(t),o||l()),this},fire:function(){return h.fireWith(this,arguments),this},fired:function(){return!!c}};return h};i.extend({Deferred:function(t){var u=[["notify","progress",i.Callbacks("memory"),i.Callbacks("memory"),2],["resolve","done",i.Callbacks("once memory"),i.Callbacks("once memory"),0,"resolved"],["reject","fail",i.Callbacks("once memory"),i.Callbacks("once memory"),1,"rejected"]],o="pending",f={state:function(){return o},always:function(){return r.done(arguments).fail(arguments),this},"catch":function(n){return f.then(null,n)},pipe:function(){var n=arguments;return i.Deferred(function(t){i.each(u,function(i,u){var f=e(n[u[4]])&&n[u[4]];r[u[1]](function(){var n=f&&f.apply(this,arguments);n&&e(n.promise)?n.promise().progress(t.notify).done(t.resolve).fail(t.reject):t[u[0]+"With"](this,f?[n]:arguments)})});n=null}).promise()},then:function(t,r,f){function s(t,r,u,f){return function(){var h=this,c=arguments,a=function(){var n,i;if(!(t<o)){if(n=u.apply(h,c),n===r.promise())throw new TypeError("Thenable self-resolution");i=n&&(typeof n=="object"||typeof n=="function")&&n.then;e(i)?f?i.call(n,s(o,r,ot,f),s(o,r,oi,f)):(o++,i.call(n,s(o,r,ot,f),s(o,r,oi,f),s(o,r,ot,r.notifyWith))):(u!==ot&&(h=undefined,c=[n]),(f||r.resolveWith)(h,c))}},l=f?a:function(){try{a()}catch(n){i.Deferred.exceptionHook&&i.Deferred.exceptionHook(n,l.error);t+1>=o&&(u!==oi&&(h=undefined,c=[n]),r.rejectWith(h,c))}};t?l():(i.Deferred.getErrorHook?l.error=i.Deferred.getErrorHook():i.Deferred.getStackHook&&(l.error=i.Deferred.getStackHook()),n.setTimeout(l))}}var o=0;return i.Deferred(function(n){u[0][3].add(s(0,n,e(f)?f:ot,n.notifyWith));u[1][3].add(s(0,n,e(t)?t:ot));u[2][3].add(s(0,n,e(r)?r:oi))}).promise()},promise:function(n){return n!=null?i.extend(n,f):f}},r={};return i.each(u,function(n,t){var i=t[2],e=t[5];f[t[1]]=i.add;e&&i.add(function(){o=e},u[3-n][2].disable,u[3-n][3].disable,u[0][2].lock,u[0][3].lock);i.add(t[3].fire);r[t[0]]=function(){return r[t[0]+"With"](this===r?undefined:this,arguments),this};r[t[0]+"With"]=i.fireWith}),f.promise(r),t&&t.call(r,r),r},when:function(n){var f=arguments.length,t=f,o=Array(t),u=v.call(arguments),r=i.Deferred(),s=function(n){return function(t){o[n]=this;u[n]=arguments.length>1?v.call(arguments):t;--f||r.resolveWith(o,u)}};if(f<=1&&(hu(n,r.done(s(t)).resolve,r.reject,!f),r.state()==="pending"||e(u[t]&&u[t].then)))return r.then();while(t--)hu(u[t],s(t),r.reject);return r.promise()}});cu=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;i.Deferred.exceptionHook=function(t,i){n.console&&n.console.warn&&t&&cu.test(t.name)&&n.console.warn("jQuery.Deferred exception: "+t.message,t.stack,i)};i.readyException=function(t){n.setTimeout(function(){throw t;})};si=i.Deferred();i.fn.ready=function(n){return si.then(n).catch(function(n){i.readyException(n)}),this};i.extend({isReady:!1,readyWait:1,ready:function(n){(n===!0?--i.readyWait:i.isReady)||(i.isReady=!0,n!==!0&&--i.readyWait>0)||si.resolveWith(u,[i])}});i.ready.then=si.then;u.readyState!=="complete"&&(u.readyState==="loading"||u.documentElement.doScroll)?(u.addEventListener("DOMContentLoaded",hi),n.addEventListener("load",hi)):n.setTimeout(i.ready);var g=function(n,t,r,u,f,o,s){var h=0,l=n.length,c=r==null;if(ft(r)==="object"){f=!0;for(h in r)g(n,t,h,r[h],!0,o,s)}else if(u!==undefined&&(f=!0,e(u)||(s=!0),c&&(s?(t.call(n,u),t=null):(c=t,t=function(n,t,r){return c.call(i(n),r)})),t))for(;h<l;h++)t(n[h],r,s?u:u.call(n[h],h,t(n[h],r)));return f?n:c?t.call(n):l?t(n[0],r):o},ye=/^-ms-/,pe=/-([a-z])/g;st=function(n){return n.nodeType===1||n.nodeType===9||!+n.nodeType};dt.uid=1;dt.prototype={cache:function(n){var t=n[this.expando];return t||(t={},st(n)&&(n.nodeType?n[this.expando]=t:Object.defineProperty(n,this.expando,{value:t,configurable:!0}))),t},set:function(n,t,i){var r,u=this.cache(n);if(typeof t=="string")u[k(t)]=i;else for(r in t)u[k(r)]=t[r];return u},get:function(n,t){return t===undefined?this.cache(n):n[this.expando]&&n[this.expando][k(t)]},access:function(n,t,i){return t===undefined||t&&typeof t=="string"&&i===undefined?this.get(n,t):(this.set(n,t,i),i!==undefined?i:t)},remove:function(n,t){var u,r=n[this.expando];if(r!==undefined){if(t!==undefined)for(Array.isArray(t)?t=t.map(k):(t=k(t),t=t in r?[t]:t.match(p)||[]),u=t.length;u--;)delete r[t[u]];(t===undefined||i.isEmptyObject(r))&&(n.nodeType?n[this.expando]=undefined:delete n[this.expando])}},hasData:function(n){var t=n[this.expando];return t!==undefined&&!i.isEmptyObject(t)}};var r=new dt,c=new dt,be=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,ke=/[A-Z]/g;i.extend({hasData:function(n){return c.hasData(n)||r.hasData(n)},data:function(n,t,i){return c.access(n,t,i)},removeData:function(n,t){c.remove(n,t)},_data:function(n,t,i){return r.access(n,t,i)},_removeData:function(n,t){r.remove(n,t)}});i.fn.extend({data:function(n,t){var f,u,e,i=this[0],o=i&&i.attributes;if(n===undefined){if(this.length&&(e=c.get(i),i.nodeType===1&&!r.get(i,"hasDataAttrs"))){for(f=o.length;f--;)o[f]&&(u=o[f].name,u.indexOf("data-")===0&&(u=k(u.slice(5)),lu(i,u,e[u])));r.set(i,"hasDataAttrs",!0)}return e}return typeof n=="object"?this.each(function(){c.set(this,n)}):g(this,function(t){var r;if(i&&t===undefined)return(r=c.get(i,n),r!==undefined)?r:(r=lu(i,n),r!==undefined)?r:void 0;this.each(function(){c.set(this,n,t)})},null,t,arguments.length>1,null,!0)},removeData:function(n){return this.each(function(){c.remove(this,n)})}});i.extend({queue:function(n,t,u){var f;if(n)return t=(t||"fx")+"queue",f=r.get(n,t),u&&(!f||Array.isArray(u)?f=r.access(n,t,i.makeArray(u)):f.push(u)),f||[]},dequeue:function(n,t){t=t||"fx";var r=i.queue(n,t),e=r.length,u=r.shift(),f=i._queueHooks(n,t),o=function(){i.dequeue(n,t)};u==="inprogress"&&(u=r.shift(),e--);u&&(t==="fx"&&r.unshift("inprogress"),delete f.stop,u.call(n,o,f));!e&&f&&f.empty.fire()},_queueHooks:function(n,t){var u=t+"queueHooks";return r.get(n,u)||r.access(n,u,{empty:i.Callbacks("once memory").add(function(){r.remove(n,[t+"queue",u])})})}});i.fn.extend({queue:function(n,t){var r=2;return(typeof n!="string"&&(t=n,n="fx",r--),arguments.length<r)?i.queue(this[0],n):t===undefined?this:this.each(function(){var r=i.queue(this,n,t);i._queueHooks(this,n);n==="fx"&&r[0]!=="inprogress"&&i.dequeue(this,n)})},dequeue:function(n){return this.each(function(){i.dequeue(this,n)})},clearQueue:function(n){return this.queue(n||"fx",[])},promise:function(n,t){var u,e=1,o=i.Deferred(),f=this,s=this.length,h=function(){--e||o.resolveWith(f,[f])};for(typeof n!="string"&&(t=n,n=undefined),n=n||"fx";s--;)u=r.get(f[s],n+"queueHooks"),u&&u.empty&&(e++,u.empty.add(h));return h(),o.promise(t)}});var au=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,gt=new RegExp("^(?:([+-])=|)("+au+")([a-z%]*)$","i"),nt=["Top","Right","Bottom","Left"],tt=u.documentElement,ht=function(n){return i.contains(n.ownerDocument,n)},ge={composed:!0};tt.getRootNode&&(ht=function(n){return i.contains(n.ownerDocument,n)||n.getRootNode(ge)===n.ownerDocument});ni=function(n,t){return n=t||n,n.style.display==="none"||n.style.display===""&&ht(n)&&i.css(n,"display")==="none"};bi={};i.fn.extend({show:function(){return ct(this,!0)},hide:function(){return ct(this)},toggle:function(n){return typeof n=="boolean"?n?this.show():this.hide():this.each(function(){ni(this)?i(this).show():i(this).hide()})}});var ti=/^(?:checkbox|radio)$/i,yu=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,pu=/^$|^module$|\/(?:java|ecma)script/i;(function(){var i=u.createDocumentFragment(),n=i.appendChild(u.createElement("div")),t=u.createElement("input");t.setAttribute("type","radio");t.setAttribute("checked","checked");t.setAttribute("name","t");n.appendChild(t);f.checkClone=n.cloneNode(!0).cloneNode(!0).lastChild.checked;n.innerHTML="<textarea>x<\/textarea>";f.noCloneChecked=!!n.cloneNode(!0).lastChild.defaultValue;n.innerHTML="<option><\/option>";f.option=!!n.lastChild})();y={thead:[1,"<table>","<\/table>"],col:[2,"<table><colgroup>","<\/colgroup><\/table>"],tr:[2,"<table><tbody>","<\/tbody><\/table>"],td:[3,"<table><tbody><tr>","<\/tr><\/tbody><\/table>"],_default:[0,"",""]};y.tbody=y.tfoot=y.colgroup=y.caption=y.thead;y.th=y.td;f.option||(y.optgroup=y.option=[1,"<select multiple='multiple'>","<\/select>"]);wu=/<|&#?\w+;/;di=/^([^.]*)(?:\.(.+)|)/;i.event={global:{},add:function(n,t,u,f,e){var l,a,w,v,b,h,s,c,o,k,d,y=r.get(n);if(st(n))for(u.handler&&(l=u,u=l.handler,e=l.selector),e&&i.find.matchesSelector(tt,e),u.guid||(u.guid=i.guid++),(v=y.events)||(v=y.events=Object.create(null)),(a=y.handle)||(a=y.handle=function(t){return typeof i!="undefined"&&i.event.triggered!==t.type?i.event.dispatch.apply(n,arguments):undefined}),t=(t||"").match(p)||[""],b=t.length;b--;)(w=di.exec(t[b])||[],o=d=w[1],k=(w[2]||"").split(".").sort(),o)&&(s=i.event.special[o]||{},o=(e?s.delegateType:s.bindType)||o,s=i.event.special[o]||{},h=i.extend({type:o,origType:d,data:f,handler:u,guid:u.guid,selector:e,needsContext:e&&i.expr.match.needsContext.test(e),namespace:k.join(".")},l),(c=v[o])||(c=v[o]=[],c.delegateCount=0,s.setup&&s.setup.call(n,f,k,a)!==!1||n.addEventListener&&n.addEventListener(o,a)),s.add&&(s.add.call(n,h),h.handler.guid||(h.handler.guid=u.guid)),e?c.splice(c.delegateCount++,0,h):c.push(h),i.event.global[o]=!0)},remove:function(n,t,u,f,e){var v,k,h,a,y,s,c,l,o,b,d,w=r.hasData(n)&&r.get(n);if(w&&(a=w.events)){for(t=(t||"").match(p)||[""],y=t.length;y--;){if(h=di.exec(t[y])||[],o=d=h[1],b=(h[2]||"").split(".").sort(),!o){for(o in a)i.event.remove(n,o+t[y],u,f,!0);continue}for(c=i.event.special[o]||{},o=(f?c.delegateType:c.bindType)||o,l=a[o]||[],h=h[2]&&new RegExp("(^|\\.)"+b.join("\\.(?:.*\\.|)")+"(\\.|$)"),k=v=l.length;v--;)s=l[v],(e||d===s.origType)&&(!u||u.guid===s.guid)&&(!h||h.test(s.namespace))&&(!f||f===s.selector||f==="**"&&s.selector)&&(l.splice(v,1),s.selector&&l.delegateCount--,c.remove&&c.remove.call(n,s));k&&!l.length&&(c.teardown&&c.teardown.call(n,b,w.handle)!==!1||i.removeEvent(n,o,w.handle),delete a[o])}i.isEmptyObject(a)&&r.remove(n,"handle events")}},dispatch:function(n){var u,c,s,e,f,l,h=new Array(arguments.length),t=i.event.fix(n),a=(r.get(this,"events")||Object.create(null))[t.type]||[],o=i.event.special[t.type]||{};for(h[0]=t,u=1;u<arguments.length;u++)h[u]=arguments[u];if(t.delegateTarget=this,!o.preDispatch||o.preDispatch.call(this,t)!==!1){for(l=i.event.handlers.call(this,t,a),u=0;(e=l[u++])&&!t.isPropagationStopped();)for(t.currentTarget=e.elem,c=0;(f=e.handlers[c++])&&!t.isImmediatePropagationStopped();)(!t.rnamespace||f.namespace===!1||t.rnamespace.test(f.namespace))&&(t.handleObj=f,t.data=f.data,s=((i.event.special[f.origType]||{}).handle||f.handler).apply(e.elem,h),s!==undefined&&(t.result=s)===!1&&(t.preventDefault(),t.stopPropagation()));return o.postDispatch&&o.postDispatch.call(this,t),t.result}},handlers:function(n,t){var f,e,u,o,s,c=[],h=t.delegateCount,r=n.target;if(h&&r.nodeType&&!(n.type==="click"&&n.button>=1))for(;r!==this;r=r.parentNode||this)if(r.nodeType===1&&!(n.type==="click"&&r.disabled===!0)){for(o=[],s={},f=0;f<h;f++)e=t[f],u=e.selector+" ",s[u]===undefined&&(s[u]=e.needsContext?i(u,this).index(r)>-1:i.find(u,this,null,[r]).length),s[u]&&o.push(e);o.length&&c.push({elem:r,handlers:o})}return r=this,h<t.length&&c.push({elem:r,handlers:t.slice(h)}),c},addProp:function(n,t){Object.defineProperty(i.Event.prototype,n,{enumerable:!0,configurable:!0,get:e(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[n]},set:function(t){Object.defineProperty(this,n,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(n){return n[i.expando]?n:new i.Event(n)},special:{load:{noBubble:!0},click:{setup:function(n){var t=this||n;return ti.test(t.type)&&t.click&&s(t,"input")&&ci(t,"click",!0),!1},trigger:function(n){var t=this||n;return ti.test(t.type)&&t.click&&s(t,"input")&&ci(t,"click"),!0},_default:function(n){var t=n.target;return ti.test(t.type)&&t.click&&s(t,"input")&&r.get(t,"click")||s(t,"a")}},beforeunload:{postDispatch:function(n){n.result!==undefined&&n.originalEvent&&(n.originalEvent.returnValue=n.result)}}}};i.removeEvent=function(n,t,i){n.removeEventListener&&n.removeEventListener(t,i)};i.Event=function(n,t){if(!(this instanceof i.Event))return new i.Event(n,t);n&&n.type?(this.originalEvent=n,this.type=n.type,this.isDefaultPrevented=n.defaultPrevented||n.defaultPrevented===undefined&&n.returnValue===!1?lt:at,this.target=n.target&&n.target.nodeType===3?n.target.parentNode:n.target,this.currentTarget=n.currentTarget,this.relatedTarget=n.relatedTarget):this.type=n;t&&i.extend(this,t);this.timeStamp=n&&n.timeStamp||Date.now();this[i.expando]=!0};i.Event.prototype={constructor:i.Event,isDefaultPrevented:at,isPropagationStopped:at,isImmediatePropagationStopped:at,isSimulated:!1,preventDefault:function(){var n=this.originalEvent;this.isDefaultPrevented=lt;n&&!this.isSimulated&&n.preventDefault()},stopPropagation:function(){var n=this.originalEvent;this.isPropagationStopped=lt;n&&!this.isSimulated&&n.stopPropagation()},stopImmediatePropagation:function(){var n=this.originalEvent;this.isImmediatePropagationStopped=lt;n&&!this.isSimulated&&n.stopImmediatePropagation();this.stopPropagation()}};i.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},i.event.addProp);i.each({focus:"focusin",blur:"focusout"},function(n,t){function f(n){if(u.documentMode){var e=r.get(this,"handle"),f=i.event.fix(n);f.type=n.type==="focusin"?"focus":"blur";f.isSimulated=!0;e(n);f.target===f.currentTarget&&e(f)}else i.event.simulate(t,n.target,i.event.fix(n))}i.event.special[n]={setup:function(){var i;if(ci(this,n,!0),u.documentMode)i=r.get(this,t),i||this.addEventListener(t,f),r.set(this,t,(i||0)+1);else return!1},trigger:function(){return ci(this,n),!0},teardown:function(){var n;if(u.documentMode)n=r.get(this,t)-1,n?r.set(this,t,n):(this.removeEventListener(t,f),r.remove(this,t));else return!1},_default:function(t){return r.get(t.target,n)},delegateType:t};i.event.special[t]={setup:function(){var i=this.ownerDocument||this.document||this,e=u.documentMode?this:i,o=r.get(e,t);o||(u.documentMode?this.addEventListener(t,f):i.addEventListener(n,f,!0));r.set(e,t,(o||0)+1)},teardown:function(){var e=this.ownerDocument||this.document||this,i=u.documentMode?this:e,o=r.get(i,t)-1;o?r.set(i,t,o):(u.documentMode?this.removeEventListener(t,f):e.removeEventListener(n,f,!0),r.remove(i,t))}}});i.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(n,t){i.event.special[n]={delegateType:t,bindType:t,handle:function(n){var u,f=this,r=n.relatedTarget,e=n.handleObj;return r&&(r===f||i.contains(f,r))||(n.type=e.origType,u=e.handler.apply(this,arguments),n.type=t),u}}});i.fn.extend({on:function(n,t,i,r){return gi(this,n,t,i,r)},one:function(n,t,i,r){return gi(this,n,t,i,r,1)},off:function(n,t,r){var u,f;if(n&&n.preventDefault&&n.handleObj)return u=n.handleObj,i(n.delegateTarget).off(u.namespace?u.origType+"."+u.namespace:u.origType,u.selector,u.handler),this;if(typeof n=="object"){for(f in n)this.off(f,t,n[f]);return this}return(t===!1||typeof t=="function")&&(r=t,t=undefined),r===!1&&(r=at),this.each(function(){i.event.remove(this,n,r,t)})}});var to=/<script|<style|<link/i,io=/checked\s*(?:[^=]|=\s*.checked.)/i,ro=/^\s*<!\[CDATA\[|\]\]>\s*$/g;i.extend({htmlPrefilter:function(n){return n},clone:function(n,t,r){var u,h,o,e,s=n.cloneNode(!0),c=ht(n);if(!f.noCloneChecked&&(n.nodeType===1||n.nodeType===11)&&!i.isXMLDoc(n))for(e=l(s),o=l(n),u=0,h=o.length;u<h;u++)eo(o[u],e[u]);if(t)if(r)for(o=o||l(n),e=e||l(s),u=0,h=o.length;u<h;u++)du(o[u],e[u]);else du(n,s);return e=l(s,"script"),e.length>0&&ki(e,!c&&l(n,"script")),s},cleanData:function(n){for(var u,t,f,o=i.event.special,e=0;(t=n[e])!==undefined;e++)if(st(t)){if(u=t[r.expando]){if(u.events)for(f in u.events)o[f]?i.event.remove(t,f):i.removeEvent(t,f,u.handle);t[r.expando]=undefined}t[c.expando]&&(t[c.expando]=undefined)}}});i.fn.extend({detach:function(n){return gu(this,n,!0)},remove:function(n){return gu(this,n)},text:function(n){return g(this,function(n){return n===undefined?i.text(this):this.empty().each(function(){(this.nodeType===1||this.nodeType===11||this.nodeType===9)&&(this.textContent=n)})},null,n,arguments.length)},append:function(){return vt(this,arguments,function(n){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){var t=ku(this,n);t.appendChild(n)}})},prepend:function(){return vt(this,arguments,function(n){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){var t=ku(this,n);t.insertBefore(n,t.firstChild)}})},before:function(){return vt(this,arguments,function(n){this.parentNode&&this.parentNode.insertBefore(n,this)})},after:function(){return vt(this,arguments,function(n){this.parentNode&&this.parentNode.insertBefore(n,this.nextSibling)})},empty:function(){for(var n,t=0;(n=this[t])!=null;t++)n.nodeType===1&&(i.cleanData(l(n,!1)),n.textContent="");return this},clone:function(n,t){return n=n==null?!1:n,t=t==null?n:t,this.map(function(){return i.clone(this,n,t)})},html:function(n){return g(this,function(n){var t=this[0]||{},r=0,u=this.length;if(n===undefined&&t.nodeType===1)return t.innerHTML;if(typeof n=="string"&&!to.test(n)&&!y[(yu.exec(n)||["",""])[1].toLowerCase()]){n=i.htmlPrefilter(n);try{for(;r<u;r++)t=this[r]||{},t.nodeType===1&&(i.cleanData(l(t,!1)),t.innerHTML=n);t=0}catch(f){}}t&&this.empty().append(n)},null,n,arguments.length)},replaceWith:function(){var n=[];return vt(this,arguments,function(t){var r=this.parentNode;i.inArray(this,n)<0&&(i.cleanData(l(this)),r&&r.replaceChild(t,this))},n)}});i.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(n,t){i.fn[n]=function(n){for(var u,f=[],e=i(n),o=e.length-1,r=0;r<=o;r++)u=r===o?this:this.clone(!0),i(e[r])[t](u),ui.apply(f,u.get());return this.pushStack(f)}});var nr=new RegExp("^("+au+")(?!px)[a-z%]+$","i"),tr=/^--/,li=function(t){var i=t.ownerDocument.defaultView;return i&&i.opener||(i=n),i.getComputedStyle(t)},nf=function(n,t,i){var u,r,f={};for(r in t)f[r]=n.style[r],n.style[r]=t[r];u=i.call(n);for(r in t)n.style[r]=f[r];return u},oo=new RegExp(nt.join("|"),"i");(function(){function r(){if(t){s.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0";t.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%";tt.appendChild(s).appendChild(t);var i=n.getComputedStyle(t);h=i.top!=="1%";v=e(i.marginLeft)===12;t.style.right="60%";a=e(i.right)===36;c=e(i.width)===36;t.style.position="absolute";l=e(t.offsetWidth/3)===12;tt.removeChild(s);t=null}}function e(n){return Math.round(parseFloat(n))}var h,c,l,a,o,v,s=u.createElement("div"),t=u.createElement("div");t.style&&(t.style.backgroundClip="content-box",t.cloneNode(!0).style.backgroundClip="",f.clearCloneStyle=t.style.backgroundClip==="content-box",i.extend(f,{boxSizingReliable:function(){return r(),c},pixelBoxStyles:function(){return r(),a},pixelPosition:function(){return r(),h},reliableMarginLeft:function(){return r(),v},scrollboxSize:function(){return r(),l},reliableTrDimensions:function(){var i,t,r,f;return o==null&&(i=u.createElement("table"),t=u.createElement("tr"),r=u.createElement("div"),i.style.cssText="position:absolute;left:-11111px;border-collapse:separate",t.style.cssText="box-sizing:content-box;border:1px solid",t.style.height="1px",r.style.height="9px",r.style.display="block",tt.appendChild(i).appendChild(t).appendChild(r),f=n.getComputedStyle(t),o=parseInt(f.height,10)+parseInt(f.borderTopWidth,10)+parseInt(f.borderBottomWidth,10)===t.offsetHeight,tt.removeChild(i)),o}}))})();var rf=["Webkit","Moz","ms"],uf=u.createElement("div").style,ff={};var ho=/^(none|table(?!-c[ea]).+)/,co={position:"absolute",visibility:"hidden",display:"block"},ef={letterSpacing:"0",fontWeight:"400"};i.extend({cssHooks:{opacity:{get:function(n,t){if(t){var i=ii(n,"opacity");return i===""?"1":i}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(n,t,r,u){if(n&&n.nodeType!==3&&n.nodeType!==8&&n.style){var e,s,o,c=k(t),l=tr.test(t),h=n.style;if(l||(t=ir(c)),o=i.cssHooks[t]||i.cssHooks[c],r!==undefined){if(s=typeof r,s==="string"&&(e=gt.exec(r))&&e[1]&&(r=vu(n,t,e),s="number"),r==null||r!==r)return;s!=="number"||l||(r+=e&&e[3]||(i.cssNumber[c]?"":"px"));f.clearCloneStyle||r!==""||t.indexOf("background")!==0||(h[t]="inherit");o&&"set"in o&&(r=o.set(n,r,u))===undefined||(l?h.setProperty(t,r):h[t]=r)}else return o&&"get"in o&&(e=o.get(n,!1,u))!==undefined?e:h[t]}},css:function(n,t,r,u){var f,o,e,s=k(t),h=tr.test(t);return(h||(t=ir(s)),e=i.cssHooks[t]||i.cssHooks[s],e&&"get"in e&&(f=e.get(n,!0,r)),f===undefined&&(f=ii(n,t,u)),f==="normal"&&t in ef&&(f=ef[t]),r===""||r)?(o=parseFloat(f),r===!0||isFinite(o)?o||0:f):f}});i.each(["height","width"],function(n,t){i.cssHooks[t]={get:function(n,r,u){if(r)return ho.test(i.css(n,"display"))&&(!n.getClientRects().length||!n.getBoundingClientRect().width)?nf(n,co,function(){return sf(n,t,u)}):sf(n,t,u)},set:function(n,r,u){var s,e=li(n),h=!f.scrollboxSize()&&e.position==="absolute",l=h||u,c=l&&i.css(n,"boxSizing",!1,e)==="border-box",o=u?rr(n,t,u,c,e):0;return c&&h&&(o-=Math.ceil(n["offset"+t[0].toUpperCase()+t.slice(1)]-parseFloat(e[t])-rr(n,t,"border",!1,e)-.5)),o&&(s=gt.exec(r))&&(s[3]||"px")!=="px"&&(n.style[t]=r,r=i.css(n,t)),of(n,r,o)}}});i.cssHooks.marginLeft=tf(f.reliableMarginLeft,function(n,t){if(t)return(parseFloat(ii(n,"marginLeft"))||n.getBoundingClientRect().left-nf(n,{marginLeft:0},function(){return n.getBoundingClientRect().left}))+"px"});i.each({margin:"",padding:"",border:"Width"},function(n,t){i.cssHooks[n+t]={expand:function(i){for(var r=0,f={},u=typeof i=="string"?i.split(" "):[i];r<4;r++)f[n+nt[r]+t]=u[r]||u[r-2]||u[0];return f}};n!=="margin"&&(i.cssHooks[n+t].set=of)});i.fn.extend({css:function(n,t){return g(this,function(n,t,r){var f,e,o={},u=0;if(Array.isArray(t)){for(f=li(n),e=t.length;u<e;u++)o[t[u]]=i.css(n,t[u],!1,f);return o}return r!==undefined?i.style(n,t,r):i.css(n,t)},n,t,arguments.length>1)}});i.Tween=a;a.prototype={constructor:a,init:function(n,t,r,u,f,e){this.elem=n;this.prop=r;this.easing=f||i.easing._default;this.options=t;this.start=this.now=this.cur();this.end=u;this.unit=e||(i.cssNumber[r]?"":"px")},cur:function(){var n=a.propHooks[this.prop];return n&&n.get?n.get(this):a.propHooks._default.get(this)},run:function(n){var t,r=a.propHooks[this.prop];return this.pos=this.options.duration?t=i.easing[this.easing](n,this.options.duration*n,0,1,this.options.duration):t=n,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),r&&r.set?r.set(this):a.propHooks._default.set(this),this}};a.prototype.init.prototype=a.prototype;a.propHooks={_default:{get:function(n){var t;return n.elem.nodeType!==1||n.elem[n.prop]!=null&&n.elem.style[n.prop]==null?n.elem[n.prop]:(t=i.css(n.elem,n.prop,""),!t||t==="auto"?0:t)},set:function(n){i.fx.step[n.prop]?i.fx.step[n.prop](n):n.elem.nodeType===1&&(i.cssHooks[n.prop]||n.elem.style[ir(n.prop)]!=null)?i.style(n.elem,n.prop,n.now+n.unit):n.elem[n.prop]=n.now}}};a.propHooks.scrollTop=a.propHooks.scrollLeft={set:function(n){n.elem.nodeType&&n.elem.parentNode&&(n.elem[n.prop]=n.now)}};i.easing={linear:function(n){return n},swing:function(n){return.5-Math.cos(n*Math.PI)/2},_default:"swing"};i.fx=a.prototype.init;i.fx.step={};hf=/^(?:toggle|show|hide)$/;cf=/queueHooks$/;i.Animation=i.extend(w,{tweeners:{"*":[function(n,t){var i=this.createTween(n,t);return vu(i.elem,n,gt.exec(t),i),i}]},tweener:function(n,t){e(n)?(t=n,n=["*"]):n=n.match(p);for(var i,r=0,u=n.length;r<u;r++)i=n[r],w.tweeners[i]=w.tweeners[i]||[],w.tweeners[i].unshift(t)},prefilters:[lo],prefilter:function(n,t){t?w.prefilters.unshift(n):w.prefilters.push(n)}});i.speed=function(n,t,r){var u=n&&typeof n=="object"?i.extend({},n):{complete:r||!r&&t||e(n)&&n,duration:n,easing:r&&t||t&&!e(t)&&t};return i.fx.off?u.duration=0:typeof u.duration!="number"&&(u.duration=u.duration in i.fx.speeds?i.fx.speeds[u.duration]:i.fx.speeds._default),(u.queue==null||u.queue===!0)&&(u.queue="fx"),u.old=u.complete,u.complete=function(){e(u.old)&&u.old.call(this);u.queue&&i.dequeue(this,u.queue)},u};i.fn.extend({fadeTo:function(n,t,i,r){return this.filter(ni).css("opacity",0).show().end().animate({opacity:t},n,i,r)},animate:function(n,t,u,f){var s=i.isEmptyObject(n),o=i.speed(t,u,f),e=function(){var t=w(this,i.extend({},n),o);(s||r.get(this,"finish"))&&t.stop(!0)};return e.finish=e,s||o.queue===!1?this.each(e):this.queue(o.queue,e)},stop:function(n,t,u){var f=function(n){var t=n.stop;delete n.stop;t(u)};return typeof n!="string"&&(u=t,t=n,n=undefined),t&&this.queue(n||"fx",[]),this.each(function(){var s=!0,t=n!=null&&n+"queueHooks",o=i.timers,e=r.get(this);if(t)e[t]&&e[t].stop&&f(e[t]);else for(t in e)e[t]&&e[t].stop&&cf.test(t)&&f(e[t]);for(t=o.length;t--;)o[t].elem===this&&(n==null||o[t].queue===n)&&(o[t].anim.stop(u),s=!1,o.splice(t,1));(s||!u)&&i.dequeue(this,n)})},finish:function(n){return n!==!1&&(n=n||"fx"),this.each(function(){var t,e=r.get(this),u=e[n+"queue"],o=e[n+"queueHooks"],f=i.timers,s=u?u.length:0;for(e.finish=!0,i.queue(this,n,[]),o&&o.stop&&o.stop.call(this,!0),t=f.length;t--;)f[t].elem===this&&f[t].queue===n&&(f[t].anim.stop(!0),f.splice(t,1));for(t=0;t<s;t++)u[t]&&u[t].finish&&u[t].finish.call(this);delete e.finish})}});i.each(["toggle","show","hide"],function(n,t){var r=i.fn[t];i.fn[t]=function(n,i,u){return n==null||typeof n=="boolean"?r.apply(this,arguments):this.animate(vi(t,!0),n,i,u)}});i.each({slideDown:vi("show"),slideUp:vi("hide"),slideToggle:vi("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(n,t){i.fn[n]=function(n,i,r){return this.animate(t,n,i,r)}});i.timers=[];i.fx.tick=function(){var r,n=0,t=i.timers;for(yt=Date.now();n<t.length;n++)r=t[n],r()||t[n]!==r||t.splice(n--,1);t.length||i.fx.stop();yt=undefined};i.fx.timer=function(n){i.timers.push(n);i.fx.start()};i.fx.interval=13;i.fx.start=function(){ai||(ai=!0,ur())};i.fx.stop=function(){ai=null};i.fx.speeds={slow:600,fast:200,_default:400};i.fn.delay=function(t,r){return t=i.fx?i.fx.speeds[t]||t:t,r=r||"fx",this.queue(r,function(i,r){var u=n.setTimeout(i,t);r.stop=function(){n.clearTimeout(u)}})},function(){var n=u.createElement("input"),t=u.createElement("select"),i=t.appendChild(u.createElement("option"));n.type="checkbox";f.checkOn=n.value!=="";f.optSelected=i.selected;n=u.createElement("input");n.value="t";n.type="radio";f.radioValue=n.value==="t"}();pt=i.expr.attrHandle;i.fn.extend({attr:function(n,t){return g(this,i.attr,n,t,arguments.length>1)},removeAttr:function(n){return this.each(function(){i.removeAttr(this,n)})}});i.extend({attr:function(n,t,r){var u,f,e=n.nodeType;if(e!==3&&e!==8&&e!==2){if(typeof n.getAttribute=="undefined")return i.prop(n,t,r);if(e===1&&i.isXMLDoc(n)||(f=i.attrHooks[t.toLowerCase()]||(i.expr.match.bool.test(t)?vf:undefined)),r!==undefined){if(r===null){i.removeAttr(n,t);return}return f&&"set"in f&&(u=f.set(n,r,t))!==undefined?u:(n.setAttribute(t,r+""),r)}return f&&"get"in f&&(u=f.get(n,t))!==null?u:(u=i.find.attr(n,t),u==null?undefined:u)}},attrHooks:{type:{set:function(n,t){if(!f.radioValue&&t==="radio"&&s(n,"input")){var i=n.value;return n.setAttribute("type",t),i&&(n.value=i),t}}}},removeAttr:function(n,t){var i,u=0,r=t&&t.match(p);if(r&&n.nodeType===1)while(i=r[u++])n.removeAttribute(i)}});vf={set:function(n,t,r){return t===!1?i.removeAttr(n,r):n.setAttribute(r,r),r}};i.each(i.expr.match.bool.source.match(/\w+/g),function(n,t){var r=pt[t]||i.find.attr;pt[t]=function(n,t,i){var f,e,u=t.toLowerCase();return i||(e=pt[u],pt[u]=f,f=r(n,t,i)!=null?u:null,pt[u]=e),f}});yf=/^(?:input|select|textarea|button)$/i;pf=/^(?:a|area)$/i;i.fn.extend({prop:function(n,t){return g(this,i.prop,n,t,arguments.length>1)},removeProp:function(n){return this.each(function(){delete this[i.propFix[n]||n]})}});i.extend({prop:function(n,t,r){var f,u,e=n.nodeType;if(e!==3&&e!==8&&e!==2)return(e===1&&i.isXMLDoc(n)||(t=i.propFix[t]||t,u=i.propHooks[t]),r!==undefined)?u&&"set"in u&&(f=u.set(n,r,t))!==undefined?f:n[t]=r:u&&"get"in u&&(f=u.get(n,t))!==null?f:n[t]},propHooks:{tabIndex:{get:function(n){var t=i.find.attr(n,"tabindex");return t?parseInt(t,10):yf.test(n.nodeName)||pf.test(n.nodeName)&&n.href?0:-1}}},propFix:{"for":"htmlFor","class":"className"}});f.optSelected||(i.propHooks.selected={get:function(n){var t=n.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(n){var t=n.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}});i.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){i.propFix[this.toLowerCase()]=this});i.fn.extend({addClass:function(n){var r,t,f,o,u,s;return e(n)?this.each(function(t){i(this).addClass(n.call(this,t,rt(this)))}):(r=fr(n),r.length)?this.each(function(){if(f=rt(this),t=this.nodeType===1&&" "+it(f)+" ",t){for(u=0;u<r.length;u++)o=r[u],t.indexOf(" "+o+" ")<0&&(t+=o+" ");s=it(t);f!==s&&this.setAttribute("class",s)}}):this},removeClass:function(n){var r,t,f,o,u,s;return e(n)?this.each(function(t){i(this).removeClass(n.call(this,t,rt(this)))}):arguments.length?(r=fr(n),r.length)?this.each(function(){if(f=rt(this),t=this.nodeType===1&&" "+it(f)+" ",t){for(u=0;u<r.length;u++)for(o=r[u];t.indexOf(" "+o+" ")>-1;)t=t.replace(" "+o+" "," ");s=it(t);f!==s&&this.setAttribute("class",s)}}):this:this.attr("class","")},toggleClass:function(n,t){var s,u,f,o,h=typeof n,c=h==="string"||Array.isArray(n);return e(n)?this.each(function(r){i(this).toggleClass(n.call(this,r,rt(this),t),t)}):typeof t=="boolean"&&c?t?this.addClass(n):this.removeClass(n):(s=fr(n),this.each(function(){if(c)for(o=i(this),f=0;f<s.length;f++)u=s[f],o.hasClass(u)?o.removeClass(u):o.addClass(u);else(n===undefined||h==="boolean")&&(u=rt(this),u&&r.set(this,"__className__",u),this.setAttribute&&this.setAttribute("class",u||n===!1?"":r.get(this,"__className__")||""))}))},hasClass:function(n){for(var t,r=0,i=" "+n+" ";t=this[r++];)if(t.nodeType===1&&(" "+it(rt(t))+" ").indexOf(i)>-1)return!0;return!1}});wf=/\r/g;i.fn.extend({val:function(n){var t,r,f,u=this[0];return arguments.length?(f=e(n),this.each(function(r){var u;this.nodeType===1&&(u=f?n.call(this,r,i(this).val()):n,u==null?u="":typeof u=="number"?u+="":Array.isArray(u)&&(u=i.map(u,function(n){return n==null?"":n+""})),t=i.valHooks[this.type]||i.valHooks[this.nodeName.toLowerCase()],t&&"set"in t&&t.set(this,u,"value")!==undefined||(this.value=u))})):u?(t=i.valHooks[u.type]||i.valHooks[u.nodeName.toLowerCase()],t&&"get"in t&&(r=t.get(u,"value"))!==undefined)?r:(r=u.value,typeof r=="string")?r.replace(wf,""):r==null?"":r:void 0}});i.extend({valHooks:{option:{get:function(n){var t=i.find.attr(n,"value");return t!=null?t:it(i.text(n))}},select:{get:function(n){for(var e,t,o=n.options,u=n.selectedIndex,f=n.type==="select-one",h=f?null:[],c=f?u+1:o.length,r=u<0?c:f?u:0;r<c;r++)if(t=o[r],(t.selected||r===u)&&!t.disabled&&(!t.parentNode.disabled||!s(t.parentNode,"optgroup"))){if(e=i(t).val(),f)return e;h.push(e)}return h},set:function(n,t){for(var u,r,f=n.options,e=i.makeArray(t),o=f.length;o--;)r=f[o],(r.selected=i.inArray(i.valHooks.option.get(r),e)>-1)&&(u=!0);return u||(n.selectedIndex=-1),e}}}});i.each(["radio","checkbox"],function(){i.valHooks[this]={set:function(n,t){if(Array.isArray(t))return n.checked=i.inArray(i(n).val(),t)>-1}};f.checkOn||(i.valHooks[this].get=function(n){return n.getAttribute("value")===null?"on":n.value})});var ri=n.location,bf={guid:Date.now()},er=/\?/;i.parseXML=function(t){var r,u;if(!t||typeof t!="string")return null;try{r=(new n.DOMParser).parseFromString(t,"text/xml")}catch(f){}return u=r&&r.getElementsByTagName("parsererror")[0],(!r||u)&&i.error("Invalid XML: "+(u?i.map(u.childNodes,function(n){return n.textContent}).join("\n"):t)),r};or=/^(?:focusinfocus|focusoutblur)$/;sr=function(n){n.stopPropagation()};i.extend(i.event,{trigger:function(t,f,o,s){var k,c,l,d,v,y,a,w,b=[o||u],h=bt.call(t,"type")?t.type:t,p=bt.call(t,"namespace")?t.namespace.split("."):[];if((c=w=l=o=o||u,o.nodeType!==3&&o.nodeType!==8)&&!or.test(h+i.event.triggered)&&(h.indexOf(".")>-1&&(p=h.split("."),h=p.shift(),p.sort()),v=h.indexOf(":")<0&&"on"+h,t=t[i.expando]?t:new i.Event(h,typeof t=="object"&&t),t.isTrigger=s?2:3,t.namespace=p.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=undefined,t.target||(t.target=o),f=f==null?[t]:i.makeArray(f,[t]),a=i.event.special[h]||{},s||!a.trigger||a.trigger.apply(o,f)!==!1)){if(!s&&!a.noBubble&&!ut(o)){for(d=a.delegateType||h,or.test(d+h)||(c=c.parentNode);c;c=c.parentNode)b.push(c),l=c;l===(o.ownerDocument||u)&&b.push(l.defaultView||l.parentWindow||n)}for(k=0;(c=b[k++])&&!t.isPropagationStopped();)w=c,t.type=k>1?d:a.bindType||h,y=(r.get(c,"events")||Object.create(null))[t.type]&&r.get(c,"handle"),y&&y.apply(c,f),y=v&&c[v],y&&y.apply&&st(c)&&(t.result=y.apply(c,f),t.result===!1&&t.preventDefault());return t.type=h,s||t.isDefaultPrevented()||(!a._default||a._default.apply(b.pop(),f)===!1)&&st(o)&&v&&e(o[h])&&!ut(o)&&(l=o[v],l&&(o[v]=null),i.event.triggered=h,t.isPropagationStopped()&&w.addEventListener(h,sr),o[h](),t.isPropagationStopped()&&w.removeEventListener(h,sr),i.event.triggered=undefined,l&&(o[v]=l)),t.result}},simulate:function(n,t,r){var u=i.extend(new i.Event,r,{type:n,isSimulated:!0});i.event.trigger(u,null,t)}});i.fn.extend({trigger:function(n,t){return this.each(function(){i.event.trigger(n,t,this)})},triggerHandler:function(n,t){var r=this[0];if(r)return i.event.trigger(n,t,r,!0)}});var vo=/\[\]$/,kf=/\r?\n/g,yo=/^(?:submit|button|image|reset|file)$/i,po=/^(?:input|select|textarea|keygen)/i;i.param=function(n,t){var r,u=[],f=function(n,t){var i=e(t)?t():t;u[u.length]=encodeURIComponent(n)+"="+encodeURIComponent(i==null?"":i)};if(n==null)return"";if(Array.isArray(n)||n.jquery&&!i.isPlainObject(n))i.each(n,function(){f(this.name,this.value)});else for(r in n)hr(r,n[r],t,f);return u.join("&")};i.fn.extend({serialize:function(){return i.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var n=i.prop(this,"elements");return n?i.makeArray(n):this}).filter(function(){var n=this.type;return this.name&&!i(this).is(":disabled")&&po.test(this.nodeName)&&!yo.test(n)&&(this.checked||!ti.test(n))}).map(function(n,t){var r=i(this).val();return r==null?null:Array.isArray(r)?i.map(r,function(n){return{name:t.name,value:n.replace(kf,"\r\n")}}):{name:t.name,value:r.replace(kf,"\r\n")}}).get()}});var wo=/%20/g,bo=/#.*$/,ko=/([?&])_=[^&]*/,go=/^(.*?):[ \t]*([^\r\n]*)$/mg,ns=/^(?:GET|HEAD)$/,ts=/^\/\//,df={},cr={},gf="*/".concat("*"),lr=u.createElement("a");return lr.href=ri.href,i.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:ri.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(ri.protocol),global:!0,processData:!0,"async":!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":gf,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":i.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(n,t){return t?ar(ar(n,i.ajaxSettings),t):ar(i.ajaxSettings,n)},ajaxPrefilter:ne(df),ajaxTransport:ne(cr),ajax:function(t,r){function b(t,r,u,l){var y,rt,g,p,b,a=r;s||(s=!0,d&&n.clearTimeout(d),c=undefined,k=l||"",e.readyState=t>0?4:0,y=t>=200&&t<300||t===304,u&&(p=is(f,e,u)),!y&&i.inArray("script",f.dataTypes)>-1&&i.inArray("json",f.dataTypes)<0&&(f.converters["text script"]=function(){}),p=rs(f,p,e,y),y?(f.ifModified&&(b=e.getResponseHeader("Last-Modified"),b&&(i.lastModified[o]=b),b=e.getResponseHeader("etag"),b&&(i.etag[o]=b)),t===204||f.type==="HEAD"?a="nocontent":t===304?a="notmodified":(a=p.state,rt=p.data,g=p.error,y=!g)):(g=a,(t||!a)&&(a="error",t<0&&(t=0))),e.status=t,e.statusText=(r||a)+"",y?tt.resolveWith(h,[rt,a,e]):tt.rejectWith(h,[e,a,g]),e.statusCode(w),w=undefined,v&&nt.trigger(y?"ajaxSuccess":"ajaxError",[e,f,y?rt:g]),it.fireWith(h,[e,a]),v&&(nt.trigger("ajaxComplete",[e,f]),--i.active||i.event.trigger("ajaxStop")))}typeof t=="object"&&(r=t,t=undefined);r=r||{};var c,o,k,a,d,l,s,v,g,y,f=i.ajaxSetup({},r),h=f.context||f,nt=f.context&&(h.nodeType||h.jquery)?i(h):i.event,tt=i.Deferred(),it=i.Callbacks("once memory"),w=f.statusCode||{},rt={},ut={},ft="canceled",e={readyState:0,getResponseHeader:function(n){var t;if(s){if(!a)for(a={};t=go.exec(k);)a[t[1].toLowerCase()+" "]=(a[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=a[n.toLowerCase()+" "]}return t==null?null:t.join(", ")},getAllResponseHeaders:function(){return s?k:null},setRequestHeader:function(n,t){return s==null&&(n=ut[n.toLowerCase()]=ut[n.toLowerCase()]||n,rt[n]=t),this},overrideMimeType:function(n){return s==null&&(f.mimeType=n),this},statusCode:function(n){var t;if(n)if(s)e.always(n[e.status]);else for(t in n)w[t]=[w[t],n[t]];return this},abort:function(n){var t=n||ft;return c&&c.abort(t),b(0,t),this}};if(tt.promise(e),f.url=((t||f.url||ri.href)+"").replace(ts,ri.protocol+"//"),f.type=r.method||r.type||f.method||f.type,f.dataTypes=(f.dataType||"*").toLowerCase().match(p)||[""],f.crossDomain==null){l=u.createElement("a");try{l.href=f.url;l.href=l.href;f.crossDomain=lr.protocol+"//"+lr.host!=l.protocol+"//"+l.host}catch(et){f.crossDomain=!0}}if(f.data&&f.processData&&typeof f.data!="string"&&(f.data=i.param(f.data,f.traditional)),te(df,f,r,e),s)return e;v=i.event&&f.global;v&&i.active++==0&&i.event.trigger("ajaxStart");f.type=f.type.toUpperCase();f.hasContent=!ns.test(f.type);o=f.url.replace(bo,"");f.hasContent?f.data&&f.processData&&(f.contentType||"").indexOf("application/x-www-form-urlencoded")===0&&(f.data=f.data.replace(wo,"+")):(y=f.url.slice(o.length),f.data&&(f.processData||typeof f.data=="string")&&(o+=(er.test(o)?"&":"?")+f.data,delete f.data),f.cache===!1&&(o=o.replace(ko,"$1"),y=(er.test(o)?"&":"?")+"_="+bf.guid+++y),f.url=o+y);f.ifModified&&(i.lastModified[o]&&e.setRequestHeader("If-Modified-Since",i.lastModified[o]),i.etag[o]&&e.setRequestHeader("If-None-Match",i.etag[o]));(f.data&&f.hasContent&&f.contentType!==!1||r.contentType)&&e.setRequestHeader("Content-Type",f.contentType);e.setRequestHeader("Accept",f.dataTypes[0]&&f.accepts[f.dataTypes[0]]?f.accepts[f.dataTypes[0]]+(f.dataTypes[0]!=="*"?", "+gf+"; q=0.01":""):f.accepts["*"]);for(g in f.headers)e.setRequestHeader(g,f.headers[g]);if(f.beforeSend&&(f.beforeSend.call(h,e,f)===!1||s))return e.abort();if(ft="abort",it.add(f.complete),e.done(f.success),e.fail(f.error),c=te(cr,f,r,e),c){if(e.readyState=1,v&&nt.trigger("ajaxSend",[e,f]),s)return e;f.async&&f.timeout>0&&(d=n.setTimeout(function(){e.abort("timeout")},f.timeout));try{s=!1;c.send(rt,b)}catch(et){if(s)throw et;b(-1,et)}}else b(-1,"No Transport");return e},getJSON:function(n,t,r){return i.get(n,t,r,"json")},getScript:function(n,t){return i.get(n,undefined,t,"script")}}),i.each(["get","post"],function(n,t){i[t]=function(n,r,u,f){return e(r)&&(f=f||u,u=r,r=undefined),i.ajax(i.extend({url:n,type:t,dataType:f,data:r,success:u},i.isPlainObject(n)&&n))}}),i.ajaxPrefilter(function(n){for(var t in n.headers)t.toLowerCase()==="content-type"&&(n.contentType=n.headers[t]||"")}),i._evalUrl=function(n,t,r){return i.ajax({url:n,type:"GET",dataType:"script",cache:!0,"async":!1,global:!1,converters:{"text script":function(){}},dataFilter:function(n){i.globalEval(n,t,r)}})},i.fn.extend({wrapAll:function(n){var t;return this[0]&&(e(n)&&(n=n.call(this[0])),t=i(n,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var n=this;n.firstElementChild;)n=n.firstElementChild;return n}).append(this)),this},wrapInner:function(n){return e(n)?this.each(function(t){i(this).wrapInner(n.call(this,t))}):this.each(function(){var t=i(this),r=t.contents();r.length?r.wrapAll(n):t.append(n)})},wrap:function(n){var t=e(n);return this.each(function(r){i(this).wrapAll(t?n.call(this,r):n)})},unwrap:function(n){return this.parent(n).not("body").each(function(){i(this).replaceWith(this.childNodes)}),this}}),i.expr.pseudos.hidden=function(n){return!i.expr.pseudos.visible(n)},i.expr.pseudos.visible=function(n){return!!(n.offsetWidth||n.offsetHeight||n.getClientRects().length)},i.ajaxSettings.xhr=function(){try{return new n.XMLHttpRequest}catch(t){}},ie={0:200,1223:204},wt=i.ajaxSettings.xhr(),f.cors=!!wt&&"withCredentials"in wt,f.ajax=wt=!!wt,i.ajaxTransport(function(t){var i,r;if(f.cors||wt&&!t.crossDomain)return{send:function(u,f){var o,e=t.xhr();if(e.open(t.type,t.url,t.async,t.username,t.password),t.xhrFields)for(o in t.xhrFields)e[o]=t.xhrFields[o];t.mimeType&&e.overrideMimeType&&e.overrideMimeType(t.mimeType);t.crossDomain||u["X-Requested-With"]||(u["X-Requested-With"]="XMLHttpRequest");for(o in u)e.setRequestHeader(o,u[o]);i=function(n){return function(){i&&(i=r=e.onload=e.onerror=e.onabort=e.ontimeout=e.onreadystatechange=null,n==="abort"?e.abort():n==="error"?typeof e.status!="number"?f(0,"error"):f(e.status,e.statusText):f(ie[e.status]||e.status,e.statusText,(e.responseType||"text")!=="text"||typeof e.responseText!="string"?{binary:e.response}:{text:e.responseText},e.getAllResponseHeaders()))}};e.onload=i();r=e.onerror=e.ontimeout=i("error");e.onabort!==undefined?e.onabort=r:e.onreadystatechange=function(){e.readyState===4&&n.setTimeout(function(){i&&r()})};i=i("abort");try{e.send(t.hasContent&&t.data||null)}catch(s){if(i)throw s;}},abort:function(){i&&i()}}}),i.ajaxPrefilter(function(n){n.crossDomain&&(n.contents.script=!1)}),i.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(n){return i.globalEval(n),n}}}),i.ajaxPrefilter("script",function(n){n.cache===undefined&&(n.cache=!1);n.crossDomain&&(n.type="GET")}),i.ajaxTransport("script",function(n){if(n.crossDomain||n.scriptAttrs){var r,t;return{send:function(f,e){r=i("<script>").attr(n.scriptAttrs||{}).prop({charset:n.scriptCharset,src:n.url}).on("load error",t=function(n){r.remove();t=null;n&&e(n.type==="error"?404:200,n.type)});u.head.appendChild(r[0])},abort:function(){t&&t()}}}}),vr=[],yi=/(=)\?(?=&|$)|\?\?/,i.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var n=vr.pop()||i.expando+"_"+bf.guid++;return this[n]=!0,n}}),i.ajaxPrefilter("json jsonp",function(t,r,u){var f,o,s,h=t.jsonp!==!1&&(yi.test(t.url)?"url":typeof t.data=="string"&&(t.contentType||"").indexOf("application/x-www-form-urlencoded")===0&&yi.test(t.data)&&"data");if(h||t.dataTypes[0]==="jsonp")return f=t.jsonpCallback=e(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,h?t[h]=t[h].replace(yi,"$1"+f):t.jsonp!==!1&&(t.url+=(er.test(t.url)?"&":"?")+t.jsonp+"="+f),t.converters["script json"]=function(){return s||i.error(f+" was not called"),s[0]},t.dataTypes[0]="json",o=n[f],n[f]=function(){s=arguments},u.always(function(){o===undefined?i(n).removeProp(f):n[f]=o;t[f]&&(t.jsonpCallback=r.jsonpCallback,vr.push(f));s&&e(o)&&o(s[0]);s=o=undefined}),"script"}),f.createHTMLDocument=function(){var n=u.implementation.createHTMLDocument("").body;return n.innerHTML="<form><\/form><form><\/form>",n.childNodes.length===2}(),i.parseHTML=function(n,t,r){if(typeof n!="string")return[];typeof t=="boolean"&&(r=t,t=!1);var s,e,o;return(t||(f.createHTMLDocument?(t=u.implementation.createHTMLDocument(""),s=t.createElement("base"),s.href=u.location.href,t.head.appendChild(s)):t=u),e=iu.exec(n),o=!r&&[],e)?[t.createElement(e[1])]:(e=bu([n],t,o),o&&o.length&&i(o).remove(),i.merge([],e.childNodes))},i.fn.load=function(n,t,r){var u,s,h,f=this,o=n.indexOf(" ");return o>-1&&(u=it(n.slice(o)),n=n.slice(0,o)),e(t)?(r=t,t=undefined):t&&typeof t=="object"&&(s="POST"),f.length>0&&i.ajax({url:n,type:s||"GET",dataType:"html",data:t}).done(function(n){h=arguments;f.html(u?i("<div>").append(i.parseHTML(n)).find(u):n)}).always(r&&function(n,t){f.each(function(){r.apply(this,h||[n.responseText,t,n])})}),this},i.expr.pseudos.animated=function(n){return i.grep(i.timers,function(t){return n===t.elem}).length},i.offset={setOffset:function(n,t,r){var o,s,h,c,u,l,y,a=i.css(n,"position"),v=i(n),f={};a==="static"&&(n.style.position="relative");u=v.offset();h=i.css(n,"top");l=i.css(n,"left");y=(a==="absolute"||a==="fixed")&&(h+l).indexOf("auto")>-1;y?(o=v.position(),c=o.top,s=o.left):(c=parseFloat(h)||0,s=parseFloat(l)||0);e(t)&&(t=t.call(n,r,i.extend({},u)));t.top!=null&&(f.top=t.top-u.top+c);t.left!=null&&(f.left=t.left-u.left+s);"using"in t?t.using.call(n,f):v.css(f)}},i.fn.extend({offset:function(n){if(arguments.length)return n===undefined?this:this.each(function(t){i.offset.setOffset(this,n,t)});var r,u,t=this[0];if(t)return t.getClientRects().length?(r=t.getBoundingClientRect(),u=t.ownerDocument.defaultView,{top:r.top+u.pageYOffset,left:r.left+u.pageXOffset}):{top:0,left:0}},position:function(){if(this[0]){var n,u,f,t=this[0],r={top:0,left:0};if(i.css(t,"position")==="fixed")u=t.getBoundingClientRect();else{for(u=this.offset(),f=t.ownerDocument,n=t.offsetParent||f.documentElement;n&&(n===f.body||n===f.documentElement)&&i.css(n,"position")==="static";)n=n.parentNode;n&&n!==t&&n.nodeType===1&&(r=i(n).offset(),r.top+=i.css(n,"borderTopWidth",!0),r.left+=i.css(n,"borderLeftWidth",!0))}return{top:u.top-r.top-i.css(t,"marginTop",!0),left:u.left-r.left-i.css(t,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var n=this.offsetParent;n&&i.css(n,"position")==="static";)n=n.offsetParent;return n||tt})}}),i.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(n,t){var r="pageYOffset"===t;i.fn[n]=function(i){return g(this,function(n,i,u){var f;if(ut(n)?f=n:n.nodeType===9&&(f=n.defaultView),u===undefined)return f?f[t]:n[i];f?f.scrollTo(r?f.pageXOffset:u,r?u:f.pageYOffset):n[i]=u},n,i,arguments.length)}}),i.each(["top","left"],function(n,t){i.cssHooks[t]=tf(f.pixelPosition,function(n,r){if(r)return r=ii(n,t),nr.test(r)?i(n).position()[t]+"px":r})}),i.each({Height:"height",Width:"width"},function(n,t){i.each({padding:"inner"+n,content:t,"":"outer"+n},function(r,u){i.fn[u]=function(f,e){var o=arguments.length&&(r||typeof f!="boolean"),s=r||(f===!0||e===!0?"margin":"border");return g(this,function(t,r,f){var e;return ut(t)?u.indexOf("outer")===0?t["inner"+n]:t.document.documentElement["client"+n]:t.nodeType===9?(e=t.documentElement,Math.max(t.body["scroll"+n],e["scroll"+n],t.body["offset"+n],e["offset"+n],e["client"+n])):f===undefined?i.css(t,r,s):i.style(t,r,f,s)},t,o?f:undefined,o)}})}),i.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(n,t){i.fn[t]=function(n){return this.on(t,n)}}),i.fn.extend({bind:function(n,t,i){return this.on(n,null,t,i)},unbind:function(n,t){return this.off(n,null,t)},delegate:function(n,t,i,r){return this.on(t,n,i,r)},undelegate:function(n,t,i){return arguments.length===1?this.off(n,"**"):this.off(t,n||"**",i)},hover:function(n,t){return this.on("mouseenter",n).on("mouseleave",t||n)}}),i.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(n,t){i.fn[t]=function(n,i){return arguments.length>0?this.on(t,null,n,i):this.trigger(t)}}),re=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g,i.proxy=function(n,t){var u,f,r;return(typeof t=="string"&&(u=n[t],t=n,n=u),!e(n))?undefined:(f=v.call(arguments,2),r=function(){return n.apply(t||this,f.concat(v.call(arguments)))},r.guid=n.guid=n.guid||i.guid++,r)},i.holdReady=function(n){n?i.readyWait++:i.ready(!0)},i.isArray=Array.isArray,i.parseJSON=JSON.parse,i.nodeName=s,i.isFunction=e,i.isWindow=ut,i.camelCase=k,i.type=ft,i.now=Date.now,i.isNumeric=function(n){var t=i.type(n);return(t==="number"||t==="string")&&!isNaN(n-parseFloat(n))},i.trim=function(n){return n==null?"":(n+"").replace(re,"$1")},typeof define=="function"&&define.amd&&define("jquery",[],function(){return i}),ue=n.jQuery,fe=n.$,i.noConflict=function(t){return n.$===i&&(n.$=fe),t&&n.jQuery===i&&(n.jQuery=ue),i},typeof t=="undefined"&&(n.jQuery=n.$=i),i});
/*!
 * jQuery Migrate - v3.4.0 - 2022-03-24T16:30Z
 * Copyright OpenJS Foundation and other contributors
 */
(function(n){"use strict";typeof define=="function"&&define.amd?define(["jquery"],function(t){return n(t,window)}):typeof module=="object"&&module.exports?module.exports=n(require("jquery"),window):n(jQuery,window)})(function(n,t){"use strict";function st(n,t){for(var r=/^(\d+)\.(\d+)\.(\d+)/,u=r.exec(n)||[],f=r.exec(t)||[],i=1;i<=3;i++){if(+u[i]>+f[i])return 1;if(+u[i]<+f[i])return-1}return 0}function f(t){return st(n.fn.jquery,t)>=0}function i(i,r){var u=t.console;!n.migrateIsPatchEnabled(i)||n.migrateDeduplicateWarnings&&h[r]||(h[r]=!0,n.migrateWarnings.push(r+" ["+i+"]"),u&&u.warn&&!n.migrateMute&&(u.warn("JQMIGRATE: "+r),n.migrateTrace&&u.trace&&u.trace()))}function a(n,t,r,u,f){Object.defineProperty(n,t,{configurable:!0,enumerable:!0,get:function(){return i(u,f),r},set:function(n){i(u,f);r=n}})}function k(t,r,u,f,e){var o,s=t[r];t[r]=function(){return e&&i(f,e),o=n.migrateIsPatchEnabled(f)?u:s||n.noop,o.apply(this,arguments)}}function r(n,t,i,r,u){if(!u)throw new Error("No warning message provided");return k(n,t,i,r,u)}function u(n,t,i,r){return k(n,t,i,r)}function y(n){return n.replace(/-([a-z])/g,function(n,t){return t.toUpperCase()})}function kt(n){return wt.test(n)&&bt.test(n[0].toUpperCase()+n.slice(1))}var s,h,g,v,e,o,w,tt,it,ft,et,b,ot;n.migrateVersion="3.4.0";s=Object.create(null);n.migrateDisablePatches=function(){for(var n=0;n<arguments.length;n++)s[arguments[n]]=!0};n.migrateEnablePatches=function(){for(var n=0;n<arguments.length;n++)delete s[arguments[n]]};n.migrateIsPatchEnabled=function(n){return!s[n]},function(){t.console&&t.console.log&&(n&&f("3.0.0")||t.console.log("JQMIGRATE: jQuery 3.0.0+ REQUIRED"),n.migrateWarnings&&t.console.log("JQMIGRATE: Migrate plugin loaded multiple times"),t.console.log("JQMIGRATE: Migrate is installed"+(n.migrateMute?"":" with logging active")+", version "+n.migrateVersion))}();h={};n.migrateDeduplicateWarnings=!0;n.migrateWarnings=[];n.migrateTrace===undefined&&(n.migrateTrace=!0);n.migrateReset=function(){h={};n.migrateWarnings.length=0};t.document.compatMode==="BackCompat"&&i("quirks","jQuery is not compatible with Quirks Mode");var c,d={},ht=n.fn.init,l=n.find,ct=/\[(\s*[-\w]+\s*)([~|^$*]?=)\s*([-\w#]*?#[-\w#]*)\s*\]/,lt=/\[(\s*[-\w]+\s*)([~|^$*]?=)\s*([-\w#]*?#[-\w#]*)\s*\]/g,at=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;u(n.fn,"init",function(t){var r=Array.prototype.slice.call(arguments);return n.migrateIsPatchEnabled("selector-empty-id")&&typeof t=="string"&&t==="#"&&(i("selector-empty-id","jQuery( '#' ) is not a valid selector"),r[0]=[]),ht.apply(this,r)},"selector-empty-id");n.fn.init.prototype=n.fn;u(n,"find",function(n){var r=Array.prototype.slice.call(arguments);if(typeof n=="string"&&ct.test(n))try{t.document.querySelector(n)}catch(u){n=n.replace(lt,function(n,t,i,r){return"["+t+i+'"'+r+'"]'});try{t.document.querySelector(n);i("selector-hash","Attribute selector with '#' must be quoted: "+r[0]);r[0]=n}catch(f){i("selector-hash","Attribute selector with '#' was not fixed: "+r[0])}}return l.apply(this,r)},"selector-hash");for(c in l)Object.prototype.hasOwnProperty.call(l,c)&&(n.find[c]=l[c]);r(n.fn,"size",function(){return this.length},"size","jQuery.fn.size() is deprecated and removed; use the .length property");r(n,"parseJSON",function(){return JSON.parse.apply(null,arguments)},"parseJSON","jQuery.parseJSON is deprecated; use JSON.parse");r(n,"holdReady",n.holdReady,"holdReady","jQuery.holdReady is deprecated");r(n,"unique",n.uniqueSort,"unique","jQuery.unique is deprecated; use jQuery.uniqueSort");a(n.expr,"filters",n.expr.pseudos,"expr-pre-pseudos","jQuery.expr.filters is deprecated; use jQuery.expr.pseudos");a(n.expr,":",n.expr.pseudos,"expr-pre-pseudos","jQuery.expr[':'] is deprecated; use jQuery.expr.pseudos");f("3.1.1")&&r(n,"trim",function(n){return n==null?"":(n+"").replace(at,"")},"trim","jQuery.trim is deprecated; use String.prototype.trim");f("3.2.0")&&(r(n,"nodeName",function(n,t){return n.nodeName&&n.nodeName.toLowerCase()===t.toLowerCase()},"nodeName","jQuery.nodeName is deprecated"),r(n,"isArray",Array.isArray,"isArray","jQuery.isArray is deprecated; use Array.isArray"));f("3.3.0")&&(r(n,"isNumeric",function(n){var t=typeof n;return(t==="number"||t==="string")&&!isNaN(n-parseFloat(n))},"isNumeric","jQuery.isNumeric() is deprecated"),n.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(n,t){d["[object "+t+"]"]=t.toLowerCase()}),r(n,"type",function(n){return n==null?n+"":typeof n=="object"||typeof n=="function"?d[Object.prototype.toString.call(n)]||"object":typeof n},"type","jQuery.type is deprecated"),r(n,"isFunction",function(n){return typeof n=="function"},"isFunction","jQuery.isFunction() is deprecated"),r(n,"isWindow",function(n){return n!=null&&n===n.window},"isWindow","jQuery.isWindow() is deprecated"));n.ajax&&(g=n.ajax,v=/(=)\?(?=&|$)|\?\?/,u(n,"ajax",function(){var n=g.apply(this,arguments);return n.promise&&(r(n,"success",n.done,"jqXHR-methods","jQXHR.success is deprecated and removed"),r(n,"error",n.fail,"jqXHR-methods","jQXHR.error is deprecated and removed"),r(n,"complete",n.always,"jqXHR-methods","jQXHR.complete is deprecated and removed")),n},"jqXHR-methods"),f("4.0.0")||n.ajaxPrefilter("+json",function(n){n.jsonp!==!1&&(v.test(n.url)||typeof n.data=="string"&&(n.contentType||"").indexOf("application/x-www-form-urlencoded")===0&&v.test(n.data))&&i("jsonp-promotion","JSON-to-JSONP auto-promotion is deprecated")}));var vt=n.fn.removeAttr,yt=n.fn.toggleClass,pt=/\S+/g;u(n.fn,"removeAttr",function(t){var r=this;return n.each(t.match(pt),function(t,u){n.expr.match.bool.test(u)&&(i("removeAttr-bool","jQuery.fn.removeAttr no longer sets boolean properties: "+u),r.prop(u,!1))}),vt.apply(this,arguments)},"removeAttr-bool");u(n.fn,"toggleClass",function(t){return t!==undefined&&typeof t!="boolean"?yt.apply(this,arguments):(i("toggleClass-bool","jQuery.fn.toggleClass( boolean ) is deprecated"),this.each(function(){var i=this.getAttribute&&this.getAttribute("class")||"";i&&n.data(this,"__className__",i);this.setAttribute&&this.setAttribute("class",i||t===!1?"":n.data(this,"__className__")||"")}))},"toggleClass-bool");var nt,p=!1,wt=/^[a-z]/,bt=/^(?:Border(?:Top|Right|Bottom|Left)?(?:Width|)|(?:Margin|Padding)?(?:Top|Right|Bottom|Left)?|(?:Min|Max)?(?:Width|Height))$/;n.swap&&n.each(["height","width","reliableMarginRight"],function(t,i){var r=n.cssHooks[i]&&n.cssHooks[i].get;r&&(n.cssHooks[i].get=function(){var n;return p=!0,n=r.apply(this,arguments),p=!1,n})});u(n,"swap",function(n,t,r,u){var e,f,o={};p||i("swap","jQuery.swap() is undocumented and deprecated");for(f in t)o[f]=n.style[f],n.style[f]=t[f];e=r.apply(n,u||[]);for(f in t)n.style[f]=o[f];return e},"swap");f("3.4.0")&&typeof Proxy!="undefined"&&(n.cssProps=new Proxy(n.cssProps||{},{set:function(){return i("cssProps","jQuery.cssProps is deprecated"),Reflect.set.apply(this,arguments)}}));f("4.0.0")&&typeof Proxy!="undefined"&&(n.cssNumber=new Proxy({animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},{get:function(){return i("css-number","jQuery.cssNumber is deprecated"),Reflect.get.apply(this,arguments)},set:function(){return i("css-number","jQuery.cssNumber is deprecated"),Reflect.set.apply(this,arguments)}}));nt=n.fn.css;u(n.fn,"css",function(t,r){var u,f=this;return t&&typeof t=="object"&&!Array.isArray(t)?(n.each(t,function(t,i){n.fn.css.call(f,t,i)}),this):(typeof r=="number"&&(u=y(t),kt(u)||n.cssNumber[u]||i("css-number",'Number-typed values are deprecated for jQuery.fn.css( "'+t+'", value )')),nt.apply(this,arguments))},"css-number");e=n.data;u(n,"data",function(t,r,u){var o,s,f;if(r&&typeof r=="object"&&arguments.length===2){o=n.hasData(t)&&e.call(this,t);s={};for(f in r)f!==y(f)?(i("data-camelCase","jQuery.data() always sets/gets camelCased names: "+f),o[f]=r[f]):s[f]=r[f];return e.call(this,t,s),r}return r&&typeof r=="string"&&r!==y(r)&&(o=n.hasData(t)&&e.call(this,t),o&&r in o)?(i("data-camelCase","jQuery.data() always sets/gets camelCased names: "+r),arguments.length>2&&(o[r]=u),o[r]):e.apply(this,arguments)},"data-camelCase");n.fx&&(tt=n.Tween.prototype.run,it=function(n){return n},u(n.Tween.prototype,"run",function(){n.easing[this.easing].length>1&&(i("easing-one-arg","'jQuery.easing."+this.easing.toString()+"' should use only one argument"),n.easing[this.easing]=it);tt.apply(this,arguments)},"easing-one-arg"),o=n.fx.interval,w="jQuery.fx.interval is deprecated",t.requestAnimationFrame&&Object.defineProperty(n.fx,"interval",{configurable:!0,enumerable:!0,get:function(){return(t.document.hidden||i("fx-interval",w),!n.migrateIsPatchEnabled("fx-interval"))?o:o===undefined?13:o},set:function(n){i("fx-interval",w);o=n}}));var dt=n.fn.load,gt=n.event.add,ni=n.event.fix;n.event.props=[];n.event.fixHooks={};a(n.event.props,"concat",n.event.props.concat,"event-old-patch","jQuery.event.props.concat() is deprecated and removed");u(n.event,"fix",function(t){var f,e=t.type,u=this.fixHooks[e],r=n.event.props;if(r.length)for(i("event-old-patch","jQuery.event.props are deprecated and removed: "+r.join());r.length;)n.event.addProp(r.pop());if(u&&!u._migrated_&&(u._migrated_=!0,i("event-old-patch","jQuery.event.fixHooks are deprecated and removed: "+e),(r=u.props)&&r.length))while(r.length)n.event.addProp(r.pop());return f=ni.call(this,t),u&&u.filter?u.filter(f,t):f},"event-old-patch");u(n.event,"add",function(n,r){return n===t&&r==="load"&&t.document.readyState==="complete"&&i("load-after-event","jQuery(window).on('load'...) called after load event occurred"),gt.apply(this,arguments)},"load-after-event");n.each(["load","unload","error"],function(t,r){u(n.fn,r,function(){var n=Array.prototype.slice.call(arguments,0);return r==="load"&&typeof n[0]=="string"?dt.apply(this,n):(i("shorthand-removed-v3","jQuery.fn."+r+"() is deprecated"),n.splice(0,0,r),arguments.length)?this.on.apply(this,n):(this.triggerHandler.apply(this,n),this)},"shorthand-removed-v3")});n.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(t,i){r(n.fn,i,function(n,t){return arguments.length>0?this.on(i,null,n,t):this.trigger(i)},"shorthand-deprecated-v3","jQuery.fn."+i+"() event shorthand is deprecated")});n(function(){n(t.document).triggerHandler("ready")});n.event.special.ready={setup:function(){this===t.document&&i("ready-event","'ready' event is deprecated")}};r(n.fn,"bind",function(n,t,i){return this.on(n,null,t,i)},"pre-on-methods","jQuery.fn.bind() is deprecated");r(n.fn,"unbind",function(n,t){return this.off(n,null,t)},"pre-on-methods","jQuery.fn.unbind() is deprecated");r(n.fn,"delegate",function(n,t,i,r){return this.on(t,n,i,r)},"pre-on-methods","jQuery.fn.delegate() is deprecated");r(n.fn,"undelegate",function(n,t,i){return arguments.length===1?this.off(n,"**"):this.off(t,n||"**",i)},"pre-on-methods","jQuery.fn.undelegate() is deprecated");r(n.fn,"hover",function(n,t){return this.on("mouseenter",n).on("mouseleave",t||n)},"pre-on-methods","jQuery.fn.hover() is deprecated");var rt=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([a-z][^\/\0>\x20\t\r\n\f]*)[^>]*)\/>/gi,ut=function(n){var i=t.document.implementation.createHTMLDocument("");return i.body.innerHTML=n,i.body&&i.body.innerHTML},ti=function(n){var t=n.replace(rt,"<$1><\/$2>");t!==n&&ut(n)!==ut(t)&&i("self-closed-tags","HTML tags must be properly nested and closed: "+n)};return n.UNSAFE_restoreLegacyHtmlPrefilter=function(){n.migrateEnablePatches("self-closed-tags")},u(n,"htmlPrefilter",function(n){return ti(n),n.replace(rt,"<$1><\/$2>")},"self-closed-tags"),n.migrateDisablePatches("self-closed-tags"),ft=n.fn.offset,u(n.fn,"offset",function(){var n=this[0];return n&&(!n.nodeType||!n.getBoundingClientRect)?(i("offset-valid-elem","jQuery.fn.offset() requires a valid DOM element"),arguments.length?this:undefined):ft.apply(this,arguments)},"offset-valid-elem"),n.ajax&&(et=n.param,u(n,"param",function(t,r){var u=n.ajaxSettings&&n.ajaxSettings.traditional;return r===undefined&&u&&(i("param-ajax-traditional","jQuery.param() no longer uses jQuery.ajaxSettings.traditional"),r=u),et.call(this,t,r)},"param-ajax-traditional")),r(n.fn,"andSelf",n.fn.addBack,"andSelf","jQuery.fn.andSelf() is deprecated and removed, use jQuery.fn.addBack()"),n.Deferred&&(b=n.Deferred,ot=[["resolve","done",n.Callbacks("once memory"),n.Callbacks("once memory"),"resolved"],["reject","fail",n.Callbacks("once memory"),n.Callbacks("once memory"),"rejected"],["notify","progress",n.Callbacks("memory"),n.Callbacks("memory")]],u(n,"Deferred",function(t){function f(){var t=arguments;return n.Deferred(function(r){n.each(ot,function(n,f){var e=typeof t[n]=="function"&&t[n];i[f[1]](function(){var n=e&&e.apply(this,arguments);n&&typeof n.promise=="function"?n.promise().done(r.resolve).fail(r.reject).progress(r.notify):r[f[0]+"With"](this===u?r.promise():this,e?[n]:arguments)})});t=null}).promise()}var i=b(),u=i.promise();return r(i,"pipe",f,"deferred-pipe","deferred.pipe() is deprecated"),r(u,"pipe",f,"deferred-pipe","deferred.pipe() is deprecated"),t&&t.call(i,i),i},"deferred-pipe"),n.Deferred.exceptionHook=b.exceptionHook),n});
/*!
 * jQuery blockUI plugin
 * Version 2.70.0-2014.11.23
 * Requires jQuery v1.7 or later
 *
 * Examples at: malsup.com/jquery/block/
 * Copyright (c) 2007-2013 M. Alsup
 * Dual licensed under the MIT and GPL licenses:
 * http://www.opensource.org/licenses/mit-license.php
 * http://www.gnu.org/licenses/gpl.html
 *
 * Thanks to Amir-Hossein Sobhi for some excellent contributions!
 */
(function(){"use strict";function n(n){function s(s,h){var rt,ut,p=s==window,l=h&&h.message!==undefined?h.message:undefined,g,k,d,tt,nt,w,b,it,ft,et,at;if(h=n.extend({},n.blockUI.defaults,h||{}),!h.ignoreIfBlocked||!n(s).data("blockUI.isBlocked")){if(h.overlayCSS=n.extend({},n.blockUI.defaults.overlayCSS,h.overlayCSS||{}),rt=n.extend({},n.blockUI.defaults.css,h.css||{}),h.onOverlayClick&&(h.overlayCSS.cursor="pointer"),ut=n.extend({},n.blockUI.defaults.themedCSS,h.themedCSS||{}),l=l===undefined?h.message:l,p&&t&&e(window,{fadeOut:0}),l&&typeof l!="string"&&(l.parentNode||l.jquery)&&(g=l.jquery?l[0]:l,k={},n(s).data("blockUI.history",k),k.el=g,k.parent=g.parentNode,k.display=g.style.display,k.position=g.style.position,k.parent&&k.parent.removeChild(g)),n(s).data("blockUI.onUnblock",h.onUnblock),d=h.baseZ,tt=f||h.forceIframe?n('<iframe class="blockUI" style="z-index:'+d+++';display:none;border:none;margin:0;padding:0;position:absolute;width:100%;height:100%;top:0;left:0" src="'+h.iframeSrc+'"><\/iframe>'):n('<div class="blockUI" style="display:none"><\/div>'),nt=h.theme?n('<div class="blockUI blockOverlay ui-widget-overlay" style="z-index:'+d+++';display:none"><\/div>'):n('<div class="blockUI blockOverlay" style="z-index:'+d+++';display:none;border:none;margin:0;padding:0;width:100%;height:100%;top:0;left:0"><\/div>'),h.theme&&p?(b='<div class="blockUI '+h.blockMsgClass+' blockPage ui-dialog ui-widget ui-corner-all" style="z-index:'+(d+10)+';display:none;position:fixed">',h.title&&(b+='<div class="ui-widget-header ui-dialog-titlebar ui-corner-all blockTitle">'+(h.title||"&nbsp;")+"<\/div>"),b+='<div class="ui-widget-content ui-dialog-content"><\/div>',b+="<\/div>"):h.theme?(b='<div class="blockUI '+h.blockMsgClass+' blockElement ui-dialog ui-widget ui-corner-all" style="z-index:'+(d+10)+';display:none;position:absolute">',h.title&&(b+='<div class="ui-widget-header ui-dialog-titlebar ui-corner-all blockTitle">'+(h.title||"&nbsp;")+"<\/div>"),b+='<div class="ui-widget-content ui-dialog-content"><\/div>',b+="<\/div>"):b=p?'<div class="blockUI '+h.blockMsgClass+' blockPage" style="z-index:'+(d+10)+';display:none;position:fixed"><\/div>':'<div class="blockUI '+h.blockMsgClass+' blockElement" style="z-index:'+(d+10)+';display:none;position:absolute"><\/div>',w=n(b),l&&(h.theme?(w.css(ut),w.addClass("ui-widget-content")):w.css(rt)),h.theme||nt.css(h.overlayCSS),nt.css("position",p?"fixed":"absolute"),(f||h.forceIframe)&&tt.css("opacity",0),it=[tt,nt,w],ft=p?n("body"):n(s),n.each(it,function(){this.appendTo(ft)}),h.theme&&h.draggable&&n.fn.draggable&&w.draggable({handle:".ui-dialog-titlebar",cancel:"li"}),et=v&&(!n.support.boxModel||n("object,embed",p?null:s).length>0),o||et){if(p&&h.allowBodyStretch&&n.support.boxModel&&n("html,body").css("height","100%"),(o||!n.support.boxModel)&&!p)var ot=r(s,"borderTopWidth"),st=r(s,"borderLeftWidth"),ht=ot?"(0 - "+ot+")":0,ct=st?"(0 - "+st+")":0;n.each(it,function(n,t){var i=t[0].style,r,u;i.position="absolute";n<2?(p?i.setExpression("height","Math.max(document.body.scrollHeight, document.body.offsetHeight) - (jQuery.support.boxModel?0:"+h.quirksmodeOffsetHack+') + "px"'):i.setExpression("height",'this.parentNode.offsetHeight + "px"'),p?i.setExpression("width",'jQuery.support.boxModel && document.documentElement.clientWidth || document.body.clientWidth + "px"'):i.setExpression("width",'this.parentNode.offsetWidth + "px"'),ct&&i.setExpression("left",ct),ht&&i.setExpression("top",ht)):h.centerY?(p&&i.setExpression("top",'(document.documentElement.clientHeight || document.body.clientHeight) / 2 - (this.offsetHeight / 2) + (blah = document.documentElement.scrollTop ? document.documentElement.scrollTop : document.body.scrollTop) + "px"'),i.marginTop=0):!h.centerY&&p&&(r=h.css&&h.css.top?parseInt(h.css.top,10):0,u="((document.documentElement.scrollTop ? document.documentElement.scrollTop : document.body.scrollTop) + "+r+') + "px"',i.setExpression("top",u))})}if(l&&(h.theme?w.find(".ui-widget-content").append(l):w.append(l),(l.jquery||l.nodeType)&&n(l).show()),(f||h.forceIframe)&&h.showOverlay&&tt.show(),h.fadeIn){var lt=h.onBlock?h.onBlock:u,vt=h.showOverlay&&!l?lt:u,yt=l?lt:u;h.showOverlay&&nt._fadeIn(h.fadeIn,vt);l&&w._fadeIn(h.fadeIn,yt)}else h.showOverlay&&nt.show(),l&&w.show(),h.onBlock&&h.onBlock.bind(w)();c(1,s,h);p?(t=w[0],i=n(h.focusableElements,t),h.focusInput&&setTimeout(a,20)):y(w[0],h.centerX,h.centerY);h.timeout&&(at=setTimeout(function(){p?n.unblockUI(h):n(s).unblock(h)},h.timeout),n(s).data("blockUI.timeout",at))}}function e(r,u){var o,s=r==window,e=n(r),l=e.data("blockUI.history"),a=e.data("blockUI.timeout"),f;a&&(clearTimeout(a),e.removeData("blockUI.timeout"));u=n.extend({},n.blockUI.defaults,u||{});c(0,r,u);u.onUnblock===null&&(u.onUnblock=e.data("blockUI.onUnblock"),e.removeData("blockUI.onUnblock"));f=s?n("body").children().filter(".blockUI").add("body > .blockUI"):e.find(">.blockUI");u.cursorReset&&(f.length>1&&(f[1].style.cursor=u.cursorReset),f.length>2&&(f[2].style.cursor=u.cursorReset));s&&(t=i=null);u.fadeOut?(o=f.length,f.stop().fadeOut(u.fadeOut,function(){--o==0&&h(f,l,u,r)})):h(f,l,u,r)}function h(t,i,r,u){var f=n(u);if(!f.data("blockUI.isBlocked")){if(t.each(function(){this.parentNode&&this.parentNode.removeChild(this)}),i&&i.el&&(i.el.style.display=i.display,i.el.style.position=i.position,i.el.style.cursor="default",i.parent&&i.parent.appendChild(i.el),f.removeData("blockUI.history")),f.data("blockUI.static")&&f.css("position","static"),typeof r.onUnblock=="function")r.onUnblock(u,r);var e=n(document.body),o=e.width(),s=e[0].style.width;e.width(o-1).width(o);e[0].style.width=s}}function c(i,r,u){var f=r==window,o=n(r),e;(i||(!f||t)&&(f||o.data("blockUI.isBlocked")))&&(o.data("blockUI.isBlocked",i),f&&u.bindEvents&&(!i||u.showOverlay))&&(e="mousedown mouseup keydown keypress keyup touchstart touchend touchmove",i?n(document).bind(e,u,l):n(document).unbind(e,l))}function l(r){var u,f;if(r.type==="keydown"&&r.keyCode&&r.keyCode==9&&t&&r.data.constrainTabKey){var e=i,s=!r.shiftKey&&r.target===e[e.length-1],o=r.shiftKey&&r.target===e[0];if(s||o)return setTimeout(function(){a(o)},10),!1}if(u=r.data,f=n(r.target),f.hasClass("blockOverlay")&&u.onOverlayClick)u.onOverlayClick(r);return f.parents("div."+u.blockMsgClass).length>0?!0:f.parents().children().filter("div.blockUI").length===0}function a(n){if(i){var t=i[n===!0?i.length-1:0];t&&t.focus()}}function y(n,t,i){var u=n.parentNode,f=n.style,e=(u.offsetWidth-n.offsetWidth)/2-r(u,"borderLeftWidth"),o=(u.offsetHeight-n.offsetHeight)/2-r(u,"borderTopWidth");t&&(f.left=e>0?e+"px":"0");i&&(f.top=o>0?o+"px":"0")}function r(t,i){return parseInt(n.css(t,i),10)||0}var t,i;n.fn._fadeIn=n.fn.fadeIn;var u=n.noop||function(){},f=/MSIE/.test(navigator.userAgent),o=/MSIE 6.0/.test(navigator.userAgent)&&!/MSIE 8.0/.test(navigator.userAgent),p=document.documentMode||0,v=n.isFunction(document.createElement("div").style.setExpression);n.blockUI=function(n){s(window,n)};n.unblockUI=function(n){e(window,n)};n.growlUI=function(t,i,r,u){var f=n('<div class="growlUI"><\/div>'),e,o;t&&f.append("<h1>"+t+"<\/h1>");i&&f.append("<h2>"+i+"<\/h2>");r===undefined&&(r=3e3);e=function(t){t=t||{};n.blockUI({message:f,fadeIn:typeof t.fadeIn!="undefined"?t.fadeIn:700,fadeOut:typeof t.fadeOut!="undefined"?t.fadeOut:1e3,timeout:typeof t.timeout!="undefined"?t.timeout:r,centerY:!1,showOverlay:!1,onUnblock:u,css:n.blockUI.defaults.growlCSS})};e();o=f.css("opacity");f.mouseover(function(){e({fadeIn:0,timeout:3e4});var t=n(".blockMsg");t.stop();t.fadeTo(300,1)}).mouseout(function(){n(".blockMsg").fadeOut(1e3)})};n.fn.block=function(t){if(this[0]===window)return n.blockUI(t),this;var i=n.extend({},n.blockUI.defaults,t||{});return this.each(function(){var t=n(this);i.ignoreIfBlocked&&t.data("blockUI.isBlocked")||t.unblock({fadeOut:0})}),this.each(function(){n.css(this,"position")=="static"&&(this.style.position="relative",n(this).data("blockUI.static",!0));this.style.zoom=1;s(this,t)})};n.fn.unblock=function(t){return this[0]===window?(n.unblockUI(t),this):this.each(function(){e(this,t)})};n.blockUI.version=2.7;n.blockUI.defaults={message:"<h1>Please wait...<\/h1>",title:null,draggable:!0,theme:!1,css:{padding:0,margin:0,width:"30%",top:"40%",left:"35%",textAlign:"center",color:"#000",border:"3px solid #aaa",backgroundColor:"#fff",cursor:"wait"},themedCSS:{width:"30%",top:"40%",left:"35%"},overlayCSS:{backgroundColor:"#000",opacity:.6,cursor:"wait"},cursorReset:"default",growlCSS:{width:"350px",top:"10px",left:"",right:"10px",border:"none",padding:"5px",opacity:.6,cursor:"default",color:"#fff",backgroundColor:"#000","-webkit-border-radius":"10px","-moz-border-radius":"10px","border-radius":"10px"},iframeSrc:/^https/i.test(window.location.href||"")?"javascript:false":"about:blank",forceIframe:!1,baseZ:1e3,centerX:!0,centerY:!0,allowBodyStretch:!0,bindEvents:!0,constrainTabKey:!0,fadeIn:200,fadeOut:400,timeout:0,showOverlay:!0,focusInput:!0,focusableElements:":input:enabled:visible",onBlock:null,onUnblock:null,onOverlayClick:null,quirksmodeOffsetHack:4,blockMsgClass:"blockMsg",ignoreIfBlocked:!1};t=null;i=[]}typeof define=="function"&&define.amd&&define.amd.jQuery?define(["jquery"],n):n(jQuery)})();
/*!
 * jQuery Cookie Plugin v1.4.1
 * https://github.com/carhartl/jquery-cookie
 *
 * Copyright 2013 Klaus Hartl
 * Released under the MIT license
 */
(function(n){typeof define=="function"&&define.amd?define(["jquery"],n):typeof exports=="object"?n(require("jquery")):n(jQuery)})(function(n){function i(n){return t.raw?n:encodeURIComponent(n)}function f(n){return t.raw?n:decodeURIComponent(n)}function e(n){return i(t.json?JSON.stringify(n):String(n))}function o(n){n.indexOf('"')===0&&(n=n.slice(1,-1).replace(/\\"/g,'"').replace(/\\\\/g,"\\"));try{return n=decodeURIComponent(n.replace(u," ")),t.json?JSON.parse(n):n}catch(i){}}function r(i,r){var u=t.raw?i:o(i);return n.isFunction(r)?r(u):u}var u=/\+/g,t=n.cookie=function(u,o,s){var y,a,h,v,c,p;if(o!==undefined&&!n.isFunction(o))return s=n.extend({},t.defaults,s),typeof s.expires=="number"&&(y=s.expires,a=s.expires=new Date,a.setTime(+a+y*864e5)),document.cookie=[i(u),"=",e(o),s.expires?"; expires="+s.expires.toUTCString():"",s.path?"; path="+s.path:"",s.domain?"; domain="+s.domain:"",s.secure?"; secure":""].join("");for(h=u?undefined:{},v=document.cookie?document.cookie.split("; "):[],c=0,p=v.length;c<p;c++){var w=v[c].split("="),b=f(w.shift()),l=w.join("=");if(u&&u===b){h=r(l,o);break}u||(l=r(l))===undefined||(h[b]=l)}return h};t.defaults={};n.removeCookie=function(t,i){return n.cookie(t)===undefined?!1:(n.cookie(t,"",n.extend({},i,{expires:-1})),!n.cookie(t))}});
/**
 * @name        jQuery FullScreen Plugin
 * <AUTHOR> Angelov, Morten Sjøgren
 * @version     1.2
 * @url         http://tutorialzine.com/2012/02/enhance-your-website-fullscreen-api/
 * @license     MIT License
 */
(function(n){"use strict";function r(){var n=document.documentElement;return"requestFullscreen"in n||"mozRequestFullScreen"in n&&document.mozFullScreenEnabled||"webkitRequestFullScreen"in n}function u(n){n.requestFullscreen?n.requestFullscreen():n.mozRequestFullScreen?n.mozRequestFullScreen():n.webkitRequestFullScreen&&n.webkitRequestFullScreen(Element.ALLOW_KEYBOARD_INPUT)}function i(){return document.fullscreen||document.mozFullScreen||document.webkitIsFullScreen||!1}function t(){document.exitFullscreen?document.exitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.webkitCancelFullScreen&&document.webkitCancelFullScreen()}function f(t){n(document).on("fullscreenchange mozfullscreenchange webkitfullscreenchange",function(){t(i())})}n.support.fullscreen=r();n.fn.fullScreen=function(r){if(!n.support.fullscreen||this.length!==1)return this;if(i())return t(),this;var o=n.extend({background:"#111",callback:n.noop(),fullscreenClass:"fullScreen"},r),e=this,s=n("<div>",{css:{"overflow-y":"auto",background:o.background,width:"100%",height:"100%"}}).insertBefore(e).append(e);return e.addClass(o.fullscreenClass),u(s.get(0)),s.click(function(n){n.target==this&&t()}),e.cancel=function(){return t(),e},f(function(t){t||(n(document).off("fullscreenchange mozfullscreenchange webkitfullscreenchange"),e.removeClass(o.fullscreenClass).insertBefore(s),s.remove());o.callback&&o.callback(t)}),e};n.fn.cancelFullScreen=function(){return t(),this}})(jQuery);