/*! jquery.views.js v1.0.13: http://jsviews.com/ */
!function(e,t){var n=t.jQuery;"object"==typeof exports?module.exports=n?e(t,n):function(n){return e(t,n)}:"function"==typeof define&&define.amd?define(["jquery","./jsrender","./jquery.observable"],function(n,i,r){return e(t,n,i,r)}):e(t,!1)}(function(e,t,n,i){"use strict";function r(e,t,n,i,r){var a,o,l,s,d,p,c,f,v,g,u,_,h,m,b,x,C,k,y,E;if(i&&i._tgId&&(k=i,i=k._tgId,k.bindTo||(S(vt[i],k),k.bindTo=[0])),(p=vt[i])&&(u=p.to))for(u=u[t||0],a=p.linkCtx,v=a.elem,d=a.view,k=a.tag,!k&&u._cxp&&(k=u._cxp.path!==xe&&u._cxp.tag,c=e[0],e=[],e[u._cxp.ind]=c),k&&(k._.chg=1,(l=k.convertBack)&&(o=fe(l)?l:d.getRsc("converters",l))),"SELECT"===v.nodeName?(v.multiple&&null===e[0]&&(e=[[]]),v._jsvSel=e):v._jsvSel&&(y=v._jsvSel,E=jt(v.value,y),E>-1&&!v.checked?y.splice(E,1):E<0&&v.checked&&y.push(v.value),e=[y.slice()]),f=e,x=u.length,o&&(e=o.apply(k,e),void 0===e&&(u=[]),Z(e)&&(e.arg0===!1||1!==x&&e.length===x&&!e.arg0)||(e=[e]));x--;)if((_=u[x])&&(_=typeof _===me?[a.data,_]:_,s=_[0],h=_.tag,c=(s&&s._ocp&&!s._vw?f:e)[x],!(void 0===c||k&&k.onBeforeUpdateVal&&k.onBeforeUpdateVal(r,{change:"change",data:s,path:_[1],index:x,tagElse:t,value:c})===!1)))if(h)void 0!==(C=h._.toIndex[_.ind])&&h.updateValue(c,C,_.tagElse,void 0,void 0,r),h.setValue(c,_.ind,_.tagElse);else if(void 0!==c&&s){if((h=r&&(g=r.target)._jsvInd===x&&g._jsvLkEl)&&void 0!==(C=h._.fromIndex[x])&&h.setValue(f[x],C,g._jsvElse),s._cpfn)for(b=a._ctxCb,m=s,s=a.data,m._cpCtx&&(s=m.data,b=m._cpCtx);m&&m.sb;)s=b(m),m=m.sb;Y(s,n).setProperty(_[1],c,void 0,_.isCpfn)}if(k)return k._.chg=void 0,k}function a(e){var n,i,r=e.target,a=c(r),o=tt[a];if(!r._jsvTr||e.delegateTarget!==je&&"number"!==r.type||"input"===e.type){for(i=fe(a)?a(r):o?t(r)[o]():t(r).attr(a),r._jsvChg=1,yt.lastIndex=0;n=yt.exec(r._jsvBnd);)$(i,r._jsvInd,r._jsvElse,void 0,n[1],e);r._jsvChg=void 0}}function o(e,t){var n,i,r,a,o,l,d,p,f,v=this,u=v.fn,_=v.tag,h=v.data,m=v.elem,b=v.convert,x=m.parentNode,C=v.view,k=C._lc,y=t&&M(C,Oe,_);if(x&&(!y||y.call(_||v,e,t)!==!1)&&(!t||"*"===e.data.prop||e.data.prop===t.path)){if(C._lc=v,t||v._toLk){if(v._toLk=0,u._er)try{i=u(h,C,de)}catch(E){o=u._er,l=Ee(E,C,new Function("data,view","return "+o+";")(h,C)),i=[{props:{},args:[l],tag:_}]}else i=u(h,C,de);if(n=_&&_.attr||v.attr||(v._dfAt=c(m,!0,void 0!==b)),v._dfAt===We&&(_&&_.parentElem||v.elem).type===Re&&(n=Fe),_){if(a=o||_._er,i=i[0]?i:[i],r=!a&&(_.onUpdate===!1||t&&fe(_.onUpdate)&&_.onUpdate(e,t,i)===!1),L(_,i,a),_._.chg&&(n===be||n===We)||r||n===Qe)return A(_,e,t),_._.chg||g(v,h,m),C._lc=k,t&&(y=M(C,De,_))&&y.call(_||v,e,t),void(_.tagCtx.props.dataMap&&_.tagCtx.props.dataMap.map(_.tagCtx.args[0],_.tagCtx,_.tagCtx.map,isRenderCall||!_._.bnd));for(_.onUnbind&&_.onUnbind(_.tagCtx,v,_.ctx,e,t),_.linkedElems=_.linkedElem=_.mainElem=_.displayElem=void 0,f=_.tagCtxs.length;f--;)p=_.tagCtxs[f],p.linkedElems=p.mainElem=p.displayElem=void 0;i=":"===_.tagName?de._cnvt(_.convert,C,i[0]):de._tag(_,C,C.tmpl,i,!0,l)}else u._tag&&(b=""===b?Ge:b,i=b?de._cnvt(b,C,i[0]||i):de._tag(u._tag,C,C.tmpl,i,!0,l),R(_=v.tag),n=v.attr||n);(d=_&&(!_.inline||v.fn._lr)&&_.template)&&g(v,h,m),s(i,v,n,_),v._noUpd=0,_&&(_._er=o,A(_,e,t))}d||g(v,h,m),_&&_._.ths&&_.updateValue(_,_.bindTo?_.bindTo.length:1),t&&(y=M(C,De,_))&&y.call(_||v,e,t),C._lc=k}}function l(e,t){e._df=t,e[(t?"set":"remove")+"Attribute"](Ze,"")}function s(n,i,r,a){var o,s,d,p,c,f,v,g,_,h,m,b,x,C,k=!(r===Qe||void 0===n||i._noUpd||(r===We||r===be)&&!a&&i.elem._jsvChg),y=i.data,E=a&&a.parentElem||i.elem,w=E.parentNode,j=t(E),A=i.view,T=i._val,V=a;return a&&(a._.unlinked=!0,a.parentElem=a.parentElem||i.expr||a._elCnt?E:w,s=a._prv,d=a._nxt),k?("visible"===r&&(r="css-display"),/^css-/.test(r)?("visible"===i.attr&&(x=(E.currentStyle||wt.call(e,E,"")).display,n?(n=E._jsvd||x,n!==Qe||(n=ft[b=E.nodeName])||(m=le.createElement(b),le.body.appendChild(m),n=ft[b]=(m.currentStyle||wt.call(e,m,"")).display,le.body.removeChild(m))):(E._jsvd=x,n=Qe)),(V=V||T!==n)&&t.style(E,r.slice(4),n)):"link"!==r&&(/^data-/.test(r)?t.data(E,r.slice(5),n):/^prop-/.test(r)?(f=!0,r=r.slice(5)):r===Fe?(f=!0,E.name&&Z(n)?(E._jsvSel=n,n=jt(E.value,n)>-1):n=n&&"false"!==n):r===Ke?(f=!0,r=Fe,n=E.value===n):"selected"===r||"disabled"===r||"multiple"===r||"readonly"===r?n=n&&"false"!==n?r:null:r===We&&"SELECT"===E.nodeName&&(E._jsvSel=Z(n)?n:""+n),(o=tt[r])?r===be?a&&a.inline?(c=a.nodes(!0),a._elCnt&&(s&&s!==d?O(s,d,E,a._tgId,"^",!0):(v=s?s.getAttribute(qe):E._df,g=a._tgId+"^",_=v.indexOf("#"+g)+1,h=v.indexOf("/"+g),_&&h>0&&(_+=g.length,h>_&&(D(v.slice(_,h)),v=v.slice(0,_)+v.slice(h),s?s.setAttribute(qe,v):E._df&&l(E,v)))),s=s?s.previousSibling:d?d.previousSibling:E.lastChild),t(c).remove(),p=A.link(A.data,E,s,d,n,a&&{tag:a._tgId})):(k=k&&T!==n,k&&(j.empty(),p=A.link(y,E,s,d,n,a&&{tag:a._tgId}))):E._jsvSel?j[o](n):((V=V||T!==n)&&("text"===r&&E.children&&!E.children[0]?E[Ue]=null===n?"":n:j[o](n)),void 0===(C=w._jsvSel)||r!==We&&void 0!==j.attr(We)||(E.selected=jt(""+n,Z(C)?C:[C])>-1)):(V=V||T!==n)&&j[f?"prop":"attr"](r,void 0!==n||f?n:null)),i._val=n,u(p),V):void(i._val=n)}function d(e,t){var n=this,i=M(n,Oe,n.tag),r=M(n,De,n.tag);if(!i||i.call(n,e,t)!==!1){if(t){var a=t.change,o=t.index,l=t.items;switch(n._.srt=t.refresh,a){case"insert":n.addViews(o,l,t._dly);break;case"remove":n.removeViews(o,l.length,void 0,t._dly);break;case"move":n.moveViews(t.oldIndex,o,l.length);break;case"refresh":n._.srt=void 0,n.fixIndex(0)}}r&&r.call(n,e,t)}}function p(e){var n,i,r=e.type,a=e.data,o=e._.bnd;!e._.useKey&&o&&((i=e._.bndArr)&&(t([i[1]]).off(he,i[0]),e._.bndArr=void 0),o!==!!o?r?o._.arrVws[e._.id]=e:delete o._.arrVws[e._.id]:r&&a&&(n=function(t){t.data&&t.data.off||d.apply(e,arguments)},t([a]).on(he,n),e._.bndArr=[n,a]))}function c(e,t,n){var i=e.nodeName.toLowerCase(),r=ue._fe[i]||e.contentEditable===Ge&&{to:be,from:be};return r?t?"input"===i&&e.type===Ke?Ke:r.to:r.from:t?n?"text":be:""}function f(e,n,i,r,a,o,l){var s,d,p,c,f,v=e.parentElem,g=e._prv,_=e._nxt,h=e._elCnt;if(g&&g.parentNode!==v&&ye("Missing parentNode"),l){c=e.nodes(),h&&g&&g!==_&&O(g,_,v,e._.id,"_",!0),e.removeViews(void 0,void 0,!0),d=_,h&&(g=g?g.previousSibling:_?_.previousSibling:v.lastChild),t(c).remove();for(f in e._.bnds)P(f)}else{if(n){if(p=r[n-1],!p)return!1;g=p._nxt}h?(d=g,g=d?d.previousSibling:v.lastChild):d=g.nextSibling}s=i.render(a,o,e._.useKey&&l,e,l||n,!0),u(e.link(a,v,g,d,s,p))}function v(e,t,n){var i,r;return n?(r="^`",R(n),i=n._tgId,i||(vt[i=gt++]=n,n._tgId=""+i)):(r="_`",Ne[i=t._.id]=t),"#"+i+r+(void 0!=e?e:"")+"/"+i+r}function g(e,t,n){var i,r,a,o,l,s,d,p,c,f,v,g,u=e.tag,_=!u,h=e.convertBack,m=e._hdl;if(t="object"==typeof t&&t,u&&((c=u.convert)&&(c=c===Ge?u.tagCtx.props.convert:c,c=e.view.getRsc("converters",c)||c,c=c&&c.depends,c=c&&de._dp(c,t,m)),(f=u.tagCtx.props.depends||u.depends)&&(f=de._dp(f,u,m),c=c?c.concat(f):f),g=u.linkedElems),c=c||[],!e._depends||""+e._depends!=""+c){if(l=e.fn.deps.slice(),e._depends&&(v=e._depends.bdId,Y._apply(1,[t],l,e._depends,m,e._ctxCb,!0)),u){for(r=u.boundProps.length;r--;)for(d=u.boundProps[r],a=u._.bnd.paths.length;a--;)p=u._.bnd.paths[a]["_"+d],p&&p.length&&p.skp&&(l=l.concat(p));_=void 0===u.onArrayChange||u.onArrayChange===!0}for(r=l.length;r--;)s=l[r],s._cpfn&&(l[r]=ce({},s));if(i=Y._apply(_?0:1,[t],l,c,m,e._ctxCb),v||(v=e._bndId||""+gt++,e._bndId=void 0,n._jsvBnd=(n._jsvBnd||"")+"&"+v,e.view._.bnds[v]=v),i.elem=n,i.linkCtx=e,i._tgId=v,c.bdId=v,e._depends=c,vt[v]=i,(g||void 0!==h||u&&u.bindTo)&&S(i,u,h),g)for(r=g.length;r--;)for(o=g[r],a=o&&o.length;a--;)o[a]._jsvLkEl?o[a]._jsvBnd||(o[a]._jsvBnd="&"+v+"+"):(o[a]._jsvLkEl=u,I(u,o[a]),o[a]._jsvBnd="&"+v+"+");else void 0!==h&&I(u,n);u&&!u.inline&&(u.flow||n.setAttribute(qe,(n.getAttribute(qe)||"")+"#"+v+"^/"+v+"^"),u._tgId=""+v)}}function u(e){var t;if(e)for(;t=e.pop();)t._hdl()}function _(e,t,n,i,r,a,o){return h(this,e,t,n,i,r,a,o)}function h(e,n,i,r,o,s,d,p){if(r===!0?(o=r,r=void 0):r="object"!=typeof r?void 0:ce({},r),e&&n){n=n.jquery?n:t(n),je||(je=le.body,Be="oninput"in je,t(je).on(Me,a).on("blur.jsv","[contenteditable]",a));for(var c,f,g,_,h,m,x,C,k,E,w=v,j=r&&"replace"===r.target,A=n.length;A--;){if(x=n[A],E=s||Te(x),typeof e===me)b(k=[],e,x,E,void 0,"expr",i,r);else{if(void 0!==e.markup)j&&(m=x.parentNode),E._.scp=!0,g=e.render(i,r,o,E,void 0,w,!0),E._.scp=void 0,m?(d=x.previousSibling,p=x.nextSibling,t.cleanData([x],!0),m.removeChild(x),x=m):(d=p=void 0,t(x).empty());else{if(e!==!0||E!==oe)break;C={lnk:"top"}}if(x._df&&!p){for(_=y(x._df,!0,ht),c=0,f=_.length;c<f;c++)h=_[c],(h=Ne[h.id])&&void 0!==h.data&&h.parent.removeViews(h._.key,void 0,!0);l(x)}k=E.link(i,x,d,p,g,C,r)}u(k)}}return n}function m(e,n,i,r,a,o,s,c){function f(e,t,n,i,r,o,l,s,d,p,c,f,v,g){var u,_,m="";return g?(h=0,e):(x=(d||p||"").toLowerCase(),i=i||c,n=n||v,Z&&!n&&(!e||i||x||o&&!h)&&(Z=void 0,Y=be.shift()),i=i||n,i&&(i=i.toLowerCase(),h=0,Z=void 0,z&&(n||v?ct[Y]||/;svg;|;math;/.test(";"+be.join(";")+";")||(u="'<"+Y+".../"):ct[i]?u="'</"+i:be.length&&i===Y||(u="Mismatch: '</"+i),u&&Ce(u+">' in:\n"+a)),re=ie,Y=be.shift(),ie=dt[Y],c=c?"</"+c+">":"",re&&(_e+=oe,oe="",ie?_e+="-":(m=c+Ye+"@"+_e+Je+(f||""),_e=xe.shift()))),ie&&!h?(o?oe+=o:t=c||v||"",x&&(t+=x,oe&&(t+=" "+qe+'="'+oe+'"',oe=""))):t=o?t+m+r+(h?"":Ye+o+Je)+s+x:m||e,z&&l&&(h&&Ce("{^{ within elem markup ("+h+' ). Use data-link="..."'),"#"===o.charAt(0)?be.unshift(o.slice(1)):o.slice(1)!==(_=be.shift())&&Ce("Closing tag for {^{...}} under different elem: <"+_+">")),x&&(h=x,be.unshift(Y),Y=x.slice(1),z&&be[0]&&be[0]===pt[Y]&&ye("Parent of <tr> must be <tbody>"),Z=ct[Y],(ie=dt[Y])&&!re&&(xe.unshift(_e),_e=""),re=ie,_e&&ie&&(_e+="+")),t)}function g(e,t){var i,r,a,o,s,c,f,g=[];if(e){for("@"===e._tkns.charAt(0)&&(t=N.previousSibling,N.parentNode.removeChild(N),N=void 0),I=e.length;I--;){if(B=e[I],a=B.ch,i=B.path)for(V=i.length-1;r=i.charAt(V--);)"+"===r?"-"===i.charAt(V)?(V--,t=t.previousElementSibling):t=t.parentNode:t=t.lastElementChild;"^"===a?(x=vt[s=B.id])&&(f=t&&(!N||N.parentNode!==t),N&&!f||(x.parentElem=t),B.elCnt&&f&&l(t,(B.open?"#":"/")+s+a+(t._df||"")),g.push([f?null:N,B])):(L=Ne[s=B.id])&&(L.parentElem||(L.parentElem=t||N&&N.parentNode||n,L._.onRender=v,L._.onArrayChange=d,p(L)),o=L.parentElem,B.open?(L._elCnt=B.elCnt,t&&!N?l(t,"#"+s+a+(t._df||"")):(L._prv||l(o,C(o._df,"#"+s+a)),L._prv=N)):(!t||N&&N.parentNode===t?N&&(L._nxt||l(o,C(o._df,"/"+s+a)),L._nxt=N):(l(t,"/"+s+a+(t._df||"")),L._nxt=void 0),(c=M(L,$e)||we)&&c.call(L.ctx.tag,L)))}for(I=g.length;I--;)he.push(g[I])}return!e||e.elCnt}function u(e){var t,n,i;if(e)for(I=e.length,V=0;V<I;V++)if(B=e[V],x=vt[B.id],!x._is&&x.linkCtx&&(n=x=x.linkCtx.tag,i=x.tagName===K,!x.flow||i)){if(!R){for(t=1;n=n.parent;)t++;Q=Q||t}!R&&t!==Q||K&&!i||F.push(x)}}function _(){var o,l,d="",p={},c=Ie+(fe?",["+Ze+"]":"");for(S=st?n.querySelectorAll(c):t(c,n).get(),T=S.length,i&&i.innerHTML&&(U=st?i.querySelectorAll(c):t(c,i).get(),i=U.length?U[U.length-1]:i),Q=0,j=0;j<T;j++)if(N=S[j],i&&!de)de=N===i;else{if(r&&N===r){fe&&(d+=k(N));break}if(N.parentNode)if(fe){if(d+=k(N),N._df){for(o=j+1;o<T&&N.contains(S[o]);)o++;p[o-1]=N._df}p[j]&&(d+=p[j]||"")}else ce&&(B=y(N,void 0,bt))&&(B=B[0])&&(pe=pe?B.id!==pe&&pe:B.open&&B.id),!pe&&je(y(N))&&N.getAttribute(Ve)&&he.push([N])}if(fe&&(d+=n._df||"",(l=d.indexOf("#"+fe.id)+1)&&(d=d.slice(l+fe.id.length)),l=d.indexOf("/"+fe.id),l+1&&(d=d.slice(0,l)),u(y(d,void 0,Ct))),void 0===a&&n.getAttribute(Ve)&&he.push([n]),E(i,ie),E(r,ie),!fe)for(ie&&_e+oe&&(N=r,_e&&(r?g(y(_e+"+",!0),r):g(y(_e,!0),n)),g(y(oe,!0),n),r&&(d=r.getAttribute(qe),(T=d.indexOf(se)+1)&&(d=d.slice(T+se.length-1)),r.setAttribute(qe,oe+d))),T=he.length,j=0;j<T;j++)N=he[j],P=N[1],N=N[0],P?(x=vt[P.id])&&((m=x.linkCtx)&&(x=m.tag,x.linkCtx=m),P.open?(N&&(x.parentElem=N.parentNode,x._prv=N),x._elCnt=P.elCnt,L=x.tagCtx.view,b(Ee,void 0,x._prv,L,P.id)):(x._nxt=N,x._.unlinked&&!x._toLk&&(H=x.tagCtx,L=H.view,A(x)))):b(Ee,N.getAttribute(Ve),N,Te(N),void 0,ce,e,s)}var h,m,x,j,T,V,I,S,N,L,B,P,U,q,O,D,$,F,R,K,H,z,Q,W,X,G,J,Y,Z,ee,te,ne,ie,re,ae,oe,se,de,pe,ce,fe,ve=this,ge=ve._.id+"_",_e="",he=[],be=[],xe=[],Ee=[],we=M(ve,$e),je=g;if(o&&(o.tmpl?O="/"+o._.id+"_":(ce=o.lnk,o.tag&&(ge=o.tag+"^",o=!0),(fe=o.get)&&(je=u,F=fe.tags,R=fe.deep,K=fe.name)),o=o===!0),n=n?typeof n===me?t(n)[0]:n.jquery?n[0]:n:ve.parentElem||le.body,z=!ue.noValidate&&n.contentEditable!==Ge,Y=n.tagName.toLowerCase(),ie=!!dt[Y],i=i&&w(i,ie),r=r&&w(r,ie)||null,void 0!=a){if(te=le.createElement("div"),ee=te,se=oe="",ae="http://www.w3.org/2000/svg"===n.namespaceURI?"svg_ns":(J=ke.exec(a))&&J[1]||"",ie){for($=r;$&&!(D=y($));)$=$.nextSibling;(ne=D?D._tkns:n._df)&&(q=O||"",!o&&O||(q+="#"+ge),V=ne.indexOf(q),V+1&&(V+=q.length,se=oe=ne.slice(0,V),ne=ne.slice(V),D?$.setAttribute(qe,ne):l(n,ne)))}if(Z=void 0,a=(""+a).replace(_t,f),z&&be.length&&Ce("Mismatched '<"+Y+"...>' in:\n"+a),c)return;for(lt.appendChild(te),ae=Se[ae]||Se.div,W=ae[0],ee.innerHTML=ae[1]+a+ae[2];W--;)ee=ee.lastChild;for(lt.removeChild(te),X=le.createDocumentFragment();G=ee.firstChild;)X.appendChild(G);n.insertBefore(X,r)}return _(),Ee}function b(e,t,n,i,r,a,o,l){var s,d,p,f,v,g,u,_,h,m,b,C=[];if(r)_=vt[r],_=_.linkCtx?_.linkCtx.tag:_,u=_.linkCtx||{type:"inline",data:i.data,elem:_._elCnt?_.parentElem:n,view:i,ctx:i.ctx,attr:be,fn:_._.bnd,tag:_,_bndId:r},_.linkCtx=u,x(u,e),_._toLk=u._bndId;else if(t&&n){for(o=a?o:i.data,s=i.tmpl,t=j(t,c(n)),b=Ae.lastIndex=0;d=Ae.exec(t);)C.push(d),b=Ae.lastIndex;for(b<t.length&&Ce(t);d=C.shift();){for(h=Ae.lastIndex,p=d[1],v=d[3];C[0]&&"else"===C[0][4];)v+=re+te+C.shift()[3],m=!0;m&&(v+=re+te+ne+"/"+d[4]+ie),u={type:a||"link",data:o,elem:n,view:i,ctx:l,attr:p,_toLk:1,_noUpd:d[2]},f=void 0,d[6]&&(f=d[10]||void 0,u.convert=d[5]||"",void 0!==f&&c(n)&&(p&&Ce(v+"- Remove target: "+p),u.convertBack=f=f.slice(1))),u.expr=p+v,g=ot[v],g||(ot[v]=g=de.tmplFn(v.replace(we,"\\$&"),s,!0,f,m)),u.fn=g,x(u,e),Ae.lastIndex=h}}}function x(e,n){function i(t,n){n&&n.refresh||o.call(e,t,n)}var r,a=e.type;if("top"!==a&&"expr"!==a||(e.view=new de.View(de.extendCtx(e.ctx,e.view.ctx),"link",e.view,e.data,e.expr,(void 0),v)),e._ctxCb=de._gccb(r=e.view),e._hdl=i,"SELECT"===e.elem.nodeName&&(e.elem._jsvLkEl||"link"===a&&!e.attr&&void 0!==e.convert)){var l,s=e.elem,d=t(s);d.on("jsv-domchange",function(){arguments[3].refresh||(s._jsvLkEl?d.val(s._jsvLkEl.cvtArgs(s._jsvElse,1)[s._jsvInd]):e.tag?d.val(e.tag.cvtArgs(0,1)):(l=e.fn(r.data,r,de),d.val(e.convert||e.convertBack?de._cnvt(e.convert,r,l):l)))})}e.fn._lr?(e._toLk=1,n.push(e)):i(!0)}function C(e,t){var n;return e?(n=e.indexOf(t),n+1?e.slice(0,n)+e.slice(n+t.length):e):""}function k(e){return e&&(typeof e===me?e:e.tagName===Xe?e.type.slice(3):1===e.nodeType&&e.getAttribute(qe)||"")}function y(e,t,n){function i(e,t,n,i,a,l){o.push({elCnt:r,id:i,ch:a,open:t,close:n,path:l,token:e})}var r,a,o=[];if(a=t?e:k(e))return r=o.elCnt=e.tagName!==Xe,r="@"===a.charAt(0)||r,o._tkns=a,a.replace(n||kt,i),o}function E(e,t){e&&("jsv"===e.type?e.parentNode.removeChild(e):t&&""===e.getAttribute(Ve)&&e.removeAttribute(Ve))}function w(e,t){for(var n=e;t&&n&&1!==n.nodeType;)n=n.previousSibling;return n&&(1!==n.nodeType?(n=le.createElement(Xe),n.type="jsv",e.parentNode.insertBefore(n,e)):k(n)||n.getAttribute(Ve)||n.setAttribute(Ve,"")),n}function j(e,n){return e=t.trim(e),e.slice(-1)!==ie?e=ne+":"+e+(n?":":"")+ie:e}function A(e,n,i){function r(){a=k.linkedElems||e.linkedElems||e.linkedElem&&[e.linkedElem],a&&(e.linkedElems=k.linkedElems=a,e.linkedElem=a[0]=e.linkedElem||a[0]),(l=k.mainElem||e.mainElem)&&(k.mainElem=e.mainElem=l),(l=k.displayElem||e.displayElem)&&(k.displayElem=e.displayElem=l)}var a,o,l,s,d,p,c,f,v,g,u,_,h,m,b,x,C,k=e.tagCtx,y=e.tagCtxs,E=y&&y.length,w=e.linkCtx,j=e.bindTo||{};if(e._.unlinked){if(p=t(w.elem),e.linkedElement||e.mainElement||e.displayElement){if(o=e.linkedElement)for(e.linkedElem=void 0,s=o.length;s--;)if(o[s])for(c=!e.inline&&p.filter(o[s]),d=E;d--;)g=y[d],a=g.linkedElems=g.linkedElems||new Array(s),l=c[0]?c:g.contents(!0,o[s]),l[0]&&l[0].type!==Ke&&(a[s]=l.eq(0));if(o=e.mainElement)for(c=!e.inline&&p.filter(o),d=E;d--;)g=y[d],l=c[0]?c:g.contents(!0,o).eq(0),l[0]&&(g.mainElem=l);if(o=e.displayElement)for(c=!e.inline&&p.filter(o),d=E;d--;)g=y[d],l=c[0]?c:g.contents(!0,o).eq(0),l[0]&&(g.displayElem=l);r()}e.onBind&&(e.onBind(k,w,e.ctx,n,i),r())}for(d=E;d--;){if(g=y[d],u=g.props,e._.unlinked&&g.map&&e.mapProps){for(b=e.mapProps.length,x=u.mapDepends||e.mapDepends||[],x=Z(x)?x:[x];b--;){var A=e.mapProps[b];C=e._.bnd.paths[d]["_"+A],C&&C.length&&C.skp&&(x=x.concat(C))}x.length&&g.map.observe(x,w)}(l=g.mainElem||!e.mainElement&&g.linkedElems&&g.linkedElems[0])&&(l[0]&&u.id&&!l[0].id&&(l[0].id=u.id),e.setSize&&((_=!j.height&&u.height||e.height)&&l.height(_),(_=!j.width&&u.width||e.width)&&l.width(_))),(_=(l=g.displayElem||l)&&(!j["class"]&&u["class"]||e.className))&&(h=l[0]._jsvCl,_!==h&&(l.hasClass(h)&&l.removeClass(h),l.addClass(_),l[0]._jsvCl=_))}if(e.onAfterLink&&(e.onAfterLink(k,w,e.ctx,n,i),r()),!e.flow&&!e._.chg)for(e._tgId&&e._.unlinked&&(e.linkedElems||e.bindTo)&&S(vt[e._tgId],e),d=y.length;d--;){for(u=e.cvtArgs(d,1),s=u.length;s--;)e.setValue(u[s],s,d,n,i);if(e._.unlinked)for(k=y[d],a=k.linkedElems||!d&&e.linkedElem&&[e.linkedElem],m=(e.bindTo||[0]).length;m--;)if((l=a&&a[m])&&(s=l.length))for(;s--;)f=l[s],v=f._jsvLkEl,v&&v===e||(f._jsvLkEl=e,f._jsvInd=m,f._jsvElse=d,I(e,f),e._tgId&&(f._jsvBnd="&"+e._tgId+"+"))}e._.unlinked=void 0,e._.lt&&e.refresh()}function T(e){var t=e.which;t>15&&t<21||t>32&&t<41||t>111&&t<131||27===t||144===t||setTimeout(function(){a(e)})}function V(e,t,n){t!==!0||!Be||Pe&&e[0].contentEditable===Ge?(t=typeof t===me?t:"keydown.jsv",e[n](t,t.indexOf("keydown")>=0?T:a)):e[n]("input.jsv",a)}function I(e,n){var i,r,a=n._jsvTr||!1;e&&(r=e.tagCtx.props.trigger,void 0===r&&(r=e.trigger)),void 0===r&&(r=pe.trigger),r=r&&("INPUT"===n.tagName&&n.type!==Re&&n.type!==Ke||"textarea"===n.type||n.contentEditable===Ge)&&r||!1,a!==r&&(i=t(n),V(i,a,"off"),V(i,n._jsvTr=r,"on"))}function S(e,t,n){var i,r,a,o,l,s,d,p,c,f,v,g,u,_,h,m=1,b=[],x=e.linkCtx,C=x.data,k=x.fn.paths;if(e&&!e.to){for(t&&(t.convertBack||(t.convertBack=n),s=t.bindTo,m=t.tagCtxs?t.tagCtxs.length:1);m--;){if(u=[],g=k[m])for(s=g._jsvto?["jsvto"]:s||[0],!m&&t&&t._.ths&&(s=s.concat("this")),p=s.length;p--;){if(r="",v=x._ctxCb,d=s[p],d=g[+d===d?d:"_"+d],i=d&&d.length){if(a=d[i-1],a._cpfn){for(o=a;a.sb&&a.sb._cpfn;)r=a=a.sb;r=a.sb||r&&r.path,h=a._cpfn&&!a.sb,a=r?r.slice(1):o.path}l=r?[o,a]:N(a,C,v)}else f=t.linkedCtxParam,l=[],_=t._.fromIndex,_&&f&&f[_[p]]&&(l=[t.tagCtxs[m].ctx[f[_[p]]][0],xe]);(c=l._cxp)&&c.tag&&a.indexOf(".")<0&&(l=c),l.isCpfn=h,u.unshift(l)}b.unshift(u)}e.to=b}}function N(e,t,n){for(var i,r,a,o,l,s,d,p;e&&e!==xe&&(a=n(i=e.split("^").join(".")))&&(o=a.length);){if(l=a[0]._cxp)if(d=d||l,s=a[0][0],xe in s?(p=s,s=s._vw):p=s.data,d.path=e=a[0][1],a=[d.data=p,e],n=de._gccb(s),e._cpfn){for(r=e,r.data=a[0],r._cpCtx=n;e.sb&&e.sb._cpfn;)i=e=e.sb;i=e.sb||i&&i.path,e=i?i.slice(1):r.path,a=[r,e]}else l.tag&&l.path===xe&&(a=l);else a=o>1?[a[o-2],a[o-1]]:[a[o-1]];t=a[0],e=a[1]}return a=a||[t,i],a._cxp=d,a}function L(e,t,n){var i,r,a=e.tagCtx.view,o=e.tagCtxs||[e.tagCtx],l=o.length,s=!t;if(s){if(t=e._.bnd.call(a.tmpl,(e.linkCtx||a).data,a,de),t.lt)return;e._.lt=void 0,t=Z(t)?t:[t]}if(n)o=e.tagCtxs=t,e.tagCtx=o[0],R(e);else for(;l--;)i=o[l],r=t[l],ce(i.ctx,r.ctx),i.args=r.args,s&&(i.tmpl=r.tmpl),Y(i.props).setProperty(r.props);return de._thp(e,o[0]),o}function B(e){for(var t,n,i,r=[],a=e.length,o=a;o--;)r.push(e[o]);for(o=a;o--;)if(n=r[o],n.parentNode){if(i=n._jsvBnd)for(i=i.slice(1).split("&"),n._jsvBnd="",t=i.length;t--;)P(i[t],n._jsvLkEl,n);D(k(n)+(n._df||""),n)}}function P(e,n,i){var r,a,o,l,s,d,p,c,f,v,g,u,_,h,m=vt[e];if(n)i._jsvLkEl=void 0;else if(m&&(!i||i===m.elem)){delete vt[e];for(r in m.bnd)(l=m.bnd[r])&&(s=m.cbId,Z(l)?t([l]).off(he+s).off(_e+s):t(l).off(_e+s),delete m.bnd[r]);if(a=m.linkCtx){if(o=a.tag){if(d=o.tagCtxs)for(p=d.length;p--;)u=d[p],(c=u.map)&&c.unmap(),(_=u.linkedElems)&&(h=(h||[]).concat(_));o.onUnbind&&o.onUnbind(o.tagCtx,a,o.ctx),o.onDispose&&o.onDispose(),o._elCnt||(o._prv&&o._prv.parentNode.removeChild(o._prv),o._nxt&&o._nxt.parentNode.removeChild(o._nxt))}for(_=h||[t(a.elem)],p=_.length;p--;)f=_[p],(v=f&&f[0]&&f[0]._jsvTr)&&(V(f,v,"off"),f[0]._jsvTr=void 0);g=a.view,"link"===g.type?g.parent.removeViews(g._.key,void 0,!0):delete g._.bnds[e]}delete m.s[m.cbId]}}function U(e){e?(e=e.jquery?e:t(e),e.each(function(){for(var e;(e=Te(this,!0))&&e.parent;)e.parent.removeViews(e._.key,void 0,!0);B(this.getElementsByTagName("*"))}),B(e)):(je&&(t(je).off(Me,a).off("blur.jsv","[contenteditable]",a),je=void 0),oe.removeViews(),B(le.body.getElementsByTagName("*")))}function q(e){return e.type===Re?e[Fe]:e.value}function M(e,t,n){return n&&n[t]||e.ctx[t]&&e.ctxPrm(t)||ee.helpers[t]}function O(e,t,n,i,r,a){var o,s,d,p,c,f,v,g=0,u=e===t;if(e){for(d=y(e)||[],o=0,s=d.length;o<s;o++){if(p=d[o],f=p.id,f===i&&p.ch===r){if(!a)break;s=0}u||(c="_"===p.ch?Ne[f]:vt[f].linkCtx.tag,c&&(p.open?c._prv=t:p.close&&(c._nxt=t))),g+=f.length+2}g&&e.setAttribute(qe,e.getAttribute(qe).slice(g)),v=t?t.getAttribute(qe):n._df,(s=v.indexOf("/"+i+r)+1)&&(v=d._tkns.slice(0,g)+v.slice(s+(a?-1:i.length+1))),v&&(t?t.setAttribute(qe,v):l(n,v))}else l(n,C(n._df,"#"+i+r)),a||t||l(n,C(n._df,"/"+i+r))}function D(e,t){var n,i,r,a;if(a=y(e,!0,mt))for(n=0,i=a.length;n<i;n++)r=a[n],"_"===r.ch?!(r=Ne[r.id])||!r.type||t&&r._prv!==t&&r.parentElem!==t||r.parent.removeViews(r._.key,void 0,!0):P(r.id,void 0,t)}function $(e,t,n,i,a,o){var l=this,s=[];return l&&l._tgId&&(a=l),arguments.length<4&&(+t!==t?(i=t,n=t=0):+n!==n&&(i=n,n=0)),s[t||0]=e,r(s,n,i,a,o),l}function F(){for(var e=this.tag.bindTo.length,t=arguments[e],n=arguments[e+1];e--;)this.tag.setValue(arguments[e],e,this.index,t,n)}function R(e){var n,i,a,o,d,v,u,_;if(e.contents=function(e,n){e!==!!e&&(n=e,e=void 0);var i,r=t(this.nodes());return r[0]&&(n=e?n||"*":n,i=n?r.filter(n):r,r=e?i.add(r.find(n)):i),r},e.nodes=function(e,t,n){var i,r=this.contentView||this,a=r._elCnt,o=!t&&a,l=[];if(!r.args)for(t=t||r._prv,n=n||r._nxt,i=o?t===r._nxt?r.parentElem.lastSibling:t:r.inline===!1?t||r.linkCtx.elem.firstChild:t&&t.nextSibling;i&&(!n||i!==n);)(e||a||i.tagName!==Xe)&&l.push(i),i=i.nextSibling;return l},e.childTags=function(e,t){e!==!!e&&(t=e,e=void 0);var n=this.contentView||this,i=n.link?n:n.tagCtx.view,r=n._prv,a=n._elCnt,o=[];return n.args||i.link(void 0,n.parentElem,a?r&&r.previousSibling:r,n._nxt,void 0,{get:{tags:o,deep:e,name:t,id:n.link?n._.id+"_":n._tgId+"^"}}),o},"tag"===e._is){for(u=e,i=u.tagCtxs.length;i--;)a=u.tagCtxs[i],a.setValues=F,a.contents=e.contents,a.childTags=e.childTags,a.nodes=e.nodes;if(o=u.boundProps=u.boundProps||[],d=u.bindFrom)for(n=d.length;n--;)v=d[n],typeof v===me&&(d[v]=1,jt(v,o)<0&&o.push(v));u.setValue=de._gm(u.constructor.prototype.setValue||function(e){return e},function(e,i,r,a,o){i=i||0,r=r||0;var l,s,d,p,c,f,v=u.tagCtxs[r];if(!v._bdArgs||!o&&void 0===e||v._bdArgs[i]!==e||o&&"set"===o.change&&(a.target===e||o.value===e)?(v._bdArgs=v._bdArgs||[],v._bdArgs[i]=e,f=u.base.call(u,e,i,r,a,o),void 0!==f&&(v._bdVals=v._bdVals||[],v._bdVals[i]=f,e=f)):v._bdVals&&(e=v._bdVals[i]),void 0!==e&&(d=u.linkedCtxParam)&&d[i]&&v.ctxPrm(d[i],e),p=u._.toIndex[i],void 0!==p&&(c=v.linkedElems||u.linkedElem&&[u.linkedElem])&&(l=c[p])&&(n=l.length))for(;n--;)s=l[n],void 0===e||s._jsvChg||u.linkCtx._val===e||(void 0!==s.value?s.type===Re?s[Fe]=t.isArray(e)?t.inArray(s.value,e)>-1:e&&"false"!==e:s.type===Ke?s[Fe]=s.value===e:t(s).val(e):s[s.contentEditable===Ge?"innerHTML":Ue]=e),v.props.name&&(s.name=s.name||v.props.name);return u}),u.updateValue=$,u.updateValues=function(){var e,t,n=this,i=n.bindTo?n.bindTo.length:1,a=arguments.length-i;return a&&(e=arguments[i],a>1?t=a>1?arguments[i+1]:void 0:+e!==e&&(t=e,e=0)),r(arguments,e,t,this)},u.setValues=function(){return F.apply(u.tagCtx,arguments),u},u.refresh=function(){var e,t,n=u.linkCtx,i=u.tagCtx.view;if(t=L(u))return u.onUnbind&&(u.onUnbind(u.tagCtx,n,u.ctx),u._.unlinked=!0),e=u.inline?be:n.attr||c(u.parentElem,!0),t=":"===u.tagName?de._cnvt(u.convert,i,u.tagCtx):de._tag(u,i,i.tmpl,t,!0),g(n,n.data,n.elem),s(t,n,e,u),A(u),u},u.domChange=function(){var e=this.parentElem,n=t._data(e).events,i="jsv-domchange";n&&n[i]&&t(e).triggerHandler(i,arguments)}}else _=e,_.addViews=function(e,t,n){var i,r=this,a=t.length,o=r.views;!r._.useKey&&a&&(i=o.length+a,!n&&i!==r.data.length||f(r,e,r.tmpl,o,t,r.ctx)===!1||r._.srt||r.fixIndex(e+a))},_.removeViews=function(e,n,i,r){function a(e){var n,r,a,o,l,s,d=f[e];if(d&&d.link){n=d._.id,i||(s=d.nodes()),d.removeViews(void 0,void 0,!0),d.type=void 0,o=d._prv,l=d._nxt,a=d.parentElem,i||(d._elCnt&&O(o,l,a,n,"_"),t(s).remove()),!d._elCnt&&o&&(o.parentNode.removeChild(o),l.parentNode.removeChild(l)),p(d);for(r in d._.bnds)P(r);delete Ne[n]}}var o,l,s,d=this,c=!d._.useKey,f=d.views;if(c&&(s=f.length),void 0===e)if(c){for(o=s;o--;)a(o);d.views=[]}else{for(l in f)a(l);d.views={}}else if(void 0===n&&(c?n=1:(a(e),delete f[e])),c&&n&&(r||s-n===d.data.length)){for(o=e+n;o-- >e;)a(o);f.splice(e,n),d._.srt||d.fixIndex(e)}},_.moveViews=function(e,n,i){function r(e,t){return RegExp("^(.*)("+(t?"\\/":"#")+e._.id+"_.*)$").exec(t||e._prv.getAttribute(qe))}function a(e,t){var n,i=e._prv;i.setAttribute(qe,t),t.replace(xt,function(e,t,r,a){n=vt[a].linkCtx.tag,n.inline&&(n[t?"_prv":"_nxt"]=i)}),t.replace(bt,function(e,t,n,r){Ne[r][t?"_prv":"_nxt"]=i})}var o,s,d,p=this,c=p._nxt,f=p.views,v=n<e,g=v?n:e,u=v?e:n,_=n,h=[],m=f.splice(e,i);for(n>f.length&&(n=f.length),f.splice.apply(f,[n,0].concat(m)),i=m.length,d=n+i,u+=i,_;_<d;_++)s=f[_],o=s.nodes(!0),h=p._elCnt?h.concat(o):h.concat(s._prv,o,s._nxt);if(h=t(h),d<f.length?h.insertBefore(f[d]._prv):c?h.insertBefore(c):h.appendTo(p.parentElem),p._elCnt){var b,x=v?g+i:u-i,C=(f[g-1],f[g]),k=f[x],y=f[u],E=r(C),w=r(k);a(C,w[1]+E[2]),y?(b=r(y),a(y,E[1]+b[2])):(f[u-1]._nxt=c,c?(b=r(p,c.getAttribute(qe)),c.setAttribute(qe,E[1]+b[2])):(b=r(p,p.parentElem._df),l(p.parentElem,E[1]+b[2]))),a(k,b[1]+w[2])}p.fixIndex(g)},_.refresh=function(){var e=this,t=e.parent;return t&&(f(e,e.index,e.tmpl,t.views,e.data,void 0,!0),p(e)),e},_.fixIndex=function(e){for(var t=this.views,n=t.length;e<n--;)t[n].index!==n&&Y(t[n]).setProperty("index",n)},_.link=m}function K(e,t,n){var i,r,a=e.options.props;if(z(e.propsArr,n.path,n.value,n.remove),void 0!==a.sort||void 0!==a.start||void 0!==a.end||void 0!==a.step||a.filter||a.reverse)e.update();else if("set"===n.change){for(i=e.tgt,r=i.length;r--&&i[r].key!==n.path;);r===-1?n.path&&!n.remove&&Y(i).insert({key:n.path,prop:n.value}):n.remove?Y(i).remove(r):Y(i[r]).setProperty("prop",n.value)}}function H(e,t,n){var i,r,a,o,l=e.src,s=n.change;if("set"===s)"prop"===n.path?Y(l).setProperty(t.target.key,n.value):(Y(l).removeProperty(n.oldValue),Y(l).setProperty(n.value,t.target.prop));else if("insert"===s||(o="remove"===s))for(i=n.items,r=i.length;r--;)(a=i[r].key)&&(z(e.propsArr,a,i[r].prop,o),o?(Y(l).removeProperty(a),delete l[a]):Y(l).setProperty(a,i[r].prop))}function z(e,t,n,i){for(var r=e.length;r--&&e[r].key!==t;);r===-1?t&&!i&&e.push({key:t,prop:n}):i&&e.splice(r,1)}function Q(e){return Et.test(e)}var W=t===!1;n=n||W&&e.jsrender,t=t||e.jQuery;var X="v1.0.13",G="jquery.views.js requires ";if(!t||!t.fn)throw G+"jQuery";n&&!n.fn&&n.views.sub._jq(t);var J,Y,Z=t.isArray,ee=t.views;if(!t.render)throw G+"jsrender.js";if(ee.jsviews!==X)throw G+"query.observable.js "+X;if(!ee||!ee.map||ee.jsviews!==X)throw G+"jsrender.js "+X;var te,ne,ie,re,ae,oe,le=e.document,se=ee.settings,de=ee.sub,pe=de.settings,ce=de.extend,fe=t.isFunction,ve=(t.expando,ee.converters),ge=ee.tags,ue=pe.advanced,_e=de.propChng=de.propChng||"propertyChange",he=de.arrChng=de.arrChng||"arrayChange",me="string",be="html",xe="_ocp",Ce=de.syntaxErr,ke=/<(?!script)(\w+)[>\s]/,ye=de._er,Ee=de._err,we=/['"\\]/g;if(t.link)return t;pe.trigger=!0;var je,Ae,Te,Ve,Ie,Se,Ne,Le,Be,Pe=window.navigator.userAgent,Ue=void 0!==le.textContent?"textContent":"innerText",qe="data-jsv",Me="change.jsv",Oe="onBeforeChange",De="onAfterChange",$e="onAfterCreate",Fe="checked",Re="checkbox",Ke="radio",He="input[type=",ze=He+Re+"]",Qe="none",We="value",Xe="SCRIPT",Ge="true",Je='"></script>',Ye='<script type="jsv',Ze=qe+"-df",et="script,["+qe+"]",tt={value:"val",input:"val",html:be,text:"text"},nt={from:We,to:We},it=0,rt=t.cleanData,at=se.delimiters,ot={},lt=le.createDocumentFragment(),st=le.querySelector,dt={ol:1,ul:1,table:1,tbody:1,thead:1,tfoot:1,tr:1,colgroup:1,dl:1,select:1,optgroup:1,svg:1,svg_ns:1},pt={tr:"table"},ct={br:1,img:1,input:1,hr:1,area:1,base:1,col:1,link:1,meta:1,command:1,embed:1,keygen:1,param:1,source:1,track:1,wbr:1},ft={},vt={},gt=1,ut=/^#(view\.?)?/,_t=/((\/>)|<\/(\w+)>|)(\s*)([#\/]\d+(?:_|(\^)))`(\s*)(<\w+(?=[\s\/>]))?|\s*(?:(<\w+(?=[\s\/>]))|<\/(\w+)>(\s*)|(\/>)\s*|(>)|$)/g,ht=/(#)()(\d+)(_)/g,mt=/(#)()(\d+)([_^])/g,bt=/(?:(#)|(\/))(\d+)(_)/g,xt=/(?:(#)|(\/))(\d+)(\^)/g,Ct=/(#)()(\d+)(\^)/g,kt=/(?:(#)|(\/))(\d+)([_^])([-+@\d]+)?/g,yt=/&(\d+)\+?/g,Et=/^[^.]*$/,wt=e.getComputedStyle,jt=t.inArray;if(He+=Ke+"]",Pe=Pe.indexOf("MSIE ")>0||Pe.indexOf("Trident/")>0,Y=t.observable,!Y)throw G+"jquery.observable.js";return J=Y.observe,pe._clFns=function(){ot={}},R(de.View.prototype),de.onStore.template=function(e,n,i){null===n?(delete t.link[e],delete t.render[e]):(n.link=_,e&&!i&&"jsvTmpl"!==e&&(t.render[e]=n,t.link[e]=function(){return _.apply(n,arguments)}))},de.viewInfos=y,(se.delimiters=function(){var e=at.apply(0,arguments),t=pe.delimiters;return te=t[0].charAt(0),ne=t[0].charAt(1),ie=t[1].charAt(0),re=t[1].charAt(1),ae=t[2],Ae=new RegExp("(?:^|\\s*)([\\w-]*)(\\"+ae+")?(\\"+ne+de.rTag+"(:\\w*)?\\"+ie+")","g"),e})(),de.addSetting("trigger"),ve.merge=function(e){var t,n=this.linkCtx.elem.className,i=this.tagCtx.props.toggle;return i&&(t=i.replace(/[\\^$.|?*+()[{]/g,"\\$&"),t="(\\s(?="+t+"$)|(\\s)|^)("+t+"(\\s|$))",n=n.replace(new RegExp(t),"$2"),e=n+(e?(n&&" ")+i:"")),e},ge({on:{attr:Qe,bindTo:[],init:function(e){for(var n,i=this,r=0,a=e.args,o=a.length;r<o&&!fe(a[r]);r++);i._hi=o>r&&r+1,i.inline&&(de.rTmpl.exec(n=t.trim(e.tmpl.markup))||(i.template="<button>"+(n||e.params.args[r]||"noop")+"</button>"),i.attr=be)},onBind:function(){this.template&&(this.mainElem=this.contents("button"))},onAfterLink:function(e,n){var i,r,a,o=this,l=o._hi,s=e.args,d=s.length,p=e.props,c=p.data,f=e.view,v=p.context;l&&(i=s[l-1],r=s.slice(l),s=s.slice(0,l-1),o._sel=s[1],a=o.activeElem=o.activeElem||t(o.inline?(o._sel=s[1]||"*",o.parentElem):n.elem),v||(v=/^(.*)[.^][\w$]+$/.exec(e.params.args.slice(-r.length-1)[0]),v=v&&de.tmplFn(ne+":"+v[1]+ie,f.tmpl,!0)(n.data,f,de)),o._evs&&o.onUnbind(e,n,o.ctx),a.on(o._evs=s[0]||"click",o._sel,void 0==c?null:c,o._hlr=function(e){var t,a=!o.inline;if(!a)for(t=o.contents("*"),d=t.length;!a&&d--;)t[d].contains(e.target)&&(a=!0);if(a)return i.apply(v||n.data,[].concat(r,e,{change:e.type,view:f,linkCtx:n},r.slice.call(arguments,1)))}))},onUpdate:!1,onArrayChange:!1,onUnbind:function(){var e=this,t=it;e.activeElem&&(it=0,e.activeElem.off(e._evs,e._sel,e._hlr),it=t)},contentCtx:!0,setSize:!0,dataBoundOnly:!0},radiogroup:{boundProps:["disabled"],init:function(e){this.name=e.props.name||(Math.random()+"jsv").slice(9)},onBind:function(e,n){var i,r,a,o=this,l=e.params.props;for(l=l&&l.disabled,o.inline?(i=o.contents("*")[0],i=i&&Te(i).ctx.tag===o.parent?i:o.parentElem,r=o.contents(!0,He)):(i=n.elem,r=t(He,n.elem)),o.linkedElem=r,a=r.length;a--;)r[a].name=r[a].name||o.name;t(i).on("jsv-domchange",o._dmChg=function(t,n,s,d){var p,c,f=n.ctx.parentTags;if(!d.refresh&&(!o.inline||i!==o.parentElem||f&&f[o.tagName]===o)){for(c=o.cvtArgs()[0],r=o.linkedElem=o.contents(!0,He),a=r.length;a--;)p=r[a],p._jsvLkEl=o,p.name=p.name||o.name,p._jsvBnd="&"+o._tgId+"+",p.checked=c===p.value,l&&(p.disabled=!!e.props.disabled);o.linkedElems=e.linkedElems=[r]}}),o._dmChg.tgt=i},onAfterLink:function(e,t,n,i,r){var a=e.params.props;a&&a.disabled&&this.linkedElem.prop("disabled",!!e.props.disabled)},onUnbind:function(){var e=this;e._dmChg&&(t(e._dmChg.tgt).off("jsv-domchange",e._dmChg),
e._dmChg=void 0)},onUpdate:!1,contentCtx:!0,dataBoundOnly:!0},checkboxgroup:{boundProps:["disabled"],init:function(e){this.name=e.props.name||(Math.random()+"jsv").slice(9)},onBind:function(e,n){for(var i,r=this,a=e.params.props,o=a&&a.disabled,l=e.params.args[0],s=r.contents(!0,ze),d=s.length;d--;)s[d].name=s[d].name||r.name,s[d]._jsvLkEl=r;for(d in a)l+=" "+d+"="+a[d];s.link(l,n.data,void 0,void 0,n.view),r.linkedElem=s,r.inline?(i=r.contents("*")[0],i=i&&t.view(i).ctx.tag===r.parent?i:r.parentElem):i=n.elem,t(i).on("jsv-domchange",r._dmChg=function(n,a,p,c){var f,v=a.ctx.parentTags;if(!c.refresh&&(!r.inline||i!==r.parentElem||v&&v[r.tagName]===r))for(s=r.contents(!0,ze),d=s.length;d--;)f=s[d],f._jsvSel||(f.name=f.name||r.name,t.link(l,f,p.data),o&&(f.disabled=!!e.props.disabled))}),r._dmChg.tgt=i},onAfterLink:function(e,t,n,i,r){var a=e.params.props;a&&a.disabled&&this.contents(!0,ze).prop("disabled",!!e.props.disabled)},onUnbind:function(){var e=this;e._dmChg&&(t(e._dmChg.tgt).off("jsv-domchange",e._dmChg),e._dmChg=void 0)},onUpdate:!1,contentCtx:!0,dataBoundOnly:!0}}),ce(ge["for"],{sortDataMap:ee.map({getTgt:ge["for"].sortDataMap.getTgt,obsSrc:function(e,t,n){e.update()},obsTgt:function(e,t,n){var i,r=n.items,a=e.src;if("remove"===n.change)for(i=r.length;i--;)Y(a).remove(jt(r[i],a));else"insert"===n.change&&Y(a).insert(r)}}),mapProps:["filter","sort","reverse","start","end","step"],bindTo:["paged","sorted"],bindFrom:[0],onArrayChange:function(e,t,n,i){var r,a,o=e.target.length,l=this;if(!l.rendering)if(l._.noVws||l.tagCtxs[1]&&("insert"===t.change&&o===t.items.length||"remove"===t.change&&!o))a=n.map&&n.map.propsArr,l.refresh(),a&&(n.map.propsArr=a);else for(r in l._.arrVws)r=l._.arrVws[r],r.data===e.target&&d.apply(r,arguments);l.domChange(n,i,t),e.done=!0},onUpdate:function(e,t,n){this.setDataMap(n)},onBind:function(e,t,n,i,r){for(var a,o=this,l=0,s=o._ars=o._ars||{},d=o.tagCtxs,p=d.length,c=o.selected||0;l<=c;l++)e=d[l],a=e.map?e.map.tgt:e.args.length?e.args[0]:e.view.data,s[l]&&(J(s[l],!0),delete s[l]),!s[l]&&Z(a)&&!function(){var n=e;J(a,s[l]=function(e,i){o.onArrayChange(e,i,n,t)})}();for(l=c+1;l<p;l++)s[l]&&(J(s[l],!0),delete s[l]);r&&o.domChange(e,t,r)},onAfterLink:function(e){for(var n,i,r,a=this,o=0,l=a.tagCtxs,s=(l.length,a.selected||0);o<=s;o++)e=l[o],i=e.map,n=e.map?i.tgt:e.args.length?e.args[0]:e.view.data,Z(n)&&(r=e.params.props)&&(r.paged&&!a.paged&&(t.observable(a).setProperty("paged",n.slice()),a.updateValue(a.paged,0,o,!0)),r.sorted&&!a.sorted&&(t.observable(a).setProperty("sorted",i&&i.sorted||n.slice()),a.updateValue(a.sorted,1,o,!0)))},onDispose:function(){var e,t=this;for(e in t._ars)J(t._ars[e],!0)}}),ce(ge["if"],{onUpdate:function(e,t,n){for(var i,r,a=0;i=this.tagCtxs[a];a++)if(r=i.props.tmpl!==n[a].props.tmpl||i.args.length&&!(i=i.args[0])!=!n[a].args[0],!this.convert&&i||r)return r;return!1},onAfterLink:function(e,t,n,i,r){r&&this.domChange(e,t,r)}}),ge("props",{baseTag:"for",dataMap:ee.map({getTgt:ge.props.dataMap.getTgt,obsSrc:K,obsTgt:H,tgtFlt:Q}),flow:!0}),ce(t,{view:Te=function(e,n,i){function r(e,t){if(e)for(o=y(e,t,ht),s=0,d=o.length;s<d&&(!(a=Ne[o[s].id])||!(a=a&&i?a.get(!0,i):a));s++);}n!==!!n&&(i=n,n=void 0);var a,o,l,s,d,p,c,f=0,v=le.body;if(e&&e!==v&&oe._.useKey>1&&(e=typeof e===me?t(e)[0]:e.jquery?e[0]:e)){if(n){if(r(e._df,!0),!a&&e.tagName)for(c=st?e.querySelectorAll(et):t(et,e).get(),p=c.length,l=0;!a&&l<p;l++)r(c[l]);return a}for(;e;){if(o=y(e,void 0,bt))for(p=o.length;p--;)if(a=o[p],a.open){if(f<1)return a=Ne[a.id],a&&i?a.get(i):a||oe;f--}else f++;e=e.previousSibling||e.parentNode}}return oe},link:h,unlink:U,cleanData:function(e){e.length&&it&&B(e),rt.apply(t,arguments)}}),ce(t.fn,{link:function(e,t,n,i,r,a,o){return h(e,this,t,n,i,r,a,o)},unlink:function(){return U(this)},view:function(e,t){return Te(this[0],e,t)}}),t.each([be,"replaceWith","empty","remove"],function(e,n){var i=t.fn[n];t.fn[n]=function(){var e;it++;try{e=i.apply(this,arguments)}finally{it--}return e}}),ce(oe=de.topView,{tmpl:{links:{}}}),Ne={0:oe},de._glt=function(e){for(var t,n=/#(\d*)\^\/\1\^/g,i=[],r=k(e);t=n.exec(r);)(t=vt[t[1]])&&i.push(t.linkCtx.tag);return i},de._gccb=function(e){return function(t,n){var i,r,a,o,l,s,d,p,c,f,v;if(e&&t){if(t._cpfn)try{return ue.cache?e.getCache(t._cpKey):t._cpfn.call(e.tmpl,e.data,e,de)}catch(g){return}if("~"===t.charAt(0)){if("~tag"===t.slice(0,4)&&(r=e.ctx,"."===t.charAt(4)?(i=t.slice(5),r=r.tag):"~tagCtx."===t.slice(0,8)&&(i=t.slice(8),r=r.tagCtx),i))return r?[r,i]:[];if(t=t.slice(1).split("."),o=e.ctxPrm(l=t.shift(),void 0,!0))if(p=o._cxp){if(t.length&&(s="."+t.join("."),l=o[d=o.length-1],l._cpfn?(l.sb=s,l.bnd=!!n):(o[d]=(l+s).replace("#data.",""),"#view"===l.slice(0,5)&&(o[d]=o[d].slice(6),o.splice(d,0,e)))),a=[o],(r=p.tag)&&r.convert)for(v=r.bindTo||[0],d=v.length;d--;)void 0!==n&&d!==p.ind&&(f=v[d],c=[o[0],r.tagCtx.params[+f===f?"args":"props"]],c._cxp=p,a.push(c))}else(t.length||fe(o))&&(a=[o,t.join(".")]);return a||[]}if("#"===t.charAt(0))return"#data"===t?[]:[e,t.replace(ut,"")]}}},de._cp=function(e,n,i,r){if(i.linked){if(r&&(r.cvt||void 0===r.tag._.toIndex[r.ind]))e=[{_ocp:e}],r.updateValue=function(n){return t.observable(e._cxp.data).setProperty(xe,n),this};else if(n){var a=ne+":"+n+ie,o=ot[a];o||(ot[a]=o=de.tmplFn(a.replace(we,"\\$&"),i.tmpl,!0)),e=o.deps[0]?[i,o]:[{_ocp:r?e:o()}]}else e=[{_ocp:e}];e._cxp=r||{updateValue:function(t){return Y(e._cxp.data).setProperty(e._cxp.path,t),this}}}return e},de._ucp=function(e,t,n,i){var r=i.tag,a=r?jt(e,r.linkedCtxParam):0;return i.path||N("~"+e,n.data,de._gccb(n)),(i.updateValue||r.updateValue)(t,a,i.tagElse,void 0,r)},de._ceo=function At(e){for(var t,n=[],i=e.length;i--;)t=e[i],t._cpfn&&(t=ce({},t),t.prm=At(t.prm)),n.unshift(t);return n},Le=de.advSet,de.advSet=function(){Le.call(de),e._jsv=ue._jsv?ce(e._jsv||{},{views:Ne,bindings:vt}):void 0,Ve=ue.linkAttr,Ie=et+",["+Ve+"]",Se=ue._wm,Se.optgroup=Se.option,Se.tbody=Se.tfoot=Se.colgroup=Se.caption=Se.thead,Se.th=Se.td},se.advanced({linkAttr:"data-link",useViews:!1,noValidate:!1,_wm:{option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],area:[1,"<map>","</map>"],param:[1,"<object>","</object>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],svg_ns:[1,"<svg>","</svg>"],div:t.support.htmlSerialize?[0,"",""]:[1,"X<div>","</div>"]},_fe:{input:{from:q,to:We},textarea:nt,select:nt,optgroup:{to:"label"}}}),t},window);
//# sourceMappingURL=jquery.views.min.js.map
