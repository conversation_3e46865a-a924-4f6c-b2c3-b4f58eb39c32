﻿/* css reset */
body {
    color: #000;
    font-size: 12px;
    line-height: 166.6%;
    text-align: center;
}

    body.move {
        -webkit-transition: padding 0.3s ease;
        -moz-transition: padding 0.3s ease;
        -o-transition: padding 0.3s ease;
        -ms-transition: padding 0.3s ease;
        transition: padding 0.3s ease;
    }

body, input, select, button {
    font-family: verdana
}

h1, h2, h3, select, input, button {
    font-size: 100%
}

body, h1, h2, h3, ul, li, p, img {
    margin: 0;
    padding: 0;
    border: 0;
    background-color: #367fa9;
}

input, button, select, img {
    margin: 0;
    line-height: normal
}

select {
    padding: 1px
}

ul {
    list-style: none
}

select, input, button, button img, label {
    vertical-align: middle
}

header, footer, section, aside, nav, hgroup, figure, figcaption {
    display: block;
    margin: 0;
    padding: 0;
    border: none
}

a {
    text-decoration: none;
    color: #959595
}

    a:hover {
        color: #626262
    }

.fontWeight {
    font-weight: 700;
}
/* global */
.vcenter {
    width: 100%;
    height: 720px;
    position: absolute;
    top: 50%;
    margin-top: -390px; /**/
}

.txt-suc {
    color: #22AC38
}

.txt-err {
    color: #e60012
}

.txt-yixin {
    color: #279C7B;
}
/* backgroundImage */
.themeCtrl a,
.loginFormIpt,
.headerIntro,
.verify-input-line,
.themeText li,
.btn,
.btn-moblogin,
.ico {
    background: #FFFFFF;
}

.headerLogo,
.headerNav,
.formIpt,
.formIpt2 {
    position: absolute
}
/* ico */
.ico-uid {
    width: 16px;
    height: 16px;
    background-position: -154px -64px;
    background: url(../../../syjx/image/user.png) no-repeat;
}

.ico-pwd {
    width: 16px;
    height: 16px;
    background-position: -178px -64px;
    background: url(../../../syjx/image/locked.png) no-repeat;
}

.ico-yzm {
    width: 14px;
    height: 17px;
    background-position: -220px -64px
}

.ico-new {
    display: block;
    width: 19px;
    height: 12px;
    background: url('') no-repeat;
}
/* header */
.header {
    width: 1000px;
    height: 105px;
    position: relative;
    margin: 0 auto;
    z-index: 3;
    overflow: hidden;
}

.headerLogo {
    left: 50px;
    font: bold 24px/40px "Microsoft YaHei";
}

    .headerLogo a, .headerLogo span {
        display: block;
        float: left;
        padding-top: 10px;
    }

    .headerLogo a {
        padding: 0px 10px
    }

    .headerLogo span {
        color: #444;
    }

.headerIntro {
    width: 140px;
    height: 30px;
    background-position: 0 -64px;
    margin-top: 16px
}

.headerNav {
    top: 21px;
    right: 100px;
    text-align: right;
    color: #cfd0d0;
}

    .headerNav a {
        padding-left: 13px;
        display: inline-block;
    }

    .headerNav .last {
        padding-left: 0;
    }

/* main */
.main {
    width: 100%;
    height: 894px;
    margin-top: 40px;
    position: relative;
    background-repeat: no-repeat;
    min-width: 1000px;
    background-position: center top;
}

#mainCnt {
    width: 100%;
    height: 650px;
    position: relative;
    clear: both;
    background-repeat: no-repeat;
    background-position: center top;
}


.theme_a {
    margin: 0 auto;
    height: 536px;
    width: 1000px;
    overflow: hidden;
    position: relative;
}

/* login*/
.login {
    width: 360px;
    height: 355px;
    padding: 13px 14px 15px;
    top: 220px;
    left: 50%;
    margin-left: 100px;
    text-align: left;
    position: absolute;
    z-index: 2;
    background: #80aed0;
    border: 1px solid rgba(255,255,255,.3);
    border-radius: 10px;
}

@media (max-width: 600px) {
    .main {
        width: 100%;
        height: 500px;
        margin-top: 40px;
        position: relative;
        background-repeat: no-repeat;
        min-width: 300px;
        background-position: center top;
    }

    .login {
        width: 360px;
        height: 355px;
        padding: 13px 14px 15px;
        top: 200px;
        left: 5%;
        margin-left: 10px;
        text-align: left;
        position: absolute;
        z-index: 2;
        background: #80aed0;
        border: 1px solid rgba(255,255,255,.3);
        border-radius: 10px;
    }
}


.tab-2 {
    background-position: -1px 0;
}
/* form */
.loginForm {
    position: relative;
    /*padding-top: 10px;*/
    display: block;
    height: 240px;
}

.loginFormIpt {
    position: relative;
    width: 280px;
    height: 34px;
    margin: 0 0 30px 25px;
    clear: both;
    background-position: 0 -352px;
    z-index: 2;
}

.loginFormIpt2 {
    background-position: 0 -352px;
    z-index: 3
}

.loginFormIpt .ico {
    position: absolute;
    left: 7px;
    top: 11px;
    z-index: 1;
}

.loginFormBtn {
    position: relative;
    width: 280px;
    height: 38px;
    margin: 18px 0 0 25px;
    clear: both;
}

#pcRegister {
    text-align: center;
}

    #pcRegister img {
        margin-top: 30px;
        width: 240px;
    }

.rightBlock {
    display: block;
    position: absolute;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    overflow: hidden;
    z-index: -999;
}

    .rightBlock img {
        background-color: #80aed0;
    }

.rightNone {
    display: none;
    z-index: -999;
}

.regBlock {
    display: block;
}

.regNone {
    display: none;
}

.err-cont {
    color: #fff;
    font-size: 15px;
    margin-left: 100px;
    margin-right: 20px;
}

.refresh {
    color: #e4393c;
    font-size: 15px;
}

.formIpt {
    width: 250px;
    padding: 0 0 0 10px;
    height: 38px;
    left: 29px;
    color: #333;
    font-size: 14px;
    font-weight: 700;
    border: none;
    font-family: verdana,"Microsoft YaHei";
    line-height: 38px;
    background: transparent !important;
}
    /*"Microsoft YaHei"*/
    .formIpt:focus {
        outline: none;
    }

.formIpt2 {
    width: 80px;
}

.loginFormIpt2 .yzmipt {
    float: left;
    width: 110px;
    height: 30px;
    display: block;
}

.loginFormIpt2 .yzmbox {
    margin: 2px 1px 0 4px;
    width: 80px;
    height: 39px;
    overflow: hidden;
    float: right;
    display: block;
}

    .loginFormIpt2 .yzmbox p {
        padding: 3px 0 0 3px;
        display: block;
        float: left;
        line-height: 15px;
        color: #666
    }

    .loginFormIpt2 .yzmbox a {
        display: block;
        float: left;
        line-height: 18px;
    }

.showPlaceholder .placeholder {
    visibility: visible;
    cursor: text;
    color: #ebebeb;
}

.placeholder {
    color: #ebebeb;
    font-size: 14px;
    position: absolute;
    left: 30px;
    top: 14px;
    line-height: 14px;
    visibility: hidden;
    background: none;
}

.btn_1 {
    width: 280px;
    height: 38px;
    float: left;
    text-align: center;
    cursor: pointer;
    border: 0;
    padding: 0;
    font-weight: 700;
    font-size: 14px;
    display: inline-block;
    vertical-align: baseline;
    line-height: 38px;
    outline: 0;
    background-color: #1780bd;
}

.btn-main1 {
    color: #fff;
    box-shadow: 0 2px 5px rgba(0,28,88,.3);
}

.btn-login1 {
    background-position: 0 -208px;
}

    .btn-login1:hover {
        background-position: 0 -256px;
        background: #0d87ce;
    }

    .btn-login1:active {
        background-position: 0 -304px;
        color: #0d87ce;
    }

.btn {
    width: 110px;
    height: 38px;
    float: left;
    text-align: center;
    cursor: pointer;
    border: 0;
    padding: 0;
    font-weight: 700;
    font-size: 14px;
    display: inline-block;
    vertical-align: baseline;
    line-height: 38px;
    outline: 0;
    background-color: transparent;
}

.btn-main {
    color: #fff;
    box-shadow: 0 2px 5px rgba(0,28,88,.3);
}



.btn-side {
    color: #6d798c;
    box-shadow: 0 2px 5px rgba(0,0,0,.1);
}

.btn-login {
    background-position: 0 -208px;
}

    .btn-login:hover {
        background-position: 0 -256px;
    }

    .btn-login:active {
        background-position: 0 -304px;
        color: #b5d1ee;
    }

.btn-reg {
    background-position: -117px -208px;
    float: right;
}

    .btn-reg:hover {
        background-position: -117px -256px;
        color: #347bc7;
    }

    .btn-reg:active {
        background-position: -117px -304px;
        color: #6d798c;
    }

.loginFormConf {
    height: 14px;
    line-height: 14px;
    margin-left: 25px;
    margin-top: 20px;
    clear: both;
    width: 245px;
    position: relative;
    color: #848585;
    z-index: 1;
}

.loginFormVer {
    float: left;
    width: 160px;
}

.logintext {
    height: 60px;
    font-size: 20px;
    font-family: "Microsoft YaHei";
    padding-left: 24px;
    font-weight: bold;
    padding-top: 15px;
    color: #fff;
}

/* ewm */
.ewmbox {
    margin: 18px 0 0 0;
}

    .ewmbox div, .ewmbox p {
        display: block;
        float: left;
        color: #666;
    }

    .ewmbox div {
        margin-right: 15px;
    }

/* tab-2 */
.tab-2 .ico-mob {
    top: 12px;
    width: 13px;
    height: 18px;
    background-position: -200px -64px;
}
/* footer */
.footer {
    height: 65px;
    margin: 0 auto;
}

.footer-inner {
    width: 1000px;
    height: 63px;
    overflow: visible;
    margin: 0 auto;
    color: #ffffff;
    position: relative;
    padding-top: 5px;
}

.foottext {
    color: #FFFFFF;
}

    .foottext:hover {
        color: #9cd5ff;
    }

    .foottext:visited {
        color: #9cd5ff;
    }

.copyright {
    margin-left: 12px
}

/* mask */
.mask {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: #000;
    filter: alpha(opacity=30);
    -moz-opacity: 0.3;
    opacity: 0.3;
    z-index: 998
}

.checkbox-custom {
    position: relative;
    padding: 0 15px 0px 48px;
    margin-bottom: 0px;
    display: inline-block;
    color: #fff;
}

    .checkbox-custom input[type="checkbox"] {
        opacity: 0;
        position: absolute;
        cursor: pointer;
        z-index: 2;
        margin: -8px 0 0 0;
        top: 50%;
        left: 3px;
    }

    .checkbox-custom label:before {
        content: '';
        position: absolute;
        top: 45%;
        left: 0;
        margin-top: -10px;
        width: 18px;
        height: 17px;
        display: inline-block;
        border-radius: 2px;
        border: 1px solid #bbb;
        background: #fff;
        margin-left: 25px;
    }

    .checkbox-custom input[type="checkbox"]:checked + label:after {
        position: absolute;
        display: inline-block;
        font-family: 'Glyphicons Halflings';
        content: "\e013";
        top: 42%;
        left: 27px;
        margin-top: -7px;
        font-size: 14px;
        line-height: 1;
        width: 15px;
        height: 15px;
        color: #333;
    }

    .checkbox-custom label {
        cursor: pointer;
        line-height: 1.2;
        font-weight: normal;
        margin-bottom: 0;
        text-align: left;
        margin-top: -5px;
    }

.login-footer, .login-footer a {
    color: #dddddd;
}
/* other */
@-webkit-keyframes shake {
    0% {
        margin-left: -265px;
    }

    25% {
        margin-left: -262px;
    }

    50% {
        margin-left: -265px;
    }

    75% {
        margin-left: -262px;
    }

    100% {
        margin-left: -265px;
    }
}

@-moz-keyframes shake {
    0% {
        margin-left: -265px;
    }

    25% {
        margin-left: -262px;
    }

    50% {
        margin-left: -265px;
    }

    75% {
        margin-left: -262px;
    }

    100% {
        margin-left: -265px;
    }
}

@-o-keyframes shake {
    0% {
        margin-left: -265px;
    }

    25% {
        margin-left: -262px;
    }

    50% {
        margin-left: -265px;
    }

    75% {
        margin-left: -262px;
    }

    100% {
        margin-left: -265px;
    }
}

@keyframes shake {
    0% {
        margin-left: -265px;
    }

    25% {
        margin-left: -262px;
    }

    50% {
        margin-left: -265px;
    }

    75% {
        margin-left: -262px;
    }

    100% {
        margin-left: -265px;
    }
}


/*3条标语*/
.runarea1 {
    margin: 0 auto;
    height: 536px;
    width: 1000px;
    overflow: hidden;
    position: relative;
    height: 100px;
    border: 0px solid #fff;
    color: #fff;
    top: 40px;
    left: 50px;
}

.runbox1 {
    clear: both;
    width: 300px;
    height: 100px;
    overflow: hidden;
    position: relative;
}

    .runbox1 li {
        clear: both;
        width: 300px;
        height: 100px;
        overflow: hidden;
    }

.run_prev1, .run_next1 {
    display: hidden;
}
/*底部新闻3条*/
#mainMask {
    position: absolute;
    top: 496px;
    left: 0;
    width: 100%;
    height: 40px;
    background: #000;
    opacity: 0.3;
    filter: alpha(opacity=30);
    z-index: 1;
}

.lg-list-news {
    position: absolute;
    top: 496px;
    left: 0;
    height: 40px;
    z-index: 2;
    padding: 0;
    width: 100%;
}

    .lg-list-news ul {
        width: 960px;
        height: 40px;
        margin: 0 auto;
        position: relative;
        padding: 0;
    }

    .lg-list-news li {
        text-align: left;
        padding: 0;
        margin: 0;
        float: left;
        width: 300px;
        margin: 0 9px;
        height: 40px;
        line-height: 40px;
        overflow: hidden;
    }

    .lg-list-news a {
        color: #ccff66;
    }

    .lg-list-news span a {
        color: #fff
    }


/*list*/
.logolist {
    height: 80px;
    background: #ddd;
}

.logobox {
    width: 980px;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
    _height: 100%;
    border: 0px solid #ccc;
}

.picbox {
    width: 860px;
    height: 85px;
    overflow: hidden;
    position: relative;
    left: 60px;
}

.piclist {
    width: 860px;
    height: 85px;
    overflow: hidden;
}

    .piclist li {
        width: 172px;
        margin: 5px 0px;
        float: left;
        text-align: center;
    }

.swaplist {
    position: absolute;
    left: -4000px;
    top: 0px
}

.og_prev, .og_next {
    width: 60px;
    height: 50px;
    background: url(../../images/login-kfqzc/icon.png) no-repeat;
    position: absolute;
    top: 15px;
    z-index: 99;
    cursor: pointer;
    filter: alpha(opacity=70);
    opacity: 0.7;
}

.og_prev {
    background-position: 15px -60px;
    left: 4px;
}

.og_next {
    background-position: 15px 0;
    right: 4px;
}

.footer {
    clear: both;
    text-align: center;
    line-height: 30px;
    color: #99ccff;
    margin-top: 20px;
}

/* 20150913鼠标响应登录弹窗 */
#btnReg_mask {
    width: 110px;
    height: 80px;
    z-index: 10;
    position: absolute;
    background: #ebf8f5;
}

#btnReg_qy, #btnReg_xx {
    width: 110px;
    height: 40px;
    float: left;
    text-align: center;
    cursor: pointer;
    border: 0px none;
    padding: 0px;
    font-weight: 700;
    font-size: 14px;
    display: inline-block;
    vertical-align: baseline;
    line-height: 38px;
    outline: 0px none;
    background-color: transparent;
    ;
    background-position: -117px -208px;
    position: absolute;
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
}


/*二维码扫码登录开始*/

#divLogType {
    text-align: center;
    padding: 30px 0px 0px 20px
}

    #divLogType a {
        width: 99%;
        height: 18px;
        left: 0;
        top: 18px;
        text-decoration: none;
        /*color: #666;*/
        font-size: 18px;
        text-align: center;
        cursor: pointer;
    }

        #divLogType a.checked, #divLogType a:hover {
            font-weight: 700;
            color: #e4393c;
        }

    #divLogType div {
        float: left;
        width: 120px;
    }

.currentLoginType {
    font-weight: 700;
    color: #e4393c;
}

.defaultLoginType {
    color: #666;
}

.refresh-btn {
    display: block;
    background: #e4393c;
    width: 80px;
    height: 30px;
    position: absolute;
    top: 95px;
    left: 113px;
    line-height: 30px;
    opacity: 1;
    z-index: 19;
    color: #fbfbfb;
    text-decoration: none;
}

#divWxOpen {
    text-align: center;
    padding: 20px;
}

    #divWxOpen p {
        font-size: 14px;
        /*padding: 5px;*/
    }

#imgQrCode {
    border: 10px solid #fff;
}

.qrcode-error {
    font-size: 14px;
    color: #fbfbfb;
    margin: -218px 0px 0px -30px;
    position: relative;
    z-index: 1;
}

    .qrcode-error .qrcode-error-mask {
        display: block;
        position: absolute;
        width: 178px;
        background: #000;
        filter: alpha(opacity=60);
        -moz-opacity: .6;
        opacity: .6;
        height: 180px;
        left: 70px;
        z-index: 9;
        top: 0;
        text-align: center;
        line-height: 100px;
    }

    .qrcode-error .err-cont {
        position: absolute;
        left: 0;
        top: 55px;
        width: 100%;
        z-index: 19;
        font-weight: 700;
    }

    .qrcode-error .refresh-btn {
        display: block;
        background: #e4393c;
        width: 80px;
        height: 30px;
        position: absolute;
        top: 95px;
        left: 113px;
        line-height: 30px;
        opacity: 1;
        z-index: 19;
        color: #fbfbfb;
        text-decoration: none;
    }

.login-panel .form-control {
    display: block;
}

.login-panel .uname {
    background: #fff url(../../image/user.png) no-repeat 95% center;
    color: #333
}

.login-panel .pword {
    background: #fff url(../../image/locked.png) no-repeat 95% center;
    color: #333
}

.yzmInput {
    margin-left: 10px;
    margin-bottom: 15px;
}

#imgCaptchaCode {
    margin-left: 15px;
}
/*二维码扫码登录结束*/
