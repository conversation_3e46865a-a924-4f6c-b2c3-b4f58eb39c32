/*! JsObservable v1.0.13: http://jsviews.com/#jsobservable */
!function(t,e){var n=e.jQuery;"object"==typeof exports?module.exports=n?t(e,n):function(n){return t(e,n)}:"function"==typeof define&&define.amd?define(["jquery"],function(n){return t(e,n)}):t(e,!1)}(function(t,e){"use strict";var n=e===!1;if(e=e||t.jQuery,!e||!e.fn)throw"jquery.observable.js requires jQuery";var r,i,o="v1.0.13",s=e.views=e.views||n&&t.jsrender&&jsrender.views||{jsviews:o,sub:{settings:{}},settings:{advanced:function(t){return f=c.advanced=c.advanced||{_jsv:!0},t?("_jsv"in t&&(f._jsv=t._jsv),a.advSet(),s.settings):f}}},a=s.sub,c=a.settings,f=c.advanced,l=e.isFunction,p=e.expando,u=e.isArray,h="string",d="object";if(s.jsviews!==o)throw"jquery.observable.js requires jsrender.js "+o;if(!e.observe){var v=e.event.special,_=[].slice,g=[].splice,b=[].concat,y=parseInt,m=/\S+/g,I=/^[^.[]*$/,w=a.propChng=a.propChng||"propertyChange",j=a.arrChng=a.arrChng||"arrayChange",x={},A=w+".observe",k=1,C=1,F=1,P=e.data,S={},T=[],V=function(t){return t?t._cId=t._cId||".obs"+C++:""},q=function(t,e){return this._data=e,this._ns=t,this},D=function(t,e){return this._data=e,this._ns=t,this},O=function(t){return u(t)?[t]:t},H=function(t,e,n){t=t?u(t)?t:[t]:[];var r,i,o,s,a=o=e,c=t&&t.length,f=[];for(r=0;r<c;r++)i=t[r],l(i)?(s=e.tagName?e.linkCtx.data:e,f=f.concat(H(i.call(e,s,n),s,n))):typeof i===h?(a!==o&&f.push(o=a),f.push(i)):(e=a=i=void 0===i?null:i,a!==o&&f.push(o=a));return f.length&&(f.unshift({_ar:1}),f.push({_ar:-1})),f},Q=function(t,e){function n(t){return typeof t===d&&(h[0]||!p&&u(t))}if(!t.data||!t.data.off){var r,i,o,s=e.oldValue,a=e.value,c=t.data,f=c.observeAll,l=c.cb,p=c._arOk?0:1,h=c.paths,v=c.ns;t.type===j?(l.array||l).call(c,t,e):c.prop!==e.path&&"*"!==c.prop||(f?(r=f._path+"."+e.path,i=f.filter,o=[t.target].concat(f.parents()),n(s)&&M(void 0,v,[s],h,l,!0,i,[o],r),n(a)&&M(void 0,v,[a],h,l,void 0,i,[o],r)):(n(s)&&M(p,v,[s],h,l,!0),n(a)&&M(p,v,[a],h,l)),c.cb(t,e))}},M=function(){var t=b.apply([],arguments);return r.apply(t.shift(),t)},B=function(t,e,n){$(this._ns,this._data,t,e,[],"root",n)},N=function(t,e){B.call(this,t,e,!0)},$=function(t,n,o,s,a,c,f,l){function h(t,e){for(g=t.length,y=c+"[]";g--;)v(t,g,e,1)}function v(e,n,r,a){var c,f;+n!==n&&n===p||!(c=i._fltr(y,e[n],m,s))||(f=m.slice(),a&&I&&f[0]!==I&&f.unshift(I),$(t,c,o,s||(a?void 0:0),f,y,r,l))}function _(t,e){switch(c=t.data.observeAll._path,I=t.target,e.change){case"insert":h(e.items);break;case"remove":h(e.items,!0);break;case"set":y=c+"."+e.path,v(e,"oldValue",!0),v(e,"value")}I=void 0,o.apply(this,arguments)}_._wrp=1;var g,b,y,m,I,w,j=!l||l.un||!f;if(n&&typeof n===d){if(m=[n].concat(a),b=u(n)?"":"*",l&&j&&e.hasData(n)&&l[w=P(n).obId])return void l[w]++;if(l||(l={un:f}),o){if(b||0!==s)if(_._cId=V(o),j)r(t,n,b,_,f,s,m,c),w=P(n).obId,l[w]=(l[w]||0)+1;else{if(--l[P(n).obId])return;r(t,n,b,_,f,s,m,c)}}else l&&(l[P(n).obId]=1),r(t,n,b,void 0,f,s,m,c);if(b)for(g in n)y=c+"."+g,v(n,g,f);else h(n,f)}},z=function(t){return I.test(t)},E=function(){return[].push.call(arguments,!0),r.apply(void 0,arguments)},G=function(t){var e,n=this.slice();for(this.length=0,this._go=0;e=n.shift();)e.skip||e[0]._trigger(e[1],e[2],!0);this.paths={}};r=function(){function t(){function s(t,e){var n;for(q in e)n=e[q],u(n)?f(t,n,I,I):c(t,n,void 0,W,"")}function c(t,r,i,s,a,c,f){var l,p,u,h=O(r),d=$,v=z;if(s=n?s+"."+n:s,!I&&(f||c))for(R=e._data(r).events,R=R&&R[c?j:w],U=R&&R.length;U--;)if(q=R[U]&&R[U].data,q&&(f&&q.ns!==n||!f&&q.ns===n&&q.cb&&q.cb._cId===t._cId&&(!t._wrp||q.cb._wrp)))return;I||f?e(h).off(s,Q):(p=c?{}:{fullPath:i,paths:a?[a]:[],prop:L,_arOk:o},p.ns=n,p.cb=t,z&&(p.observeAll={_path:v,path:function(){return l=d.length,v.replace(/[[.]/g,function(t){return l--,"["===t?"["+e.inArray(d[l-1],d[l]):"."})},parents:function(){return d},filter:E}),e(h).on(s,null,p,Q),B&&(u=P(r),u=u.obId||(u.obId=k++),B[u]=B[u]||(B.len++,r)))}function f(t,e,n,r,s){if(o){var a,f=z;a=e,s&&(a=e[s],z=z?z+"."+s:z),E&&a&&(a=i._fltr(z,a,s?[e].concat($):$,E)),a&&(r||u(a))&&c(t,a,void 0,j+".observe"+V(t),void 0,!0,n),z=f}}function v(i){function s(i,_,g,m){function x(e){return e.ob=m(e),e.cb=function(n,r){var i=e.ob,s=e.sb,a=m(e);a!==i&&(typeof i===d&&(f(g,i,!0),(s||o&&u(i))&&t([i],s,g,m,!0)),e.ob=a,typeof a===d&&(f(g,a),(s||o&&u(a))&&t([a],s,g,m))),g(n,r)}}function A(t,i){function a(t,e){var n;if("insert"===e.change||(I="remove"===e.change)){for(n=e.items.length;n--;)A(e.items[n],i.slice());I=!1}}g&&(a._cId=V(g));var _,y,x,k,F,S,T,D=t;if(t&&t._cxp)return s(t[0],[t[1]],g,m);for(;void 0!==(L=i.shift());){if(D&&typeof D===d&&typeof L===h){if(""===L)continue;if("()"===L.slice(-2)&&(L=L.slice(0,-2),T=!0),i.length<P+1&&!D.nodeType){if(!I&&(R=e._data(D).events)){for(R=R&&R[w],U=R&&R.length,y=0;U--;)q=R[U].data,!q||q.ns!==n||q.cb._cId!==g._cId||q.cb._inId!==g._inId||!q._arOk!=!o||q.prop!==L&&"*"!==q.prop&&"**"!==q.prop||((F=i.join("."))&&q.paths.push(F),y++);if(y){D=D[L];continue}}if("*"===L||"**"===L){if(!I&&R&&R.length&&c(g,D,C,W,"",!1,!0),"*"===L){c(g,D,C,W,"");for(F in D)F!==p&&f(g,D,I,void 0,F)}else e.observable(n,D)[(I?"un":"")+"observeAll"](g);break}"[]"==L?u(D)&&(I?c(g,D,C,j+V(g),void 0,I,I):r(n,D,a,I)):L&&c(g,D,C,W+".p_"+L,i.join("^"))}if(z&&(z+="."+L),"[]"===L){for(u(D)&&(k=D,_=D.length);_--;)D=k[_],A(D,i.slice());return}L=D[L],i[0]||f(g,L,I)}if(l(L)&&(S=L,(x=S.depends)&&(D._vw&&D._ocp&&(D=D._vw,D._tgId&&(D=D.tagCtx.view),D=D.data),v(b.apply([],[[D],H(x,D,g)]))),T)){if(!i[0]){f(g,S.call(D),I);break}if(L=S.call(D),!L)break}D=L}}var k,C,P=0,S=_.length;for(!i||m||!(K="view"===i._is)&&"tag"!==i._is||(m=a._gccb(K?i:i.tagCtx.contentView),g&&!I&&!function(){var t=i,e=g;g=function(n,r){e.call(t,n,r)},g._cId=e._cId,g._inId=e._inId}(),i=K?i.data:i),_[0]||(u(i)?f(g,i,I,!0):I&&c(g,i,void 0,W,"")),k=0;k<S;k++)if(C=_[k],""!==C)if(C&&C._ar)o+=C._ar;else if(typeof C===h)if(y=C.split("^"),y[1]&&(P=y[0].split(".").length,C=y.join("."),P=C.split(".").length-P),m&&(M=m(C,P))){if(M.length){var T=M[0],D=M[1];if(T&&T._cxp&&(D=T[1],T=T[0],"view"===T._is)){s(T,[D],g);continue}typeof D===h?A(T,D.split(".")):s(M.shift(),M,g,m)}}else A(i,C.split("."));else!l(C)&&C&&C._cpfn&&(N=I?C.cb:x(C),N._cId=g._cId,N._inId=N._inId||".obIn"+F++,(C.bnd||C.prm&&C.prm.length||!C.sb)&&t([i],C.path,C.prm.length?[C.root||i]:[],C.prm,N,m,I),C.sb&&(C.sb.prm&&(C.sb.root=i),s(C.ob,[C.sb],g,m)))}for(var _,g=[],m=i.length;m--;)_=i[m],typeof _===h||_&&(_._ar||_._cpfn)?g.unshift(_):(s(_,g,C,D),g=[])}var g,y,I,C,S,T,q,D,M,B,N,$,z,E,G,J,K,L,R,U,W=A,X=1!=this?b.apply([],arguments):_.call(arguments),Y=X.pop()||!1,Z=X.length;if(typeof Y===h&&(z=Y,$=X.pop(),E=X.pop(),Y=!!X.pop(),Z-=3),Y===!!Y&&(I=Y,Y=X[Z-1],Y=!Z||typeof Y===h||Y&&!l(Y)?void 0:(Z--,X.pop()),I&&!Z&&l(X[0])&&(Y=X.shift())),C=Y,Z&&l(X[Z-1])&&(D=C,Y=C=X.pop(),Z--),!I||!C||C._cId){for(W+=C?(T=C._inId||"",I?C._cId+T:(S=V(C))+T):"",S&&!I&&(B=x[S]=x[S]||{len:0}),G=n&&n.match(m)||[""],J=G.length;J--;){if(n=G[J],I&&arguments.length<3)if(C)s(C,x[C._cId]);else if(!X[0])for(g in x)s(C,x[g]);v(X)}return S&&!B.len&&delete x[S],{cbId:S,bnd:B,s:x}}}var n,o=1==this?0:1,s=_.call(arguments),c=s[0];return typeof c===h&&(n=c,s.shift()),t.apply(1,s)},T.wait=function(){var t=this;t._go=1,setTimeout(function(){t.trigger(!0),t._go=0,t.paths={}})},i=function(t,e,n){typeof t!==h&&(n=e,e=t,t=""),n=void 0===n?f.asyncObserve:n;var r=u(e)?new D(t,e):new q(t,e);return n&&(n===!0&&(r.async=!0,n=T),n.trigger||(u(n)?(n.trigger=G,n.paths={}):n=void 0),r._batch=n),r},e.observable=i,i._fltr=function(t,e,n,r){if(!r||!l(r)||r(t,e,n))return e=l(e)?e.set&&e.call(n[0]):e,typeof e===d&&e},i.Object=q,i.Array=D,e.observe=i.observe=r,e.unobserve=i.unobserve=E,i._apply=M,q.prototype={_data:null,observeAll:B,unobserveAll:N,data:function(){return this._data},setProperty:function(t,e,n,r){t=t||"";var i,o,s,a,c=typeof t!==h,f=this,l=f._data,d=f._batch;if(l)if(c)if(n=e,u(t))for(i=t.length;i--;)o=t[i],f.setProperty(o.name,o.value,void 0===n||n);else{d||(f._batch=a=[],a.trigger=G,a.paths={});for(i in t)f.setProperty(i,t[i],n);a&&(f._batch.trigger(),f._batch=void 0)}else if(t!==p){for(s=t.split(/[.^]/);l&&s.length>1;)l=l[s.shift()];l&&f._setProperty(l,s[0],e,n,r)}return f},removeProperty:function(t){return this.setProperty(t,S),this},_setProperty:function(t,e,n,r,i){var o,s,a,c,f,p=e?t[e]:t;if(l(p)&&!l(n)){if(i&&!p.set)return;p.set&&(f=t._vw||t,s=p,o=s.set===!0?s:s.set,p=s.call(f))}(p!==n||r&&p!=n)&&(!(p instanceof Date&&n instanceof Date)||p>n||p<n)&&(o?(o.call(f,n),n=s.call(f)):(a=n===S)?void 0!==p?(delete t[e],n=void 0):e=void 0:e&&(t[e]=n),e&&(c={change:"set",path:e,value:n,oldValue:p,remove:a},t._ocp&&(c.ctxPrm=t._key),this._trigger(t,c)))},_trigger:function(t,n,r){c._cchCt++;var i,o,s,a=this;e.hasData(t)&&(!r&&(o=a._batch)?(a.async&&!o._go&&o.wait(),o.push([a,t,n]),i=P(t).obId+n.path,(s=o.paths[i])&&(o[s-1].skip=1),o.paths[i]=o.length):(e(t).triggerHandler(w+(this._ns?"."+/^\S+/.exec(this._ns)[0]:""),n),n.oldValue=null))}},D.prototype={_data:null,observeAll:B,unobserveAll:N,data:function(){return this._data},insert:function(t,e){var n=this._data;return 1===arguments.length&&(e=t,t=n.length),t=y(t),t>-1&&(e=u(e)?e:[e],e.length&&this._insert(t,e)),this},_insert:function(t,e){var n=this._data,r=n.length;t>r&&(t=r),g.apply(n,[t,0].concat(e)),this._trigger({change:"insert",index:t,items:e},r)},remove:function(t,e){var n,r=this._data;return void 0===t&&(t=r.length-1),t=y(t),e=e?y(e):0===e?0:1,e>0&&t>-1&&(n=r.slice(t,t+e),(e=n.length)&&this._remove(t,e,n)),this},_remove:function(t,e,n){var r=this._data,i=r.length;r.splice(t,e),this._trigger({change:"remove",index:t,items:n},i)},move:function(t,e,n){return n=n?y(n):0===n?0:1,t=y(t),e=y(e),n>0&&t>-1&&e>-1&&t!==e&&this._move(t,e,n),this},_move:function(t,e,n){var r,i=this._data,o=i.length,s=t+n-o;s>0&&(n-=s),n&&(r=i.splice(t,n),e>i.length&&(e=i.length),g.apply(i,[e,0].concat(r)),e!==t&&this._trigger({change:"move",oldIndex:t,index:e,items:r},o))},refresh:function(t){function e(){i&&(a.insert(r-i,c),u+=i,n+=i,i=0,c=[])}var n,r,i,o,s,a=this,c=[],f=a._data,l=f.slice(),p=f.length,u=p,h=t.length;for(a._srt=!0,r=i=0;r<h;r++)if((o=t[r])===f[r-i])e();else{for(n=r-i;n<u&&o!==f[n];n++);if(n<u){for(e(),s=0;s++<h-n&&t[r+s]===f[n+s];);a.move(n,r,s),r+=s-1}else i++,c.push(o)}return e(),u>r&&a.remove(r,u-r),a._srt=void 0,(p||h)&&a._trigger({change:"refresh",oldItems:l},p),a},_trigger:function(t,n,r){c._cchCt++;var i,o,s,a=this;e.hasData(o=a._data)&&(!r&&(s=a._batch)?(t._dly=!0,s.push([a,t,n]),a.async&&!s._go&&s.wait()):(i=o.length,o=e([o]),a._srt?t.refresh=!0:i!==n&&o.triggerHandler(w,{change:"set",path:"length",value:i,oldValue:n}),o.triggerHandler(j+(a._ns?"."+/^\S+/.exec(a._ns)[0]:""),t)))}},v[w]=v[j]={remove:function(t){var n,r,i,o,s,a=t.data;if(a&&(a.off=!0,a=a.cb)&&(n=x[a._cId])){for(i=e._data(this).events[t.type],o=i.length;o--&&!r;)r=(s=i[o].data)&&s.cb&&s.cb._cId===a._cId;r||(--n.len?delete n[P(this).obId]:delete x[a._cId])}}},s.map=function(t){function n(e,n,r,o){var s,a,c=this;c.src&&c.unmap(),n&&(n.map=c),(typeof e===d||l(e))&&(c.src=e,o?c.tgt=t.getTgt(e,n):(r&&(c.tgt=r.tgt||u(r)&&r),c.tgt=c.tgt||[],c.options=n||c.options,(a=c.update())?c=a:(t.obsSrc&&i(c.src).observeAll(c.obs=function(e,n){s||n.refresh||(s=!0,t.obsSrc(c,e,n),s=void 0)},c.srcFlt),t.obsTgt&&i(c.tgt).observeAll(c.obt=function(e,n){s||c.tgt._updt||(s=!0,t.obsTgt(c,e,n),s=void 0)},c.tgtFlt))))}return l(t)&&(t={getTgt:t}),t.baseMap&&(t=e.extend({},t.baseMap,t)),t.map=function(t,e,r,i){return new n(t,e,r,i)},(n.prototype={srcFlt:t.srcFlt||z,tgtFlt:t.tgtFlt||z,update:function(e){var n,r,o=this,s=o.tgt;if(!s._updt&&(s._updt=!0,n=o.options&&o.options.map,i(s).refresh(t.getTgt(o.src,o.options=e||o.options)),s._updt=!1,r=o.options&&o.options.map,r&&n!==r))return r},observe:function(t,n){var r=this,o=r.options;r.obmp&&E(r.obmp),r.obmp=function(){var t=n.fn(n.data,n.view,a)[o.index];e.extend(o.props,t.props),o.args=t.args,r.update()},i._apply(1,n.data,H(t,n.tag,r.obmp),r.obmp,n._ctxCb)},unmap:function(){var t=this;t.src&&t.obs&&i(t.src).unobserveAll(t.obs,t.srcFlt),t.tgt&&t.obt&&i(t.tgt).unobserveAll(t.obt,t.tgtFlt),t.obmp&&E(t.obmp),t.src=void 0},map:n,_def:t}).constructor=n,t},a.advSet=function(){a=this,f=c.advanced,t._jsv=f._jsv?{cbBindings:x}:void 0},a._dp=H,a._gck=V,a._obs=r,c._cchCt=0,f=c.advanced=f||{useViews:!1,_jsv:!1}}return e},window);
//# sourceMappingURL=jquery.observable.min.js.map
