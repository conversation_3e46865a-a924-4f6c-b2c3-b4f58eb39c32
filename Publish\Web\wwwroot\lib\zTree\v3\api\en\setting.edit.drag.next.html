<div class="apiDetail">
<div>
	<h2><span>Boolean / Function(treeId, treeNodes, targetNode)</span><span class="path">setting.edit.drag.</span>next</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.exedit</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>When drag one node to the target node, set whether to allow the node to be the target node's next sibling. It is valid when <span class="highlight_red">[setting.edit.enable = true]</span></p>
			<p class="highlight_red">If the target node is root, so z<PERSON><PERSON> will only trigger 'inner' and not trigger 'prev / next'.</p>
			<p class="highlight_red">This function mainly for the appropriate limit drag and drop (auxiliary arrow), it requires a combination of 'prev, inner' together, to achieve full functionality.</p>
			<p>Default: true</p>
		</div>
	</div>
	<h3>Boolean Format</h3>
	<div class="desc">
	<p> true means: allow the node to be the target node's next sibling.</p>
	<p> false means: don't allow the node to be the target node's next sibling.</p>
	</div>
	<h3>Function Parameter Descriptions</h3>
	<div class="desc">
	<h4><b>treeId</b><span>String</span></h4>
	<p>zTree unique identifier: <b class="highlight_red">treeId</b>.</p>
	<h4 class="topLine"><b>treeNodes</b><span>Array(JSON)</span></h4>
	<p>A collection of the nodes which has been dragged</p>
	<h4 class="topLine"><b>targetNode</b><span>JSON</span></h4>
	<p>JSON data object of the target node which treeNodes are draged over.</p>
	<h4 class="topLine"><b>Return </b><span>Boolean</span></h4>
	<p>return true or false</p>
	</div>
	<h3>Examples of setting & function</h3>
	<h4>1. disable to drag the node to the target node's next sibling.</h4>
	<pre xmlns=""><code>var setting = {
	edit: {
		enable: true,
		drag: {
			prev: true,
			next: false,
			inner: true
		}
	}
};
......</code></pre>
	<h4>2. disable to drag the node to be all of the parent nodes's next sibling.</h4>
	<pre xmlns=""><code>function canNext(treeId, nodes, targetNode) {
	return !targetNode.isParent;
}
var setting = {
	edit: {
		enable: true,
		drag: {
			prev: true,
			next: canNext,
			inner: true
		}
	}
};
......</code></pre>
</div>
</div>