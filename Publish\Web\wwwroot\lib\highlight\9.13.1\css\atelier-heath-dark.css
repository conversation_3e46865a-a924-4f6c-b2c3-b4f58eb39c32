/* Base16 Atelier Heath Dark - Theme */
/* by <PERSON> (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/heath) */
/* Original Base16 color scheme by <PERSON> (https://github.com/chris<PERSON><PERSON>on/base16) */

/* Atelier-Heath Comment */
.hljs-comment,
.hljs-quote {
  color: #9e8f9e;
}

/* Atelier-Heath Red */
.hljs-variable,
.hljs-template-variable,
.hljs-attribute,
.hljs-tag,
.hljs-name,
.hljs-regexp,
.hljs-link,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class {
  color: #ca402b;
}

/* Atelier-Heath Orange */
.hljs-number,
.hljs-meta,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params {
  color: #a65926;
}

/* Atelier-Heath Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet {
  color: #918b3b;
}

/* Atelier-Heath Blue */
.hljs-title,
.hljs-section {
  color: #516aec;
}

/* Atelier-Heath Purple */
.hljs-keyword,
.hljs-selector-tag {
  color: #7b59c0;
}

.hljs {
  display: block;
  overflow-x: auto;
  background: #1b181b;
  color: #ab9bab;
  padding: 0.5em;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}
