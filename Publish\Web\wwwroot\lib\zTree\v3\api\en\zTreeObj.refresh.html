<div class="apiDetail">
<div>
	<h2><span>Function()</span><span class="path">zTreeObj.</span>refresh</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.core</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Refresh zTree</p>
			<p class="highlight_red">If you have no special need, try not to use this method. If you refresh single node, please use updateNode() method. If you refresh child nodes in dynamic mode, please use the reAsyncChildNodes() method.</p>
			<p class="highlight_red">Please use zTree object to executing the method.</p>
		</div>
	</div>
	<h3>Function Parameter Descriptions</h3>
	<div class="desc">
	<h4><b>Return </b><span>none</span></h4>
	<p>no return value</p>
	</div>
	<h3>Examples of function</h3>
	<h4>1. refresh zTree </h4>
	<pre xmlns=""><code>var treeObj = $.fn.zTree.getZTreeObj("tree");
treeObj.refresh();
</code></pre>
</div>
</div>