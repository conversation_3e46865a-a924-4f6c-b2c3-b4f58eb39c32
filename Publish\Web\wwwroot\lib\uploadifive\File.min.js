(function(n){var t={init:function(r){return this.each(function(){var e=n(this),f,u,s,o;if(e.data("uploadifive",{inputs:{},inputCount:0,fileID:0,queue:{count:0,selected:0,replaced:0,errors:0,queued:0,cancelled:0},uploads:{current:0,attempts:0,successful:0,errors:0,count:0}}),f=e.data("uploadifive"),u=f.settings=n.extend({auto:!0,buttonClass:!1,buttonText:"Select Files",checkScript:!1,dnd:!0,dropTarget:!1,fileObjName:"Filedata",fileSizeLimit:0,fileType:!1,formData:{},height:30,itemTemplate:!1,method:"post",multi:!0,overrideEvents:[],queueID:!1,queueSizeLimit:0,removeCompleted:!1,simUploadLimit:0,truncateLength:0,uploadLimit:0,uploadScript:"uploadifive.php",width:100},r),isNaN(u.fileSizeLimit)?(s=parseInt(u.fileSizeLimit)*1.024,u.fileSizeLimit.indexOf("KB")>-1?u.fileSizeLimit=s*1e3:u.fileSizeLimit.indexOf("MB")>-1?u.fileSizeLimit=s*1e6:u.fileSizeLimit.indexOf("GB")>-1&&(u.fileSizeLimit=s*1e9)):u.fileSizeLimit=u.fileSizeLimit*1024,f.inputTemplate=n('<input type="file">').css({"font-size":u.height+"px",opacity:0,position:"absolute",right:"-3px",top:"-3px","z-index":999}),f.createInput=function(){var i=f.inputTemplate.clone(),r=i.name="input"+f.inputCount++;u.multi&&i.attr("multiple",!0);u.fileType&&i.attr("accept",u.fileType);i.bind("change",function(){var i,o;if(f.queue.selected=0,f.queue.replaced=0,f.queue.errors=0,f.queue.queued=0,i=this.files.length,f.queue.selected=i,f.queue.count+i>u.queueSizeLimit&&u.queueSizeLimit!==0)n.inArray("onError",u.overrideEvents)<0&&alert("The maximum number of queue items has been reached ("+u.queueSizeLimit+").  Please select fewer files."),typeof u.onError=="function"&&u.onError.call(e,"QUEUE_LIMIT_EXCEEDED");else{for(o=0;o<i;o++)file=this.files[o],f.addQueueItem(file);f.inputs[r]=this;f.createInput()}u.auto&&t.upload.call(e);typeof u.onSelect=="function"&&u.onSelect.call(e,f.queue)});f.currentInput&&f.currentInput.hide();f.button.append(i);f.currentInput=i},f.destroyInput=function(t){n(f.inputs[t]).remove();delete f.inputs[t];f.inputCount--},f.drop=function(i){var o;f.queue.selected=0;f.queue.replaced=0;f.queue.errors=0;f.queue.queued=0;var r=i.dataTransfer,h=r.name="input"+f.inputCount++,s=r.files.length;if(f.queue.selected=s,f.queue.count+s>u.queueSizeLimit&&u.queueSizeLimit!==0)n.inArray("onError",u.overrideEvents)<0&&alert("The maximum number of queue items has been reached ("+u.queueSizeLimit+").  Please select fewer files."),typeof u.onError=="function"&&u.onError.call(e,"QUEUE_LIMIT_EXCEEDED");else{for(o=0;o<s;o++)file=r.files[o],f.addQueueItem(file);f.inputs[h]=r}u.auto&&t.upload.call(e);typeof u.onDrop=="function"&&u.onDrop.call(e,r.files,r.files.length);i.preventDefault();i.stopPropagation()},f.fileExistsInQueue=function(n){var i,t;for(i in f.inputs)for(input=f.inputs[i],limit=input.files.length,t=0;t<limit;t++)if(existingFile=input.files[t],existingFile.name==n.name&&!existingFile.complete)return!0;return!1},f.removeExistingFile=function(n){var r,i;for(r in f.inputs)for(input=f.inputs[r],limit=input.files.length,i=0;i<limit;i++)existingFile=input.files[i],existingFile.name!=n.name||existingFile.complete||(f.queue.replaced++,t.cancel.call(e,existingFile,!0))},f.queueItem=u.itemTemplate==!1?n('<div class="uploadifive-queue-item">                        <a class="close" href="#">X<\/a>                        <div><span class="filename"><\/span><span class="fileinfo"><\/span><\/div>                        <div class="progress">                            <div class="progress-bar"><\/div>                        <\/div>                    <\/div>'):n(u.itemTemplate),f.addQueueItem=function(i){if(n.inArray("onAddQueueItem",u.overrideEvents)<0){f.removeExistingFile(i);i.queueItem=f.queueItem.clone();i.queueItem.attr("id",u.id+"-file-"+f.fileID++);i.queueItem.find(".close").bind("click",function(){return t.cancel.call(e,i),!1});var r=i.name;r.length>u.truncateLength&&u.truncateLength!=0&&(r=r.substring(0,u.truncateLength)+"...");i.queueItem.find(".filename").html(r);i.queueItem.data("file",i);f.queueEl.append(i.queueItem)}typeof u.onAddQueueItem=="function"&&u.onAddQueueItem.call(e,i);i.size>u.fileSizeLimit&&u.fileSizeLimit!=0?f.error("FILE_SIZE_LIMIT_EXCEEDED",i):(f.queue.queued++,f.queue.count++)},f.removeQueueItem=function(t,i,r){r||(r=0);var u=i?0:500;t.queueItem&&(t.queueItem.find(".fileinfo").html()!=" - Completed"&&t.queueItem.find(".fileinfo").html(" - Cancelled"),t.queueItem.find(".progress-bar").width(0),t.queueItem.delay(r).fadeOut(u,function(){n(this).remove()}),delete t.queueItem,f.queue.count--)},f.filesToUpload=function(){var t=0,i,n;for(i in f.inputs)for(input=f.inputs[i],limit=input.files.length,n=0;n<limit;n++)file=input.files[n],file.skip||file.complete||t++;return t},f.checkExists=function(i){if(n.inArray("onCheck",u.overrideEvents)<0){n.ajaxSetup({"async":!1});var r=n.extend(u.formData,{filename:i.name});if(n.post(u.checkScript,r,function(n){i.exists=parseInt(n)}),i.exists&&!confirm("A file named "+i.name+" already exists in the upload folder.\nWould you like to replace it?"))return t.cancel.call(e,i),!0}return typeof u.onCheck=="function"&&u.onCheck.call(e,i,i.exists),!1},f.uploadFile=function(t,r){var o,s;if(!t.skip&&!t.complete&&!t.uploading)if(t.uploading=!0,f.uploads.current++,f.uploads.attempted++,xhr=t.xhr=new XMLHttpRequest,typeof FormData=="function"||typeof FormData=="object"){o=new FormData;o.append(u.fileObjName,t);for(i in u.formData)o.append(i,u.formData[i]);xhr.open(u.method,u.uploadScript,!0);xhr.upload.addEventListener("progress",function(n){n.lengthComputable&&f.progress(n,t)},!1);xhr.addEventListener("load",function(n){this.readyState==4&&(t.uploading=!1,this.status==200?t.xhr.responseText!=="Invalid file type."?f.uploadComplete(n,t,r):f.error(t.xhr.responseText,t,r):this.status==404?f.error("404_FILE_NOT_FOUND",t,r):this.status==403?f.error("403_FORBIDDEN",t,r):f.error("Unknown Error",t,r))});xhr.send(o)}else s=new FileReader,s.onload=function(i){var h="-------------------------"+(new Date).getTime(),c="--",s="\r\n",o="",l,a;o+=c+h+s;o+='Content-Disposition: form-data; name="'+u.fileObjName+'"';t.name&&(o+='; filename="'+t.name+'"');o+=s;o+="Content-Type: application/octet-stream"+s+s;o+=i.target.result+s;for(key in u.formData)o+=c+h+s,o+='Content-Disposition: form-data; name="'+key+'"'+s+s,o+=u.formData[key]+s;o+=c+h+c+s;xhr.upload.addEventListener("progress",function(n){f.progress(n,t)},!1);xhr.addEventListener("load",function(n){t.uploading=!1;var i=this.status;i==404?f.error("404_FILE_NOT_FOUND",t,r):t.xhr.responseText!="Invalid file type."?f.uploadComplete(n,t,r):f.error(t.xhr.responseText,t,r)},!1);l=u.uploadScript;u.method=="get"&&(a=n(u.formData).param(),l+=a);xhr.open(u.method,u.uploadScript,!0);xhr.setRequestHeader("Content-Type","multipart/form-data; boundary="+h);typeof u.onUploadFile=="function"&&u.onUploadFile.call(e,t);xhr.sendAsBinary(o)},s.readAsBinaryString(t)},f.progress=function(t,i){if(n.inArray("onProgress",u.overrideEvents)<0){if(t.lengthComputable)var r=Math.round(t.loaded/t.total*100);i.queueItem.find(".fileinfo").html(" - "+r+"%");i.queueItem.find(".progress-bar").css("width",r+"%")}typeof u.onProgress=="function"&&u.onProgress.call(e,i,t)},f.error=function(i,r,o){if(n.inArray("onError",u.overrideEvents)<0){switch(i){case"404_FILE_NOT_FOUND":errorMsg="404 Error";break;case"403_FORBIDDEN":errorMsg="403 Forbidden";break;case"FORBIDDEN_FILE_TYPE":errorMsg="Forbidden File Type";break;case"FILE_SIZE_LIMIT_EXCEEDED":errorMsg="File Too Large";break;default:errorMsg="Unknown Error"}r.queueItem.addClass("error").find(".fileinfo").html(" - "+errorMsg);r.queueItem.find(".progress").remove()}typeof u.onError=="function"&&u.onError.call(e,i,r);r.skip=!0;i=="404_FILE_NOT_FOUND"?f.uploads.errors++:f.queue.errors++;o&&t.upload.call(e,null,!0)},f.uploadComplete=function(i,r,o){n.inArray("onUploadComplete",u.overrideEvents)<0&&(r.queueItem.find(".progress-bar").css("width","100%"),r.queueItem.find(".fileinfo").html(" - Completed"),r.queueItem.find(".progress").slideUp(250),r.queueItem.addClass("complete"));typeof u.onUploadComplete=="function"&&u.onUploadComplete.call(e,r,r.xhr.responseText);u.removeCompleted&&setTimeout(function(){t.cancel.call(e,r)},3e3);r.complete=!0;f.uploads.successful++;f.uploads.count++;f.uploads.current--;delete r.xhr;o&&t.upload.call(e,null,!0)},f.queueComplete=function(){typeof u.onQueueComplete=="function"&&u.onQueueComplete.call(e,f.uploads)},window.File&&window.FileList&&window.Blob&&(window.FileReader||window.FormData))u.id="uploadifive-"+e.attr("id"),f.button=n('<div id="'+u.id+'" class="uploadifive-button">'+u.buttonText+"<\/div>"),u.buttonClass&&f.button.addClass(u.buttonClass),f.button.css({height:u.height,"line-height":u.height+"px",overflow:"hidden",position:"relative","text-align":"center",width:u.width}),e.before(f.button).appendTo(f.button).hide(),f.createInput.call(e),u.queueID?f.queueEl=n("#"+u.queueID):(u.queueID=u.id+"-queue",f.queueEl=n('<div id="'+u.queueID+'" class="uploadifive-queue" />'),f.button.after(f.queueEl)),u.dnd&&(o=u.dropTarget?n(u.dropTarget):f.queueEl.get(0),o.addEventListener("dragleave",function(n){n.preventDefault();n.stopPropagation()},!1),o.addEventListener("dragenter",function(n){n.preventDefault();n.stopPropagation()},!1),o.addEventListener("dragover",function(n){n.preventDefault();n.stopPropagation()},!1),o.addEventListener("drop",f.drop,!1)),XMLHttpRequest.prototype.sendAsBinary||(XMLHttpRequest.prototype.sendAsBinary=function(n){function t(n){return n.charCodeAt(0)&255}var i=Array.prototype.map.call(n,t),r=new Uint8Array(i);this.send(r.buffer)}),typeof u.onInit=="function"&&u.onInit.call(e);else return typeof u.onFallback=="function"&&u.onFallback.call(e),!1})},debug:function(){return this.each(function(){console.log(n(this).data("uploadifive"))})},clearQueue:function(){this.each(function(){var u=n(this),r=u.data("uploadifive"),f=r.settings;for(var e in r.inputs)for(input=r.inputs[e],limit=input.files.length,i=0;i<limit;i++)file=input.files[i],t.cancel.call(u,file);typeof f.onClearQueue=="function"&&f.onClearQueue.call(u,n("#"+r.settings.queueID))})},cancel:function(i,r){this.each(function(){var f=n(this),u=f.data("uploadifive"),e=u.settings;typeof i=="string"&&(isNaN(i)||(fileID="uploadifive-"+n(this).attr("id")+"-file-"+i),i=n("#"+fileID).data("file"));i.skip=!0;u.filesCancelled++;i.uploading&&(u.uploads.current--,i.uploading=!1,i.xhr.abort(),delete i.xhr,t.upload.call(f));n.inArray("onCancel",e.overrideEvents)<0&&u.removeQueueItem(i,r);typeof e.onCancel=="function"&&e.onCancel.call(f,i)})},upload:function(t,i){this.each(function(){var f=n(this),r=f.data("uploadifive"),u=r.settings,e;t?r.uploadFile.call(f,t):r.uploads.count+r.uploads.current<u.uploadLimit||u.uploadLimit==0?(i||(r.uploads.attempted=0,r.uploads.successsful=0,r.uploads.errors=0,e=r.filesToUpload(),typeof u.onUpload=="function"&&u.onUpload.call(f,e)),n("#"+u.queueID).find(".uploadifive-queue-item").not(".error, .complete").each(function(){if(_file=n(this).data("file"),r.uploads.current>=u.simUploadLimit&&u.simUploadLimit!==0||r.uploads.current>=u.uploadLimit&&u.uploadLimit!==0||r.uploads.count>=u.uploadLimit&&u.uploadLimit!==0)return!1;u.checkScript?(_file.checking=!0,skipFile=r.checkExists(_file),_file.checking=!1,skipFile||r.uploadFile(_file,!0)):r.uploadFile(_file,!0)}),n("#"+u.queueID).find(".uploadifive-queue-item").not(".error, .complete").length==0&&r.queueComplete()):r.uploads.current==0&&(n.inArray("onError",u.overrideEvents)<0&&r.filesToUpload()>0&&u.uploadLimit!=0&&alert("The maximum upload limit has been reached."),typeof u.onError=="function"&&u.onError.call(f,"UPLOAD_LIMIT_EXCEEDED",r.filesToUpload()))})},destroy:function(){this.each(function(){var i=n(this),u=i.data("uploadifive"),r=u.settings;t.clearQueue.call(i);r.queueID||n("#"+r.queueID).remove();i.siblings("input").remove();i.show().insertBefore(u.button);u.button.remove();typeof r.onDestroy=="function"&&r.onDestroy.call(i)})}};n.fn.uploadifive=function(i){if(t[i])return t[i].apply(this,Array.prototype.slice.call(arguments,1));if(typeof i!="object"&&i)n.error("The method "+i+" does not exist in $.uploadify");else return t.init.apply(this,arguments)}})(jQuery);