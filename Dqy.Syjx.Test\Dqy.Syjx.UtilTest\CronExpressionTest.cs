using System;
using NUnit.Framework;
using Quartz;
using Dqy.Syjx.Util.Extension;

namespace Dqy.Syjx.UtilTest
{
    public class CronExpressionTest
    {
        [Test]
        public void TestCronExpressionTimeZone()
        {
            // 测试 cron 表达式 "0 5 22 * * ?" (每天晚上 22:05)
            string cronExpression = "0 5 22 * * ?";
            
            // 创建 CronExpression 对象并设置时区
            var cron = new CronExpression(cronExpression);
            cron.TimeZone = TimeZoneInfo.Local; // 使用本地时区
            
            // 获取当前时间
            DateTime now = DateTime.Now;
            Console.WriteLine($"当前时间: {now:yyyy-MM-dd HH:mm:ss}");
            
            // 获取下次执行时间
            DateTime? nextTime = cron.GetNextValidTimeAfter(now)?.DateTime;
            if (nextTime.HasValue)
            {
                Console.WriteLine($"下次执行时间: {nextTime.Value:yyyy-MM-dd HH:mm:ss}");
                
                // 验证时间是否为 22:05
                Assert.AreEqual(22, nextTime.Value.Hour);
                Assert.AreEqual(5, nextTime.Value.Minute);
                Assert.AreEqual(0, nextTime.Value.Second);
            }
            else
            {
                Assert.Fail("无法获取下次执行时间");
            }
        }
        
        [Test]
        public void TestCronExpressionWithUtcTimeZone()
        {
            // 测试使用 UTC 时区的情况
            string cronExpression = "0 5 22 * * ?";
            
            var cron = new CronExpression(cronExpression);
            cron.TimeZone = TimeZoneInfo.Utc; // 使用 UTC 时区
            
            DateTime now = DateTime.UtcNow;
            Console.WriteLine($"当前 UTC 时间: {now:yyyy-MM-dd HH:mm:ss}");
            
            DateTime? nextTimeUtc = cron.GetNextValidTimeAfter(now)?.DateTime;
            if (nextTimeUtc.HasValue)
            {
                // 转换为本地时间
                DateTime nextTimeLocal = TimeZoneInfo.ConvertTimeFromUtc(nextTimeUtc.Value, TimeZoneInfo.Local);
                Console.WriteLine($"下次执行时间 (UTC): {nextTimeUtc.Value:yyyy-MM-dd HH:mm:ss}");
                Console.WriteLine($"下次执行时间 (本地): {nextTimeLocal:yyyy-MM-dd HH:mm:ss}");
                
                // 验证 UTC 时间是否为 22:05
                Assert.AreEqual(22, nextTimeUtc.Value.Hour);
                Assert.AreEqual(5, nextTimeUtc.Value.Minute);
            }
        }
        
        [Test]
        public void TestTimeZoneConversion()
        {
            // 测试时区转换
            DateTime utcTime = new DateTime(2025, 8, 20, 14, 5, 0, DateTimeKind.Utc); // UTC 14:05
            DateTime localTime = TimeZoneInfo.ConvertTimeFromUtc(utcTime, TimeZoneInfo.Local);
            
            Console.WriteLine($"UTC 时间: {utcTime:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine($"本地时间: {localTime:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine($"时区偏移: {TimeZoneInfo.Local.GetUtcOffset(localTime)}");
            
            // 对于中国时区 (UTC+8)，UTC 14:05 应该对应本地时间 22:05
            if (TimeZoneInfo.Local.Id.Contains("China") || TimeZoneInfo.Local.GetUtcOffset(localTime).Hours == 8)
            {
                Assert.AreEqual(22, localTime.Hour);
                Assert.AreEqual(5, localTime.Minute);
            }
        }
    }
}
