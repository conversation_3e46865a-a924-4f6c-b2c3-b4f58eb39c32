2025-08-20 13:18:42.7896||DEBUG||程序启动 |ContentRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web |WebRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\wwwroot |IsDevelopment:True |url: |action: 
2025-08-20 13:18:46.8774||INFO|Quartz.Impl.StdSchedulerFactory|Default Quartz.NET properties loaded from embedded resource file |url: |action: 
2025-08-20 13:18:47.0833||DEBUG|Quartz.Simpl.TaskSchedulingThreadPool|TaskSchedulingThreadPool configured with max concurrency of 10 and TaskScheduler ThreadPoolTaskScheduler. |url: |action: 
2025-08-20 13:18:47.0990||INFO|Quartz.Core.SchedulerSignalerImpl|Initialized Scheduler Signaller of type: Quartz.Core.SchedulerSignalerImpl |url: |action: 
2025-08-20 13:18:47.0990||INFO|Quartz.Core.QuartzScheduler|Quartz Scheduler created |url: |action: 
2025-08-20 13:18:47.0990||INFO|Quartz.Simpl.RAMJobStore|RAMJobStore initialized. |url: |action: 
2025-08-20 13:18:47.0990||INFO|Quartz.Impl.StdSchedulerFactory|Quartz Scheduler 3.6.3.0 - 'DefaultQuartzScheduler' with instanceId 'NON_CLUSTERED' initialized |url: |action: 
2025-08-20 13:18:47.0990||INFO|Quartz.Impl.StdSchedulerFactory|Using thread pool 'Quartz.Simpl.DefaultThreadPool', size: 10 |url: |action: 
2025-08-20 13:18:47.0990||INFO|Quartz.Impl.StdSchedulerFactory|Using job store 'Quartz.Simpl.RAMJobStore', supports persistence: False, clustered: False |url: |action: 
2025-08-20 13:18:47.1197||INFO|Quartz.Core.QuartzScheduler|Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started. |url: |action: 
2025-08-20 13:18:47.1197||DEBUG|Quartz.Core.QuartzSchedulerThread|Batch acquisition of 0 triggers |url: |action: 
2025-08-20 13:18:47.2197||DEBUG|Quartz.Core.QuartzSchedulerThread|Batch acquisition of 1 triggers |url: |action: 
2025-08-20 13:18:47.2282||DEBUG|Quartz.Simpl.SimpleJobFactory|Producing instance of Job '定时任务.在线巡课', class=Dqy.Syjx.Business.AutoJob.JobExecute |url: |action: 
2025-08-20 13:18:47.2282||DEBUG|Quartz.Core.QuartzSchedulerThread|Batch acquisition of 1 triggers |url: |action: 
2025-08-20 13:18:47.2282||DEBUG|Quartz.Simpl.SimpleJobFactory|Producing instance of Job '实验教学版本库备份.实验教学版本库备份', class=Dqy.Syjx.Business.AutoJob.JobExecute |url: |action: 
2025-08-20 13:18:47.2392||DEBUG|Quartz.Core.QuartzSchedulerThread|Batch acquisition of 1 triggers |url: |action: 
2025-08-20 13:18:47.2392||DEBUG|Quartz.Simpl.SimpleJobFactory|Producing instance of Job '定时任务.装备统计备份', class=Dqy.Syjx.Business.AutoJob.JobExecute |url: |action: 
2025-08-20 13:18:47.2392||DEBUG|Quartz.Core.QuartzSchedulerThread|Batch acquisition of 1 triggers |url: |action: 
2025-08-20 13:18:47.2392||DEBUG|Quartz.Simpl.SimpleJobFactory|Producing instance of Job '定时任务.统计本周未登记的实验情况', class=Dqy.Syjx.Business.AutoJob.JobExecute |url: |action: 
2025-08-20 13:18:47.2392||DEBUG|Quartz.Core.QuartzSchedulerThread|Batch acquisition of 0 triggers |url: |action: 
2025-08-20 13:18:47.2644||DEBUG|Quartz.Core.JobRunShell|Calling Execute on job 定时任务.在线巡课 |url: |action: 
2025-08-20 13:18:47.2644||DEBUG|Quartz.Core.JobRunShell|Calling Execute on job 实验教学版本库备份.实验教学版本库备份 |url: |action: 
2025-08-20 13:18:47.2644||DEBUG|Quartz.Core.JobRunShell|Calling Execute on job 定时任务.统计本周未登记的实验情况 |url: |action: 
2025-08-20 13:18:47.2644||DEBUG|Quartz.Core.JobRunShell|Calling Execute on job 定时任务.装备统计备份 |url: |action: 
2025-08-20 13:18:47.4490||INFO||处理完所有数据 |url: |action: 
2025-08-20 13:18:47.4490||INFO||==================(自动)初始化在线巡课开始【2025-08-20】===================== |url: |action: 
2025-08-20 13:18:47.7230||DEBUG||任务【装备统计备份】下次执行时间已更新为: 2025-08-20 22:05:00 |url: |action: 
2025-08-20 13:18:47.7230||DEBUG||任务【实验教学版本库备份】下次执行时间已更新为: 2025-08-20 22:05:00 |url: |action: 
2025-08-20 13:18:47.7416||INFO||==================(自动)初始化在线巡课结束【2025-08-20】===================== |url: |action: 
2025-08-20 13:18:47.7416||INFO||==================(自动)更新在线巡课开始【2025-08-20】===================== |url: |action: 
2025-08-20 13:18:47.7810||INFO||任务【装备统计备份】执行完成，状态: 失败 |url: |action: 
2025-08-20 13:18:47.7870||DEBUG|Quartz.Core.JobRunShell|Trigger instruction : NoInstruction |url: |action: 
2025-08-20 13:18:47.7870||INFO||任务【实验教学版本库备份】执行完成，状态: 失败 |url: |action: 
2025-08-20 13:18:47.7870||DEBUG|Quartz.Core.JobRunShell|Trigger instruction : NoInstruction |url: |action: 
2025-08-20 13:18:47.9618||INFO|| ================【更新[2025-08-20]“在线巡课”数据】=> 查询到 [2025-08-20] 需要更新的巡课数据：0条 ================ |url: |action: 
2025-08-20 13:18:47.9618||INFO|| ================【更新[2025-08-20]“在线巡课”数据】=> 未查询到[2025-08-20]需要更新的巡课数据 ================ |url: |action: 
2025-08-20 13:18:47.9618||INFO||==================(自动)更新在线巡课结束【2025-08-20】===================== |url: |action: 
2025-08-20 13:18:47.9618||DEBUG||任务【在线巡课】下次执行时间已更新为: 2025-08-20 22:05:00 |url: |action: 
2025-08-20 13:18:47.9618||INFO||任务【在线巡课】执行完成，状态: 成功 |url: |action: 
2025-08-20 13:18:47.9741||DEBUG|Quartz.Core.JobRunShell|Trigger instruction : NoInstruction |url: |action: 
2025-08-20 13:18:48.0317||DEBUG||任务【统计本周未登记的实验情况】下次执行时间已更新为: 2025-08-20 22:05:00 |url: |action: 
2025-08-20 13:18:48.0317||INFO||任务【统计本周未登记的实验情况】执行完成，状态: 成功 |url: |action: 
2025-08-20 13:18:48.0367||DEBUG|Quartz.Core.JobRunShell|Trigger instruction : NoInstruction |url: |action: 
2025-08-20 13:19:16.3839||DEBUG|Quartz.Core.QuartzSchedulerThread|Batch acquisition of 0 triggers |url: |action: 
2025-08-20 13:19:45.5454||DEBUG|Quartz.Core.QuartzSchedulerThread|Batch acquisition of 0 triggers |url: |action: 
2025-08-20 13:20:10.2233||DEBUG|Quartz.Core.QuartzSchedulerThread|Batch acquisition of 0 triggers |url: |action: 
2025-08-20 13:20:39.1570||DEBUG|Quartz.Core.QuartzSchedulerThread|Batch acquisition of 0 triggers |url: |action: 
2025-08-20 13:21:03.9948||DEBUG|Quartz.Core.QuartzSchedulerThread|Batch acquisition of 0 triggers |url: |action: 
2025-08-20 13:21:30.3066||DEBUG|Quartz.Core.QuartzSchedulerThread|Batch acquisition of 0 triggers |url: |action: 
2025-08-20 13:21:54.6721||DEBUG|Quartz.Core.QuartzSchedulerThread|Batch acquisition of 0 triggers |url: |action: 
2025-08-20 13:22:22.7054||DEBUG|Quartz.Core.QuartzSchedulerThread|Batch acquisition of 0 triggers |url: |action: 
2025-08-20 13:22:48.3639||DEBUG|Quartz.Core.QuartzSchedulerThread|Batch acquisition of 0 triggers |url: |action: 
2025-08-20 13:23:17.2733||DEBUG|Quartz.Core.QuartzSchedulerThread|Batch acquisition of 0 triggers |url: |action: 
2025-08-20 13:23:43.0147||DEBUG|Quartz.Core.QuartzSchedulerThread|Batch acquisition of 0 triggers |url: |action: 
