/*!
 * Select2 4.1.0-rc.0
 * https://select2.github.io
 *
 * Released under the MIT license
 * https://github.com/select2/select2/blob/master/LICENSE.md
 */
(function(n){typeof define=="function"&&define.amd?define(["jquery"],n):typeof module=="object"&&module.exports?module.exports=function(t,i){return i===undefined&&(i=typeof window!="undefined"?require("jquery"):require("jquery")(t)),n(i),i}:n(jQuery)})(function(n){var t=function(){var t;return n&&n.fn&&n.fn.select2&&n.fn.select2.amd&&(t=n.fn.select2.amd),function(){if(!t||!t.requirejs){t?i=t:t={};
/**
 * @license almond 0.3.3 Copyright jQuery Foundation and other contributors.
 * Released under MIT license, http://github.com/requirejs/almond/LICENSE
 */
var n,i,r;(function(t){function e(n,t){return d.call(n,t)}function l(n,t){var o,s,u,e,h,y,c,b,i,l,p,k,r=t&&t.split("/"),a=f.map,v=a&&a["*"]||{};if(n){for(n=n.split("/"),h=n.length-1,f.nodeIdCompat&&w.test(n[h])&&(n[h]=n[h].replace(w,"")),n[0].charAt(0)==="."&&r&&(k=r.slice(0,r.length-1),n=k.concat(n)),i=0;i<n.length;i++)if(p=n[i],p===".")n.splice(i,1),i-=1;else if(p==="..")if(i===0||i===1&&n[2]===".."||n[i-1]==="..")continue;else i>0&&(n.splice(i-1,2),i-=2);n=n.join("/")}if((r||v)&&a){for(o=n.split("/"),i=o.length;i>0;i-=1){if(s=o.slice(0,i).join("/"),r)for(l=r.length;l>0;l-=1)if(u=a[r.slice(0,l).join("/")],u&&(u=u[s],u)){e=u;y=i;break}if(e)break;!c&&v&&v[s]&&(c=v[s],b=i)}!e&&c&&(e=c,y=b);e&&(o.splice(0,y,e),n=o.join("/"))}return n}function b(n,i){return function(){var r=g.call(arguments,0);return typeof r[0]!="string"&&r.length===1&&r.push(null),o.apply(t,r.concat([n,i]))}}function nt(n){return function(t){return l(t,n)}}function tt(n){return function(t){u[n]=t}}function a(n){if(e(h,n)){var i=h[n];delete h[n];y[n]=!0;c.apply(t,i)}if(!e(u,n)&&!e(y,n))throw new Error("No "+n);return u[n]}function p(n){var i,t=n?n.indexOf("!"):-1;return t>-1&&(i=n.substring(0,t),n=n.substring(t+1,n.length)),[i,n]}function k(n){return n?p(n):[]}function it(n){return function(){return f&&f.config&&f.config[n]||{}}}var c,o,v,s,u={},h={},f={},y={},d=Object.prototype.hasOwnProperty,g=[].slice,w=/\.js$/;v=function(n,t){var r,u=p(n),i=u[0],f=t[1];return n=u[1],i&&(i=l(i,f),r=a(i)),i?n=r&&r.normalize?r.normalize(n,nt(f)):l(n,f):(n=l(n,f),u=p(n),i=u[0],n=u[1],i&&(r=a(i))),{f:i?i+"!"+n:n,n:n,pr:i,p:r}};s={require:function(n){return b(n)},exports:function(n){var t=u[n];return typeof t!="undefined"?t:u[n]={}},module:function(n){return{id:n,uri:"",exports:u[n],config:it(n)}}};c=function(n,i,r,f){var p,o,d,w,c,g,l=[],nt=typeof r,it;if(f=f||n,g=k(f),nt==="undefined"||nt==="function"){for(i=!i.length&&r.length?["require","exports","module"]:i,c=0;c<i.length;c+=1)if(w=v(i[c],g),o=w.f,o==="require")l[c]=s.require(n);else if(o==="exports")l[c]=s.exports(n),it=!0;else if(o==="module")p=l[c]=s.module(n);else if(e(u,o)||e(h,o)||e(y,o))l[c]=a(o);else if(w.p)w.p.load(w.n,b(f,!0),tt(o),{}),l[c]=u[o];else throw new Error(n+" missing "+o);d=r?r.apply(u[n],l):undefined;n&&(p&&p.exports!==t&&p.exports!==u[n]?u[n]=p.exports:d===t&&it||(u[n]=d))}else n&&(u[n]=r)};n=i=o=function(n,i,r,u,e){if(typeof n=="string")return s[n]?s[n](i):a(v(n,k(i)).f);if(!n.splice){if(f=n,f.deps&&o(f.deps,f.callback),!i)return;i.splice?(n=i,i=r,r=null):n=t}return i=i||function(){},typeof r=="function"&&(r=u,u=e),u?c(t,n,i,r):setTimeout(function(){c(t,n,i,r)},4),o};o.config=function(n){return o(n)};n._defined=u;r=function(n,t,i){if(typeof n!="string")throw new Error("See almond README: incorrect module build, no module name");t.splice||(i=t,t=[]);e(u,n)||e(h,n)||(h[n]=[n,t,i])};r.amd={jQuery:!0}})();t.requirejs=n;t.require=i;t.define=r}}(),t.define("almond",function(){}),t.define("jquery",[],function(){var t=n||$;return t==null&&console&&console.error&&console.error("Select2: An instance of jQuery or a jQuery-compatible library was not found. Make sure that you are including jQuery before Select2 on your web page."),t}),t.define("select2/utils",["jquery"],function(n){function r(n){var i=n.prototype,r=[],t,u;for(t in i)(u=i[t],typeof u=="function")&&t!=="constructor"&&r.push(t);return r}var t={},i,u;return t.Extend=function(n,t){function r(){this.constructor=n}var u={}.hasOwnProperty;for(var i in t)u.call(t,i)&&(n[i]=t[i]);return r.prototype=t.prototype,n.prototype=new r,n.__super__=t.prototype,n},t.Decorate=function(n,t){function i(){var r=Array.prototype.unshift,u=t.prototype.constructor.length,i=n.prototype.constructor;u>0&&(r.call(arguments,n.prototype.constructor),i=t.prototype.constructor);i.apply(this,arguments)}function l(){this.constructor=i}var s=r(t),h=r(n),u,e,c,f,o;for(t.displayName=n.displayName,i.prototype=new l,u=0;u<h.length;u++)e=h[u],i.prototype[e]=n.prototype[e];for(c=function(n){var r=function(){},u;return n in i.prototype&&(r=i.prototype[n]),u=t.prototype[n],function(){var n=Array.prototype.unshift;return n.call(arguments,r),u.apply(this,arguments)}},f=0;f<s.length;f++)o=s[f],i.prototype[o]=c(o);return i},i=function(){this.listeners={}},i.prototype.on=function(n,t){this.listeners=this.listeners||{};n in this.listeners?this.listeners[n].push(t):this.listeners[n]=[t]},i.prototype.trigger=function(n){var i=Array.prototype.slice,t=i.call(arguments,1);this.listeners=this.listeners||{};t==null&&(t=[]);t.length===0&&t.push({});t[0]._type=n;n in this.listeners&&this.invoke(this.listeners[n],i.call(arguments,1));"*"in this.listeners&&this.invoke(this.listeners["*"],arguments)},i.prototype.invoke=function(n,t){for(var i=0,r=n.length;i<r;i++)n[i].apply(this,t)},t.Observable=i,t.generateChars=function(n){for(var r,t="",i=0;i<n;i++)r=Math.floor(Math.random()*36),t+=r.toString(36);return t},t.bind=function(n,t){return function(){n.apply(t,arguments)}},t._convertData=function(n){var f,r,i,u,t;for(f in n)if(r=f.split("-"),i=n,r.length!==1){for(u=0;u<r.length;u++)t=r[u],t=t.substring(0,1).toLowerCase()+t.substring(1),t in i||(i[t]={}),u==r.length-1&&(i[t]=n[f]),i=i[t];delete n[f]}return n},t.hasScroll=function(t,i){var u=n(i),f=i.style.overflowX,r=i.style.overflowY;return f===r&&(r==="hidden"||r==="visible")?!1:f==="scroll"||r==="scroll"?!0:u.innerHeight()<i.scrollHeight||u.innerWidth()<i.scrollWidth},t.escapeMarkup=function(n){var t={"\\":"&#92;","&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#47;"};return typeof n!="string"?n:String(n).replace(/[&<>"'\/\\]/g,function(n){return t[n]})},t.__cache={},u=0,t.GetUniqueElementId=function(n){var i=n.getAttribute("data-select2-id");return i!=null?i:(i=n.id?"select2-data-"+n.id:"select2-data-"+(++u).toString()+"-"+t.generateChars(4),n.setAttribute("data-select2-id",i),i)},t.StoreData=function(n,i,r){var u=t.GetUniqueElementId(n);t.__cache[u]||(t.__cache[u]={});t.__cache[u][i]=r},t.GetData=function(i,r){var u=t.GetUniqueElementId(i);return r?t.__cache[u]?t.__cache[u][r]!=null?t.__cache[u][r]:n(i).data(r):n(i).data(r):t.__cache[u]},t.RemoveData=function(n){var i=t.GetUniqueElementId(n);t.__cache[i]!=null&&delete t.__cache[i];n.removeAttribute("data-select2-id")},t.copyNonInternalCssClasses=function(n,t){var r=n.getAttribute("class").trim().split(/\s+/),i,u;r=r.filter(function(n){return n.indexOf("select2-")===0});i=t.getAttribute("class").trim().split(/\s+/);i=i.filter(function(n){return n.indexOf("select2-")!==0});u=r.concat(i);n.setAttribute("class",u.join(" "))},t}),t.define("select2/results",["jquery","./utils"],function(n,t){function i(n,t,r){this.$element=n;this.data=r;this.options=t;i.__super__.constructor.call(this)}return t.Extend(i,t.Observable),i.prototype.render=function(){var t=n('<ul class="select2-results__options" role="listbox"><\/ul>');return this.options.get("multiple")&&t.attr("aria-multiselectable","true"),this.$results=t,t},i.prototype.clear=function(){this.$results.empty()},i.prototype.displayMessage=function(t){var u=this.options.get("escapeMarkup"),i,r;this.clear();this.hideLoading();i=n('<li role="alert" aria-live="assertive" class="select2-results__option"><\/li>');r=this.options.get("translations").get(t.message);i.append(u(r(t.args)));i[0].className+=" select2-results__message";this.$results.append(i)},i.prototype.hideMessages=function(){this.$results.find(".select2-results__message").remove()},i.prototype.append=function(n){var i,t,r,u;if(this.hideLoading(),i=[],n.results==null||n.results.length===0){this.$results.children().length===0&&this.trigger("results:message",{message:"noResults"});return}for(n.results=this.sort(n.results),t=0;t<n.results.length;t++)r=n.results[t],u=this.option(r),i.push(u);this.$results.append(i)},i.prototype.position=function(n,t){var i=t.find(".select2-results");i.append(n)},i.prototype.sort=function(n){var t=this.options.get("sorter");return t(n)},i.prototype.highlightFirstItem=function(){var n=this.$results.find(".select2-results__option--selectable"),t=n.filter(".select2-results__option--selected");t.length>0?t.first().trigger("mouseenter"):n.first().trigger("mouseenter");this.ensureHighlightVisible()},i.prototype.setClasses=function(){var i=this;this.data.current(function(r){var u=r.map(function(n){return n.id.toString()}),f=i.$results.find(".select2-results__option--selectable");f.each(function(){var r=n(this),i=t.GetData(this,"data"),f=""+i.id;i.element!=null&&i.element.selected||i.element==null&&u.indexOf(f)>-1?(this.classList.add("select2-results__option--selected"),r.attr("aria-selected","true")):(this.classList.remove("select2-results__option--selected"),r.attr("aria-selected","false"))})})},i.prototype.showLoading=function(n){this.hideLoading();var i=this.options.get("translations").get("searching"),r={disabled:!0,loading:!0,text:i(n)},t=this.option(r);t.className+=" loading-results";this.$results.prepend(t)},i.prototype.hideLoading=function(){this.$results.find(".loading-results").remove()},i.prototype.option=function(i){var r=document.createElement("li"),u,l,o,a,s,f,h,e,v,y,c;r.classList.add("select2-results__option");r.classList.add("select2-results__option--selectable");u={role:"option"};l=window.Element.prototype.matches||window.Element.prototype.msMatchesSelector||window.Element.prototype.webkitMatchesSelector;(i.element!=null&&l.call(i.element,":disabled")||i.element==null&&i.disabled)&&(u["aria-disabled"]="true",r.classList.remove("select2-results__option--selectable"),r.classList.add("select2-results__option--disabled"));i.id==null&&r.classList.remove("select2-results__option--selectable");i._resultId!=null&&(r.id=i._resultId);i.title&&(r.title=i.title);i.children&&(u.role="group",u["aria-label"]=i.text,r.classList.remove("select2-results__option--selectable"),r.classList.add("select2-results__option--group"));for(o in u)a=u[o],r.setAttribute(o,a);if(i.children){for(s=n(r),f=document.createElement("strong"),f.className="select2-results__group",this.template(i,f),h=[],e=0;e<i.children.length;e++)v=i.children[e],y=this.option(v),h.push(y);c=n("<ul><\/ul>",{"class":"select2-results__options select2-results__options--nested",role:"none"});c.append(h);s.append(f);s.append(c)}else this.template(i,r);return t.StoreData(r,"data",i),r},i.prototype.bind=function(i){var r=this,u=i.id+"-results";this.$results.attr("id",u);i.on("results:all",function(n){r.clear();r.append(n.data);i.isOpen()&&(r.setClasses(),r.highlightFirstItem())});i.on("results:append",function(n){r.append(n.data);i.isOpen()&&r.setClasses()});i.on("query",function(n){r.hideMessages();r.showLoading(n)});i.on("select",function(){i.isOpen()&&(r.setClasses(),r.options.get("scrollAfterSelect")&&r.highlightFirstItem())});i.on("unselect",function(){i.isOpen()&&(r.setClasses(),r.options.get("scrollAfterSelect")&&r.highlightFirstItem())});i.on("open",function(){r.$results.attr("aria-expanded","true");r.$results.attr("aria-hidden","false");r.setClasses();r.ensureHighlightVisible()});i.on("close",function(){r.$results.attr("aria-expanded","false");r.$results.attr("aria-hidden","true");r.$results.removeAttr("aria-activedescendant")});i.on("results:toggle",function(){var n=r.getHighlightedResults();n.length!==0&&n.trigger("mouseup")});i.on("results:select",function(){var n=r.getHighlightedResults(),i;n.length!==0&&(i=t.GetData(n[0],"data"),n.hasClass("select2-results__option--selected")?r.trigger("close",{}):r.trigger("select",{data:i}))});i.on("results:previous",function(){var i=r.getHighlightedResults(),u=r.$results.find(".select2-results__option--selectable"),f=u.index(i),n,t;if(!(f<=0)){n=f-1;i.length===0&&(n=0);t=u.eq(n);t.trigger("mouseenter");var e=r.$results.offset().top,o=t.offset().top,s=r.$results.scrollTop()+(o-e);n===0?r.$results.scrollTop(0):o-e<0&&r.$results.scrollTop(s)}});i.on("results:next",function(){var e=r.getHighlightedResults(),t=r.$results.find(".select2-results__option--selectable"),o=t.index(e),i=o+1,n;if(!(i>=t.length)){n=t.eq(i);n.trigger("mouseenter");var u=r.$results.offset().top+r.$results.outerHeight(!1),f=n.offset().top+n.outerHeight(!1),s=r.$results.scrollTop()+f-u;i===0?r.$results.scrollTop(0):f>u&&r.$results.scrollTop(s)}});i.on("results:focus",function(n){n.element[0].classList.add("select2-results__option--highlighted");n.element[0].setAttribute("aria-selected","true")});i.on("results:message",function(n){r.displayMessage(n)});if(n.fn.mousewheel)this.$results.on("mousewheel",function(n){var t=r.$results.scrollTop(),i=r.$results.get(0).scrollHeight-t+n.deltaY,u=n.deltaY>0&&t-n.deltaY<=0,f=n.deltaY<0&&i<=r.$results.height();u?(r.$results.scrollTop(0),n.preventDefault(),n.stopPropagation()):f&&(r.$results.scrollTop(r.$results.get(0).scrollHeight-r.$results.height()),n.preventDefault(),n.stopPropagation())});this.$results.on("mouseup",".select2-results__option--selectable",function(i){var f=n(this),u=t.GetData(this,"data");if(f.hasClass("select2-results__option--selected")){r.options.get("multiple")?r.trigger("unselect",{originalEvent:i,data:u}):r.trigger("close",{});return}r.trigger("select",{originalEvent:i,data:u})});this.$results.on("mouseenter",".select2-results__option--selectable",function(){var i=t.GetData(this,"data");r.getHighlightedResults().removeClass("select2-results__option--highlighted").attr("aria-selected","false");r.trigger("results:focus",{data:i,element:n(this)})})},i.prototype.getHighlightedResults=function(){return this.$results.find(".select2-results__option--highlighted")},i.prototype.destroy=function(){this.$results.remove()},i.prototype.ensureHighlightVisible=function(){var n=this.getHighlightedResults();if(n.length!==0){var f=this.$results.find(".select2-results__option--selectable"),e=f.index(n),t=this.$results.offset().top,i=n.offset().top,r=this.$results.scrollTop()+(i-t),u=i-t;r-=n.outerHeight(!1)*2;e<=2?this.$results.scrollTop(0):(u>this.$results.outerHeight()||u<0)&&this.$results.scrollTop(r)}},i.prototype.template=function(t,i){var u=this.options.get("templateResult"),f=this.options.get("escapeMarkup"),r=u(t,i);r==null?i.style.display="none":typeof r=="string"?i.innerHTML=f(r):n(i).append(r)},i}),t.define("select2/keys",[],function(){return{BACKSPACE:8,TAB:9,ENTER:13,SHIFT:16,CTRL:17,ALT:18,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46}}),t.define("select2/selection/base",["jquery","../utils","../keys"],function(n,t,i){function r(n,t){this.$element=n;this.options=t;r.__super__.constructor.call(this)}return t.Extend(r,t.Observable),r.prototype.render=function(){var i=n('<span class="select2-selection" role="combobox"  aria-haspopup="true" aria-expanded="false"><\/span>');return this._tabindex=0,t.GetData(this.$element[0],"old-tabindex")!=null?this._tabindex=t.GetData(this.$element[0],"old-tabindex"):this.$element.attr("tabindex")!=null&&(this._tabindex=this.$element.attr("tabindex")),i.attr("title",this.$element.attr("title")),i.attr("tabindex",this._tabindex),i.attr("aria-disabled","false"),this.$selection=i,i},r.prototype.bind=function(n){var t=this,r=n.id+"-results";this.container=n;this.$selection.on("focus",function(n){t.trigger("focus",n)});this.$selection.on("blur",function(n){t._handleBlur(n)});this.$selection.on("keydown",function(n){t.trigger("keypress",n);n.which===i.SPACE&&n.preventDefault()});n.on("results:focus",function(n){t.$selection.attr("aria-activedescendant",n.data._resultId)});n.on("selection:update",function(n){t.update(n.data)});n.on("open",function(){t.$selection.attr("aria-expanded","true");t.$selection.attr("aria-owns",r);t._attachCloseHandler(n)});n.on("close",function(){t.$selection.attr("aria-expanded","false");t.$selection.removeAttr("aria-activedescendant");t.$selection.removeAttr("aria-owns");t.$selection.trigger("focus");t._detachCloseHandler(n)});n.on("enable",function(){t.$selection.attr("tabindex",t._tabindex);t.$selection.attr("aria-disabled","false")});n.on("disable",function(){t.$selection.attr("tabindex","-1");t.$selection.attr("aria-disabled","true")})},r.prototype._handleBlur=function(t){var i=this;window.setTimeout(function(){document.activeElement==i.$selection[0]||n.contains(i.$selection[0],document.activeElement)||i.trigger("blur",t)},1)},r.prototype._attachCloseHandler=function(i){n(document.body).on("mousedown.select2."+i.id,function(i){var r=n(i.target),u=r.closest(".select2"),f=n(".select2.select2-container--open");f.each(function(){if(this!=u[0]){var n=t.GetData(this,"element");n.select2("close")}})})},r.prototype._detachCloseHandler=function(t){n(document.body).off("mousedown.select2."+t.id)},r.prototype.position=function(n,t){var i=t.find(".selection");i.append(n)},r.prototype.destroy=function(){this._detachCloseHandler(this.container)},r.prototype.update=function(){throw new Error("The `update` method must be defined in child classes.");},r.prototype.isEnabled=function(){return!this.isDisabled()},r.prototype.isDisabled=function(){return this.options.get("disabled")},r}),t.define("select2/selection/single",["jquery","./base","../utils","../keys"],function(n,t,i){function r(){r.__super__.constructor.apply(this,arguments)}return i.Extend(r,t),r.prototype.render=function(){var n=r.__super__.render.call(this);return n[0].classList.add("select2-selection--single"),n.html('<span class="select2-selection__rendered"><\/span><span class="select2-selection__arrow" role="presentation"><b role="presentation"><\/b><\/span>'),n},r.prototype.bind=function(n){var i=this,t;r.__super__.bind.apply(this,arguments);t=n.id+"-container";this.$selection.find(".select2-selection__rendered").attr("id",t).attr("role","textbox").attr("aria-readonly","true");this.$selection.attr("aria-labelledby",t);this.$selection.attr("aria-controls",t);this.$selection.on("mousedown",function(n){n.which===1&&i.trigger("toggle",{originalEvent:n})});this.$selection.on("focus",function(){});this.$selection.on("blur",function(){});n.on("focus",function(){n.isOpen()||i.$selection.trigger("focus")})},r.prototype.clear=function(){var n=this.$selection.find(".select2-selection__rendered");n.empty();n.removeAttr("title")},r.prototype.display=function(n,t){var i=this.options.get("templateSelection"),r=this.options.get("escapeMarkup");return r(i(n,t))},r.prototype.selectionContainer=function(){return n("<span><\/span>")},r.prototype.update=function(n){var r;if(n.length===0){this.clear();return}var i=n[0],t=this.$selection.find(".select2-selection__rendered"),u=this.display(i,t);t.empty().append(u);r=i.title||i.text;r?t.attr("title",r):t.removeAttr("title")},r}),t.define("select2/selection/multiple",["jquery","./base","../utils"],function(n,t,i){function r(){r.__super__.constructor.apply(this,arguments)}return i.Extend(r,t),r.prototype.render=function(){var n=r.__super__.render.call(this);return n[0].classList.add("select2-selection--multiple"),n.html('<ul class="select2-selection__rendered"><\/ul>'),n},r.prototype.bind=function(t){var u=this,f;r.__super__.bind.apply(this,arguments);f=t.id+"-container";this.$selection.find(".select2-selection__rendered").attr("id",f);this.$selection.on("click",function(n){u.trigger("toggle",{originalEvent:n})});this.$selection.on("click",".select2-selection__choice__remove",function(t){if(!u.isDisabled()){var r=n(this),f=r.parent(),e=i.GetData(f[0],"data");u.trigger("unselect",{originalEvent:t,data:e})}});this.$selection.on("keydown",".select2-selection__choice__remove",function(n){u.isDisabled()||n.stopPropagation()})},r.prototype.clear=function(){var n=this.$selection.find(".select2-selection__rendered");n.empty();n.removeAttr("title")},r.prototype.display=function(n,t){var i=this.options.get("templateSelection"),r=this.options.get("escapeMarkup");return r(i(n,t))},r.prototype.selectionContainer=function(){return n('<li class="select2-selection__choice"><button type="button" class="select2-selection__choice__remove" tabindex="-1"><span aria-hidden="true">&times;<\/span><\/button><span class="select2-selection__choice__display"><\/span><\/li>')},r.prototype.update=function(n){var e,c,u,s,h,f,l;if(this.clear(),n.length!==0){for(e=[],c=this.$selection.find(".select2-selection__rendered").attr("id")+"-choice-",u=0;u<n.length;u++){var t=n[u],r=this.selectionContainer(),a=this.display(t,r),o=c+i.generateChars(4)+"-";o+=t.id?t.id:i.generateChars(4);r.find(".select2-selection__choice__display").append(a).attr("id",o);s=t.title||t.text;s&&r.attr("title",s);h=this.options.get("translations").get("removeItem");f=r.find(".select2-selection__choice__remove");f.attr("title",h());f.attr("aria-label",h());f.attr("aria-describedby",o);i.StoreData(r[0],"data",t);e.push(r)}l=this.$selection.find(".select2-selection__rendered");l.append(e)}},r}),t.define("select2/selection/placeholder",[],function(){function n(n,t,i){this.placeholder=this.normalizePlaceholder(i.get("placeholder"));n.call(this,t,i)}return n.prototype.normalizePlaceholder=function(n,t){return typeof t=="string"&&(t={id:"",text:t}),t},n.prototype.createPlaceholder=function(n,t){var i=this.selectionContainer(),r;return i.html(this.display(t)),i[0].classList.add("select2-selection__placeholder"),i[0].classList.remove("select2-selection__choice"),r=t.title||t.text||i.text(),this.$selection.find(".select2-selection__rendered").attr("title",r),i},n.prototype.update=function(n,t){var r=t.length==1&&t[0].id!=this.placeholder.id,u=t.length>1,i;if(u||r)return n.call(this,t);this.clear();i=this.createPlaceholder(this.placeholder);this.$selection.find(".select2-selection__rendered").append(i)},n}),t.define("select2/selection/allowClear",["jquery","../keys","../utils"],function(n,t,i){function r(){}return r.prototype.bind=function(n,t,i){var r=this;n.call(this,t,i);this.placeholder==null&&this.options.get("debug")&&window.console&&console.error&&console.error("Select2: The `allowClear` option should be used in combination with the `placeholder` option.");this.$selection.on("mousedown",".select2-selection__clear",function(n){r._handleClear(n)});t.on("keypress",function(n){r._handleKeyboardClear(n,t)})},r.prototype._handleClear=function(n,t){var e,u,o,r,f;if(!this.isDisabled()&&(e=this.$selection.find(".select2-selection__clear"),e.length!==0)){if(t.stopPropagation(),u=i.GetData(e[0],"data"),o=this.$element.val(),this.$element.val(this.placeholder.id),r={data:u},this.trigger("clear",r),r.prevented){this.$element.val(o);return}for(f=0;f<u.length;f++)if(r={data:u[f]},this.trigger("unselect",r),r.prevented){this.$element.val(o);return}this.$element.trigger("input").trigger("change");this.trigger("toggle",{})}},r.prototype._handleKeyboardClear=function(n,i,r){r.isOpen()||(i.which==t.DELETE||i.which==t.BACKSPACE)&&this._handleClear(i)},r.prototype.update=function(t,r){if(t.call(this,r),this.$selection.find(".select2-selection__clear").remove(),this.$selection[0].classList.remove("select2-selection--clearable"),!(this.$selection.find(".select2-selection__placeholder").length>0)&&r.length!==0){var e=this.$selection.find(".select2-selection__rendered").attr("id"),f=this.options.get("translations").get("removeAllItems"),u=n('<button type="button" class="select2-selection__clear" tabindex="-1"><span aria-hidden="true">&times;<\/span><\/button>');u.attr("title",f());u.attr("aria-label",f());u.attr("aria-describedby",e);i.StoreData(u[0],"data",r);this.$selection.prepend(u);this.$selection[0].classList.add("select2-selection--clearable")}},r}),t.define("select2/selection/search",["jquery","../utils","../keys"],function(n,t,i){function r(n,t,i){n.call(this,t,i)}return r.prototype.render=function(t){var u=this.options.get("translations").get("search"),r=n('<span class="select2-search select2-search--inline"><textarea class="select2-search__field" type="search" tabindex="-1" autocorrect="off" autocapitalize="none" spellcheck="false" role="searchbox" aria-autocomplete="list" ><\/textarea><\/span>'),i;return this.$searchContainer=r,this.$search=r.find("textarea"),this.$search.prop("autocomplete",this.options.get("autocomplete")),this.$search.attr("aria-label",u()),i=t.call(this),this._transferTabIndex(),i.append(this.$searchContainer),i},r.prototype.bind=function(n,r,u){var f=this,s=r.id+"-results",h=r.id+"-container",e,o;n.call(this,r,u);f.$search.attr("aria-describedby",h);r.on("open",function(){f.$search.attr("aria-controls",s);f.$search.trigger("focus")});r.on("close",function(){f.$search.val("");f.resizeSearch();f.$search.removeAttr("aria-controls");f.$search.removeAttr("aria-activedescendant");f.$search.trigger("focus")});r.on("enable",function(){f.$search.prop("disabled",!1);f._transferTabIndex()});r.on("disable",function(){f.$search.prop("disabled",!0)});r.on("focus",function(){f.$search.trigger("focus")});r.on("results:focus",function(n){n.data._resultId?f.$search.attr("aria-activedescendant",n.data._resultId):f.$search.removeAttr("aria-activedescendant")});this.$selection.on("focusin",".select2-search--inline",function(n){f.trigger("focus",n)});this.$selection.on("focusout",".select2-search--inline",function(n){f._handleBlur(n)});this.$selection.on("keydown",".select2-search--inline",function(n){var u,r,e;n.stopPropagation();f.trigger("keypress",n);f._keyUpPrevented=n.isDefaultPrevented();u=n.which;u===i.BACKSPACE&&f.$search.val()===""&&(r=f.$selection.find(".select2-selection__choice").last(),r.length>0&&(e=t.GetData(r[0],"data"),f.searchRemoveChoice(e),n.preventDefault()))});this.$selection.on("click",".select2-search--inline",function(n){f.$search.val()&&n.stopPropagation()});e=document.documentMode;o=e&&e<=11;this.$selection.on("input.searchcheck",".select2-search--inline",function(){if(o){f.$selection.off("input.search input.searchcheck");return}f.$selection.off("keyup.search")});this.$selection.on("keyup.search input.search",".select2-search--inline",function(n){if(o&&n.type==="input"){f.$selection.off("input.search input.searchcheck");return}var t=n.which;t!=i.SHIFT&&t!=i.CTRL&&t!=i.ALT&&t!=i.TAB&&f.handleSearch(n)})},r.prototype._transferTabIndex=function(){this.$search.attr("tabindex",this.$selection.attr("tabindex"));this.$selection.attr("tabindex","-1")},r.prototype.createPlaceholder=function(n,t){this.$search.attr("placeholder",t.text)},r.prototype.update=function(n,t){var i=this.$search[0]==document.activeElement;this.$search.attr("placeholder","");n.call(this,t);this.resizeSearch();i&&this.$search.trigger("focus")},r.prototype.handleSearch=function(){if(this.resizeSearch(),!this._keyUpPrevented){var n=this.$search.val();this.trigger("query",{term:n})}this._keyUpPrevented=!1},r.prototype.searchRemoveChoice=function(n,t){this.trigger("unselect",{data:t});this.$search.val(t.text);this.handleSearch()},r.prototype.resizeSearch=function(){var n,t;this.$search.css("width","25px");n="100%";this.$search.attr("placeholder")===""&&(t=this.$search.val().length+1,n=t*.75+"em");this.$search.css("width",n)},r}),t.define("select2/selection/selectionCss",["../utils"],function(n){function t(){}return t.prototype.render=function(t){var r=t.call(this),i=this.options.get("selectionCssClass")||"";return i.indexOf(":all:")!==-1&&(i=i.replace(":all:",""),n.copyNonInternalCssClasses(r[0],this.$element[0])),r.addClass(i),r},t}),t.define("select2/selection/eventRelay",["jquery"],function(n){function t(){}return t.prototype.bind=function(t,i,r){var u=this,f=["open","opening","close","closing","select","selecting","unselect","unselecting","clear","clearing"],e=["opening","closing","selecting","unselecting","clearing"];t.call(this,i,r);i.on("*",function(t,i){if(f.indexOf(t)!==-1){i=i||{};var r=n.Event("select2:"+t,{params:i});(u.$element.trigger(r),e.indexOf(t)!==-1)&&(i.prevented=r.isDefaultPrevented())}})},t}),t.define("select2/translation",["jquery","require"],function(n,t){function i(n){this.dict=n||{}}return i.prototype.all=function(){return this.dict},i.prototype.get=function(n){return this.dict[n]},i.prototype.extend=function(t){this.dict=n.extend({},t.all(),this.dict)},i._cache={},i.loadPath=function(n){if(!(n in i._cache)){var r=t(n);i._cache[n]=r}return new i(i._cache[n])},i}),t.define("select2/diacritics",[],function(){return{"Ⓐ":"A","Ａ":"A","À":"A","Á":"A","Â":"A","Ầ":"A","Ấ":"A","Ẫ":"A","Ẩ":"A","Ã":"A","Ā":"A","Ă":"A","Ằ":"A","Ắ":"A","Ẵ":"A","Ẳ":"A","Ȧ":"A","Ǡ":"A","Ä":"A","Ǟ":"A","Ả":"A","Å":"A","Ǻ":"A","Ǎ":"A","Ȁ":"A","Ȃ":"A","Ạ":"A","Ậ":"A","Ặ":"A","Ḁ":"A","Ą":"A","Ⱥ":"A","Ɐ":"A","Ꜳ":"AA","Æ":"AE","Ǽ":"AE","Ǣ":"AE","Ꜵ":"AO","Ꜷ":"AU","Ꜹ":"AV","Ꜻ":"AV","Ꜽ":"AY","Ⓑ":"B","Ｂ":"B","Ḃ":"B","Ḅ":"B","Ḇ":"B","Ƀ":"B","Ƃ":"B","Ɓ":"B","Ⓒ":"C","Ｃ":"C","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","Ç":"C","Ḉ":"C","Ƈ":"C","Ȼ":"C","Ꜿ":"C","Ⓓ":"D","Ｄ":"D","Ḋ":"D","Ď":"D","Ḍ":"D","Ḑ":"D","Ḓ":"D","Ḏ":"D","Đ":"D","Ƌ":"D","Ɗ":"D","Ɖ":"D","Ꝺ":"D","Ǳ":"DZ","Ǆ":"DZ","ǲ":"Dz","ǅ":"Dz","Ⓔ":"E","Ｅ":"E","È":"E","É":"E","Ê":"E","Ề":"E","Ế":"E","Ễ":"E","Ể":"E","Ẽ":"E","Ē":"E","Ḕ":"E","Ḗ":"E","Ĕ":"E","Ė":"E","Ë":"E","Ẻ":"E","Ě":"E","Ȅ":"E","Ȇ":"E","Ẹ":"E","Ệ":"E","Ȩ":"E","Ḝ":"E","Ę":"E","Ḙ":"E","Ḛ":"E","Ɛ":"E","Ǝ":"E","Ⓕ":"F","Ｆ":"F","Ḟ":"F","Ƒ":"F","Ꝼ":"F","Ⓖ":"G","Ｇ":"G","Ǵ":"G","Ĝ":"G","Ḡ":"G","Ğ":"G","Ġ":"G","Ǧ":"G","Ģ":"G","Ǥ":"G","Ɠ":"G","Ꞡ":"G","Ᵹ":"G","Ꝿ":"G","Ⓗ":"H","Ｈ":"H","Ĥ":"H","Ḣ":"H","Ḧ":"H","Ȟ":"H","Ḥ":"H","Ḩ":"H","Ḫ":"H","Ħ":"H","Ⱨ":"H","Ⱶ":"H","Ɥ":"H","Ⓘ":"I","Ｉ":"I","Ì":"I","Í":"I","Î":"I","Ĩ":"I","Ī":"I","Ĭ":"I","İ":"I","Ï":"I","Ḯ":"I","Ỉ":"I","Ǐ":"I","Ȉ":"I","Ȋ":"I","Ị":"I","Į":"I","Ḭ":"I","Ɨ":"I","Ⓙ":"J","Ｊ":"J","Ĵ":"J","Ɉ":"J","Ⓚ":"K","Ｋ":"K","Ḱ":"K","Ǩ":"K","Ḳ":"K","Ķ":"K","Ḵ":"K","Ƙ":"K","Ⱪ":"K","Ꝁ":"K","Ꝃ":"K","Ꝅ":"K","Ꞣ":"K","Ⓛ":"L","Ｌ":"L","Ŀ":"L","Ĺ":"L","Ľ":"L","Ḷ":"L","Ḹ":"L","Ļ":"L","Ḽ":"L","Ḻ":"L","Ł":"L","Ƚ":"L","Ɫ":"L","Ⱡ":"L","Ꝉ":"L","Ꝇ":"L","Ꞁ":"L","Ǉ":"LJ","ǈ":"Lj","Ⓜ":"M","Ｍ":"M","Ḿ":"M","Ṁ":"M","Ṃ":"M","Ɱ":"M","Ɯ":"M","Ⓝ":"N","Ｎ":"N","Ǹ":"N","Ń":"N","Ñ":"N","Ṅ":"N","Ň":"N","Ṇ":"N","Ņ":"N","Ṋ":"N","Ṉ":"N","Ƞ":"N","Ɲ":"N","Ꞑ":"N","Ꞥ":"N","Ǌ":"NJ","ǋ":"Nj","Ⓞ":"O","Ｏ":"O","Ò":"O","Ó":"O","Ô":"O","Ồ":"O","Ố":"O","Ỗ":"O","Ổ":"O","Õ":"O","Ṍ":"O","Ȭ":"O","Ṏ":"O","Ō":"O","Ṑ":"O","Ṓ":"O","Ŏ":"O","Ȯ":"O","Ȱ":"O","Ö":"O","Ȫ":"O","Ỏ":"O","Ő":"O","Ǒ":"O","Ȍ":"O","Ȏ":"O","Ơ":"O","Ờ":"O","Ớ":"O","Ỡ":"O","Ở":"O","Ợ":"O","Ọ":"O","Ộ":"O","Ǫ":"O","Ǭ":"O","Ø":"O","Ǿ":"O","Ɔ":"O","Ɵ":"O","Ꝋ":"O","Ꝍ":"O","Œ":"OE","Ƣ":"OI","Ꝏ":"OO","Ȣ":"OU","Ⓟ":"P","Ｐ":"P","Ṕ":"P","Ṗ":"P","Ƥ":"P","Ᵽ":"P","Ꝑ":"P","Ꝓ":"P","Ꝕ":"P","Ⓠ":"Q","Ｑ":"Q","Ꝗ":"Q","Ꝙ":"Q","Ɋ":"Q","Ⓡ":"R","Ｒ":"R","Ŕ":"R","Ṙ":"R","Ř":"R","Ȑ":"R","Ȓ":"R","Ṛ":"R","Ṝ":"R","Ŗ":"R","Ṟ":"R","Ɍ":"R","Ɽ":"R","Ꝛ":"R","Ꞧ":"R","Ꞃ":"R","Ⓢ":"S","Ｓ":"S","ẞ":"S","Ś":"S","Ṥ":"S","Ŝ":"S","Ṡ":"S","Š":"S","Ṧ":"S","Ṣ":"S","Ṩ":"S","Ș":"S","Ş":"S","Ȿ":"S","Ꞩ":"S","Ꞅ":"S","Ⓣ":"T","Ｔ":"T","Ṫ":"T","Ť":"T","Ṭ":"T","Ț":"T","Ţ":"T","Ṱ":"T","Ṯ":"T","Ŧ":"T","Ƭ":"T","Ʈ":"T","Ⱦ":"T","Ꞇ":"T","Ꜩ":"TZ","Ⓤ":"U","Ｕ":"U","Ù":"U","Ú":"U","Û":"U","Ũ":"U","Ṹ":"U","Ū":"U","Ṻ":"U","Ŭ":"U","Ü":"U","Ǜ":"U","Ǘ":"U","Ǖ":"U","Ǚ":"U","Ủ":"U","Ů":"U","Ű":"U","Ǔ":"U","Ȕ":"U","Ȗ":"U","Ư":"U","Ừ":"U","Ứ":"U","Ữ":"U","Ử":"U","Ự":"U","Ụ":"U","Ṳ":"U","Ų":"U","Ṷ":"U","Ṵ":"U","Ʉ":"U","Ⓥ":"V","Ｖ":"V","Ṽ":"V","Ṿ":"V","Ʋ":"V","Ꝟ":"V","Ʌ":"V","Ꝡ":"VY","Ⓦ":"W","Ｗ":"W","Ẁ":"W","Ẃ":"W","Ŵ":"W","Ẇ":"W","Ẅ":"W","Ẉ":"W","Ⱳ":"W","Ⓧ":"X","Ｘ":"X","Ẋ":"X","Ẍ":"X","Ⓨ":"Y","Ｙ":"Y","Ỳ":"Y","Ý":"Y","Ŷ":"Y","Ỹ":"Y","Ȳ":"Y","Ẏ":"Y","Ÿ":"Y","Ỷ":"Y","Ỵ":"Y","Ƴ":"Y","Ɏ":"Y","Ỿ":"Y","Ⓩ":"Z","Ｚ":"Z","Ź":"Z","Ẑ":"Z","Ż":"Z","Ž":"Z","Ẓ":"Z","Ẕ":"Z","Ƶ":"Z","Ȥ":"Z","Ɀ":"Z","Ⱬ":"Z","Ꝣ":"Z","ⓐ":"a","ａ":"a","ẚ":"a","à":"a","á":"a","â":"a","ầ":"a","ấ":"a","ẫ":"a","ẩ":"a","ã":"a","ā":"a","ă":"a","ằ":"a","ắ":"a","ẵ":"a","ẳ":"a","ȧ":"a","ǡ":"a","ä":"a","ǟ":"a","ả":"a","å":"a","ǻ":"a","ǎ":"a","ȁ":"a","ȃ":"a","ạ":"a","ậ":"a","ặ":"a","ḁ":"a","ą":"a","ⱥ":"a","ɐ":"a","ꜳ":"aa","æ":"ae","ǽ":"ae","ǣ":"ae","ꜵ":"ao","ꜷ":"au","ꜹ":"av","ꜻ":"av","ꜽ":"ay","ⓑ":"b","ｂ":"b","ḃ":"b","ḅ":"b","ḇ":"b","ƀ":"b","ƃ":"b","ɓ":"b","ⓒ":"c","ｃ":"c","ć":"c","ĉ":"c","ċ":"c","č":"c","ç":"c","ḉ":"c","ƈ":"c","ȼ":"c","ꜿ":"c","ↄ":"c","ⓓ":"d","ｄ":"d","ḋ":"d","ď":"d","ḍ":"d","ḑ":"d","ḓ":"d","ḏ":"d","đ":"d","ƌ":"d","ɖ":"d","ɗ":"d","ꝺ":"d","ǳ":"dz","ǆ":"dz","ⓔ":"e","ｅ":"e","è":"e","é":"e","ê":"e","ề":"e","ế":"e","ễ":"e","ể":"e","ẽ":"e","ē":"e","ḕ":"e","ḗ":"e","ĕ":"e","ė":"e","ë":"e","ẻ":"e","ě":"e","ȅ":"e","ȇ":"e","ẹ":"e","ệ":"e","ȩ":"e","ḝ":"e","ę":"e","ḙ":"e","ḛ":"e","ɇ":"e","ɛ":"e","ǝ":"e","ⓕ":"f","ｆ":"f","ḟ":"f","ƒ":"f","ꝼ":"f","ⓖ":"g","ｇ":"g","ǵ":"g","ĝ":"g","ḡ":"g","ğ":"g","ġ":"g","ǧ":"g","ģ":"g","ǥ":"g","ɠ":"g","ꞡ":"g","ᵹ":"g","ꝿ":"g","ⓗ":"h","ｈ":"h","ĥ":"h","ḣ":"h","ḧ":"h","ȟ":"h","ḥ":"h","ḩ":"h","ḫ":"h","ẖ":"h","ħ":"h","ⱨ":"h","ⱶ":"h","ɥ":"h","ƕ":"hv","ⓘ":"i","ｉ":"i","ì":"i","í":"i","î":"i","ĩ":"i","ī":"i","ĭ":"i","ï":"i","ḯ":"i","ỉ":"i","ǐ":"i","ȉ":"i","ȋ":"i","ị":"i","į":"i","ḭ":"i","ɨ":"i","ı":"i","ⓙ":"j","ｊ":"j","ĵ":"j","ǰ":"j","ɉ":"j","ⓚ":"k","ｋ":"k","ḱ":"k","ǩ":"k","ḳ":"k","ķ":"k","ḵ":"k","ƙ":"k","ⱪ":"k","ꝁ":"k","ꝃ":"k","ꝅ":"k","ꞣ":"k","ⓛ":"l","ｌ":"l","ŀ":"l","ĺ":"l","ľ":"l","ḷ":"l","ḹ":"l","ļ":"l","ḽ":"l","ḻ":"l","ſ":"l","ł":"l","ƚ":"l","ɫ":"l","ⱡ":"l","ꝉ":"l","ꞁ":"l","ꝇ":"l","ǉ":"lj","ⓜ":"m","ｍ":"m","ḿ":"m","ṁ":"m","ṃ":"m","ɱ":"m","ɯ":"m","ⓝ":"n","ｎ":"n","ǹ":"n","ń":"n","ñ":"n","ṅ":"n","ň":"n","ṇ":"n","ņ":"n","ṋ":"n","ṉ":"n","ƞ":"n","ɲ":"n","ŉ":"n","ꞑ":"n","ꞥ":"n","ǌ":"nj","ⓞ":"o","ｏ":"o","ò":"o","ó":"o","ô":"o","ồ":"o","ố":"o","ỗ":"o","ổ":"o","õ":"o","ṍ":"o","ȭ":"o","ṏ":"o","ō":"o","ṑ":"o","ṓ":"o","ŏ":"o","ȯ":"o","ȱ":"o","ö":"o","ȫ":"o","ỏ":"o","ő":"o","ǒ":"o","ȍ":"o","ȏ":"o","ơ":"o","ờ":"o","ớ":"o","ỡ":"o","ở":"o","ợ":"o","ọ":"o","ộ":"o","ǫ":"o","ǭ":"o","ø":"o","ǿ":"o","ɔ":"o","ꝋ":"o","ꝍ":"o","ɵ":"o","œ":"oe","ƣ":"oi","ȣ":"ou","ꝏ":"oo","ⓟ":"p","ｐ":"p","ṕ":"p","ṗ":"p","ƥ":"p","ᵽ":"p","ꝑ":"p","ꝓ":"p","ꝕ":"p","ⓠ":"q","ｑ":"q","ɋ":"q","ꝗ":"q","ꝙ":"q","ⓡ":"r","ｒ":"r","ŕ":"r","ṙ":"r","ř":"r","ȑ":"r","ȓ":"r","ṛ":"r","ṝ":"r","ŗ":"r","ṟ":"r","ɍ":"r","ɽ":"r","ꝛ":"r","ꞧ":"r","ꞃ":"r","ⓢ":"s","ｓ":"s","ß":"s","ś":"s","ṥ":"s","ŝ":"s","ṡ":"s","š":"s","ṧ":"s","ṣ":"s","ṩ":"s","ș":"s","ş":"s","ȿ":"s","ꞩ":"s","ꞅ":"s","ẛ":"s","ⓣ":"t","ｔ":"t","ṫ":"t","ẗ":"t","ť":"t","ṭ":"t","ț":"t","ţ":"t","ṱ":"t","ṯ":"t","ŧ":"t","ƭ":"t","ʈ":"t","ⱦ":"t","ꞇ":"t","ꜩ":"tz","ⓤ":"u","ｕ":"u","ù":"u","ú":"u","û":"u","ũ":"u","ṹ":"u","ū":"u","ṻ":"u","ŭ":"u","ü":"u","ǜ":"u","ǘ":"u","ǖ":"u","ǚ":"u","ủ":"u","ů":"u","ű":"u","ǔ":"u","ȕ":"u","ȗ":"u","ư":"u","ừ":"u","ứ":"u","ữ":"u","ử":"u","ự":"u","ụ":"u","ṳ":"u","ų":"u","ṷ":"u","ṵ":"u","ʉ":"u","ⓥ":"v","ｖ":"v","ṽ":"v","ṿ":"v","ʋ":"v","ꝟ":"v","ʌ":"v","ꝡ":"vy","ⓦ":"w","ｗ":"w","ẁ":"w","ẃ":"w","ŵ":"w","ẇ":"w","ẅ":"w","ẘ":"w","ẉ":"w","ⱳ":"w","ⓧ":"x","ｘ":"x","ẋ":"x","ẍ":"x","ⓨ":"y","ｙ":"y","ỳ":"y","ý":"y","ŷ":"y","ỹ":"y","ȳ":"y","ẏ":"y","ÿ":"y","ỷ":"y","ẙ":"y","ỵ":"y","ƴ":"y","ɏ":"y","ỿ":"y","ⓩ":"z","ｚ":"z","ź":"z","ẑ":"z","ż":"z","ž":"z","ẓ":"z","ẕ":"z","ƶ":"z","ȥ":"z","ɀ":"z","ⱬ":"z","ꝣ":"z","Ά":"Α","Έ":"Ε","Ή":"Η","Ί":"Ι","Ϊ":"Ι","Ό":"Ο","Ύ":"Υ","Ϋ":"Υ","Ώ":"Ω","ά":"α","έ":"ε","ή":"η","ί":"ι","ϊ":"ι","ΐ":"ι","ό":"ο","ύ":"υ","ϋ":"υ","ΰ":"υ","ώ":"ω","ς":"σ","’":"'"}}),t.define("select2/data/base",["../utils"],function(n){function t(){t.__super__.constructor.call(this)}return n.Extend(t,n.Observable),t.prototype.current=function(){throw new Error("The `current` method must be defined in child classes.");},t.prototype.query=function(){throw new Error("The `query` method must be defined in child classes.");},t.prototype.bind=function(){},t.prototype.destroy=function(){},t.prototype.generateResultId=function(t,i){var r=t.id+"-result-";return r+=n.generateChars(4),r+(i.id!=null?"-"+i.id.toString():"-"+n.generateChars(4))},t}),t.define("select2/data/select",["./base","../utils","jquery"],function(n,t,i){function r(n,t){this.$element=n;this.options=t;r.__super__.constructor.call(this)}return t.Extend(r,n),r.prototype.current=function(n){var t=this,r=Array.prototype.map.call(this.$element[0].querySelectorAll(":checked"),function(n){return t.item(i(n))});n(r)},r.prototype.select=function(n){var t=this,i;if(n.selected=!0,n.element!=null&&n.element.tagName.toLowerCase()==="option"){n.element.selected=!0;this.$element.trigger("input").trigger("change");return}this.$element.prop("multiple")?this.current(function(i){var u=[],r,f;for(n=[n],n.push.apply(n,i),r=0;r<n.length;r++)f=n[r].id,u.indexOf(f)===-1&&u.push(f);t.$element.val(u);t.$element.trigger("input").trigger("change")}):(i=n.id,this.$element.val(i),this.$element.trigger("input").trigger("change"))},r.prototype.unselect=function(n){var t=this;if(this.$element.prop("multiple")){if(n.selected=!1,n.element!=null&&n.element.tagName.toLowerCase()==="option"){n.element.selected=!1;this.$element.trigger("input").trigger("change");return}this.current(function(i){for(var r,u=[],f=0;f<i.length;f++)r=i[f].id,r!==n.id&&u.indexOf(r)===-1&&u.push(r);t.$element.val(u);t.$element.trigger("input").trigger("change")})}},r.prototype.bind=function(n){var t=this;this.container=n;n.on("select",function(n){t.select(n.data)});n.on("unselect",function(n){t.unselect(n.data)})},r.prototype.destroy=function(){this.$element.find("*").each(function(){t.RemoveData(this)})},r.prototype.query=function(n,t){var r=[],u=this,f=this.$element.children();f.each(function(){if(this.tagName.toLowerCase()==="option"||this.tagName.toLowerCase()==="optgroup"){var f=i(this),e=u.item(f),t=u.matches(n,e);t!==null&&r.push(t)}});t({results:r})},r.prototype.addOptions=function(n){this.$element.append(n)},r.prototype.option=function(n){var r,u;return n.children?(r=document.createElement("optgroup"),r.label=n.text):(r=document.createElement("option"),r.textContent!==undefined?r.textContent=n.text:r.innerText=n.text),n.id!==undefined&&(r.value=n.id),n.disabled&&(r.disabled=!0),n.selected&&(r.selected=!0),n.title&&(r.title=n.title),u=this._normalizeItem(n),u.element=r,t.StoreData(r,"data",u),i(r)},r.prototype.item=function(n){var r={},f,e,o,u,s,h;if(r=t.GetData(n[0],"data"),r!=null)return r;if(f=n[0],f.tagName.toLowerCase()==="option")r={id:n.val(),text:n.text(),disabled:n.prop("disabled"),selected:n.prop("selected"),title:n.prop("title")};else if(f.tagName.toLowerCase()==="optgroup"){for(r={text:n.prop("label"),children:[],title:n.prop("title")},e=n.children("option"),o=[],u=0;u<e.length;u++)s=i(e[u]),h=this.item(s),o.push(h);r.children=o}return r=this._normalizeItem(r),r.element=n[0],t.StoreData(n[0],"data",r),r},r.prototype._normalizeItem=function(n){n!==Object(n)&&(n={id:n,text:n});n=i.extend({},{text:""},n);return n.id!=null&&(n.id=n.id.toString()),n.text!=null&&(n.text=n.text.toString()),n._resultId==null&&n.id&&this.container!=null&&(n._resultId=this.generateResultId(this.container,n)),i.extend({},{selected:!1,disabled:!1},n)},r.prototype.matches=function(n,t){var i=this.options.get("matcher");return i(n,t)},r}),t.define("select2/data/array",["./select","../utils","jquery"],function(n,t,i){function r(n,t){this._dataToConvert=t.get("data")||[];r.__super__.constructor.call(this,n,t)}return t.Extend(r,n),r.prototype.bind=function(n,t){r.__super__.bind.call(this,n,t);this.addOptions(this.convertToOptions(this._dataToConvert))},r.prototype.select=function(n){var t=this.$element.find("option").filter(function(t,i){return i.value==n.id.toString()});t.length===0&&(t=this.option(n),this.addOptions(t));r.__super__.select.call(this,n)},r.prototype.convertToOptions=function(n){function l(n){return function(){return i(this).val()==n.id}}for(var t,u,s,h=this,f=this.$element.find("option"),c=f.map(function(){return h.item(i(this)).id}).get(),e=[],r=0;r<n.length;r++){if(t=this._normalizeItem(n[r]),c.indexOf(t.id)>=0){var o=f.filter(l(t)),a=this.item(o),v=i.extend(!0,{},t,a),y=this.option(v);o.replaceWith(y);continue}u=this.option(t);t.children&&(s=this.convertToOptions(t.children),u.append(s));e.push(u)}return e},r}),t.define("select2/data/ajax",["./array","../utils","jquery"],function(n,t,i){function r(n,t){this.ajaxOptions=this._applyDefaults(t.get("ajax"));this.ajaxOptions.processResults!=null&&(this.processResults=this.ajaxOptions.processResults);r.__super__.constructor.call(this,n,t)}return t.Extend(r,n),r.prototype._applyDefaults=function(n){var t={data:function(n){return i.extend({},n,{q:n.term})},transport:function(n,t,r){var u=i.ajax(n);return u.then(t),u.fail(r),u}};return i.extend({},t,n,!0)},r.prototype.processResults=function(n){return n},r.prototype.query=function(n,t){function f(){var i=r.transport(r,function(i){var r=u.processResults(i,n);u.options.get("debug")&&window.console&&console.error&&(r&&r.results&&Array.isArray(r.results)||console.error("Select2: The AJAX results did not return an array in the `results` key of the response."));t(r)},function(){"status"in i&&(i.status===0||i.status==="0")||u.trigger("results:message",{message:"errorLoading"})});u._request=i}var u=this,r;this._request!=null&&(typeof this._request.abort=="function"&&this._request.abort(),this._request=null);r=i.extend({type:"GET"},this.ajaxOptions);typeof r.url=="function"&&(r.url=r.url.call(this.$element,n));typeof r.data=="function"&&(r.data=r.data.call(this.$element,n));this.ajaxOptions.delay&&n.term!=null?(this._queryTimeout&&window.clearTimeout(this._queryTimeout),this._queryTimeout=window.setTimeout(f,this.ajaxOptions.delay)):f()},r}),t.define("select2/data/tags",["jquery"],function(n){function t(n,t,i){var u=i.get("tags"),e=i.get("createTag"),f,r;if(e!==undefined&&(this.createTag=e),f=i.get("insertTag"),f!==undefined&&(this.insertTag=f),n.call(this,t,i),Array.isArray(u))for(r=0;r<u.length;r++){var o=u[r],s=this._normalizeItem(o),h=this.option(s);this.$element.append(h)}}return t.prototype.query=function(n,t,i){function u(n,f){for(var o,c,e=n.results,s=0;s<e.length;s++){var h=e[s],l=h.children!=null&&!u({results:h.children},!0),a=(h.text||"").toUpperCase(),v=(t.term||"").toUpperCase(),y=a===v;if(y||l){if(f)return!1;n.data=e;i(n);return}}if(f)return!0;o=r.createTag(t);o!=null&&(c=r.option(o),c.attr("data-select2-tag","true"),r.addOptions([c]),r.insertTag(e,o));n.results=e;i(n)}var r=this;if(this._removeOldTags(),t.term==null||t.page!=null){n.call(this,t,i);return}n.call(this,t,u)},t.prototype.createTag=function(n,t){if(t.term==null)return null;var i=t.term.trim();return i===""?null:{id:i,text:i}},t.prototype.insertTag=function(n,t,i){t.unshift(i)},t.prototype._removeOldTags=function(){var t=this.$element.find("option[data-select2-tag]");t.each(function(){this.selected||n(this).remove()})},t}),t.define("select2/data/tokenizer",["jquery"],function(n){function t(n,t,i){var r=i.get("tokenizer");r!==undefined&&(this.tokenizer=r);n.call(this,t,i)}return t.prototype.bind=function(n,t,i){n.call(this,t,i);this.$search=t.dropdown.$search||t.selection.$search||i.find(".select2-search__field")},t.prototype.query=function(t,i,r){function e(t){var i=u._normalizeItem(t),f=u.$element.find("option").filter(function(){return n(this).val()===i.id}),r;f.length||(r=u.option(i),r.attr("data-select2-tag",!0),u._removeOldTags(),u.addOptions([r]));o(i)}function o(n){u.trigger("select",{data:n})}var u=this,f;i.term=i.term||"";f=this.tokenizer(i,this.options,e);f.term!==i.term&&(this.$search.length&&(this.$search.val(f.term),this.$search.trigger("focus")),i.term=f.term);t.call(this,i,r)},t.prototype.tokenizer=function(t,i,r,u){for(var h=r.get("tokenSeparators")||[],e=i.term,f=0,c=this.createTag||function(n){return{id:n.term,text:n.term}},o;f<e.length;){if(o=e[f],h.indexOf(o)===-1){f++;continue}var l=e.substr(0,f),a=n.extend({},i,{term:l}),s=c(a);if(s==null){f++;continue}u(s);e=e.substr(f+1)||"";f=0}return{term:e}},t}),t.define("select2/data/minimumInputLength",[],function(){function n(n,t,i){this.minimumInputLength=i.get("minimumInputLength");n.call(this,t,i)}return n.prototype.query=function(n,t,i){if(t.term=t.term||"",t.term.length<this.minimumInputLength){this.trigger("results:message",{message:"inputTooShort",args:{minimum:this.minimumInputLength,input:t.term,params:t}});return}n.call(this,t,i)},n}),t.define("select2/data/maximumInputLength",[],function(){function n(n,t,i){this.maximumInputLength=i.get("maximumInputLength");n.call(this,t,i)}return n.prototype.query=function(n,t,i){if(t.term=t.term||"",this.maximumInputLength>0&&t.term.length>this.maximumInputLength){this.trigger("results:message",{message:"inputTooLong",args:{maximum:this.maximumInputLength,input:t.term,params:t}});return}n.call(this,t,i)},n}),t.define("select2/data/maximumSelectionLength",[],function(){function n(n,t,i){this.maximumSelectionLength=i.get("maximumSelectionLength");n.call(this,t,i)}return n.prototype.bind=function(n,t,i){var r=this;n.call(this,t,i);t.on("select",function(){r._checkIfMaximumSelected()})},n.prototype.query=function(n,t,i){var r=this;this._checkIfMaximumSelected(function(){n.call(r,t,i)})},n.prototype._checkIfMaximumSelected=function(n,t){var i=this;this.current(function(n){var r=n!=null?n.length:0;if(i.maximumSelectionLength>0&&r>=i.maximumSelectionLength){i.trigger("results:message",{message:"maximumSelected",args:{maximum:i.maximumSelectionLength}});return}t&&t()})},n}),t.define("select2/dropdown",["jquery","./utils"],function(n,t){function i(n,t){this.$element=n;this.options=t;i.__super__.constructor.call(this)}return t.Extend(i,t.Observable),i.prototype.render=function(){var t=n('<span class="select2-dropdown"><span class="select2-results"><\/span><\/span>');return t.attr("dir",this.options.get("dir")),this.$dropdown=t,t},i.prototype.bind=function(){},i.prototype.position=function(){},i.prototype.destroy=function(){this.$dropdown.remove()},i}),t.define("select2/dropdown/search",["jquery"],function(n){function t(){}return t.prototype.render=function(t){var r=t.call(this),u=this.options.get("translations").get("search"),i=n('<span class="select2-search select2-search--dropdown"><input class="select2-search__field" type="search" tabindex="-1" autocorrect="off" autocapitalize="none" spellcheck="false" role="searchbox" aria-autocomplete="list" /><\/span>');return this.$searchContainer=i,this.$search=i.find("input"),this.$search.prop("autocomplete",this.options.get("autocomplete")),this.$search.attr("aria-label",u()),r.prepend(i),r},t.prototype.bind=function(t,i,r){var u=this,f=i.id+"-results";t.call(this,i,r);this.$search.on("keydown",function(n){u.trigger("keypress",n);u._keyUpPrevented=n.isDefaultPrevented()});this.$search.on("input",function(){n(this).off("keyup")});this.$search.on("keyup input",function(n){u.handleSearch(n)});i.on("open",function(){u.$search.attr("tabindex",0);u.$search.attr("aria-controls",f);u.$search.trigger("focus");window.setTimeout(function(){u.$search.trigger("focus")},0)});i.on("close",function(){u.$search.attr("tabindex",-1);u.$search.removeAttr("aria-controls");u.$search.removeAttr("aria-activedescendant");u.$search.val("");u.$search.trigger("blur")});i.on("focus",function(){i.isOpen()||u.$search.trigger("focus")});i.on("results:all",function(n){if(n.query.term==null||n.query.term===""){var t=u.showSearch(n);t?u.$searchContainer[0].classList.remove("select2-search--hide"):u.$searchContainer[0].classList.add("select2-search--hide")}});i.on("results:focus",function(n){n.data._resultId?u.$search.attr("aria-activedescendant",n.data._resultId):u.$search.removeAttr("aria-activedescendant")})},t.prototype.handleSearch=function(){if(!this._keyUpPrevented){var n=this.$search.val();this.trigger("query",{term:n})}this._keyUpPrevented=!1},t.prototype.showSearch=function(){return!0},t}),t.define("select2/dropdown/hidePlaceholder",[],function(){function n(n,t,i,r){this.placeholder=this.normalizePlaceholder(i.get("placeholder"));n.call(this,t,i,r)}return n.prototype.append=function(n,t){t.results=this.removePlaceholder(t.results);n.call(this,t)},n.prototype.normalizePlaceholder=function(n,t){return typeof t=="string"&&(t={id:"",text:t}),t},n.prototype.removePlaceholder=function(n,t){for(var u,r=t.slice(0),i=t.length-1;i>=0;i--)u=t[i],this.placeholder.id===u.id&&r.splice(i,1);return r},n}),t.define("select2/dropdown/infiniteScroll",["jquery"],function(n){function t(n,t,i,r){this.lastParams={};n.call(this,t,i,r);this.$loadingMore=this.createLoadingMore();this.loading=!1}return t.prototype.append=function(n,t){this.$loadingMore.remove();this.loading=!1;n.call(this,t);this.showLoadingMore(t)&&(this.$results.append(this.$loadingMore),this.loadMoreIfNeeded())},t.prototype.bind=function(n,t,i){var r=this;n.call(this,t,i);t.on("query",function(n){r.lastParams=n;r.loading=!0});t.on("query:append",function(n){r.lastParams=n;r.loading=!0});this.$results.on("scroll",this.loadMoreIfNeeded.bind(this))},t.prototype.loadMoreIfNeeded=function(){var r=n.contains(document.documentElement,this.$loadingMore[0]),t,i;!this.loading&&r&&(t=this.$results.offset().top+this.$results.outerHeight(!1),i=this.$loadingMore.offset().top+this.$loadingMore.outerHeight(!1),t+50>=i&&this.loadMore())},t.prototype.loadMore=function(){this.loading=!0;var t=n.extend({},{page:1},this.lastParams);t.page++;this.trigger("query:append",t)},t.prototype.showLoadingMore=function(n,t){return t.pagination&&t.pagination.more},t.prototype.createLoadingMore=function(){var t=n('<li class="select2-results__option select2-results__option--load-more"role="option" aria-disabled="true"><\/li>'),i=this.options.get("translations").get("loadingMore");return t.html(i(this.lastParams)),t},t}),t.define("select2/dropdown/attachBody",["jquery","../utils"],function(n,t){function i(t,i,r){this.$dropdownParent=n(r.get("dropdownParent")||document.body);t.call(this,i,r)}return i.prototype.bind=function(n,t,i){var r=this;n.call(this,t,i);t.on("open",function(){r._showDropdown();r._attachPositioningHandler(t);r._bindContainerResultHandlers(t)});t.on("close",function(){r._hideDropdown();r._detachPositioningHandler(t)});this.$dropdownContainer.on("mousedown",function(n){n.stopPropagation()})},i.prototype.destroy=function(n){n.call(this);this.$dropdownContainer.remove()},i.prototype.position=function(n,t,i){t.attr("class",i.attr("class"));t[0].classList.remove("select2");t[0].classList.add("select2-container--open");t.css({position:"absolute",top:-999999});this.$container=i},i.prototype.render=function(t){var i=n("<span><\/span>"),r=t.call(this);return i.append(r),this.$dropdownContainer=i,i},i.prototype._hideDropdown=function(){this.$dropdownContainer.detach()},i.prototype._bindContainerResultHandlers=function(n,t){if(!this._containerResultsHandlersBound){var i=this;t.on("results:all",function(){i._positionDropdown();i._resizeDropdown()});t.on("results:append",function(){i._positionDropdown();i._resizeDropdown()});t.on("results:message",function(){i._positionDropdown();i._resizeDropdown()});t.on("select",function(){i._positionDropdown();i._resizeDropdown()});t.on("unselect",function(){i._positionDropdown();i._resizeDropdown()});this._containerResultsHandlersBound=!0}},i.prototype._attachPositioningHandler=function(i,r){var u=this,f="scroll.select2."+r.id,o="resize.select2."+r.id,s="orientationchange.select2."+r.id,e=this.$container.parents().filter(t.hasScroll);e.each(function(){t.StoreData(this,"select2-scroll-position",{x:n(this).scrollLeft(),y:n(this).scrollTop()})});e.on(f,function(){var i=t.GetData(this,"select2-scroll-position");n(this).scrollTop(i.y)});n(window).on(f+" "+o+" "+s,function(){u._positionDropdown();u._resizeDropdown()})},i.prototype._detachPositioningHandler=function(i,r){var u="scroll.select2."+r.id,f="resize.select2."+r.id,e="orientationchange.select2."+r.id,o=this.$container.parents().filter(t.hasScroll);o.off(u);n(window).off(u+" "+f+" "+e)},i.prototype._positionDropdown=function(){var s=n(window),e=this.$dropdown[0].classList.contains("select2-dropdown--above"),v=this.$dropdown[0].classList.contains("select2-dropdown--below"),t=null,i=this.$container.offset(),r,f;i.bottom=i.top+this.$container.outerHeight(!1);r={height:this.$container.outerHeight(!1)};r.top=i.top;r.bottom=i.top+r.height;var h={height:this.$dropdown.outerHeight(!1)},c={top:s.scrollTop(),bottom:s.scrollTop()+s.height()},l=c.top<i.top-h.height,a=c.bottom>i.bottom+h.height,o={left:i.left,top:r.bottom},u=this.$dropdownParent;u.css("position")==="static"&&(u=u.offsetParent());f={top:0,left:0};(n.contains(document.body,u[0])||u[0].isConnected)&&(f=u.offset());o.top-=f.top;o.left-=f.left;e||v||(t="below");a||!l||e?!l&&a&&e&&(t="below"):t="above";(t=="above"||e&&t!=="below")&&(o.top=r.top-f.top-h.height);t!=null&&(this.$dropdown[0].classList.remove("select2-dropdown--below"),this.$dropdown[0].classList.remove("select2-dropdown--above"),this.$dropdown[0].classList.add("select2-dropdown--"+t),this.$container[0].classList.remove("select2-container--below"),this.$container[0].classList.remove("select2-container--above"),this.$container[0].classList.add("select2-container--"+t));this.$dropdownContainer.css(o)},i.prototype._resizeDropdown=function(){var n={width:this.$container.outerWidth(!1)+"px"};this.options.get("dropdownAutoWidth")&&(n.minWidth=n.width,n.position="relative",n.width="auto");this.$dropdown.css(n)},i.prototype._showDropdown=function(){this.$dropdownContainer.appendTo(this.$dropdownParent);this._positionDropdown();this._resizeDropdown()},i}),t.define("select2/dropdown/minimumResultsForSearch",[],function(){function n(t){for(var u,i=0,r=0;r<t.length;r++)u=t[r],u.children?i+=n(u.children):i++;return i}function t(n,t,i,r){this.minimumResultsForSearch=i.get("minimumResultsForSearch");this.minimumResultsForSearch<0&&(this.minimumResultsForSearch=Infinity);n.call(this,t,i,r)}return t.prototype.showSearch=function(t,i){return n(i.data.results)<this.minimumResultsForSearch?!1:t.call(this,i)},t}),t.define("select2/dropdown/selectOnClose",["../utils"],function(n){function t(){}return t.prototype.bind=function(n,t,i){var r=this;n.call(this,t,i);t.on("close",function(n){r._handleSelectOnClose(n)})},t.prototype._handleSelectOnClose=function(t,i){var u,f,r;i&&i.originalSelect2Event!=null&&(u=i.originalSelect2Event,u._type==="select"||u._type==="unselect")||(f=this.getHighlightedResults(),f.length<1)||(r=n.GetData(f[0],"data"),r.element!=null&&r.element.selected||r.element==null&&r.selected)||this.trigger("select",{data:r})},t}),t.define("select2/dropdown/closeOnSelect",[],function(){function n(){}return n.prototype.bind=function(n,t,i){var r=this;n.call(this,t,i);t.on("select",function(n){r._selectTriggered(n)});t.on("unselect",function(n){r._selectTriggered(n)})},n.prototype._selectTriggered=function(n,t){var i=t.originalEvent;i&&(i.ctrlKey||i.metaKey)||this.trigger("close",{originalEvent:i,originalSelect2Event:t})},n}),t.define("select2/dropdown/dropdownCss",["../utils"],function(n){function t(){}return t.prototype.render=function(t){var r=t.call(this),i=this.options.get("dropdownCssClass")||"";return i.indexOf(":all:")!==-1&&(i=i.replace(":all:",""),n.copyNonInternalCssClasses(r[0],this.$element[0])),r.addClass(i),r},t}),t.define("select2/dropdown/tagsSearchHighlight",["../utils"],function(n){function t(){}return t.prototype.highlightFirstItem=function(t){var r=this.$results.find(".select2-results__option--selectable:not(.select2-results__option--selected)");if(r.length>0){var u=r.first(),f=n.GetData(u[0],"data"),i=f.element;if(i&&i.getAttribute&&i.getAttribute("data-select2-tag")==="true"){u.trigger("mouseenter");return}}t.call(this)},t}),t.define("select2/i18n/en",[],function(){return{errorLoading:function(){return"The results could not be loaded."},inputTooLong:function(n){var t=n.input.length-n.maximum,i="Please delete "+t+" character";return t!=1&&(i+="s"),i},inputTooShort:function(n){var t=n.minimum-n.input.length;return"Please enter "+t+" or more characters"},loadingMore:function(){return"Loading more results…"},maximumSelected:function(n){var t="You can only select "+n.maximum+" item";return n.maximum!=1&&(t+="s"),t},noResults:function(){return"No results found"},searching:function(){return"Searching…"},removeAllItems:function(){return"Remove all items"},removeItem:function(){return"Remove item"},search:function(){return"Search"}}}),t.define("select2/defaults",["jquery","./results","./selection/single","./selection/multiple","./selection/placeholder","./selection/allowClear","./selection/search","./selection/selectionCss","./selection/eventRelay","./utils","./translation","./diacritics","./data/select","./data/array","./data/ajax","./data/tags","./data/tokenizer","./data/minimumInputLength","./data/maximumInputLength","./data/maximumSelectionLength","./dropdown","./dropdown/search","./dropdown/hidePlaceholder","./dropdown/infiniteScroll","./dropdown/attachBody","./dropdown/minimumResultsForSearch","./dropdown/selectOnClose","./dropdown/closeOnSelect","./dropdown/dropdownCss","./dropdown/tagsSearchHighlight","./i18n/en"],function(n,t,i,r,u,f,e,o,s,h,c,l,a,v,y,p,w,b,k,d,g,nt,tt,it,rt,ut,ft,et,ot,st){function ht(){this.reset()}ht.prototype.apply=function(c){var lt,l,ht,ct;for(c=n.extend(!0,{},this.defaults,c),c.dataAdapter==null&&(c.dataAdapter=c.ajax!=null?y:c.data!=null?v:a,c.minimumInputLength>0&&(c.dataAdapter=h.Decorate(c.dataAdapter,b)),c.maximumInputLength>0&&(c.dataAdapter=h.Decorate(c.dataAdapter,k)),c.maximumSelectionLength>0&&(c.dataAdapter=h.Decorate(c.dataAdapter,d)),c.tags&&(c.dataAdapter=h.Decorate(c.dataAdapter,p)),(c.tokenSeparators!=null||c.tokenizer!=null)&&(c.dataAdapter=h.Decorate(c.dataAdapter,w))),c.resultsAdapter==null&&(c.resultsAdapter=t,c.ajax!=null&&(c.resultsAdapter=h.Decorate(c.resultsAdapter,it)),c.placeholder!=null&&(c.resultsAdapter=h.Decorate(c.resultsAdapter,tt)),c.selectOnClose&&(c.resultsAdapter=h.Decorate(c.resultsAdapter,ft)),c.tags&&(c.resultsAdapter=h.Decorate(c.resultsAdapter,st))),c.dropdownAdapter==null&&(c.multiple?c.dropdownAdapter=g:(lt=h.Decorate(g,nt),c.dropdownAdapter=lt),c.minimumResultsForSearch!==0&&(c.dropdownAdapter=h.Decorate(c.dropdownAdapter,ut)),c.closeOnSelect&&(c.dropdownAdapter=h.Decorate(c.dropdownAdapter,et)),c.dropdownCssClass!=null&&(c.dropdownAdapter=h.Decorate(c.dropdownAdapter,ot)),c.dropdownAdapter=h.Decorate(c.dropdownAdapter,rt)),c.selectionAdapter==null&&(c.selectionAdapter=c.multiple?r:i,c.placeholder!=null&&(c.selectionAdapter=h.Decorate(c.selectionAdapter,u)),c.allowClear&&(c.selectionAdapter=h.Decorate(c.selectionAdapter,f)),c.multiple&&(c.selectionAdapter=h.Decorate(c.selectionAdapter,e)),c.selectionCssClass!=null&&(c.selectionAdapter=h.Decorate(c.selectionAdapter,o)),c.selectionAdapter=h.Decorate(c.selectionAdapter,s)),c.language=this._resolveLanguage(c.language),c.language.push("en"),l=[],ht=0;ht<c.language.length;ht++)ct=c.language[ht],l.indexOf(ct)===-1&&l.push(ct);return c.language=l,c.translations=this._processTranslations(c.language,c.debug),c};ht.prototype.reset=function(){function i(n){function t(n){return l[n]||n}return n.replace(/[^\u0000-\u007E]/g,t)}function t(r,u){var f,e,o,s,h,c;if(r.term==null||r.term.trim()==="")return u;if(u.children&&u.children.length>0){for(f=n.extend(!0,{},u),e=u.children.length-1;e>=0;e--)o=u.children[e],s=t(r,o),s==null&&f.children.splice(e,1);return f.children.length>0?f:t(r,f)}return(h=i(u.text).toUpperCase(),c=i(r.term).toUpperCase(),h.indexOf(c)>-1)?u:null}this.defaults={amdLanguageBase:"./i18n/",autocomplete:"off",closeOnSelect:!0,debug:!1,dropdownAutoWidth:!1,escapeMarkup:h.escapeMarkup,language:{},matcher:t,minimumInputLength:0,maximumInputLength:0,maximumSelectionLength:0,minimumResultsForSearch:0,selectOnClose:!1,scrollAfterSelect:!1,sorter:function(n){return n},templateResult:function(n){return n.text},templateSelection:function(n){return n.text},theme:"default",width:"resolve"}};ht.prototype.applyFromElement=function(n,t){var i=n.language,r=this.defaults.language,u=t.prop("lang"),f=t.closest("[lang]").prop("lang"),e=Array.prototype.concat.call(this._resolveLanguage(u),this._resolveLanguage(i),this._resolveLanguage(r),this._resolveLanguage(f));return n.language=e,n};ht.prototype._resolveLanguage=function(t){var r,u,i,f,e;if(!t)return[];if(n.isEmptyObject(t))return[];if(n.isPlainObject(t))return[t];for(r=Array.isArray(t)?t:[t],u=[],i=0;i<r.length;i++)u.push(r[i]),typeof r[i]=="string"&&r[i].indexOf("-")>0&&(f=r[i].split("-"),e=f[0],u.push(e));return u};ht.prototype._processTranslations=function(t,i){for(var u,r,e=new c,f=0;f<t.length;f++){if(u=new c,r=t[f],typeof r=="string")try{u=c.loadPath(r)}catch(o){try{r=this.defaults.amdLanguageBase+r;u=c.loadPath(r)}catch(s){i&&window.console&&console.warn&&console.warn('Select2: The language file for "'+r+'" could not be automatically loaded. A fallback will be used instead.')}}else u=n.isPlainObject(r)?new c(r):r;e.extend(u)}return e};ht.prototype.set=function(t,i){var f=n.camelCase(t),r={},u;r[f]=i;u=h._convertData(r);n.extend(!0,this.defaults,u)};return new ht}),t.define("select2/options",["jquery","./defaults","./utils"],function(n,t,i){function r(n,i){this.options=n;i!=null&&this.fromElement(i);i!=null&&(this.options=t.applyFromElement(this.options,i));this.options=t.apply(this.options)}return r.prototype.fromElement=function(t){function l(n,t){return t.toUpperCase()}var c=["select2"],f,e,s,o,u,r;for(this.options.multiple==null&&(this.options.multiple=t.prop("multiple")),this.options.disabled==null&&(this.options.disabled=t.prop("disabled")),this.options.autocomplete==null&&t.prop("autocomplete")&&(this.options.autocomplete=t.prop("autocomplete")),this.options.dir==null&&(this.options.dir=t.prop("dir")?t.prop("dir"):t.closest("[dir]").prop("dir")?t.closest("[dir]").prop("dir"):"ltr"),t.prop("disabled",this.options.disabled),t.prop("multiple",this.options.multiple),i.GetData(t[0],"select2Tags")&&(this.options.debug&&window.console&&console.warn&&console.warn('Select2: The `data-select2-tags` attribute has been changed to use the `data-data` and `data-tags="true"` attributes and will be removed in future versions of Select2.'),i.StoreData(t[0],"data",i.GetData(t[0],"select2Tags")),i.StoreData(t[0],"tags",!0)),i.GetData(t[0],"ajaxUrl")&&(this.options.debug&&window.console&&console.warn&&console.warn("Select2: The `data-ajax-url` attribute has been changed to `data-ajax--url` and support for the old attribute will be removed in future versions of Select2."),t.attr("ajax--url",i.GetData(t[0],"ajaxUrl")),i.StoreData(t[0],"ajax-Url",i.GetData(t[0],"ajaxUrl"))),f={},e=0;e<t[0].attributes.length;e++)if(s=t[0].attributes[e].name,o="data-",s.substr(0,o.length)==o){var h=s.substring(o.length),a=i.GetData(t[0],h),v=h.replace(/-([a-z])/g,l);f[v]=a}n.fn.jquery&&n.fn.jquery.substr(0,2)=="1."&&t[0].dataset&&(f=n.extend(!0,{},t[0].dataset,f));u=n.extend(!0,{},i.GetData(t[0]),f);u=i._convertData(u);for(r in u)c.indexOf(r)>-1||(n.isPlainObject(this.options[r])?n.extend(this.options[r],u[r]):this.options[r]=u[r]);return this},r.prototype.get=function(n){return this.options[n]},r.prototype.set=function(n,t){this.options[n]=t},r}),t.define("select2/core",["jquery","./options","./utils","./keys"],function(n,t,i,r){var u=function(n,r){var e,o,f,s,h,c,l;i.GetData(n[0],"select2")!=null&&i.GetData(n[0],"select2").destroy();this.$element=n;this.id=this._generateId(n);r=r||{};this.options=new t(r,n);u.__super__.constructor.call(this);e=n.attr("tabindex")||0;i.StoreData(n[0],"old-tabindex",e);n.attr("tabindex","-1");o=this.options.get("dataAdapter");this.dataAdapter=new o(n,this.options);f=this.render();this._placeContainer(f);s=this.options.get("selectionAdapter");this.selection=new s(n,this.options);this.$selection=this.selection.render();this.selection.position(this.$selection,f);h=this.options.get("dropdownAdapter");this.dropdown=new h(n,this.options);this.$dropdown=this.dropdown.render();this.dropdown.position(this.$dropdown,f);c=this.options.get("resultsAdapter");this.results=new c(n,this.options,this.dataAdapter);this.$results=this.results.render();this.results.position(this.$results,this.$dropdown);l=this;this._bindAdapters();this._registerDomEvents();this._registerDataEvents();this._registerSelectionEvents();this._registerDropdownEvents();this._registerResultsEvents();this._registerEvents();this.dataAdapter.current(function(n){l.trigger("selection:update",{data:n})});n[0].classList.add("select2-hidden-accessible");n.attr("aria-hidden","true");this._syncAttributes();i.StoreData(n[0],"select2",this);n.data("select2",this)};return i.Extend(u,i.Observable),u.prototype._generateId=function(n){var t="";return t=n.attr("id")!=null?n.attr("id"):n.attr("name")!=null?n.attr("name")+"-"+i.generateChars(2):i.generateChars(4),t=t.replace(/(:|\.|\[|\]|,)/g,""),"select2-"+t},u.prototype._placeContainer=function(n){n.insertAfter(this.$element);var t=this._resolveWidth(this.$element,this.options.get("width"));t!=null&&n.css("width",t)},u.prototype._resolveWidth=function(n,t){var u,f,e,o,i,s,h,r,c;if(t=="resolve")return(u=this._resolveWidth(n,"style"),u!=null)?u:this._resolveWidth(n,"element");if(t=="element")return(f=n.outerWidth(!1),f<=0)?"auto":f+"px";if(t=="style"){if(e=n.attr("style"),typeof e!="string")return null;for(o=e.split(";"),i=0,s=o.length;i<s;i=i+1)if(h=o[i].replace(/\s/g,""),r=h.match(/^width:(([-+]?([0-9]*\.)?[0-9]+)(px|em|ex|%|in|cm|mm|pt|pc))/i),r!==null&&r.length>=1)return r[1];return null}return t=="computedstyle"?(c=window.getComputedStyle(n[0]),c.width):t},u.prototype._bindAdapters=function(){this.dataAdapter.bind(this,this.$container);this.selection.bind(this,this.$container);this.dropdown.bind(this,this.$container);this.results.bind(this,this.$container)},u.prototype._registerDomEvents=function(){var n=this;this.$element.on("change.select2",function(){n.dataAdapter.current(function(t){n.trigger("selection:update",{data:t})})});this.$element.on("focus.select2",function(t){n.trigger("focus",t)});this._syncA=i.bind(this._syncAttributes,this);this._syncS=i.bind(this._syncSubtree,this);this._observer=new window.MutationObserver(function(t){n._syncA();n._syncS(t)});this._observer.observe(this.$element[0],{attributes:!0,childList:!0,subtree:!1})},u.prototype._registerDataEvents=function(){var n=this;this.dataAdapter.on("*",function(t,i){n.trigger(t,i)})},u.prototype._registerSelectionEvents=function(){var n=this,t=["toggle","focus"];this.selection.on("toggle",function(){n.toggleDropdown()});this.selection.on("focus",function(t){n.focus(t)});this.selection.on("*",function(i,r){t.indexOf(i)===-1&&n.trigger(i,r)})},u.prototype._registerDropdownEvents=function(){var n=this;this.dropdown.on("*",function(t,i){n.trigger(t,i)})},u.prototype._registerResultsEvents=function(){var n=this;this.results.on("*",function(t,i){n.trigger(t,i)})},u.prototype._registerEvents=function(){var n=this;this.on("open",function(){n.$container[0].classList.add("select2-container--open")});this.on("close",function(){n.$container[0].classList.remove("select2-container--open")});this.on("enable",function(){n.$container[0].classList.remove("select2-container--disabled")});this.on("disable",function(){n.$container[0].classList.add("select2-container--disabled")});this.on("blur",function(){n.$container[0].classList.remove("select2-container--focus")});this.on("query",function(t){n.isOpen()||n.trigger("open",{});this.dataAdapter.query(t,function(i){n.trigger("results:all",{data:i,query:t})})});this.on("query:append",function(t){this.dataAdapter.query(t,function(i){n.trigger("results:append",{data:i,query:t})})});this.on("keypress",function(t){var i=t.which;n.isOpen()?i===r.ESC||i===r.UP&&t.altKey?(n.close(t),t.preventDefault()):i===r.ENTER||i===r.TAB?(n.trigger("results:select",{}),t.preventDefault()):i===r.SPACE&&t.ctrlKey?(n.trigger("results:toggle",{}),t.preventDefault()):i===r.UP?(n.trigger("results:previous",{}),t.preventDefault()):i===r.DOWN&&(n.trigger("results:next",{}),t.preventDefault()):(i===r.ENTER||i===r.SPACE||i===r.DOWN&&t.altKey)&&(n.open(),t.preventDefault())})},u.prototype._syncAttributes=function(){this.options.set("disabled",this.$element.prop("disabled"));this.isDisabled()?(this.isOpen()&&this.close(),this.trigger("disable",{})):this.trigger("enable",{})},u.prototype._isChangeMutation=function(n){var r=this,t,i;if(n.addedNodes&&n.addedNodes.length>0){for(t=0;t<n.addedNodes.length;t++)if(i=n.addedNodes[t],i.selected)return!0}else{if(n.removedNodes&&n.removedNodes.length>0)return!0;if(Array.isArray(n))return n.some(function(n){return r._isChangeMutation(n)})}return!1},u.prototype._syncSubtree=function(n){var t=this._isChangeMutation(n),i=this;t&&this.dataAdapter.current(function(n){i.trigger("selection:update",{data:n})})},u.prototype.trigger=function(n,t){var r=u.__super__.trigger,f={open:"opening",close:"closing",select:"selecting",unselect:"unselecting",clear:"clearing"},e,i;if(t===undefined&&(t={}),n in f&&(e=f[n],i={prevented:!1,name:n,args:t},r.call(this,e,i),i.prevented)){t.prevented=!0;return}r.call(this,n,t)},u.prototype.toggleDropdown=function(){this.isDisabled()||(this.isOpen()?this.close():this.open())},u.prototype.open=function(){this.isOpen()||this.isDisabled()||this.trigger("query",{})},u.prototype.close=function(n){this.isOpen()&&this.trigger("close",{originalEvent:n})},u.prototype.isEnabled=function(){return!this.isDisabled()},u.prototype.isDisabled=function(){return this.options.get("disabled")},u.prototype.isOpen=function(){return this.$container[0].classList.contains("select2-container--open")},u.prototype.hasFocus=function(){return this.$container[0].classList.contains("select2-container--focus")},u.prototype.focus=function(){this.hasFocus()||(this.$container[0].classList.add("select2-container--focus"),this.trigger("focus",{}))},u.prototype.enable=function(n){this.options.get("debug")&&window.console&&console.warn&&console.warn('Select2: The `select2("enable")` method has been deprecated and will be removed in later Select2 versions. Use $element.prop("disabled") instead.');(n==null||n.length===0)&&(n=[!0]);var t=!n[0];this.$element.prop("disabled",t)},u.prototype.data=function(){this.options.get("debug")&&arguments.length>0&&window.console&&console.warn&&console.warn('Select2: Data can no longer be set using `select2("data")`. You should consider setting the value instead using `$element.val()`.');var n=[];return this.dataAdapter.current(function(t){n=t}),n},u.prototype.val=function(n){if(this.options.get("debug")&&window.console&&console.warn&&console.warn('Select2: The `select2("val")` method has been deprecated and will be removed in later Select2 versions. Use $element.val() instead.'),n==null||n.length===0)return this.$element.val();var t=n[0];Array.isArray(t)&&(t=t.map(function(n){return n.toString()}));this.$element.val(t).trigger("input").trigger("change")},u.prototype.destroy=function(){i.RemoveData(this.$container[0]);this.$container.remove();this._observer.disconnect();this._observer=null;this._syncA=null;this._syncS=null;this.$element.off(".select2");this.$element.attr("tabindex",i.GetData(this.$element[0],"old-tabindex"));this.$element[0].classList.remove("select2-hidden-accessible");this.$element.attr("aria-hidden","false");i.RemoveData(this.$element[0]);this.$element.removeData("select2");this.dataAdapter.destroy();this.selection.destroy();this.dropdown.destroy();this.results.destroy();this.dataAdapter=null;this.selection=null;this.dropdown=null;this.results=null},u.prototype.render=function(){var t=n('<span class="select2 select2-container"><span class="selection"><\/span><span class="dropdown-wrapper" aria-hidden="true"><\/span><\/span>');return t.attr("dir",this.options.get("dir")),this.$container=t,this.$container[0].classList.add("select2-container--"+this.options.get("theme")),i.StoreData(t[0],"element",this.$element),t},u}),t.define("jquery-mousewheel",["jquery"],function(n){return n}),t.define("jquery.select2",["jquery","jquery-mousewheel","./select2/core","./select2/defaults","./select2/utils"],function(n,t,i,r,u){if(n.fn.select2==null){var f=["open","close","destroy"];n.fn.select2=function(t){if(t=t||{},typeof t=="object")return this.each(function(){var r=n.extend(!0,{},t),u=new i(n(this),r)}),this;if(typeof t=="string"){var r,e=Array.prototype.slice.call(arguments,1);return(this.each(function(){var n=u.GetData(this,"select2");n==null&&window.console&&console.error&&console.error("The select2('"+t+"') method was called on an element that is not using Select2.");r=n[t].apply(n,e)}),f.indexOf(t)>-1)?this:r}throw new Error("Invalid arguments for Select2: "+t);}}return n.fn.select2.defaults==null&&(n.fn.select2.defaults=r),i}),{define:t.define,require:t.require}}(),i=t.require("jquery.select2");return n.fn.select2.amd=t,i});
/*! Select2 4.1.0-rc.0 | https://github.com/select2/select2/blob/master/LICENSE.md */
!function(){if(jQuery&&jQuery.fn&&jQuery.fn.select2&&jQuery.fn.select2.amd)var n=jQuery.fn.select2.amd;n.define("select2/i18n/zh-CN",[],function(){return{errorLoading:function(){return"无法载入结果。"},inputTooLong:function(n){return"请删除"+(n.input.length-n.maximum)+"个字符"},inputTooShort:function(n){return"请再输入至少"+(n.minimum-n.input.length)+"个字符"},loadingMore:function(){return"载入更多结果…"},maximumSelected:function(n){return"最多只能选择"+n.maximum+"个项目"},noResults:function(){return"未找到结果"},searching:function(){return"搜索中…"},removeAllItems:function(){return"删除所有项目"}}});n.define;n.require}();