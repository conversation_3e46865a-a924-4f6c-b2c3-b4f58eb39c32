(function(n){n.hunterTimePicker=function(t,i){function e(){o();h();c();l()}function o(){for(var t="<p>小时<\/p>",n=0;n<u.hour.length;n++)t+='<li class="Hunter-hour" data-hour="'+u.hour[n]+'"><ul class="Hunter-minute-wrap"><\/ul><div class="Hunter-hour-name">'+u.hour[n]+"<\/div><\/li>";t+='<li class="Hunter-clean"><input type="button" class="Hunter-clean-btn" id="Hunter_clean_btn" value="清 空"><\/li>';r.html(t)}function s(n){for(var f=n.position(),r="<p>分钟<\/p>",e=t.val().split(":")[1],i=0;i<u.minute.length;i++)r+='<li class="Hunter-minute" data-minute="'+u.minute[i]+'">'+u.minute[i]+"<\/li>";n.find(".Hunter-minute-wrap").html(r).css("left","-"+(f.left-37)+"px").show()}function h(){r.on("click",".Hunter-hour",function(t){t.preventDefault();t.stopPropagation();var i=n(this);return r.find(".Hunter-hour").removeClass("active"),r.find(".Hunter-hour-name").removeClass("active"),r.find(".Hunter-minute-wrap").hide().children().remove(),i.addClass("active"),i.find(".Hunter-hour").addClass("active"),s(i),!1})}function c(){r.on("click",".Hunter-minute",function(r){r.preventDefault();r.stopPropagation();var u=n(this),e=n(".Hunter-time-picker .Hunter-hour.active").attr("data-hour"),o=u.data("minute"),s=e+":"+o;return t.val(s),f.remove(),i.callback&&i.callback(t),!1})}function l(){r.on("click","#Hunter_clean_btn",function(n){return n.preventDefault(),n.stopPropagation(),t.val(""),f.remove(),i.callback&&i.callback(t),!1})}var u={hour:["00","01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23"],minute:["00","05","10","15","20","25","30","35","40","45","50","55"]},t=n(t),f=n('<div class="Hunter-time-picker" id="Hunter_time_picker"><div class="Hunter-wrap"><ul class="Hunter-wrap" id="Hunter_time_wrap"><\/ul><\/div><\/div>'),r=n("#Hunter_time_wrap",f);n(document).click(function(){f.remove()});t.click(function(t){t.preventDefault();t.stopPropagation();n(".Hunter-time-picker").remove();var r=n(this),u=r.offset(),i=u.top+r.outerHeight()+15;i=i>450?i-315:i-15;f.css({left:u.left,top:i});e();n("body").append(f);n(".Hunter-time-picker").click(function(n){n.preventDefault();n.stopPropagation()})})};n.fn.extend({hunterTimePicker:function(t){return t=n.extend({},t),this.each(function(){new n.hunterTimePicker(this,t)}),this}})})(jQuery);