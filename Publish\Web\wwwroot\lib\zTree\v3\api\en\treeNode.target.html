<div class="apiDetail">
<div>
	<h2><span>String</span><span class="path">treeNode.</span>target</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.core</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Used to set the target where the node is clicked to open url. It is valid when <span class="highlight_red">[treeNode.url exists]</span></p>
			<p>Default: undefined</p>
		</div>
	</div>
	<h3>String Format</h3>
	<div class="desc">
	<p>As same as &lt;a&gt; tag's 'target' attribute. e.g.  '_blank', '_self' or other window name.</p>
	<p>if this attribute is omitted, zTree default set it to '_blank'</p>
	</div>
	<h3>Exmaples of treeNode</h3>
	<h4>1. Set target is '_blank'</h4>
	<pre xmlns=""><code>var nodes = [
	{ "id":1, "name":"test1", "url":"http://myTest.com", "target":"_blank"},
	......
]</code></pre>
</div>
</div>