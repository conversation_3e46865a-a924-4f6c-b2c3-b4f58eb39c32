(function(n){"use strict";function r(){ys.ajax({url:ctx+"SystemManage/DataDict/GetDataDictListJson",type:"get",success:function(n){if(n.Tag==1)for(var i=0;i<n.Data.length;i++)t[n.Data[i].DictType]=n.Data[i].Detail}})}function u(n){for(var r=[],i=0;i<t[n].length;i++)t[n][i].DictStatus==1&&r.push(t[n][i]);return r}function f(n,i){if(t[n])for(var r=0;r<t[n].length;r++)if(t[n][r].DictKey==i)return t[n][r].ListClass?'<span class="badge badge-'+t[n][r].ListClass+'">'+t[n][r].DictValue+"<\/span>":t[n][r].DictValue;return""}function e(){ys.ajax({url:ctx+"OrganizationManage/User/GetUserAuthorizeJson",type:"get",success:function(n){n.Tag==1&&(i=n.Data)}})}function o(n,t){var u=[],f,r,e,o;return i&&i.IsSystem!=1&&(f=/([a-zA-Z]+)Manage\/(.*)\//,r=f.exec(n),r&&r.length>=3&&(e=r[1],o=r[2],t.forEach(function(n){var r=e.toLowerCase()+":"+o.toLowerCase()+":"+n.toString().replace("btn","").toLowerCase(),t=!1;i.MenuAuthorize.forEach(function(n){if(n.Authorize==r)return t=!0,!1});t||u.push(n)}))),u}var t={},i={};r();e();n.initDataDict=r;n.getDataDict=u;n.getDataDictValue=f;n.getButtonAuthority=o})(window);