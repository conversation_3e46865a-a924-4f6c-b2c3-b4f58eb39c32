using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Unicode;
using Newtonsoft.Json.Serialization;
using Microsoft.Extensions.FileProviders;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.Extensions.Hosting;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Web.Controllers;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Authentication.Cookies;
using Dqy.Syjx.Business.AutoJob;
using Dqy.Syjx.Business.AutoJob.Job;
using NetTaste;

namespace Dqy.Syjx.Web
{
    public class Startup
    {
        public IConfiguration Configuration { get; }
        public IWebHostEnvironment WebHostEnvironment { get; set; }

        public Startup(IConfiguration configuration, IWebHostEnvironment env)
        {
            Configuration = configuration;
            WebHostEnvironment = env;
            GlobalContext.LogWhenStart(env);
            GlobalContext.HostingEnvironment = env;
        }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            if (WebHostEnvironment.IsDevelopment())
            {
                services.AddRazorPages().AddRazorRuntimeCompilation();

                services.AddHttpsRedirection(options =>
                {
                    options.HttpsPort = 5201; // 指定 HTTPS 端口
                });
            }
                   

            services.Configure<CookiePolicyOptions>(options =>
            {
                // This lambda determines whether user consent for non-essential cookies is needed for a given request.
                options.CheckConsentNeeded = context => true;
                options.MinimumSameSitePolicy = SameSiteMode.Lax;
                options.OnAppendCookie = context =>
                {
                    if (context.Context.Request.IsHttps)
                    {
                        context.CookieOptions.Secure = true;
                    }
                };
            });
            services.AddHttpClient();
            services.AddControllers(options =>
            {
                options.Filters.Add<ValidFilterAttribute>();
            });

            services.Configure<FormOptions>(options =>
            {
                options.ValueCountLimit = 5000; // 5000 items max
                options.ValueLengthLimit = 20971520; // 20MB max len form data 1024*1024*20
            });

            services.AddSingleton(HtmlEncoder.Create(UnicodeRanges.All));
            services.AddControllersWithViews(options =>
            {
                options.Filters.Add<GlobalExceptionFilter>();
                options.ModelMetadataDetailsProviders.Add(new ModelBindingMetadataProvider());
            }).AddNewtonsoftJson(options =>
            {
                // 返回数据首字母不小写，CamelCasePropertyNamesContractResolver是小写
                options.SerializerSettings.ContractResolver = new DefaultContractResolver();
            });

            services.AddMemoryCache();
            services.AddSession();
            // 注册 NetHelper，它依赖于 IHttpContextAccessor
            services.AddScoped<NetHelper>();
            services.AddHttpContextAccessor();

            services.AddOptions();

            services.AddDataProtection().PersistKeysToFileSystem(new DirectoryInfo(GlobalContext.HostingEnvironment.ContentRootPath + Path.DirectorySeparatorChar + "DataProtection"));

            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);  // 注册Encoding

            GlobalContext.SystemConfig = Configuration.GetSection("SystemConfig").Get<SystemConfig>();
            GlobalContext.Services = services;
            GlobalContext.Configuration = Configuration;
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            app.Use((context, next) =>
            {
                context.Response.Headers.Remove("X-Powered-By");
                context.Response.Headers.Add("X-Powered-By", "JunFei");
                context.Response.Headers.Add("Access-Control-Allow-Credentials", "true");
                context.Response.Headers.Add("X-Content-Type-Options", "nosniff");
                context.Response.Headers.Add("X-XSS-Protection", "1");
                context.Response.Headers.Add("X-Download-Options", "SAMEORIGIN");
                context.Response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");
                context.Response.Headers.Add("Content-Security-Policy", "frame-ancestors 'self' webchat-bj.clink.cn");
                context.Response.Headers.Add("Referrer-Policy", "no-referrer");
                context.Response.Headers.Add("X-Frame-Options", "SAMEORIGIN");
                context.Response.Headers.Add("X-Permitted-Cross-Domain-Policies", "same-origin");
                context.Response.Headers.Add("Server", "jf");
                return next();
            });

            if (!string.IsNullOrEmpty(GlobalContext.SystemConfig.VirtualDirectory))
            {
                app.UsePathBase(new PathString(GlobalContext.SystemConfig.VirtualDirectory)); // 让 Pathbase 中间件成为第一个处理请求的中间件， 才能正确的模拟虚拟路径
            }
            if (WebHostEnvironment.IsDevelopment())
            {
                GlobalContext.SystemConfig.Debug = true;
                app.UseDeveloperExceptionPage();
            }
            else
            {
                GlobalContext.SystemConfig.Debug = false;
                app.UseExceptionHandler("/Home/Error");
                // 启用 HTTPS 重定向 (仅在生产环境)
                if(GlobalContext.SystemConfig.UseHttps)
                    app.UseHttpsRedirection();
            }

            string resource = Path.Combine(env.ContentRootPath, "Resource");
            FileHelper.CreateDirectory(resource);

            app.UseStaticFiles(new StaticFileOptions
            {
                OnPrepareResponse = GlobalContext.SetCacheControl
            });
            app.UseStaticFiles(new StaticFileOptions
            {
                RequestPath = "/Resource",
                FileProvider = new PhysicalFileProvider(resource),
                OnPrepareResponse = GlobalContext.SetCacheControl
            });
            //app.UseMiddleware<RequestBodyMiddleware>()
            //    .UseMiddleware<RequestLogMiddleware>();
            //app.UseCookiePolicy(new CookiePolicyOptions() //设置cookie
            //{
            //    Secure = CookieSecurePolicy.None,
            //    MinimumSameSitePolicy = SameSiteMode.None
            //});
            //app.UseCookiePolicy();
            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();
            app.UseSession();
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllerRoute("areas", "{area:exists}/{controller=Home}/{action=Index}/{id?}");

#if WUZHONG
                endpoints.MapControllerRoute("default", "{controller=AccountThird}/{action=Wzqpt}/{id?}");
#elif HAIZHOU
                endpoints.MapControllerRoute("default", "{controller=AccountThird}/{action=HzLogin}/{id?}");
#else

                endpoints.MapControllerRoute("default", "{controller=Home}/{action=Index}/{id?}");
#endif

                //endpoints.MapControllerRoute("default", "{controller=AccountThird}/{action=HzLogin}/{id?}"); //海州区登录页面跳转
                //endpoints.MapControllerRoute("default", "{controller=AccountThird}/{action=Wzqpt}/{id?}"); //吴中区首页跳转
            });

            GlobalContext.ServiceProvider = app.ApplicationServices;

            //if (!GlobalContext.SystemConfig.Debug)
            {
                new JobCenter().Start(); // 定时任务
            }
            //可临时对历史数据进行修复，PatrolClassJobDo 中指定日期
            //new PatrolClassJobDo().Start();
        }
    }
}