{"version": 2, "dgSpecHash": "Xuagy2ZX0rU=", "success": true, "projectFilePath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Web\\Dqy.Syjx.Web\\Dqy.Syjx.Web.csproj", "expectedPackageFiles": ["C:\\NuGetPackages\\automapper\\10.1.1\\automapper.10.1.1.nupkg.sha512", "C:\\NuGetPackages\\bouncycastle.cryptography\\2.2.1\\bouncycastle.cryptography.2.2.1.nupkg.sha512", "C:\\NuGetPackages\\buildbundlerminifier\\3.2.449\\buildbundlerminifier.3.2.449.nupkg.sha512", "C:\\NuGetPackages\\castle.core\\4.4.1\\castle.core.4.4.1.nupkg.sha512", "C:\\NuGetPackages\\castle.core.asyncinterceptor\\1.7.0\\castle.core.asyncinterceptor.1.7.0.nupkg.sha512", "C:\\NuGetPackages\\dmdbms.dmprovider\\1.1.0.16649\\dmdbms.dmprovider.1.1.0.16649.nupkg.sha512", "C:\\NuGetPackages\\dmdbms.microsoft.entityframeworkcore.dm\\6.0.16.16649\\dmdbms.microsoft.entityframeworkcore.dm.6.0.16.16649.nupkg.sha512", "C:\\NuGetPackages\\dynamitey\\2.0.10.189\\dynamitey.2.0.10.189.nupkg.sha512", "C:\\NuGetPackages\\efcore.sharding\\5.0.16\\efcore.sharding.5.0.16.nupkg.sha512", "C:\\NuGetPackages\\efcore.sharding.sqlserver\\5.0.16\\efcore.sharding.sqlserver.5.0.16.nupkg.sha512", "C:\\NuGetPackages\\enums.net\\4.0.1\\enums.net.4.0.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer\\2.14.1\\humanizer.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core\\2.14.1\\humanizer.core.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.af\\2.14.1\\humanizer.core.af.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.ar\\2.14.1\\humanizer.core.ar.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.az\\2.14.1\\humanizer.core.az.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.bg\\2.14.1\\humanizer.core.bg.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.bn-bd\\2.14.1\\humanizer.core.bn-bd.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.cs\\2.14.1\\humanizer.core.cs.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.da\\2.14.1\\humanizer.core.da.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.de\\2.14.1\\humanizer.core.de.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.el\\2.14.1\\humanizer.core.el.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.es\\2.14.1\\humanizer.core.es.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.fa\\2.14.1\\humanizer.core.fa.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.fi-fi\\2.14.1\\humanizer.core.fi-fi.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.fr\\2.14.1\\humanizer.core.fr.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.fr-be\\2.14.1\\humanizer.core.fr-be.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.he\\2.14.1\\humanizer.core.he.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.hr\\2.14.1\\humanizer.core.hr.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.hu\\2.14.1\\humanizer.core.hu.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.hy\\2.14.1\\humanizer.core.hy.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.id\\2.14.1\\humanizer.core.id.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.is\\2.14.1\\humanizer.core.is.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.it\\2.14.1\\humanizer.core.it.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.ja\\2.14.1\\humanizer.core.ja.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.ko-kr\\2.14.1\\humanizer.core.ko-kr.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.ku\\2.14.1\\humanizer.core.ku.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.lv\\2.14.1\\humanizer.core.lv.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.ms-my\\2.14.1\\humanizer.core.ms-my.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.mt\\2.14.1\\humanizer.core.mt.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.nb\\2.14.1\\humanizer.core.nb.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.nb-no\\2.14.1\\humanizer.core.nb-no.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.nl\\2.14.1\\humanizer.core.nl.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.pl\\2.14.1\\humanizer.core.pl.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.pt\\2.14.1\\humanizer.core.pt.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.ro\\2.14.1\\humanizer.core.ro.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.ru\\2.14.1\\humanizer.core.ru.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.sk\\2.14.1\\humanizer.core.sk.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.sl\\2.14.1\\humanizer.core.sl.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.sr\\2.14.1\\humanizer.core.sr.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.sr-latn\\2.14.1\\humanizer.core.sr-latn.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.sv\\2.14.1\\humanizer.core.sv.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.th-th\\2.14.1\\humanizer.core.th-th.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.tr\\2.14.1\\humanizer.core.tr.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.uk\\2.14.1\\humanizer.core.uk.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.uz-cyrl-uz\\2.14.1\\humanizer.core.uz-cyrl-uz.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.uz-latn-uz\\2.14.1\\humanizer.core.uz-latn-uz.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.vi\\2.14.1\\humanizer.core.vi.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.zh-cn\\2.14.1\\humanizer.core.zh-cn.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.zh-hans\\2.14.1\\humanizer.core.zh-hans.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\humanizer.core.zh-hant\\2.14.1\\humanizer.core.zh-hant.2.14.1.nupkg.sha512", "C:\\NuGetPackages\\linqkit.microsoft.entityframeworkcore\\5.0.24\\linqkit.microsoft.entityframeworkcore.5.0.24.nupkg.sha512", "C:\\NuGetPackages\\mathnet.numerics.signed\\4.15.0\\mathnet.numerics.signed.4.15.0.nupkg.sha512", "C:\\NuGetPackages\\messagepack\\2.1.152\\messagepack.2.1.152.nupkg.sha512", "C:\\NuGetPackages\\messagepack.annotations\\2.1.152\\messagepack.annotations.2.1.152.nupkg.sha512", "C:\\NuGetPackages\\messagepackanalyzer\\2.1.152\\messagepackanalyzer.2.1.152.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.authentication.jwtbearer\\6.0.11\\microsoft.aspnetcore.authentication.jwtbearer.6.0.11.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.jsonpatch\\6.0.19\\microsoft.aspnetcore.jsonpatch.6.0.19.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.mvc.newtonsoftjson\\6.0.19\\microsoft.aspnetcore.mvc.newtonsoftjson.6.0.19.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.mvc.razor.extensions\\6.0.19\\microsoft.aspnetcore.mvc.razor.extensions.6.0.19.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.mvc.razor.runtimecompilation\\6.0.19\\microsoft.aspnetcore.mvc.razor.runtimecompilation.6.0.19.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.razor.language\\6.0.19\\microsoft.aspnetcore.razor.language.6.0.19.nupkg.sha512", "C:\\NuGetPackages\\microsoft.bcl.asyncinterfaces\\5.0.0\\microsoft.bcl.asyncinterfaces.5.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.build\\17.3.2\\microsoft.build.17.3.2.nupkg.sha512", "C:\\NuGetPackages\\microsoft.build.framework\\17.3.2\\microsoft.build.framework.17.3.2.nupkg.sha512", "C:\\NuGetPackages\\microsoft.codeanalysis.analyzers\\3.3.2\\microsoft.codeanalysis.analyzers.3.3.2.nupkg.sha512", "C:\\NuGetPackages\\microsoft.codeanalysis.analyzerutilities\\3.3.0\\microsoft.codeanalysis.analyzerutilities.3.3.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.codeanalysis.common\\4.0.0\\microsoft.codeanalysis.common.4.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.codeanalysis.csharp\\4.0.0\\microsoft.codeanalysis.csharp.4.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.codeanalysis.csharp.features\\4.0.0\\microsoft.codeanalysis.csharp.features.4.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.codeanalysis.csharp.workspaces\\4.0.0\\microsoft.codeanalysis.csharp.workspaces.4.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.codeanalysis.features\\4.0.0\\microsoft.codeanalysis.features.4.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.codeanalysis.razor\\6.0.19\\microsoft.codeanalysis.razor.6.0.19.nupkg.sha512", "C:\\NuGetPackages\\microsoft.codeanalysis.scripting.common\\4.0.0\\microsoft.codeanalysis.scripting.common.4.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.codeanalysis.workspaces.common\\4.0.0\\microsoft.codeanalysis.workspaces.common.4.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.data.sqlclient\\2.1.4\\microsoft.data.sqlclient.2.1.4.nupkg.sha512", "C:\\NuGetPackages\\microsoft.data.sqlclient.sni.runtime\\2.1.1\\microsoft.data.sqlclient.sni.runtime.2.1.1.nupkg.sha512", "C:\\NuGetPackages\\microsoft.diasymreader\\1.3.0\\microsoft.diasymreader.1.3.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.dotnet.scaffolding.shared\\6.0.16\\microsoft.dotnet.scaffolding.shared.6.0.16.nupkg.sha512", "C:\\NuGetPackages\\microsoft.entityframeworkcore\\6.0.19\\microsoft.entityframeworkcore.6.0.19.nupkg.sha512", "C:\\NuGetPackages\\microsoft.entityframeworkcore.abstractions\\6.0.19\\microsoft.entityframeworkcore.abstractions.6.0.19.nupkg.sha512", "C:\\NuGetPackages\\microsoft.entityframeworkcore.analyzers\\6.0.19\\microsoft.entityframeworkcore.analyzers.6.0.19.nupkg.sha512", "C:\\NuGetPackages\\microsoft.entityframeworkcore.relational\\6.0.19\\microsoft.entityframeworkcore.relational.6.0.19.nupkg.sha512", "C:\\NuGetPackages\\microsoft.entityframeworkcore.sqlserver\\6.0.19\\microsoft.entityframeworkcore.sqlserver.6.0.19.nupkg.sha512", "C:\\NuGetPackages\\microsoft.entityframeworkcore.sqlserver.nettopologysuite\\5.0.5\\microsoft.entityframeworkcore.sqlserver.nettopologysuite.5.0.5.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.caching.abstractions\\7.0.0\\microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.caching.memory\\7.0.0\\microsoft.extensions.caching.memory.7.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.configuration.abstractions\\6.0.0\\microsoft.extensions.configuration.abstractions.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.configuration.binder\\6.0.0\\microsoft.extensions.configuration.binder.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.dependencyinjection\\6.0.1\\microsoft.extensions.dependencyinjection.6.0.1.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.dependencyinjection.abstractions\\7.0.0\\microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.dependencymodel\\6.0.0\\microsoft.extensions.dependencymodel.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.fileproviders.abstractions\\5.0.0\\microsoft.extensions.fileproviders.abstractions.5.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.hosting.abstractions\\5.0.0\\microsoft.extensions.hosting.abstractions.5.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.http\\6.0.0\\microsoft.extensions.http.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.http.polly\\6.0.0\\microsoft.extensions.http.polly.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.logging\\6.0.0\\microsoft.extensions.logging.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.logging.abstractions\\7.0.0\\microsoft.extensions.logging.abstractions.7.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.options\\7.0.0\\microsoft.extensions.options.7.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.options.configurationextensions\\6.0.0\\microsoft.extensions.options.configurationextensions.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.primitives\\7.0.0\\microsoft.extensions.primitives.7.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.identity.client\\4.21.1\\microsoft.identity.client.4.21.1.nupkg.sha512", "C:\\NuGetPackages\\microsoft.identitymodel.jsonwebtokens\\6.17.0\\microsoft.identitymodel.jsonwebtokens.6.17.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.identitymodel.logging\\6.17.0\\microsoft.identitymodel.logging.6.17.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.identitymodel.protocols\\6.10.0\\microsoft.identitymodel.protocols.6.10.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.identitymodel.protocols.openidconnect\\6.10.0\\microsoft.identitymodel.protocols.openidconnect.6.10.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.identitymodel.tokens\\6.17.0\\microsoft.identitymodel.tokens.6.17.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.io.recyclablememorystream\\2.3.2\\microsoft.io.recyclablememorystream.2.3.2.nupkg.sha512", "C:\\NuGetPackages\\microsoft.net.http.headers\\2.2.8\\microsoft.net.http.headers.2.2.8.nupkg.sha512", "C:\\NuGetPackages\\microsoft.net.stringtools\\17.3.2\\microsoft.net.stringtools.17.3.2.nupkg.sha512", "C:\\NuGetPackages\\microsoft.netcore.platforms\\1.1.1\\microsoft.netcore.platforms.1.1.1.nupkg.sha512", "C:\\NuGetPackages\\microsoft.netcore.targets\\1.1.3\\microsoft.netcore.targets.1.1.3.nupkg.sha512", "C:\\NuGetPackages\\microsoft.visualstudio.debugger.contracts\\17.2.0\\microsoft.visualstudio.debugger.contracts.17.2.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.visualstudio.web.codegeneration\\6.0.16\\microsoft.visualstudio.web.codegeneration.6.0.16.nupkg.sha512", "C:\\NuGetPackages\\microsoft.visualstudio.web.codegeneration.core\\6.0.16\\microsoft.visualstudio.web.codegeneration.core.6.0.16.nupkg.sha512", "C:\\NuGetPackages\\microsoft.visualstudio.web.codegeneration.design\\6.0.16\\microsoft.visualstudio.web.codegeneration.design.6.0.16.nupkg.sha512", "C:\\NuGetPackages\\microsoft.visualstudio.web.codegeneration.entityframeworkcore\\6.0.16\\microsoft.visualstudio.web.codegeneration.entityframeworkcore.6.0.16.nupkg.sha512", "C:\\NuGetPackages\\microsoft.visualstudio.web.codegeneration.templating\\6.0.16\\microsoft.visualstudio.web.codegeneration.templating.6.0.16.nupkg.sha512", "C:\\NuGetPackages\\microsoft.visualstudio.web.codegeneration.utils\\6.0.16\\microsoft.visualstudio.web.codegeneration.utils.6.0.16.nupkg.sha512", "C:\\NuGetPackages\\microsoft.visualstudio.web.codegenerators.mvc\\6.0.16\\microsoft.visualstudio.web.codegenerators.mvc.6.0.16.nupkg.sha512", "C:\\NuGetPackages\\microsoft.win32.primitives\\4.3.0\\microsoft.win32.primitives.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.win32.registry\\4.7.0\\microsoft.win32.registry.4.7.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.win32.systemevents\\7.0.0\\microsoft.win32.systemevents.7.0.0.nupkg.sha512", "C:\\NuGetPackages\\mysqlconnector\\2.2.5\\mysqlconnector.2.2.5.nupkg.sha512", "C:\\NuGetPackages\\namotion.reflection\\1.0.19\\namotion.reflection.1.0.19.nupkg.sha512", "C:\\NuGetPackages\\netdevpack.security.jwtextensions\\6.0.2\\netdevpack.security.jwtextensions.6.0.2.nupkg.sha512", "C:\\NuGetPackages\\netstandard.library\\1.6.1\\netstandard.library.1.6.1.nupkg.sha512", "C:\\NuGetPackages\\nettopologysuite\\2.1.0\\nettopologysuite.2.1.0.nupkg.sha512", "C:\\NuGetPackages\\nettopologysuite.io.sqlserverbytes\\2.0.0\\nettopologysuite.io.sqlserverbytes.2.0.0.nupkg.sha512", "C:\\NuGetPackages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\NuGetPackages\\newtonsoft.json.bson\\1.0.2\\newtonsoft.json.bson.1.0.2.nupkg.sha512", "C:\\NuGetPackages\\nlog\\4.7.11\\nlog.4.7.11.nupkg.sha512", "C:\\NuGetPackages\\nlog.extensions.logging\\1.7.4\\nlog.extensions.logging.1.7.4.nupkg.sha512", "C:\\NuGetPackages\\nlog.web.aspnetcore\\4.14.0\\nlog.web.aspnetcore.4.14.0.nupkg.sha512", "C:\\NuGetPackages\\npoi\\2.6.2\\npoi.2.6.2.nupkg.sha512", "C:\\NuGetPackages\\nuget.common\\6.6.1\\nuget.common.6.6.1.nupkg.sha512", "C:\\NuGetPackages\\nuget.configuration\\6.6.1\\nuget.configuration.6.6.1.nupkg.sha512", "C:\\NuGetPackages\\nuget.dependencyresolver.core\\6.6.1\\nuget.dependencyresolver.core.6.6.1.nupkg.sha512", "C:\\NuGetPackages\\nuget.frameworks\\6.6.1\\nuget.frameworks.6.6.1.nupkg.sha512", "C:\\NuGetPackages\\nuget.librarymodel\\6.6.1\\nuget.librarymodel.6.6.1.nupkg.sha512", "C:\\NuGetPackages\\nuget.packaging\\6.6.1\\nuget.packaging.6.6.1.nupkg.sha512", "C:\\NuGetPackages\\nuget.projectmodel\\6.6.1\\nuget.projectmodel.6.6.1.nupkg.sha512", "C:\\NuGetPackages\\nuget.protocol\\6.6.1\\nuget.protocol.6.6.1.nupkg.sha512", "C:\\NuGetPackages\\nuget.versioning\\6.6.1\\nuget.versioning.6.6.1.nupkg.sha512", "C:\\NuGetPackages\\oracle.manageddataaccess.core\\3.21.100\\oracle.manageddataaccess.core.3.21.100.nupkg.sha512", "C:\\NuGetPackages\\pipelines.sockets.unofficial\\2.2.0\\pipelines.sockets.unofficial.2.2.0.nupkg.sha512", "C:\\NuGetPackages\\polly\\7.2.2\\polly.7.2.2.nupkg.sha512", "C:\\NuGetPackages\\polly.extensions.http\\3.0.0\\polly.extensions.http.3.0.0.nupkg.sha512", "C:\\NuGetPackages\\pomelo.entityframeworkcore.mysql\\6.0.2\\pomelo.entityframeworkcore.mysql.6.0.2.nupkg.sha512", "C:\\NuGetPackages\\qrcoder\\1.4.1\\qrcoder.1.4.1.nupkg.sha512", "C:\\NuGetPackages\\quartz\\3.6.3\\quartz.3.6.3.nupkg.sha512", "C:\\NuGetPackages\\refit\\8.0.0\\refit.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\refit.newtonsoft.json\\8.0.0\\refit.newtonsoft.json.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\NuGetPackages\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\NuGetPackages\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\NuGetPackages\\runtime.native.system\\4.3.0\\runtime.native.system.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\runtime.native.system.io.compression\\4.3.0\\runtime.native.system.io.compression.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\runtime.native.system.net.http\\4.3.0\\runtime.native.system.net.http.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\NuGetPackages\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\NuGetPackages\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\NuGetPackages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\NuGetPackages\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\NuGetPackages\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\NuGetPackages\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\NuGetPackages\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\NuGetPackages\\senparc.co2net\\2.1.7.3\\senparc.co2net.2.1.7.3.nupkg.sha512", "C:\\NuGetPackages\\senparc.co2net.apm\\*******\\senparc.co2net.apm.*******.nupkg.sha512", "C:\\NuGetPackages\\senparc.neuchar\\*******\\senparc.neuchar.*******.nupkg.sha512", "C:\\NuGetPackages\\senparc.neuchar.app\\*******\\senparc.neuchar.app.*******.nupkg.sha512", "C:\\NuGetPackages\\senparc.weixin\\********\\senparc.weixin.********.nupkg.sha512", "C:\\NuGetPackages\\senparc.weixin.mp\\**********\\senparc.weixin.mp.**********.nupkg.sha512", "C:\\NuGetPackages\\senparc.weixin.wxopen\\3.15.13\\senparc.weixin.wxopen.3.15.13.nupkg.sha512", "C:\\NuGetPackages\\sharpziplib\\1.3.3\\sharpziplib.1.3.3.nupkg.sha512", "C:\\NuGetPackages\\sixlabors.fonts\\1.0.0\\sixlabors.fonts.1.0.0.nupkg.sha512", "C:\\NuGetPackages\\sixlabors.imagesharp\\2.1.4\\sixlabors.imagesharp.2.1.4.nupkg.sha512", "C:\\NuGetPackages\\skiasharp\\2.88.8\\skiasharp.2.88.8.nupkg.sha512", "C:\\NuGetPackages\\skiasharp.nativeassets.macos\\2.88.8\\skiasharp.nativeassets.macos.2.88.8.nupkg.sha512", "C:\\NuGetPackages\\skiasharp.nativeassets.win32\\2.88.8\\skiasharp.nativeassets.win32.2.88.8.nupkg.sha512", "C:\\NuGetPackages\\skiasharp.qrcode\\0.6.0\\skiasharp.qrcode.0.6.0.nupkg.sha512", "C:\\NuGetPackages\\stackexchange.redis\\2.2.50\\stackexchange.redis.2.2.50.nupkg.sha512", "C:\\NuGetPackages\\system.appcontext\\4.3.0\\system.appcontext.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "C:\\NuGetPackages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.collections.concurrent\\4.3.0\\system.collections.concurrent.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.collections.immutable\\6.0.0\\system.collections.immutable.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.collections.nongeneric\\4.3.0\\system.collections.nongeneric.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.collections.specialized\\4.3.0\\system.collections.specialized.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.componentmodel\\4.3.0\\system.componentmodel.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.componentmodel.primitives\\4.3.0\\system.componentmodel.primitives.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.componentmodel.typeconverter\\4.3.0\\system.componentmodel.typeconverter.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.composition\\1.0.31\\system.composition.1.0.31.nupkg.sha512", "C:\\NuGetPackages\\system.composition.attributedmodel\\1.0.31\\system.composition.attributedmodel.1.0.31.nupkg.sha512", "C:\\NuGetPackages\\system.composition.convention\\1.0.31\\system.composition.convention.1.0.31.nupkg.sha512", "C:\\NuGetPackages\\system.composition.hosting\\1.0.31\\system.composition.hosting.1.0.31.nupkg.sha512", "C:\\NuGetPackages\\system.composition.runtime\\1.0.31\\system.composition.runtime.1.0.31.nupkg.sha512", "C:\\NuGetPackages\\system.composition.typedparts\\1.0.31\\system.composition.typedparts.1.0.31.nupkg.sha512", "C:\\NuGetPackages\\system.configuration.configurationmanager\\6.0.1\\system.configuration.configurationmanager.6.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.console\\4.3.0\\system.console.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.diagnostics.diagnosticsource\\6.0.1\\system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.diagnostics.performancecounter\\6.0.1\\system.diagnostics.performancecounter.6.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.diagnostics.tools\\4.3.0\\system.diagnostics.tools.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.diagnostics.tracesource\\4.3.0\\system.diagnostics.tracesource.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.diagnostics.tracing\\4.3.0\\system.diagnostics.tracing.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.directoryservices\\6.0.1\\system.directoryservices.6.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.directoryservices.protocols\\6.0.1\\system.directoryservices.protocols.6.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.drawing.common\\7.0.0\\system.drawing.common.7.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.dynamic.runtime\\4.3.0\\system.dynamic.runtime.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.formats.asn1\\6.0.0\\system.formats.asn1.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.globalization.calendars\\4.3.0\\system.globalization.calendars.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.globalization.extensions\\4.3.0\\system.globalization.extensions.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.identitymodel.tokens.jwt\\6.17.0\\system.identitymodel.tokens.jwt.6.17.0.nupkg.sha512", "C:\\NuGetPackages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.io.compression\\4.3.0\\system.io.compression.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.io.compression.zipfile\\4.3.0\\system.io.compression.zipfile.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.io.filesystem\\4.3.0\\system.io.filesystem.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.io.filesystem.primitives\\4.3.0\\system.io.filesystem.primitives.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.io.pipelines\\5.0.1\\system.io.pipelines.5.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.linq.dynamic.core\\1.3.5\\system.linq.dynamic.core.1.3.5.nupkg.sha512", "C:\\NuGetPackages\\system.linq.expressions\\4.3.0\\system.linq.expressions.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.memory\\4.5.5\\system.memory.4.5.5.nupkg.sha512", "C:\\NuGetPackages\\system.net.http\\4.3.4\\system.net.http.4.3.4.nupkg.sha512", "C:\\NuGetPackages\\system.net.primitives\\4.3.0\\system.net.primitives.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.net.sockets\\4.3.0\\system.net.sockets.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\NuGetPackages\\system.objectmodel\\4.3.0\\system.objectmodel.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.private.servicemodel\\4.8.1\\system.private.servicemodel.4.8.1.nupkg.sha512", "C:\\NuGetPackages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.reflection.dispatchproxy\\4.7.1\\system.reflection.dispatchproxy.4.7.1.nupkg.sha512", "C:\\NuGetPackages\\system.reflection.emit\\4.7.0\\system.reflection.emit.4.7.0.nupkg.sha512", "C:\\NuGetPackages\\system.reflection.emit.ilgeneration\\4.3.0\\system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.reflection.emit.lightweight\\4.6.0\\system.reflection.emit.lightweight.4.6.0.nupkg.sha512", "C:\\NuGetPackages\\system.reflection.extensions\\4.3.0\\system.reflection.extensions.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.reflection.metadata\\6.0.0\\system.reflection.metadata.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.reflection.metadataloadcontext\\6.0.0\\system.reflection.metadataloadcontext.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.reflection.typeextensions\\4.3.0\\system.reflection.typeextensions.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.runtime\\4.3.1\\system.runtime.4.3.1.nupkg.sha512", "C:\\NuGetPackages\\system.runtime.caching\\4.7.0\\system.runtime.caching.4.7.0.nupkg.sha512", "C:\\NuGetPackages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.runtime.interopservices.runtimeinformation\\4.3.0\\system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.runtime.numerics\\4.3.0\\system.runtime.numerics.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.accesscontrol\\6.0.0\\system.security.accesscontrol.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.cryptography.algorithms\\4.3.0\\system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.cryptography.cng\\5.0.0\\system.security.cryptography.cng.5.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.cryptography.csp\\4.3.0\\system.security.cryptography.csp.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.cryptography.encoding\\4.3.0\\system.security.cryptography.encoding.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.cryptography.openssl\\4.3.0\\system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.cryptography.pkcs\\6.0.4\\system.security.cryptography.pkcs.6.0.4.nupkg.sha512", "C:\\NuGetPackages\\system.security.cryptography.primitives\\4.3.0\\system.security.cryptography.primitives.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.cryptography.protecteddata\\6.0.0\\system.security.cryptography.protecteddata.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.cryptography.x509certificates\\4.3.0\\system.security.cryptography.x509certificates.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.cryptography.xml\\6.0.1\\system.security.cryptography.xml.6.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.security.permissions\\6.0.0\\system.security.permissions.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.servicemodel.primitives\\4.8.1\\system.servicemodel.primitives.4.8.1.nupkg.sha512", "C:\\NuGetPackages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.text.encoding.codepages\\6.0.0\\system.text.encoding.codepages.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.text.encoding.extensions\\4.3.0\\system.text.encoding.extensions.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.text.encodings.web\\6.0.0\\system.text.encodings.web.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.text.json\\6.0.3\\system.text.json.6.0.3.nupkg.sha512", "C:\\NuGetPackages\\system.text.regularexpressions\\4.3.1\\system.text.regularexpressions.4.3.1.nupkg.sha512", "C:\\NuGetPackages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.threading.tasks.dataflow\\6.0.0\\system.threading.tasks.dataflow.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "C:\\NuGetPackages\\system.threading.thread\\4.3.0\\system.threading.thread.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.threading.timer\\4.3.0\\system.threading.timer.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.windows.extensions\\6.0.0\\system.windows.extensions.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.xml.readerwriter\\4.3.0\\system.xml.readerwriter.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.xml.xdocument\\4.3.0\\system.xml.xdocument.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.xml.xmldocument\\4.3.0\\system.xml.xmldocument.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.xml.xmlserializer\\4.3.0\\system.xml.xmlserializer.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.xml.xpath\\4.3.0\\system.xml.xpath.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.xml.xpath.xmldocument\\4.3.0\\system.xml.xpath.xmldocument.4.3.0.nupkg.sha512"], "logs": []}