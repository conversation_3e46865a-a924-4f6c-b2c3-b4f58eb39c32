<div class="apiDetail">
<div>
	<h2><span>Function(event, treeId, treeNode)</span><span class="path">setting.callback.</span>onRemove</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.exedit</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Callback for remove node.</p>
			<p class="highlight_red">If you set 'setting.callback.beforeRemove',and return false, zTree will not remove node, and will not trigger the 'onRemove' callback.</p>
			<p>Default: null</p>
		</div>
	</div>
	<h3>Function Parameter Descriptions</h3>
	<div class="desc">
	<h4><b>event</b><span>js event Object</span></h4>
	<p>event Object</p>
	<h4 class="topLine"><b>treeId</b><span>String</span></h4>
	<p>zTree unique identifier: <b class="highlight_red">treeId</b>.</p>
	<h4 class="topLine"><b>treeNode</b><span>JSON</span></h4>
	<p>JSON data object of the node which was removed.</p>
	</div>
	<h3>Examples of setting & function</h3>
	<h4>1. When remove node, alert info about 'tId' and 'name'.</h4>
	<pre xmlns=""><code>function myOnRemove(event, treeId, treeNode) {
	alert(treeNode.tId + ", " + treeNode.name);
}
var setting = {
	callback: {
		onRemove: myOnRemove
	}
};
......</code></pre>
</div>
</div>