.mask-layer {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    /*z-index: 2020;*/
    z-index: 9999999;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.mask-layer > div {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.mask-layer-black {
    width: 100%;
    height: 100%;
    background: #000;
    opacity: .85;
    position: absolute;
    top: 0;
    left: 0;
}
.mask-layer-container {
    width: 95%;
    height: 90%;
    background: #fafafa;
    position: absolute;
    margin: 0 auto;
    z-index: 2030;
    padding: 0 10px;
    border-radius: 10px;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

.mask-layer-container-operate {
    width: 100%;
    height: 50px;
    padding: 10px 0;
    text-align: center;
    border-bottom: solid 1px #ddd;
}

.mask-layer-imgbox {
    width: 100%;
    height: 88%;
    overflow: hidden;
    position: relative;
    margin-top: 10px;
}

.mask-layer-imgbox > p {
    position: absolute;
    cursor: move;
    transform-origin: center;
    width: 90%;
    height: 90%;
    padding: 0;
    -webkit-margin-before: 0;
    -webkit-margin-after: 0;
    cursor: move;
    left: 5%;
    top: 5%;
    display:flex;
    justify-content:center;
    align-items:center;
}
    .mask-layer-imgbox.auto-img-center p img {
        display: inline-block;
        vertical-align: middle;
        cursor: move;
        width: auto !important;
        height: auto !important;
        margin-top:0 !important;
        max-width: 100%;
    }


/*按钮样式*/
.btn-default-styles {
    display: inline-block;
    padding: 5px 10px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.42857143;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-image: none;
    background: #8C85E6;
    color: #fff;
    border: solid 1px #8C85E6;
    border-radius: 4px;
}
.btn-default-styles:focus {
    outline: none;
}
.btn-default-styles:hover {
    background: #8078e3;
    animation: anniu 1s infinite;
}
.btn-default-styles:active {
    box-shadow: 0 2px 3px rgba(0, 0, 0, .2) inset;
}
img {
    cursor: pointer;

}

.wrapper {
    width: 100%;
    min-width: 300px;
    padding: 0 10px;
    margin-top: 10px;
}

.classDetail {
    box-shadow: 0px 0px 10px #ceccca;
    margin: 2px 0;
    border-radius: 5px;
    overflow: hidden;
}

    .classDetail .formGroup {
        font-size: 14px;
        color: #000000;
        padding: 11px 15px;
        border-bottom: 1px solid #c8c7cc;
    }

    .classDetail .formGroupHead {
        font-weight: bold;
        background-color: #468CEB;
        color: #FFFFFF;
    }

.classPicture {
    padding: 0;
}

    .classPicture img {
        width: 60px !important;
        height: 60px !important;
    }
@media (max-width: 768px) {

    .mask-layer-container {
        width: 100%;
    }

    .mask-layer-imgbox > p {
        width: 100%;
    }

        .mask-layer-imgbox > p img {
            width: 100%;
            margin-top: 20px !important;
        }

    .btn-default-styles {
        padding: 0;
        /*font-size:12px;*/
    }

    .mask-layer-container-operate {
        height: 43px;
    }
}