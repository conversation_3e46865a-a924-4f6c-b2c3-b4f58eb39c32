# 定时任务时区修复说明

## 问题描述

您的 cron 表达式 `0 5 22 * * ?` 配置为每天晚上 22:05 执行，但实际执行时间却是在上午和下午的不同时间点：

- 2025/8/15 9:37:52
- 2025/8/18 9:51:59
- 2025/8/18 14:31:12
- 2025/8/19 9:48:36
- 2025/8/19 15:42:15
- 2025/8/20 9:13:20
- 2025/8/20 11:13:09

## 问题原因

1. **Quartz.NET 默认使用 UTC 时区**：Quartz.NET 调度器默认使用 UTC 时间来解析和执行 cron 表达式
2. **时区转换错误**：在 `JobExecute.cs` 中使用了硬编码的 `AddHours(8)` 来处理时区转换
3. **调度器配置缺失**：没有正确配置 Quartz 调度器使用本地时区

## 修复内容

### 1. 修改 JobScheduler.cs
- 配置 Quartz 调度器使用 `Asia/Shanghai` 时区
- 添加完整的 Quartz 配置参数

### 2. 修改 JobCenter.cs
- 在创建 CronTrigger 时指定使用本地时区：`.WithCronSchedule(entity.CronExpression, x => x.InTimeZone(TimeZoneInfo.Local))`

### 3. 修改 JobExecute.cs
- 使用 `TimeZoneInfo.ConvertTimeFromUtc()` 替代硬编码的 `AddHours(8)`
- 正确处理 UTC 时间到本地时间的转换

### 4. 修改 JobHelper.cs
- 在 SetDailyJob 方法中也添加时区配置

### 5. 添加配置文件
- 创建 `quartz.config` 配置文件，明确指定时区设置

## 修复后的效果

修复后，cron 表达式 `0 5 22 * * ?` 将：
- 严格按照本地时间（中国标准时间 CST，UTC+8）每天晚上 22:05 执行
- 不再受到 UTC 时间影响
- 下次运行时间显示正确的本地时间

## 验证方法

1. **重新部署应用**：确保修改的代码生效
2. **检查日志**：观察定时任务是否在正确的时间（22:05）执行
3. **查看管理界面**：在定时任务管理界面查看"下次运行时间"是否显示为每天的 22:05
4. **运行测试**：执行 `CronExpressionTest.cs` 中的单元测试验证时区处理

## 注意事项

1. **服务器时区**：确保服务器系统时区设置正确
2. **数据库时间**：确保数据库服务器时间与应用服务器时间一致
3. **重启服务**：修改后需要重启应用服务以使配置生效
4. **监控执行**：建议在修复后监控几天，确认定时任务按预期执行

## 技术细节

- **时区标识**：使用 `Asia/Shanghai` 作为中国标准时间
- **Quartz 版本兼容性**：修复方案兼容当前使用的 Quartz.NET 版本
- **线程安全**：JobScheduler 使用单例模式，确保线程安全
- **配置优先级**：代码中的配置会覆盖默认配置

修复完成后，您的"在线巡课"定时任务将严格按照每天晚上 22:05 执行，不再出现时间偏差问题。
