﻿/** 复选框&单选框  */
.check-box, .radio-box {
    display: inline-block;
    box-sizing: border-box;
    cursor: pointer;
    position: relative;
    padding-left: 25px;
    padding-right: 15px;
    padding-top: 8px;
}

.icheckbox, .icheckbox-blue, .iradio, .iradio-blue, .iradio-purple {
    position: absolute;
    top: 8px;
    left: 0
}

/* iCheck */
.icheckbox-blue, .iradio-blue {
    display: block;
    margin: 0;
    padding: 0;
    width: 18px;
    height: 18px;
    background: url(minimal/blue.png) no-repeat;
    border: none;
    cursor: pointer
}

    .icheckbox-blue, .icheckbox-blue.static:hover {
        background-position: 0 0
    }

        .icheckbox-blue.hover, .icheckbox-blue:hover {
            background-position: -20px 0
        }

        .icheckbox-blue.checked {
            background-position: -40px 0
        }

        .icheckbox-blue.disabled {
            background-position: -60px 0;
            cursor: default
        }

        .icheckbox-blue.checked.disabled {
            background-position: -80px 0
        }

    .iradio-blue, .iradio-blue.static:hover {
        background-position: -100px 0
    }

        .iradio-blue.hover, .iradio-blue:hover {
            background-position: -120px 0
        }

        .iradio-blue.checked {
            background-position: -140px 0
        }

        .iradio-blue.disabled {
            background-position: -160px 0;
            cursor: default
        }

        .iradio-blue.checked.disabled {
            background-position: -180px 0
        }
