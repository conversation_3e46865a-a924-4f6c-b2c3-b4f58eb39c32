/*! highlight.js v9.13.1 | BSD3 License | git.io/hljslicense */
!function(n){var t="object"==typeof window&&window||"object"==typeof self&&self;"undefined"!=typeof exports?n(exports):t&&(t.hljs=n({}),"function"==typeof define&&define.amd&&define([],function(){return t.hljs}))}(function(n){function t(n){return n.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function s(n){return n.nodeName.toLowerCase()}function h(n,t){var i=n&&n.exec(t);return i&&0===i.index}function y(n){return st.test(n)}function g(n){var i,f,e,u,t=n.className+" ";if(t+=n.parentNode?n.parentNode.className:"",f=ht.exec(t))return r(f[1])?f[1]:"no-highlight";for(t=t.split(/\s+/),i=0,e=t.length;e>i;i++)if(u=t[i],y(u)||r(u))return u}function e(n){var t,i={},r=Array.prototype.slice.call(arguments,1);for(t in n)i[t]=n[t];return r.forEach(function(n){for(t in n)i[t]=n[t]}),i}function p(n){var t=[];return function i(n,r){for(var u=n.firstChild;u;u=u.nextSibling)3===u.nodeType?r+=u.nodeValue.length:1===u.nodeType&&(t.push({event:"start",offset:r,node:u}),r=i(u,r),s(u).match(/br|hr|img|input/)||t.push({event:"stop",offset:r,node:u}));return r}(n,0),t}function nt(n,i,r){function h(){return n.length&&i.length?n[0].offset!==i[0].offset?n[0].offset<i[0].offset?n:i:"start"===i[0].event?n:i:n.length?n:i}function c(n){function i(n){return" "+n.nodeName+'="'+t(n.value).replace('"',"&quot;")+'"'}e+="<"+s(n)+d.map.call(n.attributes,i).join("")+">"}function l(n){e+="<\/"+s(n)+">"}function a(n){("start"===n.event?c:l)(n.node)}for(var u,f=0,e="",o=[];n.length||i.length;)if(u=h(),e+=t(r.substring(f,u[0].offset)),f=u[0].offset,u===n){o.reverse().forEach(l);do a(u.splice(0,1)[0]),u=h();while(u===n&&u.length&&u[0].offset===f);o.reverse().forEach(c)}else"start"===u[0].event?o.push(u[0].node):o.pop(),a(u.splice(0,1)[0]);return e+t(r.substr(f))}function tt(n){return n.v&&!n.cached_variants&&(n.cached_variants=n.v.map(function(t){return e(n,{v:null},t)})),n.cached_variants||n.eW&&[e(n)]||[n]}function it(n){function i(n){return n&&n.source||n}function t(t,r){return new RegExp(i(t),"m"+(n.cI?"i":"")+(r?"g":""))}function r(u,f){var e,o,s;u.compiled||((u.compiled=!0,u.k=u.k||u.bK,u.k)&&(e={},o=function(t,i){n.cI&&(i=i.toLowerCase());i.split(" ").forEach(function(n){var i=n.split("|");e[i[0]]=[t,i[1]?Number(i[1]):1]})},"string"==typeof u.k?o("keyword",u.k):l(u.k).forEach(function(n){o(n,u.k[n])}),u.k=e),u.lR=t(u.l||/\w+/,!0),f&&(u.bK&&(u.b="\\b("+u.bK.split(" ").join("|")+")\\b"),u.b||(u.b=/\B|\b/),u.bR=t(u.b),u.endSameAsBegin&&(u.e=u.b),u.e||u.eW||(u.e=/\B|\b/),u.e&&(u.eR=t(u.e)),u.tE=i(u.e)||"",u.eW&&f.tE&&(u.tE+=(u.e?"|":"")+f.tE)),u.i&&(u.iR=t(u.i)),null==u.r&&(u.r=1),u.c||(u.c=[]),u.c=Array.prototype.concat.apply([],u.c.map(function(n){return tt("self"===n?u:n)})),u.c.forEach(function(n){r(n,u)}),u.starts&&r(u.starts,f),s=u.c.map(function(n){return n.bK?"\\.?("+n.b+")\\.?":n.b}).concat([u.tE,u.i]).map(i).filter(Boolean),u.t=s.length?t(s.join("|"),!0):{exec:function(){return null}})}r(n)}function o(n,f,e,s){function st(n){return new RegExp(n.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&"),"m")}function ht(n,t){for(var i=0,r=t.c.length;r>i;i++)if(h(t.c[i].bR,n))return t.c[i].endSameAsBegin&&(t.c[i].eR=st(t.c[i].bR.exec(n)[0])),t.c[i]}function rt(n,t){if(h(n.eR,t)){for(;n.endsParent&&n.parent;)n=n.parent;return n}if(n.eW)return rt(n.parent,t)}function ct(n,t){return!e&&h(t.iR,n)}function lt(n,t){var i=w.cI?t[0].toLowerCase():t[0];return n.k.hasOwnProperty(i)&&n.k[i]}function g(n,t,r,u){var e=u?"":i.classPrefix,f='<span class="'+e,o=r?"":v;return f+=n+'">',f+t+o}function at(){var r,u,n,i;if(!l.k)return t(a);for(i="",u=0,l.lR.lastIndex=0,n=l.lR.exec(a);n;)i+=t(a.substring(u,n.index)),r=lt(l,n),r?(b+=r[1],i+=g(r[0],t(n[0]))):i+=t(n[0]),u=l.lR.lastIndex,n=l.lR.exec(a);return i+t(a.substr(u))}function vt(){var i="string"==typeof l.sL,n;return i&&!u[l.sL]?t(a):(n=i?o(l.sL,a,!0,et[l.sL]):c(a,l.sL.length?l.sL:void 0),l.r>0&&(b+=n.r),i&&(et[l.sL]=n.top),g(n.language,n.value,!1,!0))}function nt(){p+=null!=l.sL?vt():at();a=""}function ut(n){p+=n.cN?g(n.cN,"",!0):"";l=Object.create(n,{parent:{value:l}})}function ft(n,t){var i,r,u;if(a+=n,null==t)return nt(),0;if(i=ht(t,l),i)return i.skip?a+=t:(i.eB&&(a+=t),nt(),i.rB||i.eB||(a=t)),ut(i,t),i.rB?0:t.length;if(r=rt(l,t),r){u=l;u.skip?a+=t:(u.rE||u.eE||(a+=t),nt(),u.eE&&(a=t));do l.cN&&(p+=v),l.skip||l.sL||(b+=l.r),l=l.parent;while(l!==r.parent);return r.starts&&(r.endSameAsBegin&&(r.starts.eR=r.eR),ut(r.starts,"")),u.rE?0:t.length}if(ct(t,l))throw new Error('Illegal lexeme "'+t+'" for mode "'+(l.cN||"<unnamed>")+'"');return a+=t,t.length||1}var w=r(n),a,b,k,ot,d;if(!w)throw new Error('Unknown language: "'+n+'"');it(w);for(var l=s||w,et={},p="",y=l;y!==w;y=y.parent)y.cN&&(p=g(y.cN,"",!0)+p);a="";b=0;try{for(d=0;;){if(l.t.lastIndex=d,k=l.t.exec(f),!k)break;ot=ft(f.substring(d,k.index),k[0]);d=k.index+ot}for(ft(f.substr(d)),y=l;y.parent;y=y.parent)y.cN&&(p+=v);return{r:b,value:p,language:n,top:l}}catch(tt){if(tt.message&&-1!==tt.message.indexOf("Illegal"))return{r:0,value:t(f)};throw tt;}}function c(n,f){f=f||i.languages||l(u);var e={r:0,value:t(n)},s=e;return f.filter(r).filter(k).forEach(function(t){var i=o(t,n,!1);i.language=t;i.r>s.r&&(s=i);i.r>e.r&&(s=e,e=i)}),s.language&&(e.second_best=s),e}function w(n){return i.tabReplace||i.useBR?n.replace(ct,function(n,t){return i.useBR&&"\n"===n?"<br>":i.tabReplace?t.replace(/\t/g,i.tabReplace):""}):n}function rt(n,t,i){var u=t?a[t]:i,r=[n.trim()];return n.match(/\bhljs\b/)||r.push("hljs"),-1===n.indexOf(u)&&r.push(u),r.join(" ").trim()}function b(n){var r,e,t,s,u,f=g(n);y(f)||(i.useBR?(r=document.createElementNS("http://www.w3.org/1999/xhtml","div"),r.innerHTML=n.innerHTML.replace(/\n/g,"").replace(/<br[ \/]*>/g,"\n")):r=n,u=r.textContent,t=f?o(f,u,!0):c(u),e=p(r),e.length&&(s=document.createElementNS("http://www.w3.org/1999/xhtml","div"),s.innerHTML=t.value,t.value=nt(e,p(s),u)),t.value=w(t.value),n.innerHTML=t.value,n.className=rt(n.className,f,t.language),n.result={language:t.language,re:t.r},t.second_best&&(n.second_best={language:t.second_best.language,re:t.second_best.r}))}function ut(n){i=e(i,n)}function f(){if(!f.called){f.called=!0;var n=document.querySelectorAll("pre code");d.forEach.call(n,b)}}function ft(){addEventListener("DOMContentLoaded",f,!1);addEventListener("load",f,!1)}function et(t,i){var r=u[t]=i(n);r.aliases&&r.aliases.forEach(function(n){a[n]=t})}function ot(){return l(u)}function r(n){return n=(n||"").toLowerCase(),u[n]||u[a[n]]}function k(n){var t=r(n);return t&&!t.disableAutodetect}var d=[],l=Object.keys,u={},a={},st=/^(no-?highlight|plain|text)$/i,ht=/\blang(?:uage)?-([\w-]+)\b/i,ct=/((^(<[^>]+>|\t|)+|(?:\n)))/gm,v="<\/span>",i={classPrefix:"hljs-",tabReplace:null,useBR:!1,languages:void 0};return n.highlight=o,n.highlightAuto=c,n.fixMarkup=w,n.highlightBlock=b,n.configure=ut,n.initHighlighting=f,n.initHighlightingOnLoad=ft,n.registerLanguage=et,n.listLanguages=ot,n.getLanguage=r,n.autoDetection=k,n.inherit=e,n.IR="[a-zA-Z]\\w*",n.UIR="[a-zA-Z_]\\w*",n.NR="\\b\\d+(\\.\\d+)?",n.CNR="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",n.BNR="\\b(0b[01]+)",n.RSR="!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",n.BE={b:"\\\\[\\s\\S]",r:0},n.ASM={cN:"string",b:"'",e:"'",i:"\\n",c:[n.BE]},n.QSM={cN:"string",b:'"',e:'"',i:"\\n",c:[n.BE]},n.PWM={b:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},n.C=function(t,i,r){var u=n.inherit({cN:"comment",b:t,e:i,c:[]},r||{});return u.c.push(n.PWM),u.c.push({cN:"doctag",b:"(?:TODO|FIXME|NOTE|BUG|XXX):",r:0}),u},n.CLCM=n.C("//","$"),n.CBCM=n.C("/\\*","\\*/"),n.HCM=n.C("#","$"),n.NM={cN:"number",b:n.NR,r:0},n.CNM={cN:"number",b:n.CNR,r:0},n.BNM={cN:"number",b:n.BNR,r:0},n.CSSNM={cN:"number",b:n.NR+"(%|em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc|px|deg|grad|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)?",r:0},n.RM={cN:"regexp",b:/\//,e:/\/[gimuy]*/,i:/\n/,c:[n.BE,{b:/\[/,e:/\]/,r:0,c:[n.BE]}]},n.TM={cN:"title",b:n.IR,r:0},n.UTM={cN:"title",b:n.UIR,r:0},n.METHOD_GUARD={b:"\\.\\s*"+n.UIR,r:0},n});hljs.registerLanguage("objectivec",function(n){var t=/[a-zA-Z@][a-zA-Z0-9_]*/,i="@interface @class @protocol @implementation";return{aliases:["mm","objc","obj-c"],k:{keyword:"int float while char export sizeof typedef const struct for union unsigned long volatile static bool mutable if do return goto void enum else break extern asm case short default double register explicit signed typename this switch continue wchar_t inline readonly assign readwrite self @synchronized id typeof nonatomic super unichar IBOutlet IBAction strong weak copy in out inout bycopy byref oneway __strong __weak __block __autoreleasing @private @protected @public @try @property @end @throw @catch @finally @autoreleasepool @synthesize @dynamic @selector @optional @required @encode @package @import @defs @compatibility_alias __bridge __bridge_transfer __bridge_retained __bridge_retain __covariant __contravariant __kindof _Nonnull _Nullable _Null_unspecified __FUNCTION__ __PRETTY_FUNCTION__ __attribute__ getter setter retain unsafe_unretained nonnull nullable null_unspecified null_resettable class instancetype NS_DESIGNATED_INITIALIZER NS_UNAVAILABLE NS_REQUIRES_SUPER NS_RETURNS_INNER_POINTER NS_INLINE NS_AVAILABLE NS_DEPRECATED NS_ENUM NS_OPTIONS NS_SWIFT_UNAVAILABLE NS_ASSUME_NONNULL_BEGIN NS_ASSUME_NONNULL_END NS_REFINED_FOR_SWIFT NS_SWIFT_NAME NS_SWIFT_NOTHROW NS_DURING NS_HANDLER NS_ENDHANDLER NS_VALUERETURN NS_VOIDRETURN",literal:"false true FALSE TRUE nil YES NO NULL",built_in:"BOOL dispatch_once_t dispatch_queue_t dispatch_sync dispatch_async dispatch_once"},l:t,i:"<\/",c:[{cN:"built_in",b:"\\b(AV|CA|CF|CG|CI|CL|CM|CN|CT|MK|MP|MTK|MTL|NS|SCN|SK|UI|WK|XC)\\w+"},n.CLCM,n.CBCM,n.CNM,n.QSM,{cN:"string",v:[{b:'@"',e:'"',i:"\\n",c:[n.BE]},{b:"'",e:"[^\\\\]'",i:"[^\\\\][^']"}]},{cN:"meta",b:"#",e:"$",c:[{cN:"meta-string",v:[{b:'"',e:'"'},{b:"<",e:">"}]}]},{cN:"class",b:"("+i.split(" ").join("|")+")\\b",e:"({|$)",eE:!0,k:i,l:t,c:[n.UTM]},{b:"\\."+n.UIR,r:0}]}});hljs.registerLanguage("sql",function(n){var t=n.C("--","$");return{cI:!0,i:/[<>{}*]/,c:[{bK:"begin end start commit rollback savepoint lock alter create drop rename call delete do handler insert load replace select truncate update set show pragma grant merge describe use explain help declare prepare execute deallocate release unlock purge reset change stop analyze cache flush optimize repair kill install uninstall checksum restore check backup revoke comment with",e:/;/,eW:!0,l:/[\w\.]+/,k:{keyword:"as abort abs absolute acc acce accep accept access accessed accessible account acos action activate add addtime admin administer advanced advise aes_decrypt aes_encrypt after agent aggregate ali alia alias allocate allow alter always analyze ancillary and any anydata anydataset anyschema anytype apply archive archived archivelog are as asc ascii asin assembly assertion associate asynchronous at atan atn2 attr attri attrib attribu attribut attribute attributes audit authenticated authentication authid authors auto autoallocate autodblink autoextend automatic availability avg backup badfile basicfile before begin beginning benchmark between bfile bfile_base big bigfile bin binary_double binary_float binlog bit_and bit_count bit_length bit_or bit_xor bitmap blob_base block blocksize body both bound buffer_cache buffer_pool build bulk by byte byteordermark bytes cache caching call calling cancel capacity cascade cascaded case cast catalog category ceil ceiling chain change changed char_base char_length character_length characters characterset charindex charset charsetform charsetid check checksum checksum_agg child choose chr chunk class cleanup clear client clob clob_base clone close cluster_id cluster_probability cluster_set clustering coalesce coercibility col collate collation collect colu colum column column_value columns columns_updated comment commit compact compatibility compiled complete composite_limit compound compress compute concat concat_ws concurrent confirm conn connec connect connect_by_iscycle connect_by_isleaf connect_by_root connect_time connection consider consistent constant constraint constraints constructor container content contents context contributors controlfile conv convert convert_tz corr corr_k corr_s corresponding corruption cos cost count count_big counted covar_pop covar_samp cpu_per_call cpu_per_session crc32 create creation critical cross cube cume_dist curdate current current_date current_time current_timestamp current_user cursor curtime customdatum cycle data database databases datafile datafiles datalength date_add date_cache date_format date_sub dateadd datediff datefromparts datename datepart datetime2fromparts day day_to_second dayname dayofmonth dayofweek dayofyear days db_role_change dbtimezone ddl deallocate declare decode decompose decrement decrypt deduplicate def defa defau defaul default defaults deferred defi defin define degrees delayed delegate delete delete_all delimited demand dense_rank depth dequeue des_decrypt des_encrypt des_key_file desc descr descri describ describe descriptor deterministic diagnostics difference dimension direct_load directory disable disable_all disallow disassociate discardfile disconnect diskgroup distinct distinctrow distribute distributed div do document domain dotnet double downgrade drop dumpfile duplicate duration each edition editionable editions element ellipsis else elsif elt empty enable enable_all enclosed encode encoding encrypt end end-exec endian enforced engine engines enqueue enterprise entityescaping eomonth error errors escaped evalname evaluate event eventdata events except exception exceptions exchange exclude excluding execu execut execute exempt exists exit exp expire explain export export_set extended extent external external_1 external_2 externally extract failed failed_login_attempts failover failure far fast feature_set feature_value fetch field fields file file_name_convert filesystem_like_logging final finish first first_value fixed flash_cache flashback floor flush following follows for forall force foreign form forma format found found_rows freelist freelists freepools fresh from from_base64 from_days ftp full function general generated get get_format get_lock getdate getutcdate global global_name globally go goto grant grants greatest group group_concat group_id grouping grouping_id groups gtid_subtract guarantee guard handler hash hashkeys having hea head headi headin heading heap help hex hierarchy high high_priority hosts hour http id ident_current ident_incr ident_seed identified identity idle_time if ifnull ignore iif ilike ilm immediate import in include including increment index indexes indexing indextype indicator indices inet6_aton inet6_ntoa inet_aton inet_ntoa infile initial initialized initially initrans inmemory inner innodb input insert install instance instantiable instr interface interleaved intersect into invalidate invisible is is_free_lock is_ipv4 is_ipv4_compat is_not is_not_null is_used_lock isdate isnull isolation iterate java join json json_exists keep keep_duplicates key keys kill language large last last_day last_insert_id last_value lax lcase lead leading least leaves left len lenght length less level levels library like like2 like4 likec limit lines link list listagg little ln load load_file lob lobs local localtime localtimestamp locate locator lock locked log log10 log2 logfile logfiles logging logical logical_reads_per_call logoff logon logs long loop low low_priority lower lpad lrtrim ltrim main make_set makedate maketime managed management manual map mapping mask master master_pos_wait match matched materialized max maxextents maximize maxinstances maxlen maxlogfiles maxloghistory maxlogmembers maxsize maxtrans md5 measures median medium member memcompress memory merge microsecond mid migration min minextents minimum mining minus minute minvalue missing mod mode model modification modify module monitoring month months mount move movement multiset mutex name name_const names nan national native natural nav nchar nclob nested never new newline next nextval no no_write_to_binlog noarchivelog noaudit nobadfile nocheck nocompress nocopy nocycle nodelay nodiscardfile noentityescaping noguarantee nokeep nologfile nomapping nomaxvalue nominimize nominvalue nomonitoring none noneditionable nonschema noorder nopr nopro noprom nopromp noprompt norely noresetlogs noreverse normal norowdependencies noschemacheck noswitch not nothing notice notnull notrim novalidate now nowait nth_value nullif nulls num numb numbe nvarchar nvarchar2 object ocicoll ocidate ocidatetime ociduration ociinterval ociloblocator ocinumber ociref ocirefcursor ocirowid ocistring ocitype oct octet_length of off offline offset oid oidindex old on online only opaque open operations operator optimal optimize option optionally or oracle oracle_date oradata ord ordaudio orddicom orddoc order ordimage ordinality ordvideo organization orlany orlvary out outer outfile outline output over overflow overriding package pad parallel parallel_enable parameters parent parse partial partition partitions pascal passing password password_grace_time password_lock_time password_reuse_max password_reuse_time password_verify_function patch path patindex pctincrease pctthreshold pctused pctversion percent percent_rank percentile_cont percentile_disc performance period period_add period_diff permanent physical pi pipe pipelined pivot pluggable plugin policy position post_transaction pow power pragma prebuilt precedes preceding precision prediction prediction_cost prediction_details prediction_probability prediction_set prepare present preserve prior priority private private_sga privileges procedural procedure procedure_analyze processlist profiles project prompt protection public publishingservername purge quarter query quick quiesce quota quotename radians raise rand range rank raw read reads readsize rebuild record records recover recovery recursive recycle redo reduced ref reference referenced references referencing refresh regexp_like register regr_avgx regr_avgy regr_count regr_intercept regr_r2 regr_slope regr_sxx regr_sxy reject rekey relational relative relaylog release release_lock relies_on relocate rely rem remainder rename repair repeat replace replicate replication required reset resetlogs resize resource respect restore restricted result result_cache resumable resume retention return returning returns reuse reverse revoke right rlike role roles rollback rolling rollup round row row_count rowdependencies rowid rownum rows rtrim rules safe salt sample save savepoint sb1 sb2 sb4 scan schema schemacheck scn scope scroll sdo_georaster sdo_topo_geometry search sec_to_time second section securefile security seed segment select self sequence sequential serializable server servererror session session_user sessions_per_user set sets settings sha sha1 sha2 share shared shared_pool short show shrink shutdown si_averagecolor si_colorhistogram si_featurelist si_positionalcolor si_stillimage si_texture siblings sid sign sin size size_t sizes skip slave sleep smalldatetimefromparts smallfile snapshot some soname sort soundex source space sparse spfile split sql sql_big_result sql_buffer_result sql_cache sql_calc_found_rows sql_small_result sql_variant_property sqlcode sqldata sqlerror sqlname sqlstate sqrt square standalone standby start starting startup statement static statistics stats_binomial_test stats_crosstab stats_ks_test stats_mode stats_mw_test stats_one_way_anova stats_t_test_ stats_t_test_indep stats_t_test_one stats_t_test_paired stats_wsr_test status std stddev stddev_pop stddev_samp stdev stop storage store stored str str_to_date straight_join strcmp strict string struct stuff style subdate subpartition subpartitions substitutable substr substring subtime subtring_index subtype success sum suspend switch switchoffset switchover sync synchronous synonym sys sys_xmlagg sysasm sysaux sysdate sysdatetimeoffset sysdba sysoper system system_user sysutcdatetime table tables tablespace tan tdo template temporary terminated tertiary_weights test than then thread through tier ties time time_format time_zone timediff timefromparts timeout timestamp timestampadd timestampdiff timezone_abbr timezone_minute timezone_region to to_base64 to_date to_days to_seconds todatetimeoffset trace tracking transaction transactional translate translation treat trigger trigger_nestlevel triggers trim truncate try_cast try_convert try_parse type ub1 ub2 ub4 ucase unarchived unbounded uncompress under undo unhex unicode uniform uninstall union unique unix_timestamp unknown unlimited unlock unnest unpivot unrecoverable unsafe unsigned until untrusted unusable unused update updated upgrade upped upper upsert url urowid usable usage use use_stored_outlines user user_data user_resources users using utc_date utc_timestamp uuid uuid_short validate validate_password_strength validation valist value values var var_samp varcharc vari varia variab variabl variable variables variance varp varraw varrawc varray verify version versions view virtual visible void wait wallet warning warnings week weekday weekofyear wellformed when whene whenev wheneve whenever where while whitespace with within without work wrapped xdb xml xmlagg xmlattributes xmlcast xmlcolattval xmlelement xmlexists xmlforest xmlindex xmlnamespaces xmlpi xmlquery xmlroot xmlschema xmlserialize xmltable xmltype xor year year_to_month years yearweek",literal:"true false null unknown",built_in:"array bigint binary bit blob bool boolean char character date dec decimal float int int8 integer interval number numeric real record serial serial8 smallint text time timestamp varchar varying void"},c:[{cN:"string",b:"'",e:"'",c:[n.BE,{b:"''"}]},{cN:"string",b:'"',e:'"',c:[n.BE,{b:'""'}]},{cN:"string",b:"`",e:"`",c:[n.BE]},n.CNM,n.CBCM,t,n.HCM]},n.CBCM,t,n.HCM]}});hljs.registerLanguage("xml",function(n){var t={eW:!0,i:/</,r:0,c:[{cN:"attr",b:"[A-Za-z0-9\\._:-]+",r:0},{b:/=\s*/,r:0,c:[{cN:"string",endsParent:!0,v:[{b:/"/,e:/"/},{b:/'/,e:/'/},{b:/[^\s"'=<>`]+/}]}]}]};return{aliases:["html","xhtml","rss","atom","xjb","xsd","xsl","plist"],cI:!0,c:[{cN:"meta",b:"<!DOCTYPE",e:">",r:10,c:[{b:"\\[",e:"\\]"}]},n.C("<!--","-->",{r:10}),{b:"<\\!\\[CDATA\\[",e:"\\]\\]>",r:10},{cN:"meta",b:/<\?xml/,e:/\?>/,r:10},{b:/<\?(php)?/,e:/\?>/,sL:"php",c:[{b:"/\\*",e:"\\*/",skip:!0},{b:'b"',e:'"',skip:!0},{b:"b'",e:"'",skip:!0},n.inherit(n.ASM,{i:null,cN:null,c:null,skip:!0}),n.inherit(n.QSM,{i:null,cN:null,c:null,skip:!0})]},{cN:"tag",b:"<style(?=\\s|>|$)",e:">",k:{name:"style"},c:[t],starts:{e:"<\/style>",rE:!0,sL:["css","xml"]}},{cN:"tag",b:"<script(?=\\s|>|$)",e:">",k:{name:"script"},c:[t],starts:{e:"<\/script>",rE:!0,sL:["actionscript","javascript","handlebars","xml"]}},{cN:"tag",b:"<\/?",e:"/?>",c:[{cN:"name",b:/[^\/><\s]+/,r:0},t]}]}});hljs.registerLanguage("markdown",function(){return{aliases:["md","mkdown","mkd"],c:[{cN:"section",v:[{b:"^#{1,6}",e:"$"},{b:"^.+?\\n[=-]{2,}$"}]},{b:"<",e:">",sL:"xml",r:0},{cN:"bullet",b:"^([*+-]|(\\d+\\.))\\s+"},{cN:"strong",b:"[*_]{2}.+?[*_]{2}"},{cN:"emphasis",v:[{b:"\\*.+?\\*"},{b:"_.+?_",r:0}]},{cN:"quote",b:"^>\\s+",e:"$"},{cN:"code",v:[{b:"^```w*s*$",e:"^```s*$"},{b:"`.+?`"},{b:"^( {4}|\t)",e:"$",r:0}]},{b:"^[-\\*]{3,}",e:"$"},{b:"\\[.+?\\][\\(\\[].*?[\\)\\]]",rB:!0,c:[{cN:"string",b:"\\[",e:"\\]",eB:!0,rE:!0,r:0},{cN:"link",b:"\\]\\(",e:"\\)",eB:!0,eE:!0},{cN:"symbol",b:"\\]\\[",e:"\\]",eB:!0,eE:!0}],r:10},{b:/^\[[^\n]+\]:/,rB:!0,c:[{cN:"symbol",b:/\[/,e:/\]/,eB:!0,eE:!0},{cN:"link",b:/:\s*/,e:/$/,eB:!0}]}]}});hljs.registerLanguage("perl",function(n){var i="getpwent getservent quotemeta msgrcv scalar kill dbmclose undef lc ma syswrite tr send umask sysopen shmwrite vec qx utime local oct semctl localtime readpipe do return format read sprintf dbmopen pop getpgrp not getpwnam rewinddir qqfileno qw endprotoent wait sethostent bless s|0 opendir continue each sleep endgrent shutdown dump chomp connect getsockname die socketpair close flock exists index shmgetsub for endpwent redo lstat msgctl setpgrp abs exit select print ref gethostbyaddr unshift fcntl syscall goto getnetbyaddr join gmtime symlink semget splice x|0 getpeername recv log setsockopt cos last reverse gethostbyname getgrnam study formline endhostent times chop length gethostent getnetent pack getprotoent getservbyname rand mkdir pos chmod y|0 substr endnetent printf next open msgsnd readdir use unlink getsockopt getpriority rindex wantarray hex system getservbyport endservent int chr untie rmdir prototype tell listen fork shmread ucfirst setprotoent else sysseek link getgrgid shmctl waitpid unpack getnetbyname reset chdir grep split require caller lcfirst until warn while values shift telldir getpwuid my getprotobynumber delete and sort uc defined srand accept package seekdir getprotobyname semop our rename seek if q|0 chroot sysread setpwent no crypt getc chown sqrt write setnetent setpriority foreach tie sin msgget map stat getlogin unless elsif truncate exec keys glob tied closedirioctl socket readlink eval xor readline binmode setservent eof ord bind alarm pipe atan2 getgrent exp time push setgrent gt lt or ne m|0 break given say state when",r={cN:"subst",b:"[$@]\\{",e:"\\}",k:i},u={b:"->{",e:"}"},f={v:[{b:/\$\d/},{b:/[\$%@](\^\w\b|#\w+(::\w+)*|{\w+}|\w+(::\w*)*)/},{b:/[\$%@][^\s\w{]/,r:0}]},e=[n.BE,r,f],t=[f,n.HCM,n.C("^\\=\\w","\\=cut",{eW:!0}),u,{cN:"string",c:e,v:[{b:"q[qwxr]?\\s*\\(",e:"\\)",r:5},{b:"q[qwxr]?\\s*\\[",e:"\\]",r:5},{b:"q[qwxr]?\\s*\\{",e:"\\}",r:5},{b:"q[qwxr]?\\s*\\|",e:"\\|",r:5},{b:"q[qwxr]?\\s*\\<",e:"\\>",r:5},{b:"qw\\s+q",e:"q",r:5},{b:"'",e:"'",c:[n.BE]},{b:'"',e:'"'},{b:"`",e:"`",c:[n.BE]},{b:"{\\w+}",c:[],r:0},{b:"-?\\w+\\s*\\=\\>",c:[],r:0}]},{cN:"number",b:"(\\b0[0-7_]+)|(\\b0x[0-9a-fA-F_]+)|(\\b[1-9][0-9_]*(\\.[0-9_]+)?)|[0_]\\b",r:0},{b:"(\\/\\/|"+n.RSR+"|\\b(split|return|print|reverse|grep)\\b)\\s*",k:"split return print reverse grep",r:0,c:[n.HCM,{cN:"regexp",b:"(s|tr|y)/(\\\\.|[^/])*/(\\\\.|[^/])*/[a-z]*",r:10},{cN:"regexp",b:"(m|qr)?/",e:"/[a-z]*",c:[n.BE],r:0}]},{cN:"function",bK:"sub",e:"(\\s*\\(.*?\\))?[;{]",eE:!0,r:5,c:[n.TM]},{b:"-\\w\\b",r:0},{b:"^__DATA__$",e:"^__END__$",sL:"mojolicious",c:[{b:"^@@.*",e:"$",cN:"comment"}]}];return r.c=t,u.c=t,{aliases:["pl","pm"],l:/[\w\.]+/,k:i,c:t}});hljs.registerLanguage("java",function(n){var t="[À-ʸa-zA-Z_$][À-ʸa-zA-Z_$0-9]*",r=t+"(<"+t+"(\\s*,\\s*"+t+")*>)?",i="false synchronized int abstract float private char boolean var static null if const for true while long strictfp finally protected import native final void enum else break transient catch instanceof byte super volatile case assert short package default double public try this switch continue throws protected public private module requires exports do";return{aliases:["jsp"],k:i,i:/<\/|#/,c:[n.C("/\\*\\*","\\*/",{r:0,c:[{b:/\w+@/,r:0},{cN:"doctag",b:"@[A-Za-z]+"}]}),n.CLCM,n.CBCM,n.ASM,n.QSM,{cN:"class",bK:"class interface",e:/[{;=]/,eE:!0,k:"class interface",i:/[:"\[\]]/,c:[{bK:"extends implements"},n.UTM]},{bK:"new throw return else",r:0},{cN:"function",b:"("+r+"\\s+)+"+n.UIR+"\\s*\\(",rB:!0,e:/[{;=]/,eE:!0,k:i,c:[{b:n.UIR+"\\s*\\(",rB:!0,r:0,c:[n.UTM]},{cN:"params",b:/\(/,e:/\)/,k:i,r:0,c:[n.ASM,n.QSM,n.CNM,n.CBCM]},n.CLCM,n.CBCM]},{cN:"number",b:"\\b(0[bB]([01]+[01_]+[01]+|[01]+)|0[xX]([a-fA-F0-9]+[a-fA-F0-9_]+[a-fA-F0-9]+|[a-fA-F0-9]+)|(([\\d]+[\\d_]+[\\d]+|[\\d]+)(\\.([\\d]+[\\d_]+[\\d]+|[\\d]+))?|\\.([\\d]+[\\d_]+[\\d]+|[\\d]+))([eE][-+]?\\d+)?)[lLfF]?",r:0},{cN:"meta",b:"@[A-Za-z]+"}]}});hljs.registerLanguage("bash",function(n){var t={cN:"variable",v:[{b:/\$[\w\d#@][\w\d_]*/},{b:/\$\{(.*?)}/}]},i={cN:"string",b:/"/,e:/"/,c:[n.BE,t,{cN:"variable",b:/\$\(/,e:/\)/,c:[n.BE]}]};return{aliases:["sh","zsh"],l:/\b-?[a-z\._]+\b/,k:{keyword:"if then else elif fi for while in do done case esac function",literal:"true false",built_in:"break cd continue eval exec exit export getopts hash pwd readonly return shift test times trap umask unset alias bind builtin caller command declare echo enable help let local logout mapfile printf read readarray source type typeset ulimit unalias set shopt autoload bg bindkey bye cap chdir clone comparguments compcall compctl compdescribe compfiles compgroups compquote comptags comptry compvalues dirs disable disown echotc echoti emulate fc fg float functions getcap getln history integer jobs kill limit log noglob popd print pushd pushln rehash sched setcap setopt stat suspend ttyctl unfunction unhash unlimit unsetopt vared wait whence where which zcompile zformat zftp zle zmodload zparseopts zprof zpty zregexparse zsocket zstyle ztcp",_:"-ne -eq -lt -gt -f -d -e -s -l -a"},c:[{cN:"meta",b:/^#![^\n]+sh\s*$/,r:10},{cN:"function",b:/\w[\w\d_]*\s*\(\s*\)\s*\{/,rB:!0,c:[n.inherit(n.TM,{b:/\w[\w\d_]*/})],r:0},n.HCM,i,{cN:"string",b:/'/,e:/'/},t]}});hljs.registerLanguage("shell",function(){return{aliases:["console"],c:[{cN:"meta",b:"^\\s{0,3}[\\w\\d\\[\\]()@-]*[>%$#]",starts:{e:"$",sL:"bash"}}]}});hljs.registerLanguage("json",function(n){var i={literal:"true false null"},t=[n.QSM,n.CNM],r={e:",",eW:!0,eE:!0,c:t,k:i},u={b:"{",e:"}",c:[{cN:"attr",b:/"/,e:/"/,c:[n.BE],i:"\\n"},n.inherit(r,{b:/:/})],i:"\\S"},f={b:"\\[",e:"\\]",c:[n.inherit(r)],i:"\\S"};return t.splice(t.length,0,u,f),{c:t,k:i,i:"\\S"}});hljs.registerLanguage("nginx",function(n){var t={cN:"variable",v:[{b:/\$\d+/},{b:/\$\{/,e:/}/},{b:"[\\$\\@]"+n.UIR}]},i={eW:!0,l:"[a-z/_]+",k:{literal:"on off yes no true false none blocked debug info notice warn error crit select break last permanent redirect kqueue rtsig epoll poll /dev/poll"},r:0,i:"=>",c:[n.HCM,{cN:"string",c:[n.BE,t],v:[{b:/"/,e:/"/},{b:/'/,e:/'/}]},{b:"([a-z]+):/",e:"\\s",eW:!0,eE:!0,c:[t]},{cN:"regexp",c:[n.BE,t],v:[{b:"\\s\\^",e:"\\s|{|;",rE:!0},{b:"~\\*?\\s+",e:"\\s|{|;",rE:!0},{b:"\\*(\\.[a-z\\-]+)+"},{b:"([a-z\\-]+\\.)+\\*"}]},{cN:"number",b:"\\b\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}(:\\d{1,5})?\\b"},{cN:"number",b:"\\b\\d+[kKmMgGdshdwy]*\\b",r:0},t]};return{aliases:["nginxconf"],c:[n.HCM,{b:n.UIR+"\\s+{",rB:!0,e:"{",c:[{cN:"section",b:n.UIR}],r:0},{b:n.UIR+"\\s",e:";|{",rB:!0,c:[{cN:"attribute",b:n.UIR,starts:i}],r:0}],i:"[^\\s\\}]"}});hljs.registerLanguage("ini",function(n){var t={cN:"string",c:[n.BE],v:[{b:"'''",e:"'''",r:10},{b:'"""',e:'"""',r:10},{b:'"',e:'"'},{b:"'",e:"'"}]};return{aliases:["toml"],cI:!0,i:/\S/,c:[n.C(";","$"),n.HCM,{cN:"section",b:/^\s*\[+/,e:/\]+/},{b:/^[a-z0-9\[\]_-]+\s*=\s*/,e:"$",rB:!0,c:[{cN:"attr",b:/[a-z0-9\[\]_-]+/},{b:/=/,eW:!0,r:0,c:[{cN:"literal",b:/\bon|off|true|false|yes|no\b/},{cN:"variable",v:[{b:/\$[\w\d"][\w\d_]*/},{b:/\$\{(.*?)}/}]},t,{cN:"number",b:/([\+\-]+)?[\d]+_[\d_]+/},n.NM]}]}]}});hljs.registerLanguage("http",function(){var n="HTTP/[0-9\\.]+";return{aliases:["https"],i:"\\S",c:[{b:"^"+n,e:"$",c:[{cN:"number",b:"\\b\\d{3}\\b"}]},{b:"^[A-Z]+ (.*?) "+n+"$",rB:!0,e:"$",c:[{cN:"string",b:" ",e:" ",eB:!0,eE:!0},{b:n},{cN:"keyword",b:"[A-Z]+"}]},{cN:"attribute",b:"^\\w",e:": ",eE:!0,i:"\\n|\\s|=",starts:{e:"$",r:0}},{b:"\\n\\n",starts:{sL:[],eW:!0}}]}});hljs.registerLanguage("css",function(n){var t={b:/[A-Z\_\.\-]+\s*:/,rB:!0,e:";",eW:!0,c:[{cN:"attribute",b:/\S/,e:":",eE:!0,starts:{eW:!0,eE:!0,c:[{b:/[\w-]+\(/,rB:!0,c:[{cN:"built_in",b:/[\w-]+/},{b:/\(/,e:/\)/,c:[n.ASM,n.QSM]}]},n.CSSNM,n.QSM,n.ASM,n.CBCM,{cN:"number",b:"#[0-9A-Fa-f]+"},{cN:"meta",b:"!important"}]}}]};return{cI:!0,i:/[=\/|'\$]/,c:[n.CBCM,{cN:"selector-id",b:/#[A-Za-z0-9_-]+/},{cN:"selector-class",b:/\.[A-Za-z0-9_-]+/},{cN:"selector-attr",b:/\[/,e:/\]/,i:"$"},{cN:"selector-pseudo",b:/:(:)?[a-zA-Z0-9\_\-\+\(\)"'.]+/},{b:"@(font-face|page)",l:"[a-z-]+",k:"font-face page"},{b:"@",e:"[{;]",i:/:/,c:[{cN:"keyword",b:/\w+/},{b:/\s/,eW:!0,eE:!0,r:0,c:[n.ASM,n.QSM,n.CSSNM]}]},{cN:"selector-tag",b:"[a-zA-Z-][a-zA-Z0-9_-]*",r:0},{b:"{",e:"}",i:/\S/,c:[n.CBCM,t]}]}});hljs.registerLanguage("javascript",function(n){var t="[A-Za-z$_][0-9A-Za-z$_]*",i={keyword:"in of if for while finally var new function do return void else break catch instanceof with throw case default try this switch continue typeof delete let yield const export super debugger as async await static import from as",literal:"true false null undefined NaN Infinity",built_in:"eval isFinite isNaN parseFloat parseInt decodeURI decodeURIComponent encodeURI encodeURIComponent escape unescape Object Function Boolean Error EvalError InternalError RangeError ReferenceError StopIteration SyntaxError TypeError URIError Number Math Date String RegExp Array Float32Array Float64Array Int16Array Int32Array Int8Array Uint16Array Uint32Array Uint8Array Uint8ClampedArray ArrayBuffer DataView JSON Intl arguments require module console window document Symbol Set Map WeakSet WeakMap Proxy Reflect Promise"},f={cN:"number",v:[{b:"\\b(0[bB][01]+)"},{b:"\\b(0[oO][0-7]+)"},{b:n.CNR}],r:0},r={cN:"subst",b:"\\$\\{",e:"\\}",k:i,c:[]},e={cN:"string",b:"`",e:"`",c:[n.BE,r]},u;return r.c=[n.ASM,n.QSM,e,f,n.RM],u=r.c.concat([n.CBCM,n.CLCM]),{aliases:["js","jsx"],k:i,c:[{cN:"meta",r:10,b:/^\s*['"]use (strict|asm)['"]/},{cN:"meta",b:/^#!/,e:/$/},n.ASM,n.QSM,e,n.CLCM,n.CBCM,f,{b:/[{,]\s*/,r:0,c:[{b:t+"\\s*:",rB:!0,r:0,c:[{cN:"attr",b:t,r:0}]}]},{b:"("+n.RSR+"|\\b(case|return|throw)\\b)\\s*",k:"return throw case",c:[n.CLCM,n.CBCM,n.RM,{cN:"function",b:"(\\(.*?\\)|"+t+")\\s*=>",rB:!0,e:"\\s*=>",c:[{cN:"params",v:[{b:t},{b:/\(\s*\)/},{b:/\(/,e:/\)/,eB:!0,eE:!0,k:i,c:u}]}]},{b:/</,e:/(\/\w+|\w+\/)>/,sL:"xml",c:[{b:/<\w+\s*\/>/,skip:!0},{b:/<\w+/,e:/(\/\w+|\w+\/)>/,skip:!0,c:[{b:/<\w+\s*\/>/,skip:!0},"self"]}]}],r:0},{cN:"function",bK:"function",e:/\{/,eE:!0,c:[n.inherit(n.TM,{b:t}),{cN:"params",b:/\(/,e:/\)/,eB:!0,eE:!0,c:u}],i:/\[|%/},{b:/\$[(.]/},n.METHOD_GUARD,{cN:"class",bK:"class",e:/[{;=]/,eE:!0,i:/[:"\[\]]/,c:[{bK:"extends"},n.UTM]},{bK:"constructor",e:/\{/,eE:!0}],i:/#(?!!)/}});hljs.registerLanguage("makefile",function(n){var t={cN:"variable",v:[{b:"\\$\\("+n.UIR+"\\)",c:[n.BE]},{b:/\$[@%<?\^\+\*]/}]},i={cN:"string",b:/"/,e:/"/,c:[n.BE,t]},r={cN:"variable",b:/\$\([\w-]+\s/,e:/\)/,k:{built_in:"subst patsubst strip findstring filter filter-out sort word wordlist firstword lastword dir notdir suffix basename addsuffix addprefix join wildcard realpath abspath error warning shell origin flavor foreach if or and call eval file value"},c:[t]},u={b:"^"+n.UIR+"\\s*[:+?]?=",i:"\\n",rB:!0,c:[{b:"^"+n.UIR,e:"[:+?]?=",eE:!0}]},f={cN:"section",b:/^[^\s]+:/,e:/$/,c:[t]};return{aliases:["mk","mak"],k:"define endef undefine ifdef ifndef ifeq ifneq else endif include -include sinclude override export unexport private vpath",l:/[\w-]+/,c:[n.HCM,t,i,r,u,{cN:"meta",b:/^\.PHONY:/,e:/$/,k:{"meta-keyword":".PHONY"},l:/[\.\w]+/},f]}});hljs.registerLanguage("ruby",function(n){var f="[a-zA-Z_]\\w*[!?=]?|[-+~]\\@|<<|>>|=~|===?|<=>|[<>]=?|\\*\\*|[-/+%^&*~`|]|\\[\\]=?",r={keyword:"and then defined module in return redo if BEGIN retry end for self when next until do begin unless END rescue else break undef not super class case require yield alias while ensure elsif or include attr_reader attr_writer attr_accessor",literal:"true false nil"},e={cN:"doctag",b:"@[A-Za-z]+"},o={b:"#<",e:">"},t=[n.C("#","$",{c:[e]}),n.C("^\\=begin","^\\=end",{c:[e],r:10}),n.C("^__END__","\\n$")],u={cN:"subst",b:"#\\{",e:"}",k:r},s={cN:"string",c:[n.BE,u],v:[{b:/'/,e:/'/},{b:/"/,e:/"/},{b:/`/,e:/`/},{b:"%[qQwWx]?\\(",e:"\\)"},{b:"%[qQwWx]?\\[",e:"\\]"},{b:"%[qQwWx]?{",e:"}"},{b:"%[qQwWx]?<",e:">"},{b:"%[qQwWx]?/",e:"/"},{b:"%[qQwWx]?%",e:"%"},{b:"%[qQwWx]?-",e:"-"},{b:"%[qQwWx]?\\|",e:"\\|"},{b:/\B\?(\\\d{1,3}|\\x[A-Fa-f0-9]{1,2}|\\u[A-Fa-f0-9]{4}|\\?\S)\b/},{b:/<<(-?)\w+$/,e:/^\s*\w+$/}]},h={cN:"params",b:"\\(",e:"\\)",endsParent:!0,k:r},i=[s,o,{cN:"class",bK:"class module",e:"$|;",i:/=/,c:[n.inherit(n.TM,{b:"[A-Za-z_]\\w*(::\\w+)*(\\?|\\!)?"}),{b:"<\\s*",c:[{b:"("+n.IR+"::)?"+n.IR}]}].concat(t)},{cN:"function",bK:"def",e:"$|;",c:[n.inherit(n.TM,{b:f}),h].concat(t)},{b:n.IR+"::"},{cN:"symbol",b:n.UIR+"(\\!|\\?)?:",r:0},{cN:"symbol",b:":(?!\\s)",c:[s,{b:f}],r:0},{cN:"number",b:"(\\b0[0-7_]+)|(\\b0x[0-9a-fA-F_]+)|(\\b[1-9][0-9_]*(\\.[0-9_]+)?)|[0_]\\b",r:0},{b:"(\\$\\W)|((\\$|\\@\\@?)(\\w+))"},{cN:"params",b:/\|/,e:/\|/,k:r},{b:"("+n.RSR+"|unless)\\s*",k:"unless",c:[o,{cN:"regexp",c:[n.BE,u],i:/\n/,v:[{b:"/",e:"/[a-z]*"},{b:"%r{",e:"}[a-z]*"},{b:"%r\\(",e:"\\)[a-z]*"},{b:"%r!",e:"![a-z]*"},{b:"%r\\[",e:"\\][a-z]*"}]}].concat(t),r:0}].concat(t);u.c=i;h.c=i;var c=[{b:/^\s*=>/,starts:{e:"$",c:i}},{cN:"meta",b:"^([>?]>|[\\w#]+\\(\\w+\\):\\d+:\\d+>|(\\w+-)?\\d+\\.\\d+\\.\\d(p\\d+)?[^>]+>)",starts:{e:"$",c:i}}];return{aliases:["rb","gemspec","podspec","thor","irb"],k:r,i:/\/\*/,c:t.concat(c).concat(i)}});hljs.registerLanguage("cs",function(n){var t={keyword:"abstract as base bool break byte case catch char checked const continue decimal default delegate do double enum event explicit extern finally fixed float for foreach goto if implicit in int interface internal is lock long nameof object operator out override params private protected public readonly ref sbyte sealed short sizeof stackalloc static string struct switch this try typeof uint ulong unchecked unsafe ushort using virtual void volatile while add alias ascending async await by descending dynamic equals from get global group into join let on orderby partial remove select set value var where yield",literal:"null false true"},i={cN:"number",v:[{b:"\\b(0b[01']+)"},{b:"(-?)\\b([\\d']+(\\.[\\d']*)?|\\.[\\d']+)(u|U|l|L|ul|UL|f|F|b|B)"},{b:"(-?)(\\b0[xX][a-fA-F0-9']+|(\\b[\\d']+(\\.[\\d']*)?|\\.[\\d']+)([eE][-+]?[\\d']+)?)"}],r:0},r={cN:"string",b:'@"',e:'"',c:[{b:'""'}]},c=n.inherit(r,{i:/\n/}),u={cN:"subst",b:"{",e:"}",k:t},f=n.inherit(u,{i:/\n/}),e={cN:"string",b:/\$"/,e:'"',i:/\n/,c:[{b:"{{"},{b:"}}"},n.BE,f]},o={cN:"string",b:/\$@"/,e:'"',c:[{b:"{{"},{b:"}}"},{b:'""'},u]},l=n.inherit(o,{i:/\n/,c:[{b:"{{"},{b:"}}"},{b:'""'},f]}),s,h;return u.c=[o,e,r,n.ASM,n.QSM,i,n.CBCM],f.c=[l,e,c,n.ASM,n.QSM,i,n.inherit(n.CBCM,{i:/\n/})],s={v:[o,e,r,n.ASM,n.QSM]},h=n.IR+"(<"+n.IR+"(\\s*,\\s*"+n.IR+")*>)?(\\[\\])?",{aliases:["csharp"],k:t,i:/::/,c:[n.C("///","$",{rB:!0,c:[{cN:"doctag",v:[{b:"///",r:0},{b:"<!--|-->"},{b:"<\/?",e:">"}]}]}),n.CLCM,n.CBCM,{cN:"meta",b:"#",e:"$",k:{"meta-keyword":"if else elif endif define undef warning error line region endregion pragma checksum"}},s,i,{bK:"class interface",e:/[{;=]/,i:/[^\s:,]/,c:[n.TM,n.CLCM,n.CBCM]},{bK:"namespace",e:/[{;=]/,i:/[^\s:]/,c:[n.inherit(n.TM,{b:"[a-zA-Z](\\.?\\w)*"}),n.CLCM,n.CBCM]},{cN:"meta",b:"^\\s*\\[",eB:!0,e:"\\]",eE:!0,c:[{cN:"meta-string",b:/"/,e:/"/}]},{bK:"new return throw await else",r:0},{cN:"function",b:"("+h+"\\s+)+"+n.IR+"\\s*\\(",rB:!0,e:/\s*[{;=]/,eE:!0,k:t,c:[{b:n.IR+"\\s*\\(",rB:!0,c:[n.TM],r:0},{cN:"params",b:/\(/,e:/\)/,eB:!0,eE:!0,k:t,r:0,c:[s,i,n.CBCM]},n.CLCM,n.CBCM]}]}});hljs.registerLanguage("coffeescript",function(n){var r={keyword:"in if for while finally new do return else break catch instanceof throw try this switch continue typeof delete debugger super yield import export from as default await then unless until loop of by when and or is isnt not",literal:"true false null undefined yes no on off",built_in:"npm require console print module global window document"},t="[A-Za-z$_][0-9A-Za-z$_]*",i={cN:"subst",b:/#\{/,e:/}/,k:r},u=[n.BNM,n.inherit(n.CNM,{starts:{e:"(\\s*/)?",r:0}}),{cN:"string",v:[{b:/'''/,e:/'''/,c:[n.BE]},{b:/'/,e:/'/,c:[n.BE]},{b:/"""/,e:/"""/,c:[n.BE,i]},{b:/"/,e:/"/,c:[n.BE,i]}]},{cN:"regexp",v:[{b:"///",e:"///",c:[i,n.HCM]},{b:"//[gim]*",r:0},{b:/\/(?![ *])(\\\/|.)*?\/[gim]*(?=\W|$)/}]},{b:"@"+t},{sL:"javascript",eB:!0,eE:!0,v:[{b:"```",e:"```"},{b:"`",e:"`"}]}];i.c=u;var f=n.inherit(n.TM,{b:t}),e="(\\(.*\\))?\\s*\\B[-=]>",o={cN:"params",b:"\\([^\\(]",rB:!0,c:[{b:/\(/,e:/\)/,k:r,c:["self"].concat(u)}]};return{aliases:["coffee","cson","iced"],k:r,i:/\/\*/,c:u.concat([n.C("###","###"),n.HCM,{cN:"function",b:"^\\s*"+t+"\\s*=\\s*"+e,e:"[-=]>",rB:!0,c:[f,o]},{b:/[:\(,=]\s*/,r:0,c:[{cN:"function",b:e,e:"[-=]>",rB:!0,c:[o]}]},{cN:"class",bK:"class",e:"$",i:/[:="\[\]]/,c:[{bK:"extends",eW:!0,i:/[:="\[\]]/,c:[f]},f]},{b:t+":",e:":",rB:!0,rE:!0,r:0}])}});hljs.registerLanguage("php",function(n){var i={b:"\\$+[a-zA-Z_-ÿ][a-zA-Z0-9_-ÿ]*"},t={cN:"meta",b:/<\?(php)?|\?>/},r={cN:"string",c:[n.BE,t],v:[{b:'b"',e:'"'},{b:"b'",e:"'"},n.inherit(n.ASM,{i:null}),n.inherit(n.QSM,{i:null})]},u={v:[n.BNM,n.CNM]};return{aliases:["php","php3","php4","php5","php6","php7"],cI:!0,k:"and include_once list abstract global private echo interface as static endswitch array null if endwhile or const for endforeach self var while isset public protected exit foreach throw elseif include __FILE__ empty require_once do xor return parent clone use __CLASS__ __LINE__ else break print eval new catch __METHOD__ case exception default die require __FUNCTION__ enddeclare final try switch continue endfor endif declare unset true false trait goto instanceof insteadof __DIR__ __NAMESPACE__ yield finally",c:[n.HCM,n.C("//","$",{c:[t]}),n.C("/\\*","\\*/",{c:[{cN:"doctag",b:"@[A-Za-z]+"}]}),n.C("__halt_compiler.+?;",!1,{eW:!0,k:"__halt_compiler",l:n.UIR}),{cN:"string",b:/<<<['"]?\w+['"]?$/,e:/^\w+;?$/,c:[n.BE,{cN:"subst",v:[{b:/\$\w+/},{b:/\{\$/,e:/\}/}]}]},t,{cN:"keyword",b:/\$this\b/},i,{b:/(::|->)+[a-zA-Z_\x7f-\xff][a-zA-Z0-9_\x7f-\xff]*/},{cN:"function",bK:"function",e:/[;{]/,eE:!0,i:"\\$|\\[|%",c:[n.UTM,{cN:"params",b:"\\(",e:"\\)",c:["self",i,n.CBCM,r,u]}]},{cN:"class",bK:"class interface",e:"{",eE:!0,i:/[:\(\$"]/,c:[{bK:"extends implements"},n.UTM]},{bK:"namespace",e:";",i:/[\.']/,c:[n.UTM]},{bK:"use",e:";",c:[n.UTM]},{b:"=>"},r,u]}});hljs.registerLanguage("cpp",function(n){var r={cN:"keyword",b:"\\b[a-z\\d_]*_t\\b"},i={cN:"string",v:[{b:'(u8?|U|L)?"',e:'"',i:"\\n",c:[n.BE]},{b:'(u8?|U|L)?R"\\(',e:'\\)"'},{b:"'\\\\?.",e:"'",i:"."}]},u={cN:"number",v:[{b:"\\b(0b[01']+)"},{b:"(-?)\\b([\\d']+(\\.[\\d']*)?|\\.[\\d']+)(u|U|l|L|ul|UL|f|F|b|B)"},{b:"(-?)(\\b0[xX][a-fA-F0-9']+|(\\b[\\d']+(\\.[\\d']*)?|\\.[\\d']+)([eE][-+]?[\\d']+)?)"}],r:0},f={cN:"meta",b:/#\s*[a-z]+\b/,e:/$/,k:{"meta-keyword":"if else elif endif define undef warning error line pragma ifdef ifndef include"},c:[{b:/\\\n/,r:0},n.inherit(i,{cN:"meta-string"}),{cN:"meta-string",b:/<[^\n>]*>/,e:/$/,i:"\\n"},n.CLCM,n.CBCM]},o=n.IR+"\\s*\\(",t={keyword:"int float while private char catch import module export virtual operator sizeof dynamic_cast|10 typedef const_cast|10 const for static_cast|10 union namespace unsigned long volatile static protected bool template mutable if public friend do goto auto void enum else break extern using asm case typeid short reinterpret_cast|10 default double register explicit signed typename try this switch continue inline delete alignof constexpr decltype noexcept static_assert thread_local restrict _Bool complex _Complex _Imaginary atomic_bool atomic_char atomic_schar atomic_uchar atomic_short atomic_ushort atomic_int atomic_uint atomic_long atomic_ulong atomic_llong atomic_ullong new throw return and or not",built_in:"std string cin cout cerr clog stdin stdout stderr stringstream istringstream ostringstream auto_ptr deque list queue stack vector map set bitset multiset multimap unordered_set unordered_map unordered_multiset unordered_multimap array shared_ptr abort abs acos asin atan2 atan calloc ceil cosh cos exit exp fabs floor fmod fprintf fputs free frexp fscanf isalnum isalpha iscntrl isdigit isgraph islower isprint ispunct isspace isupper isxdigit tolower toupper labs ldexp log10 log malloc realloc memchr memcmp memcpy memset modf pow printf putchar puts scanf sinh sin snprintf sprintf sqrt sscanf strcat strchr strcmp strcpy strcspn strlen strncat strncmp strncpy strpbrk strrchr strspn strstr tanh tan vfprintf vprintf vsprintf endl initializer_list unique_ptr",literal:"true false nullptr NULL"},e=[r,n.CLCM,n.CBCM,u,i];return{aliases:["c","cc","h","c++","h++","hpp"],k:t,i:"<\/",c:e.concat([f,{b:"\\b(deque|list|queue|stack|vector|map|set|bitset|multiset|multimap|unordered_map|unordered_set|unordered_multiset|unordered_multimap|array)\\s*<",e:">",k:t,c:["self",r]},{b:n.IR+"::",k:t},{v:[{b:/=/,e:/;/},{b:/\(/,e:/\)/},{bK:"new throw return else",e:/;/}],k:t,c:e.concat([{b:/\(/,e:/\)/,k:t,c:e.concat(["self"]),r:0}]),r:0},{cN:"function",b:"("+n.IR+"[\\*&\\s]+)+"+o,rB:!0,e:/[{;=]/,eE:!0,k:t,i:/[^\w\s\*&]/,c:[{b:o,rB:!0,c:[n.TM],r:0},{cN:"params",b:/\(/,e:/\)/,k:t,r:0,c:[n.CLCM,n.CBCM,i,u,r,{b:/\(/,e:/\)/,k:t,r:0,c:["self",n.CLCM,n.CBCM,i,u,r]}]},n.CLCM,n.CBCM,f]},{cN:"class",bK:"class struct",e:/[{;:]/,c:[{b:/</,e:/>/,c:["self"]},n.TM]}]),exports:{preprocessor:f,strings:i,k:t}}});hljs.registerLanguage("properties",function(n){var t="[ \\t\\f]*",i="("+t+"[:=]"+t+"|[ \\t\\f]+)",u="([^\\\\\\W:= \\t\\f\\n]|\\\\.)+",r="([^\\\\:= \\t\\f\\n]|\\\\.)+",f={e:i,r:0,starts:{cN:"string",e:/$/,r:0,c:[{b:"\\\\\\n"}]}};return{cI:!0,i:/\S/,c:[n.C("^\\s*[!#]","$"),{b:u+i,rB:!0,c:[{cN:"attr",b:u,endsParent:!0,r:0}],starts:f},{b:r+i,rB:!0,r:0,c:[{cN:"meta",b:r,endsParent:!0,r:0}],starts:f},{cN:"attr",r:0,b:r+t+"$"}]}});hljs.registerLanguage("diff",function(){return{aliases:["patch"],c:[{cN:"meta",r:10,v:[{b:/^@@ +\-\d+,\d+ +\+\d+,\d+ +@@$/},{b:/^\*\*\* +\d+,\d+ +\*\*\*\*$/},{b:/^\-\-\- +\d+,\d+ +\-\-\-\-$/}]},{cN:"comment",v:[{b:/Index: /,e:/$/},{b:/={3,}/,e:/$/},{b:/^\-{3}/,e:/$/},{b:/^\*{3} /,e:/$/},{b:/^\+{3}/,e:/$/},{b:/\*{5}/,e:/\*{5}$/}]},{cN:"addition",b:"^\\+",e:"$"},{cN:"deletion",b:"^\\-",e:"$"},{cN:"addition",b:"^\\!",e:"$"}]}});hljs.registerLanguage("apache",function(n){var t={cN:"number",b:"[\\$%]\\d+"};return{aliases:["apacheconf"],cI:!0,c:[n.HCM,{cN:"section",b:"<\/?",e:">"},{cN:"attribute",b:/\w+/,r:0,k:{nomarkup:"order deny allow setenv rewriterule rewriteengine rewritecond documentroot sethandler errordocument loadmodule options header listen serverroot servername"},starts:{e:/$/,r:0,k:{literal:"on off all"},c:[{cN:"meta",b:"\\s\\[",e:"\\]$"},{cN:"variable",b:"[\\$%]\\{",e:"\\}",c:["self",t]},t,n.QSM]}}],i:/\S/}});hljs.registerLanguage("python",function(n){var f={keyword:"and elif is global as in if from raise for except finally print import pass return exec else break not with class assert yield try while continue del or def lambda async await nonlocal|10 None True False",built_in:"Ellipsis NotImplemented"},t={cN:"meta",b:/^(>>>|\.\.\.) /},i={cN:"subst",b:/\{/,e:/\}/,k:f,i:/#/},r={cN:"string",c:[n.BE],v:[{b:/(u|b)?r?'''/,e:/'''/,c:[n.BE,t],r:10},{b:/(u|b)?r?"""/,e:/"""/,c:[n.BE,t],r:10},{b:/(fr|rf|f)'''/,e:/'''/,c:[n.BE,t,i]},{b:/(fr|rf|f)"""/,e:/"""/,c:[n.BE,t,i]},{b:/(u|r|ur)'/,e:/'/,r:10},{b:/(u|r|ur)"/,e:/"/,r:10},{b:/(b|br)'/,e:/'/},{b:/(b|br)"/,e:/"/},{b:/(fr|rf|f)'/,e:/'/,c:[n.BE,i]},{b:/(fr|rf|f)"/,e:/"/,c:[n.BE,i]},n.ASM,n.QSM]},u={cN:"number",r:0,v:[{b:n.BNR+"[lLjJ]?"},{b:"\\b(0o[0-7]+)[lLjJ]?"},{b:n.CNR+"[lLjJ]?"}]},e={cN:"params",b:/\(/,e:/\)/,c:["self",t,u,r]};return i.c=[r,u,t],{aliases:["py","gyp"],k:f,i:/(<\/|->|\?)|=>/,c:[t,u,r,n.HCM,{v:[{cN:"function",bK:"def"},{cN:"class",bK:"class"}],e:/:/,i:/[${=;\n,]/,c:[n.UTM,e,{b:/->/,eW:!0,k:"None"}]},{cN:"meta",b:/^[\t ]*@/,e:/$/},{b:/\b(print|exec)\(/}]}});