<div class="apiDetail">
<div>
	<h2><span>Boolean / Function(treeId, treeNode)</span><span class="path">setting.view.</span>showTitle</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.core</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Set to show or hide the 'title' attribute of node DOM.</p>
			<p class="highlight_red">Please see the <span class="highlight_red">setting.data.key.title</span> attribute</p>
			<p>Default: true</p>
		</div>
	</div>
	<h3>Boolean Format</h3>
	<div class="desc">
	<p> true means: show the 'title' attribute of node DOM.</p>
	<p> false means: hide the 'title' attribute of node DOM.</p>
	<p class="highlight_red">When setting.view.showTitle = true & setting.data.key.title = '', zTree will set the 'setting.data.key.name' attribute to the 'setting.data.key.title'.</p>
	</div>
	<h3>Function Parameter Descriptions</h3>
	<div class="desc">
	<h4><b>treeId</b><span>String</span></h4>
	<p>zTree unique identifier: <b class="highlight_red">treeId</b>.</p>
	<h4 class="topLine"><b>treeNode</b><span>JSON</span></h4>
	<p>JSON data object of the node which need to show title.</p>
	<h4 class="topLine"><b>Return </b><span>Boolean</span></h4>
	<p>Return value is same as 'Boolean Format'</p>
	</div>
	<h3>Examples of setting & function</h3>
	<h4>1. Hide the 'title' attribute of node DOM.</h4>
	<pre xmlns=""><code>var setting = {
	view: {
		showTitle: false
	}
};
......</code></pre>
	<h4>2. Hide the 'title' attribute of node DOM which level=2.</h4>
	<pre xmlns=""><code>function showTitleForTree(treeId, treeNode) {
	return treeNode.level != 2;
};
var setting = {
	view: {
		showTitle: showTitleForTree
	}
};
......</code></pre>
</div>
</div>