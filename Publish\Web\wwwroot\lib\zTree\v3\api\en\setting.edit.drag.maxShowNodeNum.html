<div class="apiDetail">
<div>
	<h2><span>Number</span><span class="path">setting.edit.drag.</span>maxShowNodeNum</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.exedit</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>When dragging more than one sibling node, the floating layer shows the maximum number of nodes. zTree using '...' instead of redundant nodes. It is valid when <span class="highlight_red">[setting.edit.enable = true]</span></p>
			<p>Default: 5</p>
			<p class="highlight_red">Please adjust the value according to needs.</p>
		</div>
	</div>
	<h3>Examples of setting</h3>
	<h4>1. Set the maximum number is 10</h4>
	<pre xmlns=""><code>var setting = {
	edit: {
		enable: true,
		drag: {
			maxShowNodeNum: 10
		}
	}
};
......</code></pre>
</div>
</div>