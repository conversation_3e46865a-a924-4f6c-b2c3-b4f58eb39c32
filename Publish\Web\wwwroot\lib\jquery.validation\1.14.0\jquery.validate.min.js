/*!
 * jQuery Validation Plugin v1.14.0
 *
 * -\-/
 *
 * Copyright (c) 2015 <PERSON><PERSON><PERSON>
 * Released under the MIT license
 */
(function(n){typeof define=="function"&&define.amd?define(["jquery"],n):n(jQuery)})(function(n){n.extend(n.fn,{validate:function(t){if(!this.length){t&&t.debug&&window.console&&console.warn("Nothing selected, can't validate, returning nothing.");return}var i=n.data(this[0],"validator");if(i)return i;if(this.attr("novalidate","novalidate"),i=new n.validator(t,this[0]),n.data(this[0],"validator",i),i.settings.onsubmit){this.on("click.validate",":submit",function(t){i.settings.submitHandler&&(i.submitButton=t.target);n(this).hasClass("cancel")&&(i.cancelSubmit=!0);n(this).attr("formnovalidate")!==undefined&&(i.cancelSubmit=!0)});this.on("submit.validate",function(t){function r(){var u,r;return i.settings.submitHandler?(i.submitButton&&(u=n("<input type='hidden'/>").attr("name",i.submitButton.name).val(n(i.submitButton).val()).appendTo(i.currentForm)),r=i.settings.submitHandler.call(i,i.currentForm,t),i.submitButton&&u.remove(),r!==undefined)?r:!1:!0}return(i.settings.debug&&t.preventDefault(),i.cancelSubmit)?(i.cancelSubmit=!1,r()):i.form()?i.pendingRequest?(i.formSubmitted=!0,!1):r():(i.focusInvalid(),!1)})}return i},valid:function(){var t,i,r;return n(this[0]).is("form")?t=this.validate().form():(r=[],t=!0,i=n(this[0].form).validate(),i&&(this.each(function(){t=i.element(this)&&t;r=r.concat(i.errorList)}),i.errorList=r)),t},rules:function(t,i){var r=this[0],e,s,f,u,o,h;if(t){e=n.data(r.form,"validator").settings;s=e.rules;f=n.validator.staticRules(r);switch(t){case"add":n.extend(f,n.validator.normalizeRule(i));delete f.messages;s[r.name]=f;i.messages&&(e.messages[r.name]=n.extend(e.messages[r.name],i.messages));break;case"remove":return i?(h={},n.each(i.split(/\s/),function(t,i){h[i]=f[i];delete f[i];i==="required"&&n(r).removeAttr("aria-required")}),h):(delete s[r.name],f)}}return u=n.validator.normalizeRules(n.extend({},n.validator.classRules(r),n.validator.attributeRules(r),n.validator.dataRules(r),n.validator.staticRules(r)),r),u.required&&(o=u.required,delete u.required,u=n.extend({required:o},u),n(r).attr("aria-required","true")),u.remote&&(o=u.remote,delete u.remote,u=n.extend(u,{remote:o})),u}});n.extend(n.expr[":"],{blank:function(t){return!n.trim(""+n(t).val())},filled:function(t){return!!n.trim(""+n(t).val())},unchecked:function(t){return!n(t).prop("checked")}});n.validator=function(t,i){this.settings=n.extend(!0,{},n.validator.defaults,t);this.currentForm=i;this.init()};n.validator.format=function(t,i){return arguments.length===1?function(){var i=n.makeArray(arguments);return i.unshift(t),n.validator.format.apply(this,i)}:(arguments.length>2&&i.constructor!==Array&&(i=n.makeArray(arguments).slice(1)),i.constructor!==Array&&(i=[i]),n.each(i,function(n,i){t=t.replace(new RegExp("\\{"+n+"\\}","g"),function(){return i})}),t)};n.extend(n.validator,{defaults:{messages:{},groups:{},rules:{},errorClass:"error",validClass:"valid",errorElement:"label",focusCleanup:!1,focusInvalid:!0,errorContainer:n([]),errorLabelContainer:n([]),onsubmit:!0,ignore:":hidden",ignoreTitle:!1,onfocusin:function(n){this.lastActive=n;this.settings.focusCleanup&&(this.settings.unhighlight&&this.settings.unhighlight.call(this,n,this.settings.errorClass,this.settings.validClass),this.hideThese(this.errorsFor(n)))},onfocusout:function(n){!this.checkable(n)&&(n.name in this.submitted||!this.optional(n))&&this.element(n)},onkeyup:function(t,i){(i.which!==9||this.elementValue(t)!=="")&&n.inArray(i.keyCode,[16,17,18,20,35,36,37,38,39,40,45,144,225])===-1&&(t.name in this.submitted||t===this.lastElement)&&this.element(t)},onclick:function(n){n.name in this.submitted?this.element(n):n.parentNode.name in this.submitted&&this.element(n.parentNode)},highlight:function(t,i,r){t.type==="radio"?this.findByName(t.name).addClass(i).removeClass(r):n(t).addClass(i).removeClass(r)},unhighlight:function(t,i,r){t.type==="radio"?this.findByName(t.name).removeClass(i).addClass(r):n(t).removeClass(i).addClass(r)}},setDefaults:function(t){n.extend(n.validator.defaults,t)},messages:{required:"This field is required.",remote:"Please fix this field.",email:"Please enter a valid email address.",url:"Please enter a valid URL.",date:"Please enter a valid date.",dateISO:"Please enter a valid date ( ISO ).",number:"Please enter a valid number.",digits:"Please enter only digits.",creditcard:"Please enter a valid credit card number.",equalTo:"Please enter the same value again.",maxlength:n.validator.format("Please enter no more than {0} characters."),minlength:n.validator.format("Please enter at least {0} characters."),rangelength:n.validator.format("Please enter a value between {0} and {1} characters long."),range:n.validator.format("Please enter a value between {0} and {1}."),max:n.validator.format("Please enter a value less than or equal to {0}."),min:n.validator.format("Please enter a value greater than or equal to {0}.")},autoCreateRanges:!1,prototype:{init:function(){function i(t){var r=n.data(this.form,"validator"),u="on"+t.type.replace(/^validate/,""),i=r.settings;i[u]&&!n(this).is(i.ignore)&&i[u].call(r,this,t)}this.labelContainer=n(this.settings.errorLabelContainer);this.errorContext=this.labelContainer.length&&this.labelContainer||n(this.currentForm);this.containers=n(this.settings.errorContainer).add(this.settings.errorLabelContainer);this.submitted={};this.valueCache={};this.pendingRequest=0;this.pending={};this.invalid={};this.reset();var r=this.groups={},t;n.each(this.settings.groups,function(t,i){typeof i=="string"&&(i=i.split(/\s/));n.each(i,function(n,i){r[i]=t})});t=this.settings.rules;n.each(t,function(i,r){t[i]=n.validator.normalizeRule(r)});n(this.currentForm).on("focusin.validate focusout.validate keyup.validate",":text, [type='password'], [type='file'], select, textarea, [type='number'], [type='search'], [type='tel'], [type='url'], [type='email'], [type='datetime'], [type='date'], [type='month'], [type='week'], [type='time'], [type='datetime-local'], [type='range'], [type='color'], [type='radio'], [type='checkbox']",i).on("click.validate","select, option, [type='radio'], [type='checkbox']",i);if(this.settings.invalidHandler)n(this.currentForm).on("invalid-form.validate",this.settings.invalidHandler);n(this.currentForm).find("[required], [data-rule-required], .required").attr("aria-required","true")},form:function(){return this.checkForm(),n.extend(this.submitted,this.errorMap),this.invalid=n.extend({},this.errorMap),this.valid()||n(this.currentForm).triggerHandler("invalid-form",[this]),this.showErrors(),this.valid()},checkForm:function(){this.prepareForm();for(var n=0,t=this.currentElements=this.elements();t[n];n++)this.check(t[n]);return this.valid()},element:function(t){var u=this.clean(t),i=this.validationTargetFor(u),r=!0;return this.lastElement=i,i===undefined?delete this.invalid[u.name]:(this.prepareElement(i),this.currentElements=n(i),r=this.check(i)!==!1,r?delete this.invalid[i.name]:this.invalid[i.name]=!0),n(t).attr("aria-invalid",!r),this.numberOfInvalids()||(this.toHide=this.toHide.add(this.containers)),this.showErrors(),r},showErrors:function(t){if(t){n.extend(this.errorMap,t);this.errorList=[];for(var i in t)this.errorList.push({message:t[i],element:this.findByName(i)[0]});this.successList=n.grep(this.successList,function(n){return!(n.name in t)})}this.settings.showErrors?this.settings.showErrors.call(this,this.errorMap,this.errorList):this.defaultShowErrors()},resetForm:function(){n.fn.resetForm&&n(this.currentForm).resetForm();this.submitted={};this.lastElement=null;this.prepareForm();this.hideErrors();var t,i=this.elements().removeData("previousValue").removeAttr("aria-invalid");if(this.settings.unhighlight)for(t=0;i[t];t++)this.settings.unhighlight.call(this,i[t],this.settings.errorClass,"");else i.removeClass(this.settings.errorClass)},numberOfInvalids:function(){return this.objectLength(this.invalid)},objectLength:function(n){var t=0;for(var i in n)t++;return t},hideErrors:function(){this.hideThese(this.toHide)},hideThese:function(n){n.not(this.containers).text("");this.addWrapper(n).hide()},valid:function(){return this.size()===0},size:function(){return this.errorList.length},focusInvalid:function(){if(this.settings.focusInvalid)try{n(this.findLastActive()||this.errorList.length&&this.errorList[0].element||[]).filter(":visible").focus().trigger("focusin")}catch(t){}},findLastActive:function(){var t=this.lastActive;return t&&n.grep(this.errorList,function(n){return n.element.name===t.name}).length===1&&t},elements:function(){var t=this,i={};return n(this.currentForm).find("input, select, textarea").not(":submit, :reset, :image, :disabled").not(this.settings.ignore).filter(function(){return(!this.name&&t.settings.debug&&window.console&&console.error("%o has no name assigned",this),this.name in i||!t.objectLength(n(this).rules()))?!1:(i[this.name]=!0,!0)})},clean:function(t){return n(t)[0]},errors:function(){var t=this.settings.errorClass.split(" ").join(".");return n(this.settings.errorElement+"."+t,this.errorContext)},reset:function(){this.successList=[];this.errorList=[];this.errorMap={};this.toShow=n([]);this.toHide=n([]);this.currentElements=n([])},prepareForm:function(){this.reset();this.toHide=this.errors().add(this.containers)},prepareElement:function(n){this.reset();this.toHide=this.errorsFor(n)},elementValue:function(t){var i,u=n(t),r=t.type;return r==="radio"||r==="checkbox"?this.findByName(t.name).filter(":checked").val():r==="number"&&typeof t.validity!="undefined"?t.validity.badInput?!1:u.val():(i=u.val(),typeof i=="string")?i.replace(/\r/g,""):i},check:function(t){t=this.validationTargetFor(this.clean(t));var r=n(t).rules(),s=n.map(r,function(n,t){return t}).length,o=!1,h=this.elementValue(t),u,f,i;for(f in r){i={method:f,parameters:r[f]};try{if(u=n.validator.methods[f].call(this,h,t,i.parameters),u==="dependency-mismatch"&&s===1){o=!0;continue}if(o=!1,u==="pending"){this.toHide=this.toHide.not(this.errorsFor(t));return}if(!u)return this.formatAndAdd(t,i),!1}catch(e){this.settings.debug&&window.console&&console.log("Exception occurred when checking element "+t.id+", check the '"+i.method+"' method.",e);e instanceof TypeError&&(e.message+=".  Exception occurred when checking element "+t.id+", check the '"+i.method+"' method.");throw e;}}if(!o)return this.objectLength(r)&&this.successList.push(t),!0},customDataMessage:function(t,i){return n(t).data("msg"+i.charAt(0).toUpperCase()+i.substring(1).toLowerCase())||n(t).data("msg")},customMessage:function(n,t){var i=this.settings.messages[n];return i&&(i.constructor===String?i:i[t])},findDefined:function(){for(var n=0;n<arguments.length;n++)if(arguments[n]!==undefined)return arguments[n];return undefined},defaultMessage:function(t,i){return this.findDefined(this.customMessage(t.name,i),this.customDataMessage(t,i),!this.settings.ignoreTitle&&t.title||undefined,n.validator.messages[i],"<strong>Warning: No message defined for "+t.name+"<\/strong>")},formatAndAdd:function(t,i){var r=this.defaultMessage(t,i.method),u=/\$?\{(\d+)\}/g;typeof r=="function"?r=r.call(this,i.parameters,t):u.test(r)&&(r=n.validator.format(r.replace(u,"{$1}"),i.parameters));this.errorList.push({message:r,element:t,method:i.method});this.errorMap[t.name]=r;this.submitted[t.name]=r},addWrapper:function(n){return this.settings.wrapper&&(n=n.add(n.parent(this.settings.wrapper))),n},defaultShowErrors:function(){for(var i,t,n=0;this.errorList[n];n++)t=this.errorList[n],this.settings.highlight&&this.settings.highlight.call(this,t.element,this.settings.errorClass,this.settings.validClass),this.showLabel(t.element,t.message);if(this.errorList.length&&(this.toShow=this.toShow.add(this.containers)),this.settings.success)for(n=0;this.successList[n];n++)this.showLabel(this.successList[n]);if(this.settings.unhighlight)for(n=0,i=this.validElements();i[n];n++)this.settings.unhighlight.call(this,i[n],this.settings.errorClass,this.settings.validClass);this.toHide=this.toHide.not(this.toShow);this.hideErrors();this.addWrapper(this.toShow).show()},validElements:function(){return this.currentElements.not(this.invalidElements())},invalidElements:function(){return n(this.errorList).map(function(){return this.element})},showLabel:function(t,i){var u,o,e,r=this.errorsFor(t),s=this.idOrName(t),f=n(t).attr("aria-describedby");r.length?(r.removeClass(this.settings.validClass).addClass(this.settings.errorClass),r.html(i)):(r=n("<"+this.settings.errorElement+">").attr("id",s+"-error").addClass(this.settings.errorClass).html(i||""),u=r,this.settings.wrapper&&(u=r.hide().show().wrap("<"+this.settings.wrapper+"/>").parent()),this.labelContainer.length?this.labelContainer.append(u):this.settings.errorPlacement?this.settings.errorPlacement(u,n(t)):u.insertAfter(t),r.is("label")?r.attr("for",s):r.parents("label[for='"+s+"']").length===0&&(e=r.attr("id").replace(/(:|\.|\[|\]|\$)/g,"\\$1"),f?f.match(new RegExp("\\b"+e+"\\b"))||(f+=" "+e):f=e,n(t).attr("aria-describedby",f),o=this.groups[t.name],o&&n.each(this.groups,function(t,i){i===o&&n("[name='"+t+"']",this.currentForm).attr("aria-describedby",r.attr("id"))})));!i&&this.settings.success&&(r.text(""),typeof this.settings.success=="string"?r.addClass(this.settings.success):this.settings.success(r,t));this.toShow=this.toShow.add(r)},errorsFor:function(t){var r=this.idOrName(t),u=n(t).attr("aria-describedby"),i="label[for='"+r+"'], label[for='"+r+"'] *";return u&&(i=i+", #"+u.replace(/\s+/g,", #")),this.errors().filter(i)},idOrName:function(n){return this.groups[n.name]||(this.checkable(n)?n.name:n.id||n.name)},validationTargetFor:function(t){return this.checkable(t)&&(t=this.findByName(t.name)),n(t).not(this.settings.ignore)[0]},checkable:function(n){return/radio|checkbox/i.test(n.type)},findByName:function(t){return n(this.currentForm).find("[name='"+t+"']")},getLength:function(t,i){switch(i.nodeName.toLowerCase()){case"select":return n("option:selected",i).length;case"input":if(this.checkable(i))return this.findByName(i.name).filter(":checked").length}return t.length},depend:function(n,t){return this.dependTypes[typeof n]?this.dependTypes[typeof n](n,t):!0},dependTypes:{boolean:function(n){return n},string:function(t,i){return!!n(t,i.form).length},"function":function(n,t){return n(t)}},optional:function(t){var i=this.elementValue(t);return!n.validator.methods.required.call(this,i,t)&&"dependency-mismatch"},startRequest:function(n){this.pending[n.name]||(this.pendingRequest++,this.pending[n.name]=!0)},stopRequest:function(t,i){this.pendingRequest--;this.pendingRequest<0&&(this.pendingRequest=0);delete this.pending[t.name];i&&this.pendingRequest===0&&this.formSubmitted&&this.form()?(n(this.currentForm).submit(),this.formSubmitted=!1):!i&&this.pendingRequest===0&&this.formSubmitted&&(n(this.currentForm).triggerHandler("invalid-form",[this]),this.formSubmitted=!1)},previousValue:function(t){return n.data(t,"previousValue")||n.data(t,"previousValue",{old:null,valid:!0,message:this.defaultMessage(t,"remote")})},destroy:function(){this.resetForm();n(this.currentForm).off(".validate").removeData("validator")}},classRuleSettings:{required:{required:!0},email:{email:!0},url:{url:!0},date:{date:!0},dateISO:{dateISO:!0},number:{number:!0},digits:{digits:!0},creditcard:{creditcard:!0}},addClassRules:function(t,i){t.constructor===String?this.classRuleSettings[t]=i:n.extend(this.classRuleSettings,t)},classRules:function(t){var i={},r=n(t).attr("class");return r&&n.each(r.split(" "),function(){this in n.validator.classRuleSettings&&n.extend(i,n.validator.classRuleSettings[this])}),i},normalizeAttributeRule:function(n,t,i,r){/min|max/.test(i)&&(t===null||/number|range|text/.test(t))&&(r=Number(r),isNaN(r)&&(r=undefined));r||r===0?n[i]=r:t===i&&t!=="range"&&(n[i]=!0)},attributeRules:function(t){var r={},f=n(t),e=t.getAttribute("type"),u,i;for(u in n.validator.methods)u==="required"?(i=t.getAttribute(u),i===""&&(i=!0),i=!!i):i=f.attr(u),this.normalizeAttributeRule(r,e,u,i);return r.maxlength&&/-1|2147483647|524288/.test(r.maxlength)&&delete r.maxlength,r},dataRules:function(t){var r={},f=n(t),e=t.getAttribute("type"),i,u;for(i in n.validator.methods)u=f.data("rule"+i.charAt(0).toUpperCase()+i.substring(1).toLowerCase()),this.normalizeAttributeRule(r,e,i,u);return r},staticRules:function(t){var i={},r=n.data(t.form,"validator");return r.settings.rules&&(i=n.validator.normalizeRule(r.settings.rules[t.name])||{}),i},normalizeRules:function(t,i){return n.each(t,function(r,u){if(u===!1){delete t[r];return}if(u.param||u.depends){var f=!0;switch(typeof u.depends){case"string":f=!!n(u.depends,i.form).length;break;case"function":f=u.depends.call(i,i)}f?t[r]=u.param!==undefined?u.param:!0:delete t[r]}}),n.each(t,function(r,u){t[r]=n.isFunction(u)?u(i):u}),n.each(["minlength","maxlength"],function(){t[this]&&(t[this]=Number(t[this]))}),n.each(["rangelength","range"],function(){var i;t[this]&&(n.isArray(t[this])?t[this]=[Number(t[this][0]),Number(t[this][1])]:typeof t[this]=="string"&&(i=t[this].replace(/[\[\]]/g,"").split(/[\s,]+/),t[this]=[Number(i[0]),Number(i[1])]))}),n.validator.autoCreateRanges&&(t.min!=null&&t.max!=null&&(t.range=[t.min,t.max],delete t.min,delete t.max),t.minlength!=null&&t.maxlength!=null&&(t.rangelength=[t.minlength,t.maxlength],delete t.minlength,delete t.maxlength)),t},normalizeRule:function(t){if(typeof t=="string"){var i={};n.each(t.split(/\s/),function(){i[this]=!0});t=i}return t},addMethod:function(t,i,r){n.validator.methods[t]=i;n.validator.messages[t]=r!==undefined?r:n.validator.messages[t];i.length<3&&n.validator.addClassRules(t,n.validator.normalizeRule(t))},methods:{required:function(t,i,r){if(!this.depend(r,i))return"dependency-mismatch";if(i.nodeName.toLowerCase()==="select"){var u=n(i).val();return u&&u.length>0}return this.checkable(i)?this.getLength(t,i)>0:t.length>0},email:function(n,t){return this.optional(t)||/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(n)},url:function(n,t){return this.optional(t)||/^(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})).?)(?::\d{2,5})?(?:[/?#]\S*)?$/i.test(n)},date:function(n,t){return this.optional(t)||!/Invalid|NaN/.test(new Date(n).toString())},dateISO:function(n,t){return this.optional(t)||/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(n)},number:function(n,t){return this.optional(t)||/^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(n)},digits:function(n,t){return this.optional(t)||/^\d+$/.test(n)},creditcard:function(n,t){if(this.optional(t))return"dependency-mismatch";if(/[^0-9 \-]+/.test(n))return!1;var f=0,i=0,u=!1,r,e;if(n=n.replace(/\D/g,""),n.length<13||n.length>19)return!1;for(r=n.length-1;r>=0;r--)e=n.charAt(r),i=parseInt(e,10),u&&(i*=2)>9&&(i-=9),f+=i,u=!u;return f%10==0},minlength:function(t,i,r){var u=n.isArray(t)?t.length:this.getLength(t,i);return this.optional(i)||u>=r},maxlength:function(t,i,r){var u=n.isArray(t)?t.length:this.getLength(t,i);return this.optional(i)||u<=r},rangelength:function(t,i,r){var u=n.isArray(t)?t.length:this.getLength(t,i);return this.optional(i)||u>=r[0]&&u<=r[1]},min:function(n,t,i){return this.optional(t)||n>=i},max:function(n,t,i){return this.optional(t)||n<=i},range:function(n,t,i){return this.optional(t)||n>=i[0]&&n<=i[1]},equalTo:function(t,i,r){var u=n(r);if(this.settings.onfocusout)u.off(".validate-equalTo").on("blur.validate-equalTo",function(){n(i).valid()});return t===u.val()},remote:function(t,i,r){if(this.optional(i))return"dependency-mismatch";var f=this.previousValue(i),u,e;return(this.settings.messages[i.name]||(this.settings.messages[i.name]={}),f.originalMessage=this.settings.messages[i.name].remote,this.settings.messages[i.name].remote=f.message,r=typeof r=="string"&&{url:r}||r,f.old===t)?f.valid:(f.old=t,u=this,this.startRequest(i),e={},e[i.name]=t,n.ajax(n.extend(!0,{mode:"abort",port:"validate"+i.name,dataType:"json",data:e,context:u.currentForm,success:function(r){var o=r===!0||r==="true",s,e,h;u.settings.messages[i.name].remote=f.originalMessage;o?(h=u.formSubmitted,u.prepareElement(i),u.formSubmitted=h,u.successList.push(i),delete u.invalid[i.name],u.showErrors()):(s={},e=r||u.defaultMessage(i,"remote"),s[i.name]=f.message=n.isFunction(e)?e(t):e,u.invalid[i.name]=!0,u.showErrors(s));f.valid=o;u.stopRequest(i,o)}},r)),"pending")}}});var t={},i;n.ajaxPrefilter?n.ajaxPrefilter(function(n,i,r){var u=n.port;n.mode==="abort"&&(t[u]&&t[u].abort(),t[u]=r)}):(i=n.ajax,n.ajax=function(r){var f=("mode"in r?r:n.ajaxSettings).mode,u=("port"in r?r:n.ajaxSettings).port;return f==="abort"?(t[u]&&t[u].abort(),t[u]=i.apply(this,arguments),t[u]):i.apply(this,arguments)})});$(document).ready(function(){$.validator.setDefaults({submitHandler:function(n){n.submit()}});jQuery.validator.addMethod("isLoginpass",function(n,t){return this.optional(t)||/^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,30}$/.test(n)},"密码至少8位，必须包含大写字母，小写字母，数字，特殊字符四种中的三种");jQuery.validator.addMethod("isQQ",function(n,t){var i=n.length;return this.optional(t)||/^[1-9]*[1-9][0-9]*$/.test(n)},"QQ号码输入有误");jQuery.validator.addMethod("isPassWord",function(n,t){var i=n.length;return this.optional(t)||/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,20}$/.test(n)},"密码至少8位，必须包含字母数字");jQuery.validator.addMethod("isPhone",function(n,t){var i=n.length;return this.optional(t)||i==11&&/^1\d{10}$/.test(n)},"请填写正确的11位手机号");jQuery.validator.addMethod("isTel",function(n,t){return this.optional(t)||/^(0\d{2,3}-)?\d{7,8}$/g.test(n)},"请填写正确的座机号码");jQuery.validator.addMethod("isName",function(n,t){return this.optional(t)||/^[\u4e00-\u9fa5]{2,6}$/.test(n)},"姓名只能用汉字,长度2-4位");jQuery.validator.addMethod("isUserName",function(n,t){return this.optional(t)||/^[a-zA-Z0-9]{2,13}$/.test(n)},"请输入数字或者字母,不包含特殊字符");jQuery.validator.addMethod("isIdentity",function(n,t){return this.optional(t)||/^(\d{15}$|^\d{18}$|^\d{17}(\d|X))$/.test(n)},"请输入正确的15或18位身份证号,末尾为大写X");jQuery.validator.addMethod("isBirth",function(n,t){return this.optional(t)||/^(19|20)\d{2}-(1[0-2]|0?[1-9])-(0?[1-9]|[1-2][0-9]|3[0-1])$/.test(n)},"出生日期格式示例2000-01-01");jQuery.validator.addMethod("isdiff",function(){var n=$("#pwdOld").val(),t=$("#pwdNew").val();return n==t?!1:!0});jQuery.validator.addMethod("issame",function(){var n=$("#confirm_password").val(),t=$("#pwdNew").val();return n==t?!0:!1});jQuery.validator.addMethod("thanMinValue",function(n,t,i){return n>i},$.validator.format("输入值必须大于{0}"));$("#basicInfoForm").validate({errorElement:"span",errorClass:"help-block error-mes",rules:{name:{required:!0,isName:!0},sex:"required",birth:"required",mobile:{required:!0,isPhone:!0},email:{required:!0,email:!0}},messages:{name:{required:"请输入中文姓名",isName:"姓名只能为汉字"},sex:{required:"请输入性别"},birth:{required:"请输入出生年月"},mobile:{required:"请输入手机号",isPhone:"请填写正确的11位手机号"},email:{required:"请输入邮箱",email:"请填写正确的邮箱格式"}},errorPlacement:function(n,t){t.next().remove();t.closest(".gg-formGroup").append(n)},highlight:function(n){$(n).closest(".gg-formGroup").addClass("has-error has-feedback")},success:function(n){var t=n.closest(".gg-formGroup").find("input");t.next().remove();n.closest(".gg-formGroup").removeClass("has-error").addClass("has-feedback has-success");n.remove()},submitHandler:function(){alert("保存成功!")}});$("#modifyPwd").validate({onfocusout:function(n){$(n).valid()},debug:!1,onkeyup:!1,rules:{pwdOld:{required:!0,minlength:6},pwdNew:{required:!0,minlength:6,isdiff:!0},confirm_password:{required:!0,minlength:6,issame:!0}},messages:{pwdOld:{required:"必填",minlength:$.validator.format("密码长度要大于8")},pwdNew:{required:"必填",minlength:$.validator.format("密码长度要大于8"),isdiff:"原密码与新密码不能重复"},confirm_password:{required:"必填",minlength:$.validator.format("密码长度要大于8"),issame:"新密码要与确认新密码一致"}},errorElement:"mes",errorClass:"gg-star",errorPlacement:function(n,t){t.closest(".gg-formGroup").append(n)}})}),function(n){typeof define=="function"&&define.amd?define(["jquery","../jquery.validate"],n):n(jQuery)}(function(n){n.extend(n.validator.messages,{required:"这是必填字段",remote:"请修正此字段",email:"请输入有效的电子邮件地址",url:"请输入有效的网址",date:"请输入有效的日期",dateISO:"请输入有效的日期 (YYYY-MM-DD)",number:"请输入有效的数字",digits:"只能输入数字",creditcard:"请输入有效的信用卡号码",equalTo:"你的输入不相同",extension:"请输入有效的后缀",maxlength:n.validator.format("最多可以输入 {0} 个字符"),minlength:n.validator.format("最少要输入 {0} 个字符"),rangelength:n.validator.format("请输入长度在 {0} 到 {1} 之间的字符串"),range:n.validator.format("请输入范围在 {0} 到 {1} 之间的数值"),max:n.validator.format("请输入不大于 {0} 的数值"),min:n.validator.format("请输入不小于 {0} 的数值")})});