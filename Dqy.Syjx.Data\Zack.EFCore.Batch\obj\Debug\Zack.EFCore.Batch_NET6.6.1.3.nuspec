﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>Zack.EFCore.Batch_NET6</id>
    <version>6.1.3</version>
    <authors><PERSON>(杨中科)</authors>
    <projectUrl>https://github.com/yangzhongke/Zack.EFCore.Batch</projectUrl>
    <description>Using this library, Entity Framework Core users can delete or update multiple records from a LINQ Query in a SQL statement without loading entities. This libary supports Entity Framework Core 6.x.
This package is a basic support package, so it can not be used directly. 
Please use the database-specific packages, like  Zack.EFCore.Batch.Npgsql, Zack.EFCore.Batch.MySQL.Pomelo, Zack.EFCore.Batch.Npgsql, etc.</description>
    <repository url="https://github.com/yangzhongke/Zack.EFCore.Batch" />
    <dependencies>
      <group targetFramework="net6.0">
        <dependency id="Microsoft.EntityFrameworkCore.Relational" version="6.0.19" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Data\Zack.EFCore.Batch\bin\Debug\net6.0\Zack.EFCore.Batch_NET6.dll" target="lib\net6.0\Zack.EFCore.Batch_NET6.dll" />
  </files>
</package>