<div class="apiDetail">
<div>
	<h2><span>String</span><span class="path">setting.data.key.</span>children</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.core</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>The node data's attribute to save the child nodes.</p>
			<p>Default: "children"</p>
		</div>
	</div>
	<h3>Examples of setting</h3>
	<h4>1. Set the 'nodes' attribute to save the child nodes.</h4>
	<pre xmlns=""><code>var setting = {
	data: {
		key: {
			children: "nodes"
		}
	}
};
......</code></pre>
</div>
</div>