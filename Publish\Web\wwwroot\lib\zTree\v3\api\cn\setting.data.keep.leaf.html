<div class="apiDetail">
<div>
	<h2><span>Bo<PERSON>an</span><span class="path">setting.data.keep.</span>leaf</h2>
	<h3>概述<span class="h3_info">[ 依赖 <span class="highlight_green">jquery.ztree.core</span> 核心 js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>zTree 的节点叶子节点属性锁，是否始终保持 isParent = false</p>
			<p>默认值：false</p>
		</div>
	</div>
	<h3>Boolean 格式说明</h3>
	<div class="desc">
	<p> true / false 分别表示 锁定 / 不锁定 叶子节点属性</p>
	<p class="highlight_red">如果设置为 true，则所有 isParent = false 的节点，都无法添加子节点。</p>
	</div>
	<h3>setting 举例</h3>
	<h4>1. 需要锁定叶子节点状态</h4>
	<pre xmlns=""><code>var setting = {
	data: {
		keep: {
			leaf: true
		}
	}
};
......</code></pre>
</div>
</div>