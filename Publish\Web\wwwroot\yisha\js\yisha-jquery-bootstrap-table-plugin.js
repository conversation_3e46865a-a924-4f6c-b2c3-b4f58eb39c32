﻿; (function ($) {
    "use strict";
    $.fn.ysTable = function (option, param) {
        //获取默认参数
        const originalOnLoadSuccess = $.fn.ysTable.defaults.onLoadSuccess;;

        //如果是调用方法
        if (typeof option == 'string') {
            return $.fn.ysTable.methods[option](this, param);
        }

        // 合并配置前保存用户自定义回调
        const userOnLoadSuccess = option ? option.onLoadSuccess : null;
        
        var _option = $.extend({}, $.fn.ysTable.defaults, option || {});
        _option.onLoadSuccess = function (obj) {
            // 先执行插件核心逻辑
            originalOnLoadSuccess.call(this, obj);

            // 再执行用户自定义回调（如果存在）
            if (typeof userOnLoadSuccess === 'function') {
                userOnLoadSuccess.call(this, obj);
            }
        };

        var target = $(this);
        target.bootstrapTable(_option);
        return target;
    };

    $.fn.ysTable.methods = {
        search: function (target) {
            // 从第一页开始
            target.bootstrapTable('refresh', { pageNumber: 1 });
        },
        refresh: function (target) {
            // 从第当前页开始
            target.bootstrapTable('refresh');
        },
        getPagination: function (target, params) {
            var pagination = {
                pageSize: params.limit,                         //页面大小
                pageIndex: (params.offset / params.limit) + 1,   //页码
                sort: params.sort,      //排序列名
                sortType: params.order //排位命令（desc，asc）
            };
            return pagination;
        }
    };

    $.fn.ysTable.defaults = {
        method: 'GET',                      // 请求方式（*）
        toolbar: '#toolbar',                // 工具按钮用哪个容器
        striped: true,                      // 是否显示行间隔色
        cache: false,                       // 是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
        pagination: true,                   // 是否显示分页（*）
        sortable: true,                     // 是否启用排序
        sortStable: true,                   // 设置为 true 将获得稳定的排序
        sortName: 'Id',                     // 排序列名称
        sortOrder: "desc",                  // 排序方式
        sidePagination: "server",           // 分页方式：client客户端分页，server服务端分页（*）
        pageNumber: 1,                      // 初始化加载第一页，默认第一页,并记录
        pageSize: 10,                       // 每页的记录行数（*）
        pageList: "10, 25, 50, 100, 200",        // 可供选择的每页的行数（*）
        search: false,                      // 是否显示表格搜索
        strictSearch: true,
        showColumns: true,                  // 是否显示所有的列（选择显示的列）
        showRefresh: true,                  // 是否显示刷新按钮
        showToggle: false,                   // 是否显示详细视图和列表视图的切换按钮
        minimumCountColumns: 2,             // 最少允许的列数
        clickToSelect: true,                // 是否启用点击选中行
        height: undefined,                  // 行高，如果没有设置height属性，表格自动根据记录条数觉得表格高度
        uniqueId: "Id",                     // 每一行的唯一标识，一般为主键列
        cardView: false,                    // 是否显示详细视图
        detailView: false,                  // 是否显示父子表
        totalField: 'Total',
        dataField: 'Data',
        columns: [],
        queryParams: {},

        //★1000101 ... 原有其他配置保持不变 ...添加按钮配置，添加设置。
        showExportSetBtn: false,            // ★ 新增属性：是否显示设置按钮（默认为false）
        showExportSetCode: '100000',        // ★ 新增属性：编码
        exportSetMultipleCode: [],        // ★ 多个导出，存储 多个编码  例子：  exportSetMultipleCode:[{BasePageCode:"101008",Title:"按学校明细导出"},{BasePageCode:"101009",Title:"按仪器导出"}],
        exportSetBtnText: '设置',           // ★ 新增属性：设置按钮文本
        exportSetBtnIcon: 'fa fa-cog',      // ★ 新增属性：设置按钮图标
        onExportSetClick: null,             // ★ 新增属性：设置按钮点击回调

        onLoadSuccess: function (obj) {
            // ★1000101 新增：初始化设置按钮
            if (this.showExportSetBtn) {
                initSettingsButton.call(this);
            }

            if (obj && obj.Tag != 1) {
                ys.alertError(obj.Message);
            }
        },
        onLoadError: function (status, s) {
            if (s.statusText != "abort") {
                ys.alertError("数据加载失败！");
            }
        }
    };

    // ★1000101 新增：初始化设置按钮的函数
    function initSettingsButton() {
        const $toolbar = $(this.toolbar);
        const btnId = 'settings-btn-export';

        // 避免重复创建按钮
        if ($toolbar.parent().parent().find('#' + btnId).length) return;

        // 创建设置按钮
        const $btn = $(`<div class="columns columns-right btn-group pull-right">
            <button id="${btnId}" class="btn btn-primary ys-settings-btn" 
                    style="margin-left: 10px">
                <i class="${this.exportSetBtnIcon}"></i>
                ${this.exportSetBtnText}
            </button></div>
        `);
        if ($("#toolbar").length) {
            $("#toolbar").parent().parent().append($btn);
        } else {
            // 添加到工具栏
            $toolbar.parent().parent().append($btn);
        }

        // 绑定点击事件
        $btn.off('click').on('click', () => {
            if (typeof this.onExportSetClick === 'function') {
                this.onExportSetClick.call(this);
            } else {
               // defaultSettingsAction.call(this);
                defaultSetExportSelect.call(this);
            }
        });
    }
    //判断是多个导出还是单个
    function defaultSetExportSelect() {
        if (this.exportSetMultipleCode && this.exportSetMultipleCode.length > 1) {
            var htmlcol = getRadioSelectHtml(this.exportSetMultipleCode);
            layer.open({ 
                type: 1,
                area: '300px',
                title: '选择需要导出的表格'
                , content: htmlcol
                , maxWidth: 500
                , btn: ['确认','取消']
                , yes: function (index, layero) {
                    //按钮【按钮一】的回调
                    var pagecode = $('input[name="inputExportSelect_checkbox"]:checked').val();
                    loadShowCloumn(pagecode);
                    layer.close(index);
                }, cancel: function (index, layero) {
                    layer.close(index);
                    return false;
                }
                , btnAlign: 'c'
            });
        } else {
            defaultSettingsAction.call(this);
        }
    }

    //多选拼接获取html
    function getRadioSelectHtml(dataArr) {
        let html = ' <div style="padding:7px 20px;" class="exportradioselect">';
        if (dataArr && dataArr.length > 0) {
            dataArr.map(function (item, i) {
                html += '<label class="iradio-box" style="position: initial;display:inline-block;padding:10px 15px"><div class="iradio-blue" style="vertical-align:bottom;position: inherit;display: inline-block;"><input name="inputExportSelect_checkbox" type="radio" value="' + item.BasePageCode +'" style="position: absolute; opacity: 0;"  onchange="exportSelectChange(this)"/></div>' + item.Title +'</label>';
            });
        }
        html += '</div>';
        return html;
    }
    // ★ 新增：默认设置操作
    function defaultSettingsAction() {
        // 示例：获取当前表格数据
        var pageCode = this.showExportSetCode;
        loadShowCloumn(pageCode);
    }

    //获取需要展示的列和自定义列的加载
    function loadShowCloumn(pageCode) {
        ys.ajax({
            url: '/BusinessManage/ExportColumnConfig/GetFormJson?code=' + pageCode,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    // 处理配置数据
                    const defaultArr = typeof obj.ExtendData === 'string' ?
                        JSON.parse(obj.ExtendData) : obj.ExtendData;

                    let selectfields = '';
                    if (obj.Data) {
                        const data = typeof obj.Data === 'string' ?
                            JSON.parse(obj.Data) : obj.Data;
                        selectfields = data.ColumFields || '';
                    }

                    // 构建配置界面
                    let htmlcol = '<div class="export-column-config">';
                    if (defaultArr && defaultArr.length) {
                        htmlcol += '<ul class="columns exportul" style="padding:1px 20px">';
                        defaultArr.forEach(item => {
                            if (item.title === '操作') return;
                            let checked = 'checked="checked"';
                            if (selectfields.length > 0) {
                                checked = selectfields.includes(item.Field) ?
                                    'checked="checked"' : '';
                            }

                            htmlcol += `
                            <li>
                                <label>
                                    <input type="checkbox" 
                                           data-field="${item.Field}" 
                                           value="${item.Field}" 
                                           ${checked}>
                                    ${item.Name}
                                </label>
                            </li>
                        `;
                        });
                        htmlcol += '</ul>';
                    }
                    htmlcol += '</div>';

                    layer.open({
                        /* title: false*/
                        type: 1,
                        area: '300px',
                        title: '设置导出列'
                        , content: htmlcol
                        , maxWidth: 500
                        /*      , closeBtn: 0 //不展示关闭*/
                        , btn: ['提交']
                        , yes: function (index, layero) {
                            //按钮【按钮一】的回调
                            /*保存*/
                            var columFields = $('.exportul li input:checked').map(function () { return this.value }).get().join(',');
                            saveSetShowCloumn(pageCode, columFields, index)
                           
                        }, cancel: function (index, layero) {
                            layer.close(index)
                            return false;
                        }
                        , btnAlign: 'c'
                    });
                } else {
                    ys.msgError(obj.Message);
                }
            }
        }); 
    }
    //保存设置好的列
    function saveSetShowCloumn(pageCode,columFields,index) {
        var postData = { PageCode: pageCode, ColumFields: columFields, PageTitle: "", PagePath: "", Remark: "" };
        ys.ajax({
            url: '/BusinessManage/ExportColumnConfig/SaveFormJson',
            type: 'post',
            data: postData,
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    layer.close(index)
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }
})(window.jQuery);

//选择导出配置列表的事件
function exportSelectChange(obj) {
    if ($(obj).prop("checked")) {
        $(".exportradioselect .iradio-blue").removeClass("checked");
        $(obj).parent().addClass("checked");
    }
}