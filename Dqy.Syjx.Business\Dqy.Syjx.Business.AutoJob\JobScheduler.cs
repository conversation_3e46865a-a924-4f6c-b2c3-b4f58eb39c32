using Quartz;
using Quartz.Impl;
using System.Collections.Specialized;

namespace Dqy.Syjx.Business.AutoJob
{
    public class JobScheduler
    {
        private static readonly object lockHelper = new();

        private static IScheduler scheduler = null;
        public static IScheduler GetScheduler()
        {
            lock (lockHelper)
            {
                if (scheduler != null)
                {
                    return scheduler;
                }
                else
                {
                    // 配置 Quartz 使用本地时区
                    var properties = new NameValueCollection
                    {
                        ["quartz.scheduler.timeZone"] = "Asia/Shanghai",
                        ["quartz.scheduler.instanceName"] = "QuartzScheduler",
                        ["quartz.scheduler.instanceId"] = "AUTO",
                        ["quartz.threadPool.type"] = "Quartz.Simpl.SimpleThreadPool, Quartz",
                        ["quartz.threadPool.threadCount"] = "10",
                        ["quartz.threadPool.threadPriority"] = "Normal",
                        ["quartz.jobStore.type"] = "Quartz.Simpl.RAMJobStore, Quartz"
                    };

                    ISchedulerFactory schedf = new StdSchedulerFactory(properties);
                    scheduler = schedf.GetScheduler().Result;
                    return scheduler;
                }
            }
        }
    }
}
