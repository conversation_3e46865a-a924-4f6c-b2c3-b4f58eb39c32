(function(n,t,i,r){var u=n.fn.peity=function(t,i){return s&&this.each(function(){var f=n(this),r=f.data("_peity");r?(t&&(r.type=t),n.extend(r.opts,i)):(r=new o(f,t,n.extend({},u.defaults[t],f.data("peity"),i)),f.change(function(){r.draw()}).data("_peity",r));r.draw()}),this},o=function(n,t,i){this.$el=n;this.type=t;this.opts=i},e=o.prototype,f=e.svgElement=function(i,r){return n(t.createElementNS("http://www.w3.org/2000/svg",i)).attr(r)},s="createElementNS"in t&&f("svg",{})[0].createSVGRect;e.draw=function(){var n=this.opts;u.graphers[this.type].call(this,n);n.after&&n.after.call(this,n)};e.fill=function(){var t=this.opts.fill;return n.isFunction(t)?t:function(n,i){return t[i%t.length]}};e.prepare=function(n,t){return this.$svg||this.$el.hide().after(this.$svg=f("svg",{"class":"peity"})),this.$svg.empty().data("_peity",this).attr({height:t,width:n})};e.values=function(){return n.map(this.$el.text().split(this.opts.delimiter),function(n){return parseFloat(n)})};u.defaults={};u.graphers={};u.register=function(n,t,i){this.defaults[n]=t;this.graphers[n]=i};u.register("pie",{fill:["#ff9900","#fff4dd","#ffc66e"],radius:8},function(t){var b,u,k,tt,s,a,v,nt,y;t.delimiter||(b=this.$el.text().match(/[^0-9\.]/),t.delimiter=b?b[0]:",");u=n.map(this.values(),function(n){return n>0?n:0});t.delimiter=="/"&&(k=u[0],tt=u[1],u=[k,i.max(0,tt-k)]);for(var o=0,d=u.length,l=0;o<d;o++)l+=u[o];l||(d=2,l=1,u=[0,1]);var it=t.radius*2,g=this.prepare(t.width||it,t.height||it),ot=g.width(),st=g.height(),h=ot/2,c=st/2,e=i.min(h,c),r=t.innerRadius;this.type!="donut"||r||(r=e*.5);var rt=i.PI,ht=this.fill(),p=this.scale=function(n,t){var r=n/l*rt*2-rt/2;return[t*i.cos(r)+h,t*i.sin(r)+c]},w=0;for(o=0;o<d;o++)if(s=u[o],a=s/l,a!=0){if(a==1)if(r){var ut=h-.01,ft=c-e,et=c-r;v=f("path",{d:["M",h,ft,"A",e,e,0,1,1,ut,ft,"L",ut,et,"A",r,r,0,1,0,h,et].join(" "),"data-value":s})}else v=f("circle",{cx:h,cy:c,"data-value":s,r:e});else nt=w+s,y=["M"].concat(p(w,e),"A",e,e,0,a>.5?1:0,1,p(nt,e),"L"),r?y=y.concat(p(nt,r),"A",r,r,0,a>.5?1:0,0,p(w,r)):y.push(h,c),w+=s,v=f("path",{d:y.join(" "),"data-value":s});v.attr("fill",ht.call(this,s,o,u));g.append(v)}});u.register("donut",n.extend(!0,{},u.defaults.pie),function(n){u.graphers.pie.call(this,n)});u.register("line",{delimiter:",",fill:"#c6d9fd",height:16,min:0,stroke:"#4d89f9",strokeWidth:1,width:32},function(n){var t=this.values(),e;t.length==1&&t.push(t[0]);var p=i.max.apply(i,n.max==r?t:t.concat(n.max)),h=i.min.apply(i,n.min==r?t:t.concat(n.min)),o=this.prepare(n.width,n.height),s=n.strokeWidth,c=o.width(),l=o.height()-s,a=p-h,w=this.x=function(n){return n*(c/(t.length-1))},v=this.y=function(n){var t=l;return a&&(t-=(n-h)/a*l),t+s/2},y=v(i.max(h,0)),u=[0,y];for(e=0;e<t.length;e++)u.push(w(e),v(t[e]));u.push(c,y);n.fill&&o.append(f("polygon",{fill:n.fill,points:u.join(" ")}));s&&o.append(f("polyline",{fill:"none",points:u.slice(2,u.length-2).join(" "),stroke:n.stroke,"stroke-width":s,"stroke-linecap":"square"}))});u.register("bar",{delimiter:",",fill:["#4D89F9"],height:16,min:0,padding:.1,width:32},function(n){for(var t=this.values(),c=i.max.apply(i,n.max==r?t:t.concat(n.max)),l=i.min.apply(i,n.min==r?t:t.concat(n.min)),a=this.prepare(n.width,n.height),g=a.width(),y=a.height(),o=c-l,p=n.padding,nt=this.fill(),w=this.x=function(n){return n*g/t.length},v=this.y=function(n){return y-(o?(n-l)/o*y:1)},u=0;u<t.length;u++){var b=w(u+p),tt=w(u+1-p)-b,s=t[u],k=v(s),h=k,d=k,e;o?s<0?h=v(i.min(c,0)):d=v(i.max(l,0)):e=1;e=d-h;e==0&&(e=1,c>0&&o&&h--);a.append(f("rect",{"data-value":s,fill:nt.call(this,s,u,t),x:b,y:h,width:tt,height:e}))}})})(jQuery,document,Math);