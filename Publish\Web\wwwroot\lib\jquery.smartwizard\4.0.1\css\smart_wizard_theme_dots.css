/* SmartWizard v4.0.1
 * jQ<PERSON>y Wizard Plugin
 * http://www.techlaboratory.net/smartwizard
 * 
 * Created by <PERSON><PERSON> <PERSON>  
 * http://dipuraj.me
 * 
 * Licensed under the terms of MIT License
 * https://github.com/techlab/SmartWizard/blob/master/MIT-LICENSE.txt 
 */

/* SmartWizard Theme: Dots */
.sw-theme-dots{
    /*box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.3);*/ 
}
.sw-theme-dots .sw-container {
    min-height: 300px;
}
.sw-theme-dots .step-content { 
    margin-top: 45px;
    padding: 10px 0;     
    border: 0px solid #D4D4D4;    
    background-color: #FFF;
    text-align: left;
}
.sw-theme-dots .sw-toolbar{
    background: #fff;
    border-radius: 0 !important; 
    padding-left: 10px;
    padding-right: 10px;
    margin-bottom: 0 !important;
}
.sw-theme-dots .sw-toolbar-top{
    border-bottom-color: #ddd !important;
}
.sw-theme-dots .sw-toolbar-bottom{
    border-top-color: #ddd !important;
    border-bottom-color: #ddd !important;
}
.sw-theme-dots > ul.step-anchor{
    background: #fff;
    display: block;
    border: 0px solid #ccc !important;
}
.sw-theme-dots > ul.step-anchor:before {
    top: 25px;
    bottom: 0;
    position: absolute;
    content: " ";
    width: 100%;
    height: 5px;
    background-color: #f5f5f5;
    border-radius: 3px;
    z-order: 0;
}
.sw-theme-dots > ul.step-anchor > li{
    position:relative;
    background: #f5f5f5;
    margin-top: 25px;
    height: 5px;
    display: block;
    border-radius: 3px;
}
.sw-theme-dots > ul.step-anchor > li > a{
    position: relative;
    text-align: center;
    font-weight: bold;
    z-index: 99;
    width: 30px;
    height: 30px;
    display: block;
    background: #f5f5f5;
    top: 2px;
    left: 50%;
    margin-top: -15px;
    margin-left: 45px;
    margin-right: 45px;
    border-radius: 50%;
    border: 0 !important;
        
    color: #428bca;
    text-decoration: none;
}
.sw-theme-dots > ul.step-anchor > li > a small{
    position: absolute;
    left: -40px;
    display: block;
    color: #999;
    margin-top: 20px;
    width: 120px;
}
.sw-theme-dots > ul.step-anchor > li > a, .sw-theme-dots > ul.step-anchor > li > a:hover  { 
    color: #bbb; 
    text-decoration: none;
    outline-style:none;
    background: #f5f5f5 !important;
}
.sw-theme-dots > ul.step-anchor > li.clickable > a:hover {
    color: #4285F4 !important; 
}
.sw-theme-dots > ul.step-anchor > li.active{
   background: #fbbd19 !important; 
}
.sw-theme-dots > ul.step-anchor > li.active > a { 
    border-color: #4285F4 !important; 
    color: #4285F4 !important; 
    background: #fbe8aa !important; 
}
.sw-theme-dots > ul.step-anchor > li.active > a:after {
    content: ' ';
    width: 14px;
    height: 14px;
    background: #fbbd19;
    border-radius: 50px;
    position: absolute;
    top: 8px;
    left: 8px;
}
.sw-theme-dots > ul.step-anchor > li.done{
   background: #fbbd19 !important; 
}
.sw-theme-dots > ul.step-anchor > li.done > a { 
    border-color: #5cb85c !important; 
    color: #5cb85c !important; 
    background: #fbe8aa !important; 
}
.sw-theme-dots > ul.step-anchor > li.done > a:after {
    content: ' ';
    width: 14px;
    height: 14px;
    background: #5cb85c;
    border-radius: 50px;
    position: absolute;
    top: 8px;
    left: 8px;
}
.sw-theme-dots > ul.step-anchor > li.danger > a { 
    border-color: #d9534f !important; 
    color: #d9534f !important; 
    background: #fff !important; 
}
.sw-theme-dots > ul.step-anchor > li.disabled > a, .sw-theme-dots > ul.step-anchor > li.disabled > a:hover {
    color: #eee !important;
}

/* Loader Animation 
 * Courtesy: http://bootsnipp.com/snippets/featured/loading-button-effect-no-js
 */
@-webkit-keyframes ld {
  0%   { transform: rotate(0deg) scale(1); }
  50%  { transform: rotate(180deg) scale(1.1); }
  100% { transform: rotate(360deg) scale(1); }
}
@-moz-keyframes ld {
  0%   { transform: rotate(0deg) scale(1); }
  50%  { transform: rotate(180deg) scale(1.1); }
  100% { transform: rotate(360deg) scale(1); }
}
@-o-keyframes ld {
  0%   { transform: rotate(0deg) scale(1); }
  50%  { transform: rotate(180deg) scale(1.1); }
  100% { transform: rotate(360deg) scale(1); }
}
@keyframes ld {
  0%   { transform: rotate(0deg) scale(1); }
  50%  { transform: rotate(180deg) scale(1.1); }
  100% { transform: rotate(360deg) scale(1); }
}
.sw-theme-dots > ul.step-anchor > li.loading:before {
    content: '';
    display: inline-block;
    position: absolute;
    background: transparent;
    border-radius: 50%;
    box-sizing: border-box;
    border: 2px solid #fff;
    border-top-color: transparent;
    border-bottom-color: transparent;
    border-left-color: #4285f4;
    border-right-color: #4285f4;
    top: 50%;
    left: 50%;
    margin-top: -16px;
    margin-left: -16px;
    width: 32px;
    height: 32px;
    -webkit-animation: ld 1s ease-in-out infinite;
    -moz-animation:    ld 1s ease-in-out infinite;
    -o-animation:      ld 1s ease-in-out infinite;
    animation:         ld 1s ease-in-out infinite;
}